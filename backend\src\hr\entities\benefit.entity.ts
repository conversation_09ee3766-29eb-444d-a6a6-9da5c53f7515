import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { EmployeeBenefit } from './employee-benefit.entity';

export enum BenefitType {
  HEALTH_INSURANCE = 'health_insurance',
  DENTAL_INSURANCE = 'dental_insurance',
  VISION_INSURANCE = 'vision_insurance',
  LIFE_INSURANCE = 'life_insurance',
  DISABILITY_INSURANCE = 'disability_insurance',
  RETIREMENT_PLAN = 'retirement_plan',
  PAID_TIME_OFF = 'paid_time_off',
  SICK_LEAVE = 'sick_leave',
  MATERNITY_LEAVE = 'maternity_leave',
  PATERNITY_LEAVE = 'paternity_leave',
  FLEXIBLE_SPENDING = 'flexible_spending',
  COMMUTER_BENEFITS = 'commuter_benefits',
  GYM_MEMBERSHIP = 'gym_membership',
  EDUCATION_ASSISTANCE = 'education_assistance',
  EMPLOYEE_DISCOUNT = 'employee_discount',
  OTHER = 'other',
}

export enum BenefitCategory {
  INSURANCE = 'insurance',
  RETIREMENT = 'retirement',
  TIME_OFF = 'time_off',
  WELLNESS = 'wellness',
  FINANCIAL = 'financial',
  LIFESTYLE = 'lifestyle',
  PROFESSIONAL_DEVELOPMENT = 'professional_development',
}

@Entity('hr_benefits')
export class Benefit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: BenefitType,
  })
  type: BenefitType;

  @Column({
    type: 'enum',
    enum: BenefitCategory,
  })
  category: BenefitCategory;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  employerCost: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  employeeCost: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  employerContributionPercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  employeeContributionPercentage: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isMandatory: boolean;

  @Column({ default: true })
  isElective: boolean;

  @Column({ type: 'int', nullable: true })
  waitingPeriodDays: number;

  @Column({ type: 'json', nullable: true })
  eligibilityCriteria: any;

  @Column({ type: 'json', nullable: true })
  coverageDetails: any;

  @Column({ length: 200, nullable: true })
  provider: string;

  @Column({ length: 200, nullable: true })
  providerContact: string;

  @Column({ type: 'date', nullable: true })
  effectiveDate: Date;

  @Column({ type: 'date', nullable: true })
  expiryDate: Date;

  @OneToMany(() => EmployeeBenefit, employeeBenefit => employeeBenefit.benefit)
  employeeBenefits: EmployeeBenefit[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
