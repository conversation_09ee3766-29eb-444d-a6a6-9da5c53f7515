import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PaymentPlan } from './payment-plan.entity';

export enum InstallmentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  PARTIAL = 'partial',
  SKIPPED = 'skipped',
  WAIVED = 'waived',
}

@Entity('payment_plan_installments')
export class PaymentPlanInstallment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  paymentPlanId: string;

  @ManyToOne(() => PaymentPlan, plan => plan.installments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'paymentPlanId' })
  paymentPlan: PaymentPlan;

  @Column({ type: 'int' })
  installmentNumber: number;

  @Column({ type: 'date' })
  dueDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  paidAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  remainingAmount: number;

  @Column({
    type: 'enum',
    enum: InstallmentStatus,
    default: InstallmentStatus.PENDING,
  })
  status: InstallmentStatus;

  @Column({ type: 'date', nullable: true })
  paidDate: Date;

  @Column({ type: 'int', default: 0 })
  daysOverdue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  lateFee: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  paymentDetails: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
