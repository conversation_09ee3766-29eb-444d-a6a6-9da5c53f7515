"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Performance = exports.PerformanceRating = exports.PerformanceStatus = exports.PerformanceType = void 0;
const typeorm_1 = require("typeorm");
const employee_entity_1 = require("./employee.entity");
var PerformanceType;
(function (PerformanceType) {
    PerformanceType["ANNUAL_REVIEW"] = "annual_review";
    PerformanceType["QUARTERLY_REVIEW"] = "quarterly_review";
    PerformanceType["MONTHLY_REVIEW"] = "monthly_review";
    PerformanceType["PROJECT_REVIEW"] = "project_review";
    PerformanceType["PROBATION_REVIEW"] = "probation_review";
    PerformanceType["GOAL_SETTING"] = "goal_setting";
})(PerformanceType || (exports.PerformanceType = PerformanceType = {}));
var PerformanceStatus;
(function (PerformanceStatus) {
    PerformanceStatus["DRAFT"] = "draft";
    PerformanceStatus["IN_PROGRESS"] = "in_progress";
    PerformanceStatus["COMPLETED"] = "completed";
    PerformanceStatus["APPROVED"] = "approved";
    PerformanceStatus["CANCELLED"] = "cancelled";
})(PerformanceStatus || (exports.PerformanceStatus = PerformanceStatus = {}));
var PerformanceRating;
(function (PerformanceRating) {
    PerformanceRating["OUTSTANDING"] = "outstanding";
    PerformanceRating["EXCEEDS_EXPECTATIONS"] = "exceeds_expectations";
    PerformanceRating["MEETS_EXPECTATIONS"] = "meets_expectations";
    PerformanceRating["BELOW_EXPECTATIONS"] = "below_expectations";
    PerformanceRating["UNSATISFACTORY"] = "unsatisfactory";
})(PerformanceRating || (exports.PerformanceRating = PerformanceRating = {}));
let Performance = class Performance {
    id;
    employeeId;
    employee;
    type;
    status;
    reviewPeriodStart;
    reviewPeriodEnd;
    dueDate;
    overallRating;
    overallScore;
    goals;
    achievements;
    competencies;
    ratings;
    employeeSelfReview;
    managerReview;
    strengths;
    areasForImprovement;
    developmentPlan;
    comments;
    reviewedBy;
    reviewedAt;
    approvedBy;
    approvedAt;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.Performance = Performance;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Performance.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Performance.prototype, "employeeId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => employee_entity_1.Employee, employee => employee.performances),
    (0, typeorm_1.JoinColumn)({ name: 'employeeId' }),
    __metadata("design:type", employee_entity_1.Employee)
], Performance.prototype, "employee", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PerformanceType,
    }),
    __metadata("design:type", String)
], Performance.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PerformanceStatus,
        default: PerformanceStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Performance.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Performance.prototype, "reviewPeriodStart", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Performance.prototype, "reviewPeriodEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Performance.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PerformanceRating,
        nullable: true,
    }),
    __metadata("design:type", String)
], Performance.prototype, "overallRating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], Performance.prototype, "overallScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Performance.prototype, "goals", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Performance.prototype, "achievements", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Performance.prototype, "competencies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Performance.prototype, "ratings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "employeeSelfReview", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "managerReview", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "strengths", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "areasForImprovement", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "developmentPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "comments", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Performance.prototype, "reviewedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Performance.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Performance.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Performance.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Performance.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Performance.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Performance.prototype, "updatedAt", void 0);
exports.Performance = Performance = __decorate([
    (0, typeorm_1.Entity)('hr_performance')
], Performance);
//# sourceMappingURL=performance.entity.js.map