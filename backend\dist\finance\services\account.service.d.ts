import { Repository } from 'typeorm';
import { Account, AccountType, AccountSubType } from '../entities/account.entity';
export declare class AccountService {
    private accountRepository;
    constructor(accountRepository: Repository<Account>);
    create(createAccountDto: any): Promise<Account>;
    findAll(): Promise<Account[]>;
    findOne(id: string): Promise<Account>;
    findByType(type: AccountType): Promise<Account[]>;
    findBySubType(subType: AccountSubType): Promise<Account[]>;
    update(id: string, updateAccountDto: any): Promise<Account>;
    remove(id: string): Promise<void>;
    updateBalance(accountId: string, amount: number, isDebit: boolean): Promise<Account>;
    getAccountHierarchy(): Promise<Account[]>;
    getTrialBalance(asOfDate?: Date): Promise<any>;
    private generateAccountNumber;
    private getAccountPrefix;
    private buildAccountTree;
}
