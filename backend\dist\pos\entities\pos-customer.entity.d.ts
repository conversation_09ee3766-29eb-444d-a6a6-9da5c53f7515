export declare enum CustomerType {
    REGULAR = "regular",
    VIP = "vip",
    WHOLESALE = "wholesale",
    EMPLOYEE = "employee",
    GUEST = "guest"
}
export declare enum CustomerStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    BLACKLISTED = "blacklisted"
}
export declare class PosCustomer {
    id: string;
    customerNumber: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date;
    type: CustomerType;
    status: CustomerStatus;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    totalSpent: number;
    averageOrderValue: number;
    totalOrders: number;
    loyaltyPoints: number;
    storeCredit: number;
    discountPercentage: number;
    lastPurchaseDate: Date;
    preferences: any;
    tags: string[];
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
