{"version": 3, "file": "customer-report.service.js", "sourceRoot": "", "sources": ["../../../src/customers/services/customer-report.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAA8C;AAC9C,iEAAmG;AACnG,yFAA8E;AAC9E,iFAAsE;AAG/D,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAGtB;IAEA;IAEA;IANV,YAEU,kBAAwC,EAExC,qBAAsD,EAEtD,iBAA8C;QAJ9C,uBAAkB,GAAlB,kBAAkB,CAAsB;QAExC,0BAAqB,GAArB,qBAAqB,CAAiC;QAEtD,sBAAiB,GAAjB,iBAAiB,CAA6B;IACrD,CAAC;IAEJ,KAAK,CAAC,6BAA6B;QACjC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAC7D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1G,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACtG,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAG9F,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB;aAChD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,MAAM,CAAC;YACN,0CAA0C;YAC1C,kDAAkD;YAClD,kDAAkD;YAClD,mDAAmD;SACpD,CAAC;aACD,SAAS,EAAE,CAAC;QAGf,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAEpD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACvD,KAAK,EAAE,EAAE,SAAS,EAAE,IAAA,iBAAO,EAAC,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE;SACzD,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE;gBACP,cAAc;gBACd,eAAe;gBACf,SAAS;gBACT,KAAK;gBACL,qBAAqB,EAAE,YAAY;gBACnC,cAAc,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aACxE;YACD,SAAS,EAAE;gBACT,YAAY,EAAE,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC;gBACzD,oBAAoB,EAAE,UAAU,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBACzE,gBAAgB,EAAE,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBACjE,kBAAkB,EAAE,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC;aACpE;YACD,MAAM,EAAE;gBACN,qBAAqB,EAAE,YAAY;gBACnC,UAAU,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aAC3E;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,6BAA6B;QACjC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACjD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC7D,WAAW,CAAC,IAAI,CAAC;gBACf,WAAW,EAAE,OAAO,CAAC,IAAI;gBACzB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,GAAG,SAAS;aACb,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,QAAQ,EAAE,WAAW;SACtB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,SAAe,EAAE,OAAa;QAC5D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACzD,KAAK,EAAE,EAAE,eAAe,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,OAAO,CAAC,EAAE;YACvD,SAAS,EAAE,CAAC,UAAU,CAAC;SACxB,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YAC5D,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YAC/D,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YAC/D,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjE,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YAC3D,MAAM,IAAI,GAAG,WAAW,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,OAAO;YACL,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;YAC9B,iBAAiB,EAAE,YAAY,CAAC,MAAM;YACtC,YAAY;YACZ,eAAe;YACf,eAAe;YACf,WAAW;YACX,yBAAyB,EAAE,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC;SACzF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,+BAA+B;QACnC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACzE,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACxE,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAGzE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB;aAClD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,MAAM,CAAC,mDAAmD,CAAC;aAC3D,SAAS,CAAC,mBAAmB,CAAC;aAC9B,KAAK,CAAC,kCAAkC,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC;aACvE,OAAO,CAAC,OAAO,CAAC;aAChB,OAAO,CAAC,OAAO,CAAC;aAChB,UAAU,EAAE,CAAC;QAGhB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC/D,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,MAAM,EAAE;SACzC,CAAC,CAAC;QAEH,MAAM,2BAA2B,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACtE,KAAK,EAAE;gBACL,MAAM,EAAE,gCAAc,CAAC,MAAM;gBAC7B,eAAe,EAAE,IAAA,iBAAO,EAAC,aAAa,EAAE,GAAG,CAAC;aAC7C;SACF,CAAC,CAAC;QAEH,MAAM,2BAA2B,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACtE,KAAK,EAAE;gBACL,MAAM,EAAE,gCAAc,CAAC,MAAM;gBAC7B,gBAAgB,EAAE,IAAA,iBAAO,EAAC,aAAa,EAAE,GAAG,CAAC;aAC9C;SACF,CAAC,CAAC;QAGH,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC7D,KAAK,EAAE;gBACL,MAAM,EAAE,gCAAc,CAAC,MAAM;gBAC7B,eAAe,EAAE,IAAA,iBAAO,EAAC,YAAY,EAAE,aAAa,CAAC;aACtD;SACF,CAAC,CAAC;QAEH,OAAO;YACL,WAAW,EAAE;gBACX,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAClC,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;iBAC5B,CAAC,CAAC;aACJ;YACD,SAAS,EAAE;gBACT,oBAAoB;gBACpB,2BAA2B;gBAC3B,2BAA2B;gBAC3B,aAAa,EAAE,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B,GAAG,oBAAoB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aACzG;YACD,SAAS,EAAE;gBACT,eAAe,EAAE,kBAAkB;gBACnC,mBAAmB,EAAE,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aACtG;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,QAAgB,EAAE;QAEjD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACtD,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,MAAM,EAAE;YACxC,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,CAAC;SAC7E,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACtD,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,MAAM,EAAE;YACxC,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;YAChC,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,CAAC;SAChF,CAAC,CAAC;QAGH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB;aAC7C,kBAAkB,CAAC,UAAU,CAAC;aAC9B,QAAQ,CAAC,uBAAuB,EAAE,aAAa,CAAC;aAChD,MAAM,CAAC;YACN,aAAa;YACb,oBAAoB;YACpB,mBAAmB;YACnB,sBAAsB;YACtB,2CAA2C;SAC5C,CAAC;aACD,KAAK,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,MAAM,EAAE,CAAC;aACrE,OAAO,CAAC,aAAa,CAAC;aACtB,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;aACnC,KAAK,CAAC,KAAK,CAAC;aACZ,UAAU,EAAE,CAAC;QAEhB,OAAO;YACL,YAAY;YACZ,YAAY;YACZ,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACtC,GAAG,QAAQ;gBACX,gBAAgB,EAAE,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC;aACtD,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3D,KAAK,EAAE;gBACL,MAAM,EAAE,gCAAc,CAAC,MAAM;gBAC7B,eAAe,EAAE,IAAA,iBAAO,EAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;aACtF;SACF,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC1D,KAAK,EAAE;gBACL,MAAM,EAAE,gCAAc,CAAC,MAAM;gBAC7B,eAAe,EAAE,IAAA,iBAAO,EACtB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAChD;aACF;SACF,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC5D,KAAK,EAAE,EAAE,MAAM,EAAE,gCAAc,CAAC,QAAQ,EAAE;SAC3C,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAE7D,OAAO;YACL,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,WAAW,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/E,SAAS,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAC7E,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAAiB;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACZ,CAAC;QAID,OAAO;YACL,YAAY,EAAE,CAAC;YACf,oBAAoB,EAAE,CAAC;YACvB,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,SAAe,EAAE,OAAa;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,SAA0B,KAAK;QACtD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACnD,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC;SAC9C,CAAC,CAAC;QAGH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC5C,cAAc,EAAE,QAAQ,CAAC,cAAc;YACvC,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,KAAK,EAAE,QAAQ,CAAC,YAAY;YAC5B,KAAK,EAAE,QAAQ,CAAC,YAAY;YAC5B,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;YACrC,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe;YACzC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;SAC5C,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,MAAM;YACN,QAAQ,EAAE,oBAAoB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;SACjF,CAAC;IACJ,CAAC;CACF,CAAA;AA3TY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAQ,CAAC,CAAA;IAE1B,WAAA,IAAA,0BAAgB,EAAC,iDAAmB,CAAC,CAAA;IAErC,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCAHN,oBAAU;QAEP,oBAAU;QAEd,oBAAU;GAP5B,qBAAqB,CA2TjC"}