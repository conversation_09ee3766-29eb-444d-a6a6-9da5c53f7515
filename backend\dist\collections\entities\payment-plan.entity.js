"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentPlan = exports.PaymentFrequency = exports.PaymentPlanStatus = void 0;
const typeorm_1 = require("typeorm");
const collection_case_entity_1 = require("./collection-case.entity");
const payment_plan_installment_entity_1 = require("./payment-plan-installment.entity");
var PaymentPlanStatus;
(function (PaymentPlanStatus) {
    PaymentPlanStatus["DRAFT"] = "draft";
    PaymentPlanStatus["PENDING_APPROVAL"] = "pending_approval";
    PaymentPlanStatus["ACTIVE"] = "active";
    PaymentPlanStatus["COMPLETED"] = "completed";
    PaymentPlanStatus["DEFAULTED"] = "defaulted";
    PaymentPlanStatus["CANCELLED"] = "cancelled";
    PaymentPlanStatus["MODIFIED"] = "modified";
})(PaymentPlanStatus || (exports.PaymentPlanStatus = PaymentPlanStatus = {}));
var PaymentFrequency;
(function (PaymentFrequency) {
    PaymentFrequency["WEEKLY"] = "weekly";
    PaymentFrequency["BIWEEKLY"] = "biweekly";
    PaymentFrequency["MONTHLY"] = "monthly";
    PaymentFrequency["QUARTERLY"] = "quarterly";
    PaymentFrequency["CUSTOM"] = "custom";
})(PaymentFrequency || (exports.PaymentFrequency = PaymentFrequency = {}));
let PaymentPlan = class PaymentPlan {
    id;
    caseId;
    case;
    planNumber;
    status;
    totalAmount;
    downPayment;
    remainingAmount;
    numberOfInstallments;
    installmentAmount;
    frequency;
    startDate;
    endDate;
    firstPaymentDate;
    interestRate;
    setupFee;
    lateFee;
    gracePeriodDays;
    terms;
    notes;
    createdBy;
    approvedBy;
    approvedAt;
    customerSignature;
    customerSignedAt;
    missedPayments;
    totalPaid;
    totalOutstanding;
    installments;
    metadata;
    createdAt;
    updatedAt;
};
exports.PaymentPlan = PaymentPlan;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PaymentPlan.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PaymentPlan.prototype, "caseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_case_entity_1.CollectionCase, collectionCase => collectionCase.paymentPlans),
    (0, typeorm_1.JoinColumn)({ name: 'caseId' }),
    __metadata("design:type", collection_case_entity_1.CollectionCase)
], PaymentPlan.prototype, "case", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "planNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentPlanStatus,
        default: PaymentPlanStatus.DRAFT,
    }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "downPayment", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "remainingAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "numberOfInstallments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "installmentAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentFrequency,
        default: PaymentFrequency.MONTHLY,
    }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "frequency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "firstPaymentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "interestRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "setupFee", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "lateFee", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 5 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "gracePeriodDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "terms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PaymentPlan.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PaymentPlan.prototype, "customerSignature", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "customerSignedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "missedPayments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "totalPaid", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlan.prototype, "totalOutstanding", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => payment_plan_installment_entity_1.PaymentPlanInstallment, installment => installment.paymentPlan, { cascade: true }),
    __metadata("design:type", Array)
], PaymentPlan.prototype, "installments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PaymentPlan.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PaymentPlan.prototype, "updatedAt", void 0);
exports.PaymentPlan = PaymentPlan = __decorate([
    (0, typeorm_1.Entity)('payment_plans')
], PaymentPlan);
//# sourceMappingURL=payment-plan.entity.js.map