import {
  Controller,
  Get,
  Query,
  UseGuards,
} from '@nestjs/common';
import { InventoryReportService } from '../services/inventory-report.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('inventory-reports')
@UseGuards(JwtAuthGuard)
export class InventoryReportController {
  constructor(private readonly inventoryReportService: InventoryReportService) {}

  @Get('valuation')
  async generateInventoryValuationReport() {
    return this.inventoryReportService.generateInventoryValuationReport();
  }

  @Get('stock-levels')
  async generateStockLevelReport() {
    return this.inventoryReportService.generateStockLevelReport();
  }

  @Get('movements')
  async generateMovementReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    return this.inventoryReportService.generateMovementReport(start, end);
  }

  @Get('purchase-orders')
  async generatePurchaseOrderReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    return this.inventoryReportService.generatePurchaseOrderReport(start, end);
  }

  @Get('abc-analysis')
  async generateABCAnalysisReport() {
    return this.inventoryReportService.generateABCAnalysisReport();
  }
}
