import { Repository } from 'typeorm';
import { PurchaseOrder } from '../entities/purchase-order.entity';
import { PurchaseOrderItem } from '../entities/purchase-order-item.entity';
import { StockService } from './stock.service';
export declare class PurchaseOrderService {
    private purchaseOrderRepository;
    private purchaseOrderItemRepository;
    private stockService;
    constructor(purchaseOrderRepository: Repository<PurchaseOrder>, purchaseOrderItemRepository: Repository<PurchaseOrderItem>, stockService: StockService);
    create(orderData: Partial<PurchaseOrder>): Promise<PurchaseOrder>;
    findAll(): Promise<PurchaseOrder[]>;
    findOne(id: string): Promise<PurchaseOrder>;
    update(id: string, updateData: Partial<PurchaseOrder>): Promise<PurchaseOrder>;
    remove(id: string): Promise<void>;
    addItem(orderId: string, itemData: Partial<PurchaseOrderItem>): Promise<PurchaseOrderItem>;
    updateItem(itemId: string, updateData: Partial<PurchaseOrderItem>): Promise<PurchaseOrderItem>;
    removeItem(itemId: string): Promise<void>;
    private recalculateOrderTotals;
    receiveOrder(orderId: string, warehouseId: string): Promise<PurchaseOrder>;
    partialReceive(orderId: string, warehouseId: string, receivedItems: Array<{
        itemId: string;
        receivedQuantity: number;
    }>): Promise<PurchaseOrder>;
    cancelOrder(orderId: string, reason: string): Promise<PurchaseOrder>;
    findBySupplier(supplierId: string): Promise<PurchaseOrder[]>;
    findByStatus(status: string): Promise<PurchaseOrder[]>;
    getPurchaseOrderStatistics(): Promise<any>;
    generateOrderNumber(): Promise<string>;
}
