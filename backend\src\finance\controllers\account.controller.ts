import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccountService } from '../services/account.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/accounts')
@UseGuards(JwtAuthGuard)
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Post()
  create(@Body() createAccountDto: any) {
    return this.accountService.create(createAccountDto);
  }

  @Get()
  findAll(@Query('type') type?: string, @Query('subType') subType?: string) {
    if (type) {
      return this.accountService.findByType(type as any);
    }
    if (subType) {
      return this.accountService.findBySubType(subType as any);
    }
    return this.accountService.findAll();
  }

  @Get('hierarchy')
  getAccountHierarchy() {
    return this.accountService.getAccountHierarchy();
  }

  @Get('trial-balance')
  getTrialBalance(@Query('asOfDate') asOfDate?: string) {
    const date = asOfDate ? new Date(asOfDate) : undefined;
    return this.accountService.getTrialBalance(date);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.accountService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateAccountDto: any) {
    return this.accountService.update(id, updateAccountDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.accountService.remove(id);
  }

  @Post(':id/update-balance')
  updateBalance(
    @Param('id') id: string,
    @Body() updateBalanceDto: { amount: number; isDebit: boolean }
  ) {
    return this.accountService.updateBalance(id, updateBalanceDto.amount, updateBalanceDto.isDebit);
  }
}
