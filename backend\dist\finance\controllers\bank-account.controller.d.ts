import { BankAccountService } from '../services/bank-account.service';
export declare class BankAccountController {
    private readonly bankAccountService;
    constructor(bankAccountService: BankAccountService);
    create(createBankAccountDto: any): Promise<import("../entities/bank-account.entity").BankAccount>;
    findAll(): Promise<import("../entities/bank-account.entity").BankAccount[]>;
    findOne(id: string): Promise<import("../entities/bank-account.entity").BankAccount>;
    update(id: string, updateBankAccountDto: any): Promise<import("../entities/bank-account.entity").BankAccount>;
    remove(id: string): Promise<void>;
    addTransaction(id: string, transactionData: any): Promise<import("../entities/bank-transaction.entity").BankTransaction>;
    importTransactions(id: string, importData: {
        transactions: any[];
    }): Promise<import("../entities/bank-transaction.entity").BankTransaction[]>;
    reconcileAccount(id: string, reconcileData: {
        reconciledBalance: number;
        reconciledBy: string;
    }): Promise<import("../entities/bank-account.entity").BankAccount>;
    getAccountStatement(id: string, startDate?: string, endDate?: string): Promise<any>;
    getUnreconciledTransactions(id: string): Promise<import("../entities/bank-transaction.entity").BankTransaction[]>;
    setDefaultAccount(id: string): Promise<import("../entities/bank-account.entity").BankAccount>;
}
