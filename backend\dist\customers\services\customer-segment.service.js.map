{"version": 3, "file": "customer-segment.service.js", "sourceRoot": "", "sources": ["../../../src/customers/services/customer-segment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoF;AACpF,6CAAmD;AACnD,qCAAkE;AAClE,iFAAsE;AACtE,iEAAuD;AAGhD,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGvB;IAEA;IAJV,YAEU,iBAA8C,EAE9C,kBAAwC;QAFxC,sBAAiB,GAAjB,iBAAiB,CAA6B;QAE9C,uBAAkB,GAAlB,kBAAkB,CAAsB;IAC/C,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,WAAqC;QAEhD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE;SAClC,CAAC,CAAC;QAEH,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,IAAI,4BAAmB,CAAC,qBAAqB,WAAW,CAAC,IAAI,iBAAiB,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAGhE,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACjC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAoC;QAC3D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGvC,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;YACxD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;aACjC,CAAC,CAAC;YACH,IAAI,eAAe,EAAE,CAAC;gBACpB,MAAM,IAAI,4BAAmB,CAAC,qBAAqB,UAAU,CAAC,IAAI,iBAAiB,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAGpD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACvC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,SAAiB;QAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAGvE,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,EAAE;YAC7C,aAAa,EAAE,SAAS,CAAC,MAAM;YAC/B,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC,MAAM,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,QAAa;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAG5E,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnC,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5F,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,QAAQ,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,QAAQ,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC1C,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;YACnG,CAAC;YACD,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC1C,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;YACnG,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC3B,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC7C,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;YAC3G,CAAC;YACD,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC7C,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,yCAAyC,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC;YACnH,CAAC;YACD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;gBACrC,YAAY,CAAC,QAAQ,CAAC,0CAA0C,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;YACtH,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,oCAAoC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9F,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAE9C,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,KAAa,EAAE,EAAE;gBACrE,YAAY,CAAC,YAAY,CAAC,MAAM,KAAK,EAAE,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;gBACvD,OAAO,0BAA0B,KAAK,EAAE,CAAC;YAC3C,CAAC,CAAC,CAAC;YACH,YAAY,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC1C,YAAY,CAAC,QAAQ,CAAC,2CAA2C,EAAE,EAAE,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC;QAClH,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBAC7B,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;YAC3G,CAAC;YACD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9G,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QAM/E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;gBACL,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE,CAAC;gBACR,IAAI;gBACJ,UAAU,EAAE,CAAC;aACd,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAClC,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;QAE7D,OAAO;YACL,SAAS;YACT,KAAK;YACL,IAAI;YACJ,UAAU;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,WAAmB,KAAK;QACnD,OAAO,IAAI,CAAC,MAAM,CAAC;YACjB,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,uCAAuC,QAAQ,EAAE;YAC9D,QAAQ,EAAE;gBACR,UAAU,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;gBAC7B,MAAM,EAAE,CAAC,QAAQ,CAAC;aACnB;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,YAAoB,IAAI;QACjD,OAAO,IAAI,CAAC,MAAM,CAAC;YACjB,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,kBAAkB,SAAS,kBAAkB;YAC1D,QAAQ,EAAE;gBACR,aAAa,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;gBACjC,MAAM,EAAE,CAAC,QAAQ,CAAC;aACnB;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,wBAAgC,EAAE;QAC5D,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,qBAAqB,CAAC,CAAC;QAEjE,OAAO,IAAI,CAAC,MAAM,CAAC;YACjB,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,sCAAsC,qBAAqB,OAAO;YAC/E,QAAQ,EAAE;gBACR,gBAAgB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;gBACxC,MAAM,EAAE,CAAC,QAAQ,CAAC;aACnB;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,wBAAgC,EAAE;QAC/D,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,qBAAqB,CAAC,CAAC;QAEjE,OAAO,IAAI,CAAC,MAAM,CAAC;YACjB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,oCAAoC,qBAAqB,OAAO;YAC7E,QAAQ,EAAE;gBACR,SAAS,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;aACjC;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,SAAiB;QACzC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QAE7E,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;QACxC,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,MAAM,YAAY,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAGzF,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC5D,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC1D,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC1D,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,OAAO;YACL,SAAS;YACT,WAAW,EAAE,OAAO,CAAC,IAAI;YACzB,cAAc;YACd,UAAU;YACV,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;YAClB,gBAAgB;YAChB,gBAAgB;YAChB,WAAW,EAAE,OAAO,CAAC,WAAW;SACjC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACjD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACrE,OAAO,CAAC,IAAI,CAAC;gBACX,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,aAAa;aACd,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU;QAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU;QAChC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;CACF,CAAA;AA5UY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;IAEjC,WAAA,IAAA,0BAAgB,EAAC,0BAAQ,CAAC,CAAA;qCADA,oBAAU;QAET,oBAAU;GAL7B,sBAAsB,CA4UlC"}