import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { PurchaseOrderService } from '../services/purchase-order.service';
import { PurchaseOrder } from '../entities/purchase-order.entity';
import { PurchaseOrderItem } from '../entities/purchase-order-item.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('purchase-orders')
@UseGuards(JwtAuthGuard)
export class PurchaseOrderController {
  constructor(private readonly purchaseOrderService: PurchaseOrderService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createPurchaseOrderDto: Partial<PurchaseOrder>) {
    return this.purchaseOrderService.create(createPurchaseOrderDto);
  }

  @Get()
  async findAll(@Query('status') status?: string) {
    if (status) {
      return this.purchaseOrderService.findByStatus(status);
    }
    return this.purchaseOrderService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.purchaseOrderService.getPurchaseOrderStatistics();
  }

  @Get('supplier/:supplierId')
  async findBySupplier(@Param('supplierId') supplierId: string) {
    return this.purchaseOrderService.findBySupplier(supplierId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.purchaseOrderService.findOne(id);
  }

  @Post('generate-order-number')
  async generateOrderNumber() {
    const orderNumber = await this.purchaseOrderService.generateOrderNumber();
    return { orderNumber };
  }

  @Post(':id/items')
  async addItem(
    @Param('id') id: string,
    @Body() createItemDto: Partial<PurchaseOrderItem>,
  ) {
    return this.purchaseOrderService.addItem(id, createItemDto);
  }

  @Patch('items/:itemId')
  async updateItem(
    @Param('itemId') itemId: string,
    @Body() updateItemDto: Partial<PurchaseOrderItem>,
  ) {
    return this.purchaseOrderService.updateItem(itemId, updateItemDto);
  }

  @Delete('items/:itemId')
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeItem(@Param('itemId') itemId: string) {
    return this.purchaseOrderService.removeItem(itemId);
  }

  @Post(':id/receive')
  async receiveOrder(
    @Param('id') id: string,
    @Body() receiveData: { warehouseId: string },
  ) {
    return this.purchaseOrderService.receiveOrder(id, receiveData.warehouseId);
  }

  @Post(':id/partial-receive')
  async partialReceive(
    @Param('id') id: string,
    @Body() receiveData: {
      warehouseId: string;
      receivedItems: Array<{ itemId: string; receivedQuantity: number }>;
    },
  ) {
    return this.purchaseOrderService.partialReceive(
      id,
      receiveData.warehouseId,
      receiveData.receivedItems,
    );
  }

  @Post(':id/cancel')
  async cancelOrder(
    @Param('id') id: string,
    @Body() cancelData: { reason: string },
  ) {
    return this.purchaseOrderService.cancelOrder(id, cancelData.reason);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updatePurchaseOrderDto: Partial<PurchaseOrder>,
  ) {
    return this.purchaseOrderService.update(id, updatePurchaseOrderDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.purchaseOrderService.remove(id);
  }
}
