"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditNoteController = void 0;
const common_1 = require("@nestjs/common");
const credit_note_service_1 = require("../services/credit-note.service");
const create_credit_note_dto_1 = require("../dto/create-credit-note.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CreditNoteController = class CreditNoteController {
    creditNoteService;
    constructor(creditNoteService) {
        this.creditNoteService = creditNoteService;
    }
    create(createCreditNoteDto, req) {
        return this.creditNoteService.create(createCreditNoteDto, req.user.tenantId);
    }
    findAll(req) {
        return this.creditNoteService.findAll(req.user.tenantId);
    }
    getStats(req) {
        return this.creditNoteService.getCreditNoteStats(req.user.tenantId);
    }
    findOne(id, req) {
        return this.creditNoteService.findOne(id, req.user.tenantId);
    }
    update(id, updateCreditNoteDto, req) {
        return this.creditNoteService.update(id, updateCreditNoteDto, req.user.tenantId);
    }
    remove(id, req) {
        return this.creditNoteService.remove(id, req.user.tenantId);
    }
    updateStatus(id, body, req) {
        return this.creditNoteService.updateStatus(id, body.status, req.user.tenantId);
    }
};
exports.CreditNoteController = CreditNoteController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_credit_note_dto_1.CreateCreditNoteDto, Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], CreditNoteController.prototype, "updateStatus", null);
exports.CreditNoteController = CreditNoteController = __decorate([
    (0, common_1.Controller)('sales/credit-notes'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [credit_note_service_1.CreditNoteService])
], CreditNoteController);
//# sourceMappingURL=credit-note.controller.js.map