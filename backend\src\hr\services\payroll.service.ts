import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Payroll, PayrollStatus } from '../entities/payroll.entity';
import { PayrollItem } from '../entities/payroll-item.entity';

@Injectable()
export class PayrollService {
  constructor(
    @InjectRepository(Payroll)
    private payrollRepository: Repository<Payroll>,
    @InjectRepository(PayrollItem)
    private payrollItemRepository: Repository<PayrollItem>,
  ) {}

  async create(createPayrollDto: any): Promise<Payroll> {
    const payrollNumber = await this.generatePayrollNumber();
    
    const payroll = this.payrollRepository.create({
      ...createPayrollDto,
      payrollNumber,
    });

    return this.payrollRepository.save(payroll);
  }

  async findAll(filters?: any): Promise<Payroll[]> {
    const queryBuilder = this.payrollRepository.createQueryBuilder('payroll')
      .leftJoinAndSelect('payroll.employee', 'employee')
      .leftJoinAndSelect('payroll.payrollItems', 'payrollItems');

    if (filters?.employeeId) {
      queryBuilder.andWhere('payroll.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters?.status) {
      queryBuilder.andWhere('payroll.status = :status', { status: filters.status });
    }

    if (filters?.payPeriod) {
      queryBuilder.andWhere('payroll.payPeriod = :payPeriod', { payPeriod: filters.payPeriod });
    }

    return queryBuilder
      .orderBy('payroll.payDate', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Payroll> {
    const payroll = await this.payrollRepository.findOne({
      where: { id },
      relations: ['employee', 'payrollItems'],
    });

    if (!payroll) {
      throw new NotFoundException(`Payroll with ID ${id} not found`);
    }

    return payroll;
  }

  async calculatePayroll(id: string): Promise<Payroll> {
    const payroll = await this.findOne(id);
    
    // Calculate totals from payroll items
    const earnings = payroll.payrollItems.filter(item => item.type === 'earning');
    const deductions = payroll.payrollItems.filter(item => item.type === 'deduction');
    const benefits = payroll.payrollItems.filter(item => item.type === 'benefit');
    const taxes = payroll.payrollItems.filter(item => item.type === 'tax');

    payroll.grossPay = earnings.reduce((sum, item) => sum + item.amount, 0);
    payroll.totalDeductions = deductions.reduce((sum, item) => sum + item.amount, 0) +
                              taxes.reduce((sum, item) => sum + item.amount, 0);
    payroll.totalBenefits = benefits.reduce((sum, item) => sum + item.amount, 0);
    payroll.netPay = payroll.grossPay - payroll.totalDeductions + payroll.totalBenefits;

    payroll.status = PayrollStatus.CALCULATED;
    payroll.calculatedAt = new Date();

    return this.payrollRepository.save(payroll);
  }

  async approvePayroll(id: string, approvedBy: string): Promise<Payroll> {
    const payroll = await this.findOne(id);
    
    payroll.status = PayrollStatus.APPROVED;
    payroll.approvedBy = approvedBy;
    payroll.approvedAt = new Date();

    return this.payrollRepository.save(payroll);
  }

  async processPayment(id: string, paidBy: string): Promise<Payroll> {
    const payroll = await this.findOne(id);
    
    payroll.status = PayrollStatus.PAID;
    payroll.paidBy = paidBy;
    payroll.paidAt = new Date();

    return this.payrollRepository.save(payroll);
  }

  async addPayrollItem(payrollId: string, createPayrollItemDto: any): Promise<PayrollItem> {
    const payrollItem = this.payrollItemRepository.create({
      ...createPayrollItemDto,
      payrollId,
    });

    return this.payrollItemRepository.save(payrollItem);
  }

  private async generatePayrollNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    const prefix = `PAY-${year}${month}-`;

    const lastPayroll = await this.payrollRepository.findOne({
      where: { payrollNumber: Like(`${prefix}%`) },
      order: { payrollNumber: 'DESC' },
    });

    let nextNumber = 1;
    if (lastPayroll) {
      const lastNumber = parseInt(lastPayroll.payrollNumber.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
  }
}
