"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Integration = exports.IntegrationStatus = exports.IntegrationType = void 0;
const typeorm_1 = require("typeorm");
var IntegrationType;
(function (IntegrationType) {
    IntegrationType["API"] = "api";
    IntegrationType["WEBHOOK"] = "webhook";
    IntegrationType["DATABASE"] = "database";
    IntegrationType["FILE"] = "file";
    IntegrationType["EMAIL"] = "email";
    IntegrationType["SMS"] = "sms";
    IntegrationType["PAYMENT"] = "payment";
    IntegrationType["SHIPPING"] = "shipping";
    IntegrationType["ACCOUNTING"] = "accounting";
    IntegrationType["CRM"] = "crm";
    IntegrationType["MARKETING"] = "marketing";
    IntegrationType["SOCIAL_MEDIA"] = "social_media";
    IntegrationType["CUSTOM"] = "custom";
})(IntegrationType || (exports.IntegrationType = IntegrationType = {}));
var IntegrationStatus;
(function (IntegrationStatus) {
    IntegrationStatus["ACTIVE"] = "active";
    IntegrationStatus["INACTIVE"] = "inactive";
    IntegrationStatus["ERROR"] = "error";
    IntegrationStatus["TESTING"] = "testing";
    IntegrationStatus["PENDING"] = "pending";
})(IntegrationStatus || (exports.IntegrationStatus = IntegrationStatus = {}));
let Integration = class Integration {
    id;
    name;
    description;
    type;
    status;
    provider;
    configuration;
    credentials;
    mapping;
    settings;
    lastSyncAt;
    lastError;
    syncCount;
    errorCount;
    createdBy;
    lastModifiedBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.Integration = Integration;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Integration.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Integration.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Integration.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IntegrationType,
    }),
    __metadata("design:type", String)
], Integration.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IntegrationStatus,
        default: IntegrationStatus.INACTIVE,
    }),
    __metadata("design:type", String)
], Integration.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Integration.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], Integration.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Integration.prototype, "credentials", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Integration.prototype, "mapping", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Integration.prototype, "settings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Integration.prototype, "lastSyncAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Integration.prototype, "lastError", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "syncCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "errorCount", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Integration.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Integration.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Integration.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Integration.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Integration.prototype, "updatedAt", void 0);
exports.Integration = Integration = __decorate([
    (0, typeorm_1.Entity)('integrations')
], Integration);
//# sourceMappingURL=integration.entity.js.map