import { FinancialReportService } from '../services/financial-report.service';
export declare class FinancialReportController {
    private readonly reportService;
    constructor(reportService: FinancialReportService);
    create(createReportDto: any): Promise<import("../entities/financial-report.entity").FinancialReport>;
    findAll(type?: string, status?: string): Promise<import("../entities/financial-report.entity").FinancialReport[]>;
    generateBalanceSheet(startDate: string, endDate: string): Promise<any>;
    generateIncomeStatement(startDate: string, endDate: string): Promise<any>;
    generateCashFlowStatement(startDate: string, endDate: string): Promise<any>;
    scheduleReport(scheduleReportDto: any): Promise<import("../entities/financial-report.entity").FinancialReport>;
    findOne(id: string): Promise<import("../entities/financial-report.entity").FinancialReport>;
}
