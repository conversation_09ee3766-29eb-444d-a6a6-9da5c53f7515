"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayrollService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payroll_entity_1 = require("../entities/payroll.entity");
const payroll_item_entity_1 = require("../entities/payroll-item.entity");
let PayrollService = class PayrollService {
    payrollRepository;
    payrollItemRepository;
    constructor(payrollRepository, payrollItemRepository) {
        this.payrollRepository = payrollRepository;
        this.payrollItemRepository = payrollItemRepository;
    }
    async create(createPayrollDto) {
        const payrollNumber = await this.generatePayrollNumber();
        const payroll = this.payrollRepository.create({
            ...createPayrollDto,
            payrollNumber,
        });
        return this.payrollRepository.save(payroll);
    }
    async findAll(filters) {
        const queryBuilder = this.payrollRepository.createQueryBuilder('payroll')
            .leftJoinAndSelect('payroll.employee', 'employee')
            .leftJoinAndSelect('payroll.payrollItems', 'payrollItems');
        if (filters?.employeeId) {
            queryBuilder.andWhere('payroll.employeeId = :employeeId', { employeeId: filters.employeeId });
        }
        if (filters?.status) {
            queryBuilder.andWhere('payroll.status = :status', { status: filters.status });
        }
        if (filters?.payPeriod) {
            queryBuilder.andWhere('payroll.payPeriod = :payPeriod', { payPeriod: filters.payPeriod });
        }
        return queryBuilder
            .orderBy('payroll.payDate', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const payroll = await this.payrollRepository.findOne({
            where: { id },
            relations: ['employee', 'payrollItems'],
        });
        if (!payroll) {
            throw new common_1.NotFoundException(`Payroll with ID ${id} not found`);
        }
        return payroll;
    }
    async calculatePayroll(id) {
        const payroll = await this.findOne(id);
        const earnings = payroll.payrollItems.filter(item => item.type === 'earning');
        const deductions = payroll.payrollItems.filter(item => item.type === 'deduction');
        const benefits = payroll.payrollItems.filter(item => item.type === 'benefit');
        const taxes = payroll.payrollItems.filter(item => item.type === 'tax');
        payroll.grossPay = earnings.reduce((sum, item) => sum + item.amount, 0);
        payroll.totalDeductions = deductions.reduce((sum, item) => sum + item.amount, 0) +
            taxes.reduce((sum, item) => sum + item.amount, 0);
        payroll.totalBenefits = benefits.reduce((sum, item) => sum + item.amount, 0);
        payroll.netPay = payroll.grossPay - payroll.totalDeductions + payroll.totalBenefits;
        payroll.status = payroll_entity_1.PayrollStatus.CALCULATED;
        payroll.calculatedAt = new Date();
        return this.payrollRepository.save(payroll);
    }
    async approvePayroll(id, approvedBy) {
        const payroll = await this.findOne(id);
        payroll.status = payroll_entity_1.PayrollStatus.APPROVED;
        payroll.approvedBy = approvedBy;
        payroll.approvedAt = new Date();
        return this.payrollRepository.save(payroll);
    }
    async processPayment(id, paidBy) {
        const payroll = await this.findOne(id);
        payroll.status = payroll_entity_1.PayrollStatus.PAID;
        payroll.paidBy = paidBy;
        payroll.paidAt = new Date();
        return this.payrollRepository.save(payroll);
    }
    async addPayrollItem(payrollId, createPayrollItemDto) {
        const payrollItem = this.payrollItemRepository.create({
            ...createPayrollItemDto,
            payrollId,
        });
        return this.payrollItemRepository.save(payrollItem);
    }
    async generatePayrollNumber() {
        const year = new Date().getFullYear();
        const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
        const prefix = `PAY-${year}${month}-`;
        const lastPayroll = await this.payrollRepository.findOne({
            where: { payrollNumber: (0, typeorm_2.Like)(`${prefix}%`) },
            order: { payrollNumber: 'DESC' },
        });
        let nextNumber = 1;
        if (lastPayroll) {
            const lastNumber = parseInt(lastPayroll.payrollNumber.split('-')[2]);
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
    }
};
exports.PayrollService = PayrollService;
exports.PayrollService = PayrollService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payroll_entity_1.Payroll)),
    __param(1, (0, typeorm_1.InjectRepository)(payroll_item_entity_1.PayrollItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PayrollService);
//# sourceMappingURL=payroll.service.js.map