"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockMovement = exports.MovementReason = exports.MovementType = void 0;
const typeorm_1 = require("typeorm");
const product_entity_1 = require("./product.entity");
const warehouse_entity_1 = require("./warehouse.entity");
const location_entity_1 = require("./location.entity");
const stock_entity_1 = require("./stock.entity");
var MovementType;
(function (MovementType) {
    MovementType["RECEIPT"] = "receipt";
    MovementType["ISSUE"] = "issue";
    MovementType["TRANSFER"] = "transfer";
    MovementType["ADJUSTMENT"] = "adjustment";
    MovementType["RETURN"] = "return";
    MovementType["DAMAGE"] = "damage";
    MovementType["EXPIRY"] = "expiry";
    MovementType["SALE"] = "sale";
    MovementType["PURCHASE"] = "purchase";
    MovementType["PRODUCTION"] = "production";
    MovementType["CONSUMPTION"] = "consumption";
})(MovementType || (exports.MovementType = MovementType = {}));
var MovementReason;
(function (MovementReason) {
    MovementReason["PURCHASE_ORDER"] = "purchase_order";
    MovementReason["SALES_ORDER"] = "sales_order";
    MovementReason["STOCK_TRANSFER"] = "stock_transfer";
    MovementReason["STOCK_ADJUSTMENT"] = "stock_adjustment";
    MovementReason["PHYSICAL_COUNT"] = "physical_count";
    MovementReason["DAMAGE_WRITE_OFF"] = "damage_write_off";
    MovementReason["EXPIRY_WRITE_OFF"] = "expiry_write_off";
    MovementReason["CUSTOMER_RETURN"] = "customer_return";
    MovementReason["SUPPLIER_RETURN"] = "supplier_return";
    MovementReason["PRODUCTION_CONSUMPTION"] = "production_consumption";
    MovementReason["PRODUCTION_OUTPUT"] = "production_output";
    MovementReason["MANUAL_ADJUSTMENT"] = "manual_adjustment";
})(MovementReason || (exports.MovementReason = MovementReason = {}));
let StockMovement = class StockMovement {
    id;
    productId;
    product;
    warehouseId;
    warehouse;
    locationId;
    location;
    stockId;
    stock;
    type;
    reason;
    quantity;
    quantityBefore;
    quantityAfter;
    unitCost;
    totalCost;
    currency;
    movementDate;
    referenceNumber;
    relatedEntityType;
    relatedEntityId;
    batchNumber;
    serialNumber;
    expiryDate;
    notes;
    performedBy;
    approvedBy;
    approvedAt;
    metadata;
    createdAt;
    updatedAt;
};
exports.StockMovement = StockMovement;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], StockMovement.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], StockMovement.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.Product, product => product.stockMovements),
    (0, typeorm_1.JoinColumn)({ name: 'productId' }),
    __metadata("design:type", product_entity_1.Product)
], StockMovement.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], StockMovement.prototype, "warehouseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => warehouse_entity_1.Warehouse),
    (0, typeorm_1.JoinColumn)({ name: 'warehouseId' }),
    __metadata("design:type", warehouse_entity_1.Warehouse)
], StockMovement.prototype, "warehouse", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "locationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => location_entity_1.Location, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'locationId' }),
    __metadata("design:type", location_entity_1.Location)
], StockMovement.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "stockId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => stock_entity_1.Stock, stock => stock.stockMovements, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'stockId' }),
    __metadata("design:type", stock_entity_1.Stock)
], StockMovement.prototype, "stock", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: MovementType,
    }),
    __metadata("design:type", String)
], StockMovement.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: MovementReason,
    }),
    __metadata("design:type", String)
], StockMovement.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], StockMovement.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], StockMovement.prototype, "quantityBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], StockMovement.prototype, "quantityAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], StockMovement.prototype, "unitCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], StockMovement.prototype, "totalCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], StockMovement.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], StockMovement.prototype, "movementDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "referenceNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "relatedEntityType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "relatedEntityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "batchNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "serialNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], StockMovement.prototype, "expiryDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "performedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockMovement.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], StockMovement.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StockMovement.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], StockMovement.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], StockMovement.prototype, "updatedAt", void 0);
exports.StockMovement = StockMovement = __decorate([
    (0, typeorm_1.Entity)('inventory_stock_movements')
], StockMovement);
//# sourceMappingURL=stock-movement.entity.js.map