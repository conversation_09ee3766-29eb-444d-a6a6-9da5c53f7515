import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Warehouse } from '../entities/warehouse.entity';
import { Location } from '../entities/location.entity';
import { Stock } from '../entities/stock.entity';

@Injectable()
export class WarehouseService {
  constructor(
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
    @InjectRepository(Stock)
    private stockRepository: Repository<Stock>,
  ) {}

  async create(warehouseData: Partial<Warehouse>): Promise<Warehouse> {
    const warehouse = this.warehouseRepository.create(warehouseData);
    return this.warehouseRepository.save(warehouse);
  }

  async findAll(): Promise<Warehouse[]> {
    return this.warehouseRepository.find({
      relations: ['locations', 'stocks', 'stocks.product'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Warehouse> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { id },
      relations: ['locations', 'stocks', 'stocks.product'],
    });

    if (!warehouse) {
      throw new NotFoundException(`Warehouse with ID ${id} not found`);
    }

    return warehouse;
  }

  async update(id: string, updateData: Partial<Warehouse>): Promise<Warehouse> {
    await this.warehouseRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const warehouse = await this.findOne(id);
    
    // Check if warehouse has stock
    if (warehouse.stocks && warehouse.stocks.length > 0) {
      const hasStock = warehouse.stocks.some(stock => stock.quantity > 0);
      if (hasStock) {
        throw new Error('Cannot delete warehouse with existing stock');
      }
    }

    await this.warehouseRepository.remove(warehouse);
  }

  async findByCode(code: string): Promise<Warehouse> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { code },
      relations: ['locations', 'stocks'],
    });

    if (!warehouse) {
      throw new NotFoundException(`Warehouse with code ${code} not found`);
    }

    return warehouse;
  }

  async getActiveWarehouses(): Promise<Warehouse[]> {
    return this.warehouseRepository.find({
      where: { isActive: true },
      relations: ['locations'],
      order: { name: 'ASC' },
    });
  }

  async createLocation(warehouseId: string, locationData: Partial<Location>): Promise<Location> {
    const warehouse = await this.findOne(warehouseId);
    
    const location = this.locationRepository.create({
      ...locationData,
      warehouseId: warehouse.id,
    });

    return this.locationRepository.save(location);
  }

  async getWarehouseLocations(warehouseId: string): Promise<Location[]> {
    return this.locationRepository.find({
      where: { warehouseId },
      order: { zone: 'ASC', aisle: 'ASC', shelf: 'ASC' },
    });
  }

  async getWarehouseStock(warehouseId: string): Promise<Stock[]> {
    return this.stockRepository.find({
      where: { warehouseId },
      relations: ['product', 'product.category'],
      order: { 'product.name': 'ASC' },
    });
  }

  async getWarehouseStockValue(warehouseId: string): Promise<number> {
    const result = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoin('stock.product', 'product')
      .where('stock.warehouseId = :warehouseId', { warehouseId })
      .select('SUM(stock.quantity * product.costPrice)', 'totalValue')
      .getRawOne();

    return parseFloat(result.totalValue) || 0;
  }

  async getWarehouseCapacityUtilization(warehouseId: string): Promise<any> {
    const warehouse = await this.findOne(warehouseId);
    const totalStock = warehouse.stocks.reduce((sum, stock) => sum + stock.quantity, 0);
    
    const utilizationPercentage = warehouse.capacity > 0 
      ? (totalStock / warehouse.capacity) * 100 
      : 0;

    return {
      warehouseId: warehouse.id,
      warehouseName: warehouse.name,
      capacity: warehouse.capacity,
      currentStock: totalStock,
      availableCapacity: Math.max(0, warehouse.capacity - totalStock),
      utilizationPercentage: Math.round(utilizationPercentage * 100) / 100,
    };
  }

  async getWarehouseStatistics(): Promise<any> {
    const totalWarehouses = await this.warehouseRepository.count();
    const activeWarehouses = await this.warehouseRepository.count({ where: { isActive: true } });
    
    const totalCapacity = await this.warehouseRepository
      .createQueryBuilder('warehouse')
      .select('SUM(warehouse.capacity)', 'totalCapacity')
      .getRawOne();

    const totalStock = await this.stockRepository
      .createQueryBuilder('stock')
      .select('SUM(stock.quantity)', 'totalStock')
      .getRawOne();

    const totalValue = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoin('stock.product', 'product')
      .select('SUM(stock.quantity * product.costPrice)', 'totalValue')
      .getRawOne();

    return {
      totalWarehouses,
      activeWarehouses,
      inactiveWarehouses: totalWarehouses - activeWarehouses,
      totalCapacity: parseInt(totalCapacity.totalCapacity) || 0,
      totalStock: parseInt(totalStock.totalStock) || 0,
      totalStockValue: parseFloat(totalValue.totalValue) || 0,
      averageUtilization: totalCapacity.totalCapacity > 0 
        ? ((totalStock.totalStock / totalCapacity.totalCapacity) * 100) 
        : 0,
    };
  }

  async searchWarehouses(searchTerm: string): Promise<Warehouse[]> {
    return this.warehouseRepository
      .createQueryBuilder('warehouse')
      .where('warehouse.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('warehouse.code ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('warehouse.address ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('warehouse.name', 'ASC')
      .getMany();
  }

  async getWarehousesByRegion(region: string): Promise<Warehouse[]> {
    return this.warehouseRepository.find({
      where: { region },
      relations: ['locations'],
      order: { name: 'ASC' },
    });
  }

  async transferStock(
    fromWarehouseId: string,
    toWarehouseId: string,
    productId: string,
    quantity: number,
  ): Promise<{ success: boolean; message: string }> {
    const fromStock = await this.stockRepository.findOne({
      where: { warehouseId: fromWarehouseId, productId },
    });

    if (!fromStock || fromStock.availableQuantity < quantity) {
      return {
        success: false,
        message: 'Insufficient stock in source warehouse',
      };
    }

    // Reduce stock in source warehouse
    fromStock.quantity -= quantity;
    fromStock.availableQuantity -= quantity;
    await this.stockRepository.save(fromStock);

    // Increase stock in destination warehouse
    let toStock = await this.stockRepository.findOne({
      where: { warehouseId: toWarehouseId, productId },
    });

    if (toStock) {
      toStock.quantity += quantity;
      toStock.availableQuantity += quantity;
    } else {
      toStock = this.stockRepository.create({
        warehouseId: toWarehouseId,
        productId,
        quantity,
        reservedQuantity: 0,
        availableQuantity: quantity,
      });
    }

    await this.stockRepository.save(toStock);

    return {
      success: true,
      message: `Successfully transferred ${quantity} units`,
    };
  }

  async generateWarehouseCode(name: string): Promise<string> {
    const baseCode = name.substring(0, 3).toUpperCase();
    const count = await this.warehouseRepository.count();
    const sequence = (count + 1).toString().padStart(3, '0');
    return `WH-${baseCode}${sequence}`;
  }
}
