"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const audit_reports_controller_1 = require("./controllers/audit-reports.controller");
const audit_reports_service_1 = require("./services/audit-reports.service");
const audit_report_entity_1 = require("./entities/audit-report.entity");
const audit_finding_entity_1 = require("./entities/audit-finding.entity");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([audit_report_entity_1.AuditReport, audit_finding_entity_1.AuditFinding]),
        ],
        controllers: [audit_reports_controller_1.AuditReportsController],
        providers: [audit_reports_service_1.AuditReportsService],
        exports: [audit_reports_service_1.AuditReportsService],
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map