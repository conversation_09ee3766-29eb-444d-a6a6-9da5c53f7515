"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const audit_reports_controller_1 = require("./controllers/audit-reports.controller");
const business_metrics_controller_1 = require("./controllers/business-metrics.controller");
const audit_reports_service_1 = require("./services/audit-reports.service");
const business_metrics_service_1 = require("./services/business-metrics.service");
const audit_report_entity_1 = require("./entities/audit-report.entity");
const audit_finding_entity_1 = require("./entities/audit-finding.entity");
const customer_entity_1 = require("../sales/entities/customer.entity");
const invoice_entity_1 = require("../sales/entities/invoice.entity");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([audit_report_entity_1.AuditReport, audit_finding_entity_1.AuditFinding, customer_entity_1.Customer, invoice_entity_1.Invoice]),
        ],
        controllers: [audit_reports_controller_1.AuditReportsController, business_metrics_controller_1.BusinessMetricsController],
        providers: [audit_reports_service_1.AuditReportsService, business_metrics_service_1.BusinessMetricsService],
        exports: [audit_reports_service_1.AuditReportsService, business_metrics_service_1.BusinessMetricsService],
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map