import { QuotationService } from '../services/quotation.service';
import { CreateQuotationDto } from '../dto/create-quotation.dto';
export declare class QuotationController {
    private readonly quotationService;
    constructor(quotationService: QuotationService);
    create(createQuotationDto: CreateQuotationDto, req: any): Promise<import("../entities/quotation.entity").Quotation>;
    findAll(req: any): Promise<import("../entities/quotation.entity").Quotation[]>;
    getStats(req: any): Promise<{
        totalQuotations: number;
        acceptedQuotations: number;
        pendingQuotations: number;
        totalValue: number;
        conversionRate: number;
    }>;
    findOne(id: string, req: any): Promise<import("../entities/quotation.entity").Quotation>;
    update(id: string, updateQuotationDto: Partial<CreateQuotationDto>, req: any): Promise<import("../entities/quotation.entity").Quotation>;
    remove(id: string, req: any): Promise<void>;
    updateStatus(id: string, body: {
        status: string;
    }, req: any): Promise<import("../entities/quotation.entity").Quotation>;
    convertToInvoice(id: string, req: any): Promise<{
        success: boolean;
        invoiceId?: string;
    }>;
}
