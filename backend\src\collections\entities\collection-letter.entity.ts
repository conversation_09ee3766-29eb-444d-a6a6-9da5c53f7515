import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum LetterType {
  INITIAL_NOTICE = 'initial_notice',
  REMINDER = 'reminder',
  FINAL_NOTICE = 'final_notice',
  DEMAND_LETTER = 'demand_letter',
  SETTLEMENT_OFFER = 'settlement_offer',
  PAYMENT_PLAN_OFFER = 'payment_plan_offer',
  LEGAL_NOTICE = 'legal_notice',
  CEASE_AND_DESIST = 'cease_and_desist',
  VALIDATION_NOTICE = 'validation_notice',
  CUSTOM = 'custom',
}

export enum LetterStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  RETURNED = 'returned',
  FAILED = 'failed',
}

@Entity('collection_letters')
export class CollectionLetter {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: LetterType,
  })
  type: LetterType;

  @Column({ type: 'text' })
  template: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  requiresApproval: boolean;

  @Column({ type: 'json', nullable: true })
  variables: string[];

  @Column({ type: 'json', nullable: true })
  conditions: any;

  @Column()
  createdBy: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
