import { AssetService } from '../services/asset.service';
import { Asset, AssetStatus } from '../entities/asset.entity';
export declare class AssetController {
    private readonly assetService;
    constructor(assetService: AssetService);
    create(createAssetDto: Partial<Asset>): Promise<Asset>;
    findAll(status?: AssetStatus, category?: string, location?: string): Promise<Asset[]>;
    getStatistics(): Promise<any>;
    getDashboardMetrics(): Promise<any>;
    getAssetsByCategory(): Promise<{
        category: string;
        count: number;
        value: number;
    }[]>;
    getAssetsNearingWarrantyExpiry(days?: string): Promise<Asset[]>;
    searchAssets(searchTerm: string): Promise<Asset[]>;
    getUserAssets(userId: string): Promise<Asset[]>;
    findByAssetTag(assetTag: string): Promise<Asset>;
    findOne(id: string): Promise<Asset>;
    getAssetHistory(id: string): Promise<AssetAssignment[]>;
    assignAsset(id: string, assignmentData: {
        assignedToId: string;
        assignedById: string;
        notes?: string;
    }): Promise<AssetAssignment>;
    unassignAsset(id: string, unassignmentData: {
        unassignedById: string;
        notes?: string;
    }): Promise<{
        message: string;
    }>;
    update(id: string, updateAssetDto: Partial<Asset>): Promise<Asset>;
    updateStatus(id: string, statusData: {
        status: AssetStatus;
        notes?: string;
    }): Promise<Asset>;
    retireAsset(id: string, retirementData: {
        retiredById: string;
        reason: string;
    }): Promise<Asset>;
    remove(id: string): Promise<void>;
}
