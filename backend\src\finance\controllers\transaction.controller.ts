import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { TransactionService } from '../services/transaction.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/transactions')
@UseGuards(JwtAuthGuard)
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post()
  create(@Body() createTransactionDto: any) {
    return this.transactionService.create(createTransactionDto);
  }

  @Get()
  findAll(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('type') type?: string,
    @Query('status') status?: string,
    @Query('accountId') accountId?: string
  ) {
    const filters: any = {};
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (type) filters.type = type;
    if (status) filters.status = status;
    if (accountId) filters.accountId = accountId;

    return this.transactionService.findAll(filters);
  }

  @Get('account-ledger/:accountId')
  getAccountLedger(
    @Param('accountId') accountId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    return this.transactionService.getAccountLedger(accountId, start, end);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.transactionService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTransactionDto: any) {
    return this.transactionService.update(id, updateTransactionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.transactionService.remove(id);
  }

  @Post(':id/post')
  postTransaction(@Param('id') id: string, @Body() postDto: { approvedBy?: string }) {
    return this.transactionService.postTransaction(id, postDto.approvedBy);
  }

  @Post(':id/reverse')
  reverseTransaction(
    @Param('id') id: string,
    @Body() reverseDto: { reason: string; reversedBy?: string }
  ) {
    return this.transactionService.reverseTransaction(id, reverseDto.reason, reverseDto.reversedBy);
  }
}
