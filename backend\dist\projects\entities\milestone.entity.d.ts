import { Project } from './project.entity';
export declare enum MilestoneStatus {
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    OVERDUE = "overdue",
    CANCELLED = "cancelled"
}
export declare class Milestone {
    id: string;
    projectId: string;
    project: Project;
    name: string;
    description: string;
    dueDate: Date;
    completedDate: Date;
    status: MilestoneStatus;
    completionPercentage: number;
    sortOrder: number;
    deliverables: string[];
    notes: string;
    createdBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
