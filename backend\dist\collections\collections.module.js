"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const collection_case_entity_1 = require("./entities/collection-case.entity");
const collection_activity_entity_1 = require("./entities/collection-activity.entity");
const collection_strategy_entity_1 = require("./entities/collection-strategy.entity");
const collection_agent_entity_1 = require("./entities/collection-agent.entity");
const payment_plan_entity_1 = require("./entities/payment-plan.entity");
const payment_plan_installment_entity_1 = require("./entities/payment-plan-installment.entity");
const collection_note_entity_1 = require("./entities/collection-note.entity");
const collection_document_entity_1 = require("./entities/collection-document.entity");
const collection_dispute_entity_1 = require("./entities/collection-dispute.entity");
const collection_letter_entity_1 = require("./entities/collection-letter.entity");
const collection_call_entity_1 = require("./entities/collection-call.entity");
const collection_case_service_1 = require("./services/collection-case.service");
const collection_activity_service_1 = require("./services/collection-activity.service");
const collection_strategy_service_1 = require("./services/collection-strategy.service");
const payment_plan_service_1 = require("./services/payment-plan.service");
const collection_report_service_1 = require("./services/collection-report.service");
const collection_automation_service_1 = require("./services/collection-automation.service");
const collection_case_controller_1 = require("./controllers/collection-case.controller");
const collection_activity_controller_1 = require("./controllers/collection-activity.controller");
const collection_strategy_controller_1 = require("./controllers/collection-strategy.controller");
const payment_plan_controller_1 = require("./controllers/payment-plan.controller");
const collection_report_controller_1 = require("./controllers/collection-report.controller");
const collection_automation_controller_1 = require("./controllers/collection-automation.controller");
let CollectionsModule = class CollectionsModule {
};
exports.CollectionsModule = CollectionsModule;
exports.CollectionsModule = CollectionsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                collection_case_entity_1.CollectionCase,
                collection_activity_entity_1.CollectionActivity,
                collection_strategy_entity_1.CollectionStrategy,
                collection_agent_entity_1.CollectionAgent,
                payment_plan_entity_1.PaymentPlan,
                payment_plan_installment_entity_1.PaymentPlanInstallment,
                collection_note_entity_1.CollectionNote,
                collection_document_entity_1.CollectionDocument,
                collection_dispute_entity_1.CollectionDispute,
                collection_letter_entity_1.CollectionLetter,
                collection_call_entity_1.CollectionCall,
            ]),
        ],
        controllers: [
            collection_case_controller_1.CollectionCaseController,
            collection_activity_controller_1.CollectionActivityController,
            collection_strategy_controller_1.CollectionStrategyController,
            payment_plan_controller_1.PaymentPlanController,
            collection_report_controller_1.CollectionReportController,
            collection_automation_controller_1.CollectionAutomationController,
        ],
        providers: [
            collection_case_service_1.CollectionCaseService,
            collection_activity_service_1.CollectionActivityService,
            collection_strategy_service_1.CollectionStrategyService,
            payment_plan_service_1.PaymentPlanService,
            collection_report_service_1.CollectionReportService,
            collection_automation_service_1.CollectionAutomationService,
        ],
        exports: [
            collection_case_service_1.CollectionCaseService,
            collection_activity_service_1.CollectionActivityService,
            collection_strategy_service_1.CollectionStrategyService,
            payment_plan_service_1.PaymentPlanService,
            collection_report_service_1.CollectionReportService,
            collection_automation_service_1.CollectionAutomationService,
        ],
    })
], CollectionsModule);
//# sourceMappingURL=collections.module.js.map