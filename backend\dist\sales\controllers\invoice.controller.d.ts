import { InvoiceService } from '../services/invoice.service';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
export declare class InvoiceController {
    private readonly invoiceService;
    constructor(invoiceService: InvoiceService);
    create(createInvoiceDto: CreateInvoiceDto, req: any): Promise<import("../entities/invoice.entity").Invoice>;
    findAll(req: any): Promise<import("../entities/invoice.entity").Invoice[]>;
    getStats(req: any): Promise<{
        totalInvoices: number;
        paidInvoices: number;
        overdueInvoices: number;
        totalRevenue: number;
        pendingAmount: number;
    }>;
    findOne(id: string, req: any): Promise<import("../entities/invoice.entity").Invoice>;
    update(id: string, updateInvoiceDto: Partial<CreateInvoiceDto>, req: any): Promise<import("../entities/invoice.entity").Invoice>;
    remove(id: string, req: any): Promise<void>;
    updateStatus(id: string, body: {
        status: string;
    }, req: any): Promise<import("../entities/invoice.entity").Invoice>;
}
