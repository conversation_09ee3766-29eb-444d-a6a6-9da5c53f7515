"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuideStep = exports.StepType = void 0;
const typeorm_1 = require("typeorm");
const guide_section_entity_1 = require("./guide-section.entity");
var StepType;
(function (StepType) {
    StepType["TEXT"] = "text";
    StepType["IMAGE"] = "image";
    StepType["VIDEO"] = "video";
    StepType["CODE"] = "code";
    StepType["INTERACTIVE"] = "interactive";
    StepType["CHECKLIST"] = "checklist";
    StepType["WARNING"] = "warning";
    StepType["TIP"] = "tip";
    StepType["NOTE"] = "note";
})(StepType || (exports.StepType = StepType = {}));
let GuideStep = class GuideStep {
    id;
    sectionId;
    section;
    title;
    content;
    type;
    sortOrder;
    media;
    interactive;
    isVisible;
    metadata;
    createdAt;
    updatedAt;
};
exports.GuideStep = GuideStep;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], GuideStep.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], GuideStep.prototype, "sectionId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => guide_section_entity_1.GuideSection, section => section.steps, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'sectionId' }),
    __metadata("design:type", guide_section_entity_1.GuideSection)
], GuideStep.prototype, "section", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], GuideStep.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], GuideStep.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: StepType,
        default: StepType.TEXT,
    }),
    __metadata("design:type", String)
], GuideStep.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], GuideStep.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], GuideStep.prototype, "media", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], GuideStep.prototype, "interactive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], GuideStep.prototype, "isVisible", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], GuideStep.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], GuideStep.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], GuideStep.prototype, "updatedAt", void 0);
exports.GuideStep = GuideStep = __decorate([
    (0, typeorm_1.Entity)('guide_steps')
], GuideStep);
//# sourceMappingURL=guide-step.entity.js.map