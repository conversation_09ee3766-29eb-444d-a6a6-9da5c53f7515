export declare enum ProgressType {
    GUIDE = "guide",
    TUTORIAL = "tutorial",
    ONBOARDING = "onboarding",
    COURSE = "course"
}
export declare enum ProgressStatus {
    NOT_STARTED = "not_started",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    SKIPPED = "skipped",
    FAILED = "failed"
}
export declare class UserProgress {
    id: string;
    userId: string;
    contentId: string;
    type: ProgressType;
    status: ProgressStatus;
    completionPercentage: number;
    currentStep: number;
    totalSteps: number;
    startedAt: Date;
    completedAt: Date;
    lastAccessedAt: Date;
    timeSpentMinutes: number;
    completedSteps: number[];
    skippedSteps: number[];
    rating: number;
    feedback: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
