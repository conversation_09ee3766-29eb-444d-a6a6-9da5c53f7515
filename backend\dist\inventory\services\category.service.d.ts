import { Repository } from 'typeorm';
import { Category } from '../entities/category.entity';
export declare class CategoryService {
    private categoryRepository;
    constructor(categoryRepository: Repository<Category>);
    create(categoryData: Partial<Category>): Promise<Category>;
    findAll(): Promise<Category[]>;
    findOne(id: string): Promise<Category>;
    update(id: string, updateData: Partial<Category>): Promise<Category>;
    remove(id: string): Promise<void>;
    findRootCategories(): Promise<Category[]>;
    findByParent(parentId: string): Promise<Category[]>;
    getCategoryTree(): Promise<Category[]>;
    private loadCategoryTree;
    getCategoryPath(categoryId: string): Promise<string[]>;
    getCategoryStatistics(): Promise<any>;
    searchCategories(searchTerm: string): Promise<Category[]>;
    moveCategory(categoryId: string, newParentId: string | null): Promise<Category>;
    private isDescendant;
    generateCategoryCode(name: string): Promise<string>;
    getProductCountByCategory(): Promise<Array<{
        categoryId: string;
        categoryName: string;
        productCount: number;
    }>>;
}
