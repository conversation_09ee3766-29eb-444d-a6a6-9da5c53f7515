"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const knowledge_base_article_entity_1 = require("../entities/knowledge-base-article.entity");
let KnowledgeBaseService = class KnowledgeBaseService {
    articleRepository;
    constructor(articleRepository) {
        this.articleRepository = articleRepository;
    }
    async create(articleData) {
        const article = this.articleRepository.create({
            ...articleData,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
        return this.articleRepository.save(article);
    }
    async findAll() {
        return this.articleRepository.find({
            relations: ['author'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const article = await this.articleRepository.findOne({
            where: { id },
            relations: ['author'],
        });
        if (!article) {
            throw new common_1.NotFoundException(`Article with ID ${id} not found`);
        }
        await this.articleRepository.update(id, {
            viewCount: article.viewCount + 1,
        });
        return article;
    }
    async update(id, updateData) {
        await this.articleRepository.update(id, {
            ...updateData,
            updatedAt: new Date(),
        });
        return this.findOne(id);
    }
    async remove(id) {
        const article = await this.findOne(id);
        await this.articleRepository.remove(article);
    }
    async findByCategory(category) {
        return this.articleRepository.find({
            where: { category },
            relations: ['author'],
            order: { title: 'ASC' },
        });
    }
    async findByTags(tags) {
        return this.articleRepository
            .createQueryBuilder('article')
            .leftJoinAndSelect('article.author', 'author')
            .where('article.tags && :tags', { tags })
            .orderBy('article.title', 'ASC')
            .getMany();
    }
    async searchArticles(searchTerm) {
        return this.articleRepository
            .createQueryBuilder('article')
            .leftJoinAndSelect('article.author', 'author')
            .where('article.title ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('article.content ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('article.summary ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('article.title', 'ASC')
            .getMany();
    }
    async getPublishedArticles() {
        return this.articleRepository.find({
            where: { isPublished: true },
            relations: ['author'],
            order: { createdAt: 'DESC' },
        });
    }
    async getDraftArticles() {
        return this.articleRepository.find({
            where: { isPublished: false },
            relations: ['author'],
            order: { updatedAt: 'DESC' },
        });
    }
    async publishArticle(id) {
        await this.articleRepository.update(id, {
            isPublished: true,
            publishedAt: new Date(),
        });
        return this.findOne(id);
    }
    async unpublishArticle(id) {
        await this.articleRepository.update(id, {
            isPublished: false,
            publishedAt: null,
        });
        return this.findOne(id);
    }
    async getPopularArticles(limit = 10) {
        return this.articleRepository.find({
            where: { isPublished: true },
            relations: ['author'],
            order: { viewCount: 'DESC' },
            take: limit,
        });
    }
    async getRecentArticles(limit = 10) {
        return this.articleRepository.find({
            where: { isPublished: true },
            relations: ['author'],
            order: { publishedAt: 'DESC' },
            take: limit,
        });
    }
    async getArticlesByAuthor(authorId) {
        return this.articleRepository.find({
            where: { authorId },
            order: { createdAt: 'DESC' },
        });
    }
    async getCategories() {
        const result = await this.articleRepository
            .createQueryBuilder('article')
            .select('DISTINCT article.category', 'category')
            .where('article.category IS NOT NULL')
            .andWhere('article.isPublished = true')
            .orderBy('article.category', 'ASC')
            .getRawMany();
        return result.map(row => row.category);
    }
    async getAllTags() {
        const articles = await this.articleRepository.find({
            where: { isPublished: true },
            select: ['tags'],
        });
        const allTags = new Set();
        articles.forEach(article => {
            if (article.tags) {
                article.tags.forEach(tag => allTags.add(tag));
            }
        });
        return Array.from(allTags).sort();
    }
    async getArticleStatistics() {
        const totalArticles = await this.articleRepository.count();
        const publishedArticles = await this.articleRepository.count({ where: { isPublished: true } });
        const draftArticles = await this.articleRepository.count({ where: { isPublished: false } });
        const totalViews = await this.articleRepository
            .createQueryBuilder('article')
            .select('SUM(article.viewCount)', 'totalViews')
            .getRawOne();
        const categories = await this.getCategories();
        return {
            totalArticles,
            publishedArticles,
            draftArticles,
            totalViews: parseInt(totalViews.totalViews) || 0,
            categoriesCount: categories.length,
        };
    }
    async rateArticle(articleId, rating) {
        const article = await this.findOne(articleId);
        const newRatingCount = article.ratingCount + 1;
        const newAverageRating = ((article.averageRating * article.ratingCount) + rating) / newRatingCount;
        await this.articleRepository.update(articleId, {
            averageRating: Math.round(newAverageRating * 100) / 100,
            ratingCount: newRatingCount,
        });
        return this.findOne(articleId);
    }
    async getRelatedArticles(articleId, limit = 5) {
        const article = await this.findOne(articleId);
        if (!article.tags || article.tags.length === 0) {
            return [];
        }
        return this.articleRepository
            .createQueryBuilder('article')
            .leftJoinAndSelect('article.author', 'author')
            .where('article.id != :articleId', { articleId })
            .andWhere('article.isPublished = true')
            .andWhere('article.tags && :tags', { tags: article.tags })
            .orderBy('article.viewCount', 'DESC')
            .take(limit)
            .getMany();
    }
};
exports.KnowledgeBaseService = KnowledgeBaseService;
exports.KnowledgeBaseService = KnowledgeBaseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(knowledge_base_article_entity_1.KnowledgeBaseArticle)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], KnowledgeBaseService);
//# sourceMappingURL=knowledge-base.service.js.map