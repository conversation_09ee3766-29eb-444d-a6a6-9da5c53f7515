"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeatureFlag = exports.FeatureFlagStatus = exports.FeatureFlagType = void 0;
const typeorm_1 = require("typeorm");
var FeatureFlagType;
(function (FeatureFlagType) {
    FeatureFlagType["BOOLEAN"] = "boolean";
    FeatureFlagType["STRING"] = "string";
    FeatureFlagType["NUMBER"] = "number";
    FeatureFlagType["JSON"] = "json";
    FeatureFlagType["PERCENTAGE"] = "percentage";
})(FeatureFlagType || (exports.FeatureFlagType = FeatureFlagType = {}));
var FeatureFlagStatus;
(function (FeatureFlagStatus) {
    FeatureFlagStatus["ACTIVE"] = "active";
    FeatureFlagStatus["INACTIVE"] = "inactive";
    FeatureFlagStatus["TESTING"] = "testing";
    FeatureFlagStatus["DEPRECATED"] = "deprecated";
})(FeatureFlagStatus || (exports.FeatureFlagStatus = FeatureFlagStatus = {}));
let FeatureFlag = class FeatureFlag {
    id;
    key;
    name;
    description;
    type;
    status;
    defaultValue;
    variations;
    targeting;
    rolloutPercentage;
    environments;
    tags;
    isTemporary;
    expiresAt;
    createdBy;
    lastModifiedBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.FeatureFlag = FeatureFlag;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], FeatureFlag.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, unique: true }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: FeatureFlagType,
        default: FeatureFlagType.BOOLEAN,
    }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: FeatureFlagStatus,
        default: FeatureFlagStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "defaultValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], FeatureFlag.prototype, "variations", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], FeatureFlag.prototype, "targeting", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], FeatureFlag.prototype, "rolloutPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], FeatureFlag.prototype, "environments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], FeatureFlag.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], FeatureFlag.prototype, "isTemporary", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], FeatureFlag.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], FeatureFlag.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], FeatureFlag.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], FeatureFlag.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], FeatureFlag.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], FeatureFlag.prototype, "updatedAt", void 0);
exports.FeatureFlag = FeatureFlag = __decorate([
    (0, typeorm_1.Entity)('feature_flags')
], FeatureFlag);
//# sourceMappingURL=feature-flag.entity.js.map