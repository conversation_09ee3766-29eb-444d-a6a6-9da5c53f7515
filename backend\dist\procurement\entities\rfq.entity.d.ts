import { RFQResponse } from './rfq-response.entity';
export declare enum RFQStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    RESPONSES_RECEIVED = "responses_received",
    EVALUATION_IN_PROGRESS = "evaluation_in_progress",
    AWARDED = "awarded",
    CANCELLED = "cancelled",
    CLOSED = "closed"
}
export declare class RFQ {
    id: string;
    rfqNumber: string;
    title: string;
    description: string;
    status: RFQStatus;
    publishDate: Date;
    responseDeadline: Date;
    awardDate: Date;
    requirements: any;
    evaluationCriteria: any;
    invitedVendors: string[];
    attachments: string[];
    createdBy: string;
    awardedTo: string;
    awardReason: string;
    responses: RFQResponse[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
