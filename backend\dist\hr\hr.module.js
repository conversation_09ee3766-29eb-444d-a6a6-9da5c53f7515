"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HrModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const employee_entity_1 = require("./entities/employee.entity");
const department_entity_1 = require("./entities/department.entity");
const position_entity_1 = require("./entities/position.entity");
const attendance_entity_1 = require("./entities/attendance.entity");
const leave_entity_1 = require("./entities/leave.entity");
const leave_type_entity_1 = require("./entities/leave-type.entity");
const payroll_entity_1 = require("./entities/payroll.entity");
const payroll_item_entity_1 = require("./entities/payroll-item.entity");
const performance_entity_1 = require("./entities/performance.entity");
const training_entity_1 = require("./entities/training.entity");
const benefit_entity_1 = require("./entities/benefit.entity");
const employee_benefit_entity_1 = require("./entities/employee-benefit.entity");
const employee_service_1 = require("./services/employee.service");
const department_service_1 = require("./services/department.service");
const attendance_service_1 = require("./services/attendance.service");
const leave_service_1 = require("./services/leave.service");
const payroll_service_1 = require("./services/payroll.service");
const performance_service_1 = require("./services/performance.service");
const training_service_1 = require("./services/training.service");
const benefit_service_1 = require("./services/benefit.service");
const employee_controller_1 = require("./controllers/employee.controller");
const department_controller_1 = require("./controllers/department.controller");
const attendance_controller_1 = require("./controllers/attendance.controller");
const leave_controller_1 = require("./controllers/leave.controller");
const payroll_controller_1 = require("./controllers/payroll.controller");
const performance_controller_1 = require("./controllers/performance.controller");
const training_controller_1 = require("./controllers/training.controller");
const benefit_controller_1 = require("./controllers/benefit.controller");
let HrModule = class HrModule {
};
exports.HrModule = HrModule;
exports.HrModule = HrModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                employee_entity_1.Employee,
                department_entity_1.Department,
                position_entity_1.Position,
                attendance_entity_1.Attendance,
                leave_entity_1.Leave,
                leave_type_entity_1.LeaveType,
                payroll_entity_1.Payroll,
                payroll_item_entity_1.PayrollItem,
                performance_entity_1.Performance,
                training_entity_1.Training,
                benefit_entity_1.Benefit,
                employee_benefit_entity_1.EmployeeBenefit,
            ]),
        ],
        controllers: [
            employee_controller_1.EmployeeController,
            department_controller_1.DepartmentController,
            attendance_controller_1.AttendanceController,
            leave_controller_1.LeaveController,
            payroll_controller_1.PayrollController,
            performance_controller_1.PerformanceController,
            training_controller_1.TrainingController,
            benefit_controller_1.BenefitController,
        ],
        providers: [
            employee_service_1.EmployeeService,
            department_service_1.DepartmentService,
            attendance_service_1.AttendanceService,
            leave_service_1.LeaveService,
            payroll_service_1.PayrollService,
            performance_service_1.PerformanceService,
            training_service_1.TrainingService,
            benefit_service_1.BenefitService,
        ],
        exports: [
            employee_service_1.EmployeeService,
            department_service_1.DepartmentService,
            attendance_service_1.AttendanceService,
            leave_service_1.LeaveService,
            payroll_service_1.PayrollService,
            performance_service_1.PerformanceService,
            training_service_1.TrainingService,
            benefit_service_1.BenefitService,
        ],
    })
], HrModule);
//# sourceMappingURL=hr.module.js.map