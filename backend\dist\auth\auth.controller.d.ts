import { AuthService } from './auth.service';
import { RegisterCompanyDto } from './dto/register-company.dto';
import { LoginDto } from './dto/login.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    registerCompany(registerDto: RegisterCompanyDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("../user/entities/user.entity").UserRole;
            company: {
                id: string;
                name: string;
                slug: string;
                tenantId: string;
            };
        };
        token: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("../user/entities/user.entity").UserRole;
            company: {
                id: string;
                name: string;
                slug: string;
                tenantId: string;
            };
        };
        token: string;
    }>;
    getProfile(req: any): Promise<{
        user: any;
    }>;
    logout(): Promise<{
        message: string;
    }>;
}
