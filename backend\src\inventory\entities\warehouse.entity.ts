import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Location } from './location.entity';
import { Stock } from './stock.entity';

export enum WarehouseType {
  MAIN = 'main',
  DISTRIBUTION = 'distribution',
  RETAIL = 'retail',
  TRANSIT = 'transit',
  QUARANTINE = 'quarantine',
  RETURNS = 'returns',
}

@Entity('inventory_warehouses')
export class Warehouse {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: WarehouseType,
    default: WarehouseType.MAIN,
  })
  type: WarehouseType;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ length: 100, nullable: true })
  city: string;

  @Column({ length: 100, nullable: true })
  state: string;

  @Column({ length: 20, nullable: true })
  zipCode: string;

  @Column({ length: 100, nullable: true })
  country: string;

  @Column({ length: 20, nullable: true })
  phone: string;

  @Column({ length: 200, nullable: true })
  email: string;

  @Column({ nullable: true })
  managerId: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalArea: number; // in square meters

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  usableArea: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxCapacity: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ type: 'json', nullable: true })
  operatingHours: any;

  @Column({ type: 'json', nullable: true })
  contactInfo: any;

  @OneToMany(() => Location, location => location.warehouse)
  locations: Location[];

  @OneToMany(() => Stock, stock => stock.warehouse)
  stocks: Stock[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
