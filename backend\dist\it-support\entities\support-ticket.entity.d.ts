import { TicketComment } from './ticket-comment.entity';
import { TicketAttachment } from './ticket-attachment.entity';
export declare enum TicketStatus {
    OPEN = "open",
    IN_PROGRESS = "in_progress",
    PENDING_USER = "pending_user",
    PENDING_VENDOR = "pending_vendor",
    RESOLVED = "resolved",
    CLOSED = "closed",
    CANCELLED = "cancelled"
}
export declare enum TicketPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent",
    CRITICAL = "critical"
}
export declare enum TicketType {
    INCIDENT = "incident",
    SERVICE_REQUEST = "service_request",
    CHANGE_REQUEST = "change_request",
    PROBLEM = "problem",
    QUESTION = "question",
    COMPLAINT = "complaint"
}
export declare enum TicketCategory {
    HARDWARE = "hardware",
    SOFTWARE = "software",
    NETWORK = "network",
    EMAIL = "email",
    PHONE = "phone",
    PRINTER = "printer",
    ACCESS = "access",
    ACCOUNT = "account",
    TRAINING = "training",
    OTHER = "other"
}
export declare class SupportTicket {
    id: string;
    ticketNumber: string;
    subject: string;
    description: string;
    type: TicketType;
    category: TicketCategory;
    status: TicketStatus;
    priority: TicketPriority;
    requesterId: string;
    requesterName: string;
    requesterEmail: string;
    requesterPhone: string;
    assignedTo: string;
    assignedGroup: string;
    assignedAt: Date;
    firstResponseAt: Date;
    resolvedAt: Date;
    closedAt: Date;
    dueDate: Date;
    resolution: string;
    workaround: string;
    tags: string[];
    satisfactionRating: number;
    satisfactionFeedback: string;
    escalationLevel: number;
    lastEscalatedAt: Date;
    reopenCount: number;
    lastReopenedAt: Date;
    comments: TicketComment[];
    attachments: TicketAttachment[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
