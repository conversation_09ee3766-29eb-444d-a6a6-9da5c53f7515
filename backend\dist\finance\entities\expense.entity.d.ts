import { ExpenseCategory } from './expense-category.entity';
import { Account } from './account.entity';
export declare enum ExpenseStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    APPROVED = "approved",
    REJECTED = "rejected",
    PAID = "paid",
    CANCELLED = "cancelled"
}
export declare enum ExpenseType {
    BUSINESS = "business",
    TRAVEL = "travel",
    MEALS = "meals",
    OFFICE = "office",
    MARKETING = "marketing",
    UTILITIES = "utilities",
    RENT = "rent",
    INSURANCE = "insurance",
    PROFESSIONAL_SERVICES = "professional_services",
    OTHER = "other"
}
export declare class Expense {
    id: string;
    expenseNumber: string;
    title: string;
    description: string;
    type: ExpenseType;
    status: ExpenseStatus;
    expenseDate: Date;
    amount: number;
    currency: string;
    exchangeRate: number;
    baseCurrencyAmount: number;
    categoryId: string;
    category: ExpenseCategory;
    accountId: string;
    account: Account;
    vendor: string;
    receiptNumber: string;
    attachments: string[];
    employeeId: string;
    departmentId: string;
    projectId: string;
    budgetId: string;
    taxRate: number;
    taxAmount: number;
    totalAmount: number;
    isReimbursable: boolean;
    isRecurring: boolean;
    recurringFrequency: string;
    submittedBy: string;
    approvedBy: string;
    submittedAt: Date;
    approvedAt: Date;
    approvalNotes: string;
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
