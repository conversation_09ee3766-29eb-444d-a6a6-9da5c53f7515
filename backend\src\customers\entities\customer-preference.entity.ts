import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Customer } from './customer.entity';

export enum PreferenceType {
  COMMUNICATION = 'communication',
  PRODUCT = 'product',
  SERVICE = 'service',
  DELIVERY = 'delivery',
  PAYMENT = 'payment',
  MARKETING = 'marketing',
  PRIVACY = 'privacy',
  NOTIFICATION = 'notification',
  CUSTOM = 'custom',
}

@Entity('customer_preferences')
export class CustomerPreference {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer.preferences)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({
    type: 'enum',
    enum: PreferenceType,
  })
  type: PreferenceType;

  @Column({ length: 255 })
  key: string;

  @Column({ type: 'text' })
  value: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
