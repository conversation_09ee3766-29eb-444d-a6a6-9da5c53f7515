import { Repository } from 'typeorm';
import { CustomerSegment } from '../entities/customer-segment.entity';
import { Customer } from '../entities/customer.entity';
export declare class CustomerSegmentService {
    private segmentRepository;
    private customerRepository;
    constructor(segmentRepository: Repository<CustomerSegment>, customerRepository: Repository<Customer>);
    create(segmentData: Partial<CustomerSegment>): Promise<CustomerSegment>;
    findAll(): Promise<CustomerSegment[]>;
    findOne(id: string): Promise<CustomerSegment>;
    update(id: string, updateData: Partial<CustomerSegment>): Promise<CustomerSegment>;
    remove(id: string): Promise<void>;
    refreshSegmentCustomers(segmentId: string): Promise<number>;
    findCustomersByCriteria(criteria: any): Promise<Customer[]>;
    getSegmentCustomers(segmentId: string, page?: number, limit?: number): Promise<{
        customers: Customer[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    createHighValueSegment(minSpent?: number): Promise<CustomerSegment>;
    createLoyaltySegment(minPoints?: number): Promise<CustomerSegment>;
    createInactiveSegment(daysSinceLastPurchase?: number): Promise<CustomerSegment>;
    createNewCustomerSegment(daysSinceRegistration?: number): Promise<CustomerSegment>;
    getSegmentAnalytics(segmentId: string): Promise<any>;
    refreshAllSegments(): Promise<{
        segmentId: string;
        customerCount: number;
    }[]>;
    activateSegment(id: string): Promise<CustomerSegment>;
    deactivateSegment(id: string): Promise<CustomerSegment>;
}
