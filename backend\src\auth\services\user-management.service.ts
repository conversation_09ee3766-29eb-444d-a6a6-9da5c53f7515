import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatus } from '../../user/entities/user.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import * as bcrypt from 'bcrypt';

export interface CreateUserDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  department?: string;
  position?: string;
  employeeId?: string;
  roleId: string;
  departmentAccess?: string[];
  additionalPermissionIds?: string[];
  companyId: string;
}

export interface UpdateUserDto {
  firstName?: string;
  lastName?: string;
  phone?: string;
  department?: string;
  position?: string;
  employeeId?: string;
  roleId?: string;
  departmentAccess?: string[];
  additionalPermissionIds?: string[];
  status?: UserStatus;
  isActive?: boolean;
}

export interface UserPermissionCheck {
  hasPermission: boolean;
  source: 'role' | 'additional' | 'none';
  permission?: Permission;
}

@Injectable()
export class UserManagementService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async createUser(createUserDto: CreateUserDto): Promise<User> {
    // Check if email already exists
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new BadRequestException('Email already exists');
    }

    // Validate role exists
    const role = await this.roleRepository.findOne({
      where: { id: createUserDto.roleId },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // Create user
    const user = this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
      status: UserStatus.ACTIVE,
    });

    const savedUser = await this.userRepository.save(user);

    // Assign additional permissions if provided
    if (createUserDto.additionalPermissionIds && createUserDto.additionalPermissionIds.length > 0) {
      await this.assignAdditionalPermissions(savedUser.id, createUserDto.additionalPermissionIds);
    }

    return this.findUserById(savedUser.id);
  }

  async findAllUsers(): Promise<User[]> {
    return this.userRepository.find({
      relations: ['role', 'role.permissions', 'additionalPermissions'],
      order: { firstName: 'ASC', lastName: 'ASC' },
    });
  }

  async findUserById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['role', 'role.permissions', 'additionalPermissions'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findUserByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['role', 'role.permissions', 'additionalPermissions'],
    });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    return user;
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findUserById(id);

    // If role is being changed, validate it exists
    if (updateUserDto.roleId) {
      const role = await this.roleRepository.findOne({
        where: { id: updateUserDto.roleId },
      });

      if (!role) {
        throw new NotFoundException('Role not found');
      }
    }

    // Update user
    await this.userRepository.update(id, updateUserDto);

    // Update additional permissions if provided
    if (updateUserDto.additionalPermissionIds !== undefined) {
      await this.assignAdditionalPermissions(id, updateUserDto.additionalPermissionIds);
    }

    return this.findUserById(id);
  }

  async deleteUser(id: string): Promise<void> {
    const user = await this.findUserById(id);
    await this.userRepository.remove(user);
  }

  async changeUserPassword(id: string, newPassword: string): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await this.userRepository.update(id, { password: hashedPassword });
  }

  async activateUser(id: string): Promise<User> {
    await this.userRepository.update(id, {
      status: UserStatus.ACTIVE,
      isActive: true,
    });
    return this.findUserById(id);
  }

  async deactivateUser(id: string): Promise<User> {
    await this.userRepository.update(id, {
      status: UserStatus.INACTIVE,
      isActive: false,
    });
    return this.findUserById(id);
  }

  async suspendUser(id: string): Promise<User> {
    await this.userRepository.update(id, {
      status: UserStatus.SUSPENDED,
      isActive: false,
    });
    return this.findUserById(id);
  }

  async assignRole(userId: string, roleId: string): Promise<User> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    await this.userRepository.update(userId, { roleId });
    return this.findUserById(userId);
  }

  async assignAdditionalPermissions(userId: string, permissionIds: string[]): Promise<User> {
    const user = await this.findUserById(userId);
    
    if (permissionIds.length > 0) {
      const permissions = await this.permissionRepository.findByIds(permissionIds);
      user.additionalPermissions = permissions;
    } else {
      user.additionalPermissions = [];
    }

    await this.userRepository.save(user);
    return this.findUserById(userId);
  }

  async removeAdditionalPermissions(userId: string, permissionIds: string[]): Promise<User> {
    const user = await this.findUserById(userId);
    
    user.additionalPermissions = user.additionalPermissions.filter(
      permission => !permissionIds.includes(permission.id)
    );

    await this.userRepository.save(user);
    return this.findUserById(userId);
  }

  async getUserPermissions(userId: string): Promise<Permission[]> {
    const user = await this.findUserById(userId);
    
    const rolePermissions = user.role?.permissions || [];
    const additionalPermissions = user.additionalPermissions || [];
    
    // Combine and deduplicate permissions
    const allPermissions = [...rolePermissions, ...additionalPermissions];
    const uniquePermissions = allPermissions.filter((permission, index, self) =>
      index === self.findIndex(p => p.id === permission.id)
    );

    return uniquePermissions;
  }

  async checkUserPermission(
    userId: string,
    module: string,
    action: string,
    resource: string
  ): Promise<UserPermissionCheck> {
    const user = await this.findUserById(userId);
    
    if (!user.isActive || user.status !== UserStatus.ACTIVE) {
      return { hasPermission: false, source: 'none' };
    }

    // Check role permissions
    const rolePermissions = user.role?.permissions || [];
    const rolePermission = rolePermissions.find(p =>
      p.module === module && p.action === action && p.resource === resource
    );

    if (rolePermission) {
      return {
        hasPermission: true,
        source: 'role',
        permission: rolePermission,
      };
    }

    // Check additional permissions
    const additionalPermissions = user.additionalPermissions || [];
    const additionalPermission = additionalPermissions.find(p =>
      p.module === module && p.action === action && p.resource === resource
    );

    if (additionalPermission) {
      return {
        hasPermission: true,
        source: 'additional',
        permission: additionalPermission,
      };
    }

    return { hasPermission: false, source: 'none' };
  }

  async getUsersByRole(roleId: string): Promise<User[]> {
    return this.userRepository.find({
      where: { roleId },
      relations: ['role'],
      order: { firstName: 'ASC', lastName: 'ASC' },
    });
  }

  async getUsersByDepartment(department: string): Promise<User[]> {
    return this.userRepository.find({
      where: { department },
      relations: ['role'],
      order: { firstName: 'ASC', lastName: 'ASC' },
    });
  }

  async getUsersWithDepartmentAccess(department: string): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.departmentAccess @> :department', { department: [department] })
      .orWhere('role.departmentAccess @> :department', { department: [department] })
      .orderBy('user.firstName', 'ASC')
      .addOrderBy('user.lastName', 'ASC')
      .getMany();
  }

  async searchUsers(searchTerm: string): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.firstName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('user.lastName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('user.email ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('user.employeeId ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('user.firstName', 'ASC')
      .addOrderBy('user.lastName', 'ASC')
      .getMany();
  }

  async getUserStatistics(): Promise<any> {
    const totalUsers = await this.userRepository.count();
    const activeUsers = await this.userRepository.count({
      where: { status: UserStatus.ACTIVE },
    });
    const inactiveUsers = await this.userRepository.count({
      where: { status: UserStatus.INACTIVE },
    });
    const suspendedUsers = await this.userRepository.count({
      where: { status: UserStatus.SUSPENDED },
    });
    const pendingUsers = await this.userRepository.count({
      where: { status: UserStatus.PENDING },
    });

    // Users by department
    const departmentStats = await this.userRepository
      .createQueryBuilder('user')
      .select('user.department', 'department')
      .addSelect('COUNT(user.id)', 'count')
      .where('user.department IS NOT NULL')
      .groupBy('user.department')
      .orderBy('count', 'DESC')
      .getRawMany();

    // Users by role
    const roleStats = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.role', 'role')
      .select('role.name', 'roleName')
      .addSelect('COUNT(user.id)', 'count')
      .where('role.name IS NOT NULL')
      .groupBy('role.name')
      .orderBy('count', 'DESC')
      .getRawMany();

    return {
      totalUsers,
      activeUsers,
      inactiveUsers,
      suspendedUsers,
      pendingUsers,
      departmentDistribution: departmentStats.map(stat => ({
        department: stat.department,
        count: parseInt(stat.count),
      })),
      roleDistribution: roleStats.map(stat => ({
        role: stat.roleName,
        count: parseInt(stat.count),
      })),
    };
  }

  async updateLastLogin(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      lastLoginAt: new Date(),
    });
  }

  async bulkUpdateUsers(userIds: string[], updateData: Partial<UpdateUserDto>): Promise<void> {
    await this.userRepository.update(userIds, updateData);
  }

  async exportUsers(): Promise<any[]> {
    const users = await this.findAllUsers();
    
    return users.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      department: user.department,
      position: user.position,
      employeeId: user.employeeId,
      role: user.role?.name,
      status: user.status,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      departmentAccess: user.departmentAccess,
      additionalPermissions: user.additionalPermissions?.map(p => p.name),
    }));
  }
}
