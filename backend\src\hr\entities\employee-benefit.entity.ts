import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Employee } from './employee.entity';
import { Benefit } from './benefit.entity';

export enum EnrollmentStatus {
  ENROLLED = 'enrolled',
  PENDING = 'pending',
  DECLINED = 'declined',
  TERMINATED = 'terminated',
  SUSPENDED = 'suspended',
}

@Entity('hr_employee_benefits')
export class EmployeeBenefit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  employeeId: string;

  @ManyToOne(() => Employee, employee => employee.benefits)
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column()
  benefitId: string;

  @ManyToOne(() => Benefit, benefit => benefit.employeeBenefits)
  @JoinColumn({ name: 'benefitId' })
  benefit: Benefit;

  @Column({
    type: 'enum',
    enum: EnrollmentStatus,
    default: EnrollmentStatus.PENDING,
  })
  status: EnrollmentStatus;

  @Column({ type: 'date' })
  enrollmentDate: Date;

  @Column({ type: 'date' })
  effectiveDate: Date;

  @Column({ type: 'date', nullable: true })
  terminationDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  employeeContribution: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  employerContribution: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  coverageAmount: number;

  @Column({ type: 'json', nullable: true })
  dependents: any[];

  @Column({ type: 'json', nullable: true })
  beneficiaries: any[];

  @Column({ type: 'json', nullable: true })
  enrollmentChoices: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  enrolledBy: string;

  @Column({ type: 'timestamp', nullable: true })
  enrolledAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
