import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { RFQ } from './rfq.entity';

export enum ResponseStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

@Entity('rfq_responses')
export class RFQResponse {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  rfqId: string;

  @ManyToOne(() => RFQ, rfq => rfq.responses)
  @JoinColumn({ name: 'rfqId' })
  rfq: RFQ;

  @Column()
  vendorId: string;

  @Column({
    type: 'enum',
    enum: ResponseStatus,
    default: ResponseStatus.DRAFT,
  })
  status: ResponseStatus;

  @Column({ type: 'timestamp' })
  submittedAt: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  totalPrice: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'int' })
  deliveryDays: number;

  @Column({ type: 'text', nullable: true })
  proposal: string;

  @Column({ type: 'json', nullable: true })
  lineItems: any[];

  @Column({ type: 'json', nullable: true })
  terms: any;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })
  evaluationScore: number;

  @Column({ type: 'text', nullable: true })
  evaluationNotes: string;

  @Column({ nullable: true })
  evaluatedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  evaluatedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
