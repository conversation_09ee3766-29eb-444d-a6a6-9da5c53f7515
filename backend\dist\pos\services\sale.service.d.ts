import { Repository } from 'typeorm';
import { Sale } from '../entities/sale.entity';
import { SaleItem } from '../entities/sale-item.entity';
import { Payment } from '../entities/payment.entity';
export declare class SaleService {
    private saleRepository;
    private saleItemRepository;
    private paymentRepository;
    constructor(saleRepository: Repository<Sale>, saleItemRepository: Repository<SaleItem>, paymentRepository: Repository<Payment>);
    create(saleData: Partial<Sale>): Promise<Sale>;
    findAll(): Promise<Sale[]>;
    findOne(id: string): Promise<Sale>;
    update(id: string, updateData: Partial<Sale>): Promise<Sale>;
    remove(id: string): Promise<void>;
    addItem(saleId: string, itemData: Partial<SaleItem>): Promise<SaleItem>;
    updateItem(itemId: string, updateData: Partial<SaleItem>): Promise<SaleItem>;
    removeItem(itemId: string): Promise<void>;
    private recalculateSaleTotals;
    addPayment(saleId: string, paymentData: Partial<Payment>): Promise<Payment>;
    private checkSalePaymentStatus;
    completeSale(saleId: string): Promise<Sale>;
    voidSale(saleId: string, reason: string): Promise<Sale>;
    refundSale(saleId: string, refundAmount: number, reason: string): Promise<Sale>;
    findByCustomer(customerId: string): Promise<Sale[]>;
    findByCashier(cashierId: string): Promise<Sale[]>;
    findByDateRange(startDate: Date, endDate: Date): Promise<Sale[]>;
    getSalesStatistics(startDate?: Date, endDate?: Date): Promise<any>;
    getTopSellingProducts(limit?: number): Promise<any[]>;
    private generateSaleNumber;
    getDashboardMetrics(): Promise<any>;
}
