import { WarehouseService } from '../services/warehouse.service';
import { Warehouse } from '../entities/warehouse.entity';
import { Location } from '../entities/location.entity';
export declare class WarehouseController {
    private readonly warehouseService;
    constructor(warehouseService: WarehouseService);
    create(createWarehouseDto: Partial<Warehouse>): Promise<Warehouse>;
    findAll(): Promise<Warehouse[]>;
    getActiveWarehouses(): Promise<Warehouse[]>;
    getStatistics(): Promise<any>;
    searchWarehouses(searchTerm: string): Promise<Warehouse[]>;
    getWarehousesByRegion(region: string): Promise<Warehouse[]>;
    findByCode(code: string): Promise<Warehouse>;
    findOne(id: string): Promise<Warehouse>;
    getWarehouseLocations(id: string): Promise<Location[]>;
    getWarehouseStock(id: string): Promise<import("../entities/stock.entity").Stock[]>;
    getWarehouseStockValue(id: string): Promise<{
        warehouseId: string;
        stockValue: number;
    }>;
    getCapacityUtilization(id: string): Promise<any>;
    createLocation(id: string, createLocationDto: Partial<Location>): Promise<Location>;
    transferStock(transferData: {
        fromWarehouseId: string;
        toWarehouseId: string;
        productId: string;
        quantity: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    generateWarehouseCode(data: {
        name: string;
    }): Promise<{
        code: string;
    }>;
    update(id: string, updateWarehouseDto: Partial<Warehouse>): Promise<Warehouse>;
    remove(id: string): Promise<void>;
}
