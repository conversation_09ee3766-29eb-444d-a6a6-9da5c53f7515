"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerInteractionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_interaction_entity_1 = require("../entities/customer-interaction.entity");
const customer_entity_1 = require("../entities/customer.entity");
let CustomerInteractionService = class CustomerInteractionService {
    interactionRepository;
    customerRepository;
    constructor(interactionRepository, customerRepository) {
        this.interactionRepository = interactionRepository;
        this.customerRepository = customerRepository;
    }
    async create(interactionData) {
        const customer = await this.customerRepository.findOne({
            where: { id: interactionData.customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${interactionData.customerId} not found`);
        }
        const interaction = this.interactionRepository.create({
            ...interactionData,
            interactionDate: interactionData.interactionDate || new Date(),
        });
        const savedInteraction = await this.interactionRepository.save(interaction);
        await this.customerRepository.update(interactionData.customerId, { lastContactDate: savedInteraction.interactionDate });
        return savedInteraction;
    }
    async findAll(options) {
        const { customerId, type, channel, startDate, endDate, page = 1, limit = 20 } = options || {};
        const queryBuilder = this.interactionRepository.createQueryBuilder('interaction')
            .leftJoinAndSelect('interaction.customer', 'customer');
        if (customerId) {
            queryBuilder.andWhere('interaction.customerId = :customerId', { customerId });
        }
        if (type) {
            queryBuilder.andWhere('interaction.type = :type', { type });
        }
        if (channel) {
            queryBuilder.andWhere('interaction.channel = :channel', { channel });
        }
        if (startDate && endDate) {
            queryBuilder.andWhere('interaction.interactionDate BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        queryBuilder.orderBy('interaction.interactionDate', 'DESC');
        const [interactions, total] = await queryBuilder.getManyAndCount();
        const totalPages = Math.ceil(total / limit);
        return {
            interactions,
            total,
            page,
            totalPages,
        };
    }
    async findOne(id) {
        const interaction = await this.interactionRepository.findOne({
            where: { id },
            relations: ['customer'],
        });
        if (!interaction) {
            throw new common_1.NotFoundException(`Interaction with ID ${id} not found`);
        }
        return interaction;
    }
    async findByCustomer(customerId) {
        return this.interactionRepository.find({
            where: { customerId },
            relations: ['customer'],
            order: { interactionDate: 'DESC' },
        });
    }
    async update(id, updateData) {
        const interaction = await this.findOne(id);
        await this.interactionRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const interaction = await this.findOne(id);
        await this.interactionRepository.remove(interaction);
    }
    async logCall(customerId, data) {
        return this.create({
            customerId,
            type: 'call',
            channel: 'phone',
            duration: data.duration,
            outcome: data.outcome,
            notes: data.notes,
            followUpRequired: data.followUpRequired,
            followUpDate: data.followUpDate,
            contactedBy: data.contactedBy,
        });
    }
    async logEmail(customerId, data) {
        return this.create({
            customerId,
            type: 'email',
            channel: 'email',
            subject: data.subject,
            outcome: data.outcome,
            notes: data.notes,
            followUpRequired: data.followUpRequired,
            followUpDate: data.followUpDate,
            contactedBy: data.contactedBy,
        });
    }
    async logMeeting(customerId, data) {
        return this.create({
            customerId,
            type: 'meeting',
            channel: 'in_person',
            location: data.location,
            duration: data.duration,
            outcome: data.outcome,
            notes: data.notes,
            followUpRequired: data.followUpRequired,
            followUpDate: data.followUpDate,
            contactedBy: data.contactedBy,
        });
    }
    async getInteractionStats(customerId) {
        const queryBuilder = this.interactionRepository.createQueryBuilder('interaction');
        if (customerId) {
            queryBuilder.where('interaction.customerId = :customerId', { customerId });
        }
        const totalInteractions = await queryBuilder.getCount();
        const typeStats = await this.interactionRepository
            .createQueryBuilder('interaction')
            .select('interaction.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .where(customerId ? 'interaction.customerId = :customerId' : '1=1', { customerId })
            .groupBy('interaction.type')
            .getRawMany();
        const channelStats = await this.interactionRepository
            .createQueryBuilder('interaction')
            .select('interaction.channel', 'channel')
            .addSelect('COUNT(*)', 'count')
            .where(customerId ? 'interaction.customerId = :customerId' : '1=1', { customerId })
            .groupBy('interaction.channel')
            .getRawMany();
        const outcomeStats = await this.interactionRepository
            .createQueryBuilder('interaction')
            .select('interaction.outcome', 'outcome')
            .addSelect('COUNT(*)', 'count')
            .where(customerId ? 'interaction.customerId = :customerId' : '1=1', { customerId })
            .andWhere('interaction.outcome IS NOT NULL')
            .groupBy('interaction.outcome')
            .getRawMany();
        const followUpRequired = await this.interactionRepository.count({
            where: {
                ...(customerId && { customerId }),
                followUpRequired: true,
                followUpCompleted: false,
            },
        });
        return {
            totalInteractions,
            typeDistribution: typeStats,
            channelDistribution: channelStats,
            outcomeDistribution: outcomeStats,
            followUpRequired,
        };
    }
    async getUpcomingFollowUps(days = 7) {
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + days);
        return this.interactionRepository.find({
            where: {
                followUpRequired: true,
                followUpCompleted: false,
                followUpDate: (0, typeorm_2.Between)(new Date(), endDate),
            },
            relations: ['customer'],
            order: { followUpDate: 'ASC' },
        });
    }
    async markFollowUpCompleted(id, notes) {
        const interaction = await this.findOne(id);
        await this.interactionRepository.update(id, {
            followUpCompleted: true,
            followUpCompletedDate: new Date(),
            followUpNotes: notes,
        });
        return this.findOne(id);
    }
    async getInteractionTimeline(customerId, days = 30) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        return this.interactionRepository.find({
            where: {
                customerId,
                interactionDate: (0, typeorm_2.Between)(startDate, new Date()),
            },
            order: { interactionDate: 'DESC' },
        });
    }
    async getInteractionSummary(customerId) {
        const interactions = await this.findByCustomer(customerId);
        const totalInteractions = interactions.length;
        const lastInteraction = interactions[0];
        const firstInteraction = interactions[interactions.length - 1];
        const callCount = interactions.filter(i => i.type === 'call').length;
        const emailCount = interactions.filter(i => i.type === 'email').length;
        const meetingCount = interactions.filter(i => i.type === 'meeting').length;
        const totalDuration = interactions
            .filter(i => i.duration)
            .reduce((sum, i) => sum + i.duration, 0);
        const averageDuration = callCount > 0 ? totalDuration / callCount : 0;
        const pendingFollowUps = interactions.filter(i => i.followUpRequired && !i.followUpCompleted).length;
        return {
            totalInteractions,
            callCount,
            emailCount,
            meetingCount,
            totalDuration,
            averageDuration,
            pendingFollowUps,
            lastInteractionDate: lastInteraction?.interactionDate,
            firstInteractionDate: firstInteraction?.interactionDate,
            lastInteractionType: lastInteraction?.type,
            lastInteractionOutcome: lastInteraction?.outcome,
        };
    }
};
exports.CustomerInteractionService = CustomerInteractionService;
exports.CustomerInteractionService = CustomerInteractionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_interaction_entity_1.CustomerInteraction)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerInteractionService);
//# sourceMappingURL=customer-interaction.service.js.map