import { SupplierService } from '../services/supplier.service';
import { Supplier } from '../entities/supplier.entity';
export declare class SupplierController {
    private readonly supplierService;
    constructor(supplierService: SupplierService);
    create(createSupplierDto: Partial<Supplier>): Promise<Supplier>;
    findAll(): Promise<Supplier[]>;
    getActiveSuppliers(): Promise<Supplier[]>;
    getStatistics(): Promise<any>;
    searchSuppliers(searchTerm: string): Promise<Supplier[]>;
    findByCode(code: string): Promise<Supplier>;
    findOne(id: string): Promise<Supplier>;
    generateSupplierCode(data: {
        name: string;
    }): Promise<{
        code: string;
    }>;
    update(id: string, updateSupplierDto: Partial<Supplier>): Promise<Supplier>;
    remove(id: string): Promise<void>;
}
