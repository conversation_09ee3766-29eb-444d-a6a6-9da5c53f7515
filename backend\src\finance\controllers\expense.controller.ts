import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ExpenseService } from '../services/expense.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/expenses')
@UseGuards(JwtAuthGuard)
export class ExpenseController {
  constructor(private readonly expenseService: ExpenseService) {}

  @Post()
  create(@Body() createExpenseDto: any) {
    return this.expenseService.create(createExpenseDto);
  }

  @Get()
  findAll(
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('employeeId') employeeId?: string,
    @Query('departmentId') departmentId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const filters: any = {};
    if (status) filters.status = status;
    if (type) filters.type = type;
    if (employeeId) filters.employeeId = employeeId;
    if (departmentId) filters.departmentId = departmentId;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    return this.expenseService.findAll(filters);
  }

  @Get('report')
  getExpenseReport(
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('employeeId') employeeId?: string,
    @Query('departmentId') departmentId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const filters: any = {};
    if (status) filters.status = status;
    if (type) filters.type = type;
    if (employeeId) filters.employeeId = employeeId;
    if (departmentId) filters.departmentId = departmentId;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    return this.expenseService.getExpenseReport(filters);
  }

  @Get('categories')
  findAllCategories() {
    return this.expenseService.findAllCategories();
  }

  @Post('categories')
  createCategory(@Body() createCategoryDto: any) {
    return this.expenseService.createCategory(createCategoryDto);
  }

  @Get('categories/:id')
  findCategory(@Param('id') id: string) {
    return this.expenseService.findCategory(id);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.expenseService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateExpenseDto: any) {
    return this.expenseService.update(id, updateExpenseDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.expenseService.remove(id);
  }

  @Post(':id/submit')
  submitExpense(@Param('id') id: string, @Body() submitDto: { submittedBy: string }) {
    return this.expenseService.submitExpense(id, submitDto.submittedBy);
  }

  @Post(':id/approve')
  approveExpense(
    @Param('id') id: string,
    @Body() approveDto: { approvedBy: string; notes?: string }
  ) {
    return this.expenseService.approveExpense(id, approveDto.approvedBy, approveDto.notes);
  }

  @Post(':id/reject')
  rejectExpense(
    @Param('id') id: string,
    @Body() rejectDto: { rejectedBy: string; notes: string }
  ) {
    return this.expenseService.rejectExpense(id, rejectDto.rejectedBy, rejectDto.notes);
  }

  @Post(':id/mark-paid')
  markAsPaid(@Param('id') id: string) {
    return this.expenseService.markAsPaid(id);
  }
}
