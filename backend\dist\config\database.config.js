"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTenantDatabaseConfig = exports.getDatabaseConfig = void 0;
const getDatabaseConfig = (configService) => {
    const nodeEnv = configService.get('NODE_ENV');
    const usePostgres = configService.get('USE_POSTGRES', 'false') === 'true';
    if (usePostgres) {
        return {
            type: 'postgres',
            host: configService.get('DB_HOST'),
            port: configService.get('DB_PORT'),
            username: configService.get('DB_USERNAME'),
            password: configService.get('DB_PASSWORD'),
            database: configService.get('MASTER_DB_NAME'),
            entities: [__dirname + '/../**/*.entity{.ts,.js}'],
            synchronize: nodeEnv === 'development',
            logging: nodeEnv === 'development',
        };
    }
    else {
        return {
            type: 'sqlite',
            database: 'zaidanone_master.db',
            entities: [__dirname + '/../**/*.entity{.ts,.js}'],
            synchronize: true,
            logging: true,
        };
    }
};
exports.getDatabaseConfig = getDatabaseConfig;
const getTenantDatabaseConfig = (configService, tenantId) => {
    const nodeEnv = configService.get('NODE_ENV');
    const usePostgres = configService.get('USE_POSTGRES', 'false') === 'true';
    if (usePostgres) {
        return {
            type: 'postgres',
            host: configService.get('DB_HOST'),
            port: configService.get('DB_PORT'),
            username: configService.get('DB_USERNAME'),
            password: configService.get('DB_PASSWORD'),
            database: `${configService.get('TENANT_DB_PREFIX')}${tenantId}`,
            entities: [__dirname + '/../**/*.entity{.ts,.js}'],
            synchronize: nodeEnv === 'development',
            logging: nodeEnv === 'development',
        };
    }
    else {
        return {
            type: 'sqlite',
            database: `zaidanone_tenant_${tenantId}.db`,
            entities: [__dirname + '/../**/*.entity{.ts,.js}'],
            synchronize: true,
            logging: true,
        };
    }
};
exports.getTenantDatabaseConfig = getTenantDatabaseConfig;
//# sourceMappingURL=database.config.js.map