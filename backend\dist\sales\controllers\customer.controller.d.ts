import { CustomerService } from '../services/customer.service';
import { CreateCustomerDto } from '../dto/create-customer.dto';
export declare class CustomerController {
    private readonly customerService;
    constructor(customerService: CustomerService);
    create(createCustomerDto: CreateCustomerDto, req: any): Promise<import("../entities/customer.entity").Customer>;
    findAll(req: any): Promise<import("../entities/customer.entity").Customer[]>;
    getStats(req: any): Promise<{
        totalCustomers: number;
        activeCustomers: number;
        totalBalance: number;
    }>;
    findOne(id: string, req: any): Promise<import("../entities/customer.entity").Customer>;
    update(id: string, updateCustomerDto: Partial<CreateCustomerDto>, req: any): Promise<import("../entities/customer.entity").Customer>;
    remove(id: string, req: any): Promise<void>;
    updateBalance(id: string, body: {
        amount: number;
    }, req: any): Promise<void>;
}
