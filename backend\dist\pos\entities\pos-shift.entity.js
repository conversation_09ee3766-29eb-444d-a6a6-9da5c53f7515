"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosShift = exports.ShiftStatus = void 0;
const typeorm_1 = require("typeorm");
const pos_terminal_entity_1 = require("./pos-terminal.entity");
const pos_cash_drawer_entity_1 = require("./pos-cash-drawer.entity");
var ShiftStatus;
(function (ShiftStatus) {
    ShiftStatus["OPEN"] = "open";
    ShiftStatus["CLOSED"] = "closed";
    ShiftStatus["SUSPENDED"] = "suspended";
})(ShiftStatus || (exports.ShiftStatus = ShiftStatus = {}));
let PosShift = class PosShift {
    id;
    shiftNumber;
    terminalId;
    terminal;
    cashierId;
    status;
    startTime;
    endTime;
    openingCash;
    closingCash;
    expectedCash;
    cashVariance;
    totalTransactions;
    totalSales;
    totalReturns;
    totalDiscounts;
    totalTax;
    cashSales;
    cardSales;
    otherPayments;
    voidedTransactions;
    voidedAmount;
    notes;
    closedBy;
    closedAt;
    cashDrawerActivities;
    paymentSummary;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosShift = PosShift;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosShift.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PosShift.prototype, "shiftNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosShift.prototype, "terminalId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_terminal_entity_1.PosTerminal, terminal => terminal.shifts),
    (0, typeorm_1.JoinColumn)({ name: 'terminalId' }),
    __metadata("design:type", pos_terminal_entity_1.PosTerminal)
], PosShift.prototype, "terminal", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosShift.prototype, "cashierId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ShiftStatus,
        default: ShiftStatus.OPEN,
    }),
    __metadata("design:type", String)
], PosShift.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], PosShift.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosShift.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "openingCash", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "closingCash", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "expectedCash", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "cashVariance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "totalTransactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "totalSales", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "totalReturns", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "totalDiscounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "totalTax", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "cashSales", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "cardSales", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "otherPayments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "voidedTransactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosShift.prototype, "voidedAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosShift.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosShift.prototype, "closedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosShift.prototype, "closedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_cash_drawer_entity_1.PosCashDrawer, cashDrawer => cashDrawer.shift),
    __metadata("design:type", Array)
], PosShift.prototype, "cashDrawerActivities", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosShift.prototype, "paymentSummary", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosShift.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosShift.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosShift.prototype, "updatedAt", void 0);
exports.PosShift = PosShift = __decorate([
    (0, typeorm_1.Entity)('pos_shifts')
], PosShift);
//# sourceMappingURL=pos-shift.entity.js.map