import { Repository } from 'typeorm';
import { Benefit } from '../entities/benefit.entity';
import { EmployeeBenefit } from '../entities/employee-benefit.entity';
export declare class BenefitService {
    private benefitRepository;
    private employeeBenefitRepository;
    constructor(benefitRepository: Repository<Benefit>, employeeBenefitRepository: Repository<EmployeeBenefit>);
    create(createBenefitDto: any): Promise<Benefit>;
    findAll(): Promise<Benefit[]>;
    findOne(id: string): Promise<Benefit>;
    enrollEmployee(employeeId: string, benefitId: string, enrollmentData: any): Promise<EmployeeBenefit>;
    getEmployeeBenefits(employeeId: string): Promise<EmployeeBenefit[]>;
}
