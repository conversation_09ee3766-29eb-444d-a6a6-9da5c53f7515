import { PosSale } from './pos-sale.entity';
export declare enum PaymentMethod {
    CASH = "cash",
    CREDIT_CARD = "credit_card",
    DEBIT_CARD = "debit_card",
    GIFT_CARD = "gift_card",
    STORE_CREDIT = "store_credit",
    CHECK = "check",
    MOBILE_PAYMENT = "mobile_payment",
    BANK_TRANSFER = "bank_transfer",
    CRYPTOCURRENCY = "cryptocurrency",
    LOYALTY_POINTS = "loyalty_points"
}
export declare enum PaymentStatus {
    PENDING = "pending",
    AUTHORIZED = "authorized",
    CAPTURED = "captured",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
    REFUNDED = "refunded",
    PARTIALLY_REFUNDED = "partially_refunded"
}
export declare class PosPayment {
    id: string;
    saleId: string;
    sale: PosSale;
    method: PaymentMethod;
    status: PaymentStatus;
    amount: number;
    refundedAmount: number;
    currency: string;
    reference: string;
    authorizationCode: string;
    transactionId: string;
    cardType: string;
    cardLastFour: string;
    cardHolderName: string;
    processedAt: Date;
    gatewayResponse: any;
    processingFee: number;
    processingFeeAmount: number;
    notes: string;
    isRefund: boolean;
    originalPaymentId: string;
    originalPayment: PosPayment;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
