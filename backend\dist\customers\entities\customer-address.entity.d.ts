import { Customer } from './customer.entity';
export declare enum AddressType {
    BILLING = "billing",
    SHIPPING = "shipping",
    MAILING = "mailing",
    BUSINESS = "business",
    HOME = "home",
    OTHER = "other"
}
export declare class CustomerAddress {
    id: string;
    customerId: string;
    customer: Customer;
    type: AddressType;
    label: string;
    addressLine1: string;
    addressLine2: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    latitude: number;
    longitude: number;
    isPrimary: boolean;
    isActive: boolean;
    deliveryInstructions: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
