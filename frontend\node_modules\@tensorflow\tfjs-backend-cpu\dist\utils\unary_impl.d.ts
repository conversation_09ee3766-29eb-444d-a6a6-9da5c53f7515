/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-backend-cpu/dist/utils/unary_impl" />
import { SimpleUnaryImpl, SimpleUnaryOperation } from './unary_types';
/**
 * Template that creates implementation for unary op.
 */
export declare function createSimpleUnaryImpl<I extends number | string = number, O extends number | string = number>(op: SimpleUnaryOperation<I, O>): SimpleUnaryImpl<I, O>;
