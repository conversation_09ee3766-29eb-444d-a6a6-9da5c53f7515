"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseController = void 0;
const common_1 = require("@nestjs/common");
const knowledge_base_service_1 = require("../services/knowledge-base.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let KnowledgeBaseController = class KnowledgeBaseController {
    knowledgeBaseService;
    constructor(knowledgeBaseService) {
        this.knowledgeBaseService = knowledgeBaseService;
    }
    async create(createArticleDto) {
        return this.knowledgeBaseService.create(createArticleDto);
    }
    async findAll() {
        return this.knowledgeBaseService.findAll();
    }
    async getPublishedArticles() {
        return this.knowledgeBaseService.getPublishedArticles();
    }
    async getDraftArticles() {
        return this.knowledgeBaseService.getDraftArticles();
    }
    async getPopularArticles(limit) {
        const limitNum = limit ? parseInt(limit) : 10;
        return this.knowledgeBaseService.getPopularArticles(limitNum);
    }
    async getRecentArticles(limit) {
        const limitNum = limit ? parseInt(limit) : 10;
        return this.knowledgeBaseService.getRecentArticles(limitNum);
    }
    async getStatistics() {
        return this.knowledgeBaseService.getArticleStatistics();
    }
    async getCategories() {
        return this.knowledgeBaseService.getCategories();
    }
    async getAllTags() {
        return this.knowledgeBaseService.getAllTags();
    }
    async searchArticles(searchTerm) {
        return this.knowledgeBaseService.searchArticles(searchTerm);
    }
    async findByCategory(category) {
        return this.knowledgeBaseService.findByCategory(category);
    }
    async getArticlesByAuthor(authorId) {
        return this.knowledgeBaseService.getArticlesByAuthor(authorId);
    }
    async findByTags(tagsParam) {
        const tags = tagsParam.split(',');
        return this.knowledgeBaseService.findByTags(tags);
    }
    async findOne(id) {
        return this.knowledgeBaseService.findOne(id);
    }
    async getRelatedArticles(id, limit) {
        const limitNum = limit ? parseInt(limit) : 5;
        return this.knowledgeBaseService.getRelatedArticles(id, limitNum);
    }
    async update(id, updateArticleDto) {
        return this.knowledgeBaseService.update(id, updateArticleDto);
    }
    async publishArticle(id) {
        return this.knowledgeBaseService.publishArticle(id);
    }
    async unpublishArticle(id) {
        return this.knowledgeBaseService.unpublishArticle(id);
    }
    async rateArticle(id, ratingData) {
        return this.knowledgeBaseService.rateArticle(id, ratingData.rating);
    }
    async remove(id) {
        return this.knowledgeBaseService.remove(id);
    }
};
exports.KnowledgeBaseController = KnowledgeBaseController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('published'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getPublishedArticles", null);
__decorate([
    (0, common_1.Get)('drafts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getDraftArticles", null);
__decorate([
    (0, common_1.Get)('popular'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getPopularArticles", null);
__decorate([
    (0, common_1.Get)('recent'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getRecentArticles", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('categories'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getCategories", null);
__decorate([
    (0, common_1.Get)('tags'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getAllTags", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "searchArticles", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "findByCategory", null);
__decorate([
    (0, common_1.Get)('author/:authorId'),
    __param(0, (0, common_1.Param)('authorId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getArticlesByAuthor", null);
__decorate([
    (0, common_1.Get)('tags/:tags'),
    __param(0, (0, common_1.Param)('tags')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "findByTags", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/related'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getRelatedArticles", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "update", null);
__decorate([
    (0, common_1.Post)(':id/publish'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "publishArticle", null);
__decorate([
    (0, common_1.Post)(':id/unpublish'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "unpublishArticle", null);
__decorate([
    (0, common_1.Post)(':id/rate'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "rateArticle", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "remove", null);
exports.KnowledgeBaseController = KnowledgeBaseController = __decorate([
    (0, common_1.Controller)('knowledge-base'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [knowledge_base_service_1.KnowledgeBaseService])
], KnowledgeBaseController);
//# sourceMappingURL=knowledge-base.controller.js.map