import { Employee } from './employee.entity';
import { PayrollItem } from './payroll-item.entity';
export declare enum PayrollStatus {
    DRAFT = "draft",
    CALCULATED = "calculated",
    APPROVED = "approved",
    PAID = "paid",
    CANCELLED = "cancelled"
}
export declare enum PayrollPeriod {
    WEEKLY = "weekly",
    BIWEEKLY = "biweekly",
    MONTHLY = "monthly",
    QUARTERLY = "quarterly",
    ANNUALLY = "annually"
}
export declare class Payroll {
    id: string;
    employeeId: string;
    employee: Employee;
    payrollNumber: string;
    payPeriod: PayrollPeriod;
    payPeriodStart: Date;
    payPeriodEnd: Date;
    payDate: Date;
    status: PayrollStatus;
    basicSalary: number;
    grossPay: number;
    totalDeductions: number;
    totalBenefits: number;
    netPay: number;
    hoursWorked: number;
    overtimeHours: number;
    overtimePay: number;
    bonuses: number;
    commissions: number;
    incomeTax: number;
    socialSecurityTax: number;
    medicareTax: number;
    otherTaxes: number;
    currency: string;
    calculatedBy: string;
    calculatedAt: Date;
    approvedBy: string;
    approvedAt: Date;
    paidBy: string;
    paidAt: Date;
    payrollItems: PayrollItem[];
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
