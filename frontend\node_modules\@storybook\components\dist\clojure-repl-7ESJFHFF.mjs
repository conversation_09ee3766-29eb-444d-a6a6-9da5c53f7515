import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_clojure_repl=__commonJS({"../../node_modules/highlight.js/lib/languages/clojure-repl.js"(exports,module){function clojureRepl(hljs){return {name:"Clojure REPL",contains:[{className:"meta",begin:/^([\w.-]+|\s*#_)?=>/,starts:{end:/$/,subLanguage:"clojure"}}]}}module.exports=clojureRepl;}});var clojureRepl7ESJFHFF = require_clojure_repl();

export { clojureRepl7ESJFHFF as default };
