import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Customer } from './customer.entity';

export enum InteractionType {
  CALL = 'call',
  EMAIL = 'email',
  MEETING = 'meeting',
  CHAT = 'chat',
  SMS = 'sms',
  SOCIAL_MEDIA = 'social_media',
  SUPPORT_TICKET = 'support_ticket',
  COMPLAINT = 'complaint',
  FEEDBACK = 'feedback',
  SURVEY = 'survey',
  VISIT = 'visit',
  OTHER = 'other',
}

export enum InteractionDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}

export enum InteractionStatus {
  SCHEDULED = 'scheduled',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
  FOLLOW_UP_REQUIRED = 'follow_up_required',
}

@Entity('customer_interactions')
export class CustomerInteraction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer.interactions)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({
    type: 'enum',
    enum: InteractionType,
  })
  type: InteractionType;

  @Column({
    type: 'enum',
    enum: InteractionDirection,
  })
  direction: InteractionDirection;

  @Column({
    type: 'enum',
    enum: InteractionStatus,
    default: InteractionStatus.COMPLETED,
  })
  status: InteractionStatus;

  @Column({ length: 255 })
  subject: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'timestamp' })
  interactionDate: Date;

  @Column({ type: 'int', nullable: true })
  duration: number; // in minutes

  @Column()
  performedBy: string;

  @Column({ length: 255, nullable: true })
  channel: string; // phone, email, chat platform, etc.

  @Column({ type: 'json', nullable: true })
  participants: string[];

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'text', nullable: true })
  outcome: string;

  @Column({ type: 'text', nullable: true })
  nextSteps: string;

  @Column({ type: 'date', nullable: true })
  followUpDate: Date;

  @Column({ nullable: true })
  followUpBy: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'decimal', precision: 2, scale: 1, nullable: true })
  rating: number; // 1-5 stars

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
