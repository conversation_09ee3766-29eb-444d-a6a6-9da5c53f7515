import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, Button, Progress, Select, Statistic, Row, Col, List, Tag, Space, Typography } from 'antd'
import {
  securityEventLogger,
  loginAttemptTracker,
  sessionManager,
  passwordSecurity,
  type SecurityEvent
} from '../../lib/security-utils'
import {
  ShieldOutlined,
  WarningOutlined,
  LockOutlined,
  EyeOutlined,
  TeamOutlined,
  ActivityOutlined,
  KeyOutlined,
  GlobalOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select

interface SecurityMetrics {
  totalUsers: number
  activeUsers: number
  failedLogins: number
  securityEvents: number
  mfaEnabled: number
  passwordStrength: {
    strong: number
    medium: number
    weak: number
  }
  recentEvents: SecurityEvent[]
  threatLevel: 'low' | 'medium' | 'high' | 'critical'
}

export function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h')

  useEffect(() => {
    loadSecurityMetrics()
  }, [selectedTimeRange])

  const loadSecurityMetrics = async () => {
    setLoading(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800))

    // Get real security events from local storage
    const events = securityEventLogger.getEvents()
    const attempts = loginAttemptTracker.getAttempts()

    // Mock data combined with real data
    const mockMetrics: SecurityMetrics = {
      totalUsers: 156,
      activeUsers: 89,
      failedLogins: attempts.filter(a => !a.success).length,
      securityEvents: events.length,
      mfaEnabled: 134,
      passwordStrength: {
        strong: 98,
        medium: 45,
        weak: 13
      },
      recentEvents: events.slice(-10),
      threatLevel: events.some(e => e.type === 'suspicious_activity') ? 'high' : 'low'
    }

    setMetrics(mockMetrics)
    setLoading(false)
  }

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      default: return 'text-green-600 bg-green-50 border-green-200'
    }
  }

  const getEventIcon = (type: SecurityEvent['type']) => {
    switch (type) {
      case 'login': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'logout': return <CloseCircleOutlined style={{ color: '#8c8c8c' }} />
      case 'failed_login': return <WarningOutlined style={{ color: '#f5222d' }} />
      case 'password_change': return <KeyOutlined style={{ color: '#1890ff' }} />
      case 'mfa_enabled': return <ShieldOutlined style={{ color: '#52c41a' }} />
      case 'suspicious_activity': return <WarningOutlined style={{ color: '#fa8c16' }} />
      default: return <ActivityOutlined style={{ color: '#8c8c8c' }} />
    }
  }

  const formatEventTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  if (loading || !metrics) {
    return (
      <div style={{ padding: 24 }}>
        <Row gutter={[24, 24]}>
          {[...Array(4)].map((_, i) => (
            <Col xs={24} sm={12} lg={6} key={i}>
              <Card loading={true}>
                <Statistic title="Loading..." value={0} />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2}>Security Dashboard</Title>
          <Text type="secondary">Monitor and manage system security</Text>
        </Col>

        <Col>
          <Space>
            <Select
              value={selectedTimeRange}
              onChange={setSelectedTimeRange}
              style={{ width: 150 }}
            >
              <Option value="1h">Last Hour</Option>
              <Option value="24h">Last 24 Hours</Option>
              <Option value="7d">Last 7 Days</Option>
              <Option value="30d">Last 30 Days</Option>
            </Select>

            <Button onClick={loadSecurityMetrics} icon={<ReloadOutlined />}>
              Refresh
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Threat Level Alert */}
      <Card style={{ marginBottom: 24, borderColor: metrics.threatLevel === 'high' ? '#fa8c16' : '#52c41a' }}>
        <Row align="middle" gutter={16}>
          <Col>
            <ShieldOutlined style={{ fontSize: 24, color: metrics.threatLevel === 'high' ? '#fa8c16' : '#52c41a' }} />
          </Col>
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              Current Threat Level: {metrics.threatLevel.toUpperCase()}
            </Title>
            <Text type="secondary">
              {metrics.threatLevel === 'low'
                ? 'All systems operating normally'
                : metrics.threatLevel === 'medium'
                ? 'Some security events detected, monitoring closely'
                : 'Multiple security events detected, immediate attention required'
              }
            </Text>
          </Col>
        </Row>
      </Card>

      {/* Security Metrics */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={metrics.totalUsers}
              prefix={<TeamOutlined />}
              suffix={<Text type="secondary" style={{ fontSize: 12 }}>{metrics.activeUsers} active</Text>}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Failed Logins"
              value={metrics.failedLogins}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="MFA Enabled"
              value={Math.round((metrics.mfaEnabled / metrics.totalUsers) * 100)}
              suffix="%"
              prefix={<LockOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Security Events"
              value={metrics.securityEvents}
              prefix={<ActivityOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Password Strength Distribution */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <KeyOutlined style={{ color: '#1890ff' }} />
                Password Strength Distribution
              </Space>
            }
          >
            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between" align="middle">
                <Text strong style={{ color: '#52c41a' }}>Strong</Text>
                <Text type="secondary">{metrics.passwordStrength.strong} users</Text>
              </Row>
              <Progress
                percent={(metrics.passwordStrength.strong / metrics.totalUsers) * 100}
                strokeColor="#52c41a"
                showInfo={false}
              />
            </div>

            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between" align="middle">
                <Text strong style={{ color: '#fa8c16' }}>Medium</Text>
                <Text type="secondary">{metrics.passwordStrength.medium} users</Text>
              </Row>
              <Progress
                percent={(metrics.passwordStrength.medium / metrics.totalUsers) * 100}
                strokeColor="#fa8c16"
                showInfo={false}
              />
            </div>

            <div>
              <Row justify="space-between" align="middle">
                <Text strong style={{ color: '#f5222d' }}>Weak</Text>
                <Text type="secondary">{metrics.passwordStrength.weak} users</Text>
              </Row>
              <Progress
                percent={(metrics.passwordStrength.weak / metrics.totalUsers) * 100}
                strokeColor="#f5222d"
                showInfo={false}
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <EyeOutlined style={{ color: '#722ed1' }} />
                Recent Security Events
              </Space>
            }
          >
            <div style={{ maxHeight: 320, overflowY: 'auto' }}>
              {metrics.recentEvents.length === 0 ? (
                <Text type="secondary" style={{ textAlign: 'center', display: 'block', padding: 16 }}>
                  No recent security events
                </Text>
              ) : (
                <List
                  dataSource={metrics.recentEvents}
                  renderItem={(event, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <List.Item style={{ padding: '8px 0' }}>
                        <List.Item.Meta
                          avatar={getEventIcon(event.type)}
                          title={
                            <Text strong style={{ textTransform: 'capitalize' }}>
                              {event.type.replace('_', ' ')}
                            </Text>
                          }
                          description={
                            <div>
                              <Text type="secondary">{event.email || event.userId || 'System'}</Text>
                              <br />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {formatEventTime(event.timestamp)}
                              </Text>
                            </div>
                          }
                        />
                      </List.Item>
                    </motion.div>
                  )}
                />
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Security Actions */}
      <Card title="Security Actions">
        <Row gutter={[16, 16]}>
          <Col xs={12} md={6}>
            <Button
              type="default"
              size="large"
              style={{ height: 'auto', padding: 16, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 8, width: '100%' }}
            >
              <ShieldOutlined style={{ fontSize: 24 }} />
              <span style={{ fontSize: 12 }}>Security Audit</span>
            </Button>
          </Col>
          <Col xs={12} md={6}>
            <Button
              type="default"
              size="large"
              style={{ height: 'auto', padding: 16, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 8, width: '100%' }}
            >
              <KeyOutlined style={{ fontSize: 24 }} />
              <span style={{ fontSize: 12 }}>Force Password Reset</span>
            </Button>
          </Col>
          <Col xs={12} md={6}>
            <Button
              type="default"
              size="large"
              style={{ height: 'auto', padding: 16, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 8, width: '100%' }}
            >
              <LockOutlined style={{ fontSize: 24 }} />
              <span style={{ fontSize: 12 }}>Enable MFA</span>
            </Button>
          </Col>
          <Col xs={12} md={6}>
            <Button
              type="default"
              size="large"
              style={{ height: 'auto', padding: 16, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 8, width: '100%' }}
            >
              <GlobalOutlined style={{ fontSize: 24 }} />
              <span style={{ fontSize: 12 }}>IP Whitelist</span>
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  )
}
