"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pos_terminal_entity_1 = require("./entities/pos-terminal.entity");
const pos_sale_entity_1 = require("./entities/pos-sale.entity");
const pos_sale_item_entity_1 = require("./entities/pos-sale-item.entity");
const pos_payment_entity_1 = require("./entities/pos-payment.entity");
const pos_discount_entity_1 = require("./entities/pos-discount.entity");
const pos_tax_entity_1 = require("./entities/pos-tax.entity");
const pos_shift_entity_1 = require("./entities/pos-shift.entity");
const pos_cash_drawer_entity_1 = require("./entities/pos-cash-drawer.entity");
const pos_receipt_entity_1 = require("./entities/pos-receipt.entity");
const pos_return_entity_1 = require("./entities/pos-return.entity");
const pos_promotion_entity_1 = require("./entities/pos-promotion.entity");
const pos_customer_entity_1 = require("./entities/pos-customer.entity");
const pos_terminal_service_1 = require("./services/pos-terminal.service");
const pos_sale_service_1 = require("./services/pos-sale.service");
const pos_payment_service_1 = require("./services/pos-payment.service");
const pos_shift_service_1 = require("./services/pos-shift.service");
const pos_cash_drawer_service_1 = require("./services/pos-cash-drawer.service");
const pos_receipt_service_1 = require("./services/pos-receipt.service");
const pos_return_service_1 = require("./services/pos-return.service");
const pos_promotion_service_1 = require("./services/pos-promotion.service");
const pos_report_service_1 = require("./services/pos-report.service");
const pos_terminal_controller_1 = require("./controllers/pos-terminal.controller");
const pos_sale_controller_1 = require("./controllers/pos-sale.controller");
const pos_payment_controller_1 = require("./controllers/pos-payment.controller");
const pos_shift_controller_1 = require("./controllers/pos-shift.controller");
const pos_cash_drawer_controller_1 = require("./controllers/pos-cash-drawer.controller");
const pos_receipt_controller_1 = require("./controllers/pos-receipt.controller");
const pos_return_controller_1 = require("./controllers/pos-return.controller");
const pos_promotion_controller_1 = require("./controllers/pos-promotion.controller");
const pos_report_controller_1 = require("./controllers/pos-report.controller");
let PosModule = class PosModule {
};
exports.PosModule = PosModule;
exports.PosModule = PosModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                pos_terminal_entity_1.PosTerminal,
                pos_sale_entity_1.PosSale,
                pos_sale_item_entity_1.PosSaleItem,
                pos_payment_entity_1.PosPayment,
                pos_discount_entity_1.PosDiscount,
                pos_tax_entity_1.PosTax,
                pos_shift_entity_1.PosShift,
                pos_cash_drawer_entity_1.PosCashDrawer,
                pos_receipt_entity_1.PosReceipt,
                pos_return_entity_1.PosReturn,
                pos_promotion_entity_1.PosPromotion,
                pos_customer_entity_1.PosCustomer,
            ]),
        ],
        controllers: [
            pos_terminal_controller_1.PosTerminalController,
            pos_sale_controller_1.PosSaleController,
            pos_payment_controller_1.PosPaymentController,
            pos_shift_controller_1.PosShiftController,
            pos_cash_drawer_controller_1.PosCashDrawerController,
            pos_receipt_controller_1.PosReceiptController,
            pos_return_controller_1.PosReturnController,
            pos_promotion_controller_1.PosPromotionController,
            pos_report_controller_1.PosReportController,
        ],
        providers: [
            pos_terminal_service_1.PosTerminalService,
            pos_sale_service_1.PosSaleService,
            pos_payment_service_1.PosPaymentService,
            pos_shift_service_1.PosShiftService,
            pos_cash_drawer_service_1.PosCashDrawerService,
            pos_receipt_service_1.PosReceiptService,
            pos_return_service_1.PosReturnService,
            pos_promotion_service_1.PosPromotionService,
            pos_report_service_1.PosReportService,
        ],
        exports: [
            pos_terminal_service_1.PosTerminalService,
            pos_sale_service_1.PosSaleService,
            pos_payment_service_1.PosPaymentService,
            pos_shift_service_1.PosShiftService,
            pos_cash_drawer_service_1.PosCashDrawerService,
            pos_receipt_service_1.PosReceiptService,
            pos_return_service_1.PosReturnService,
            pos_promotion_service_1.PosPromotionService,
            pos_report_service_1.PosReportService,
        ],
    })
], PosModule);
//# sourceMappingURL=pos.module.js.map