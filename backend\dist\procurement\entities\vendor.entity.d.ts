import { VendorContact } from './vendor-contact.entity';
import { VendorEvaluation } from './vendor-evaluation.entity';
import { Contract } from './contract.entity';
export declare enum VendorStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    BLACKLISTED = "blacklisted",
    PENDING_APPROVAL = "pending_approval"
}
export declare enum VendorType {
    SUPPLIER = "supplier",
    SERVICE_PROVIDER = "service_provider",
    CONTRACTOR = "contractor",
    CONSULTANT = "consultant",
    DISTRIBUTOR = "distributor",
    MANUFACTURER = "manufacturer"
}
export declare class Vendor {
    id: string;
    vendorNumber: string;
    name: string;
    legalName: string;
    type: VendorType;
    status: VendorStatus;
    taxId: string;
    registrationNumber: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone: string;
    fax: string;
    email: string;
    website: string;
    categories: string[];
    certifications: string[];
    paymentTerms: number;
    currency: string;
    creditLimit: number;
    currentBalance: number;
    leadTimeDays: number;
    minimumOrderAmount: number;
    discountPercentage: number;
    rating: number;
    bankDetails: any;
    insuranceDetails: any;
    qualityMetrics: any;
    notes: string;
    contacts: VendorContact[];
    evaluations: VendorEvaluation[];
    contracts: Contract[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
