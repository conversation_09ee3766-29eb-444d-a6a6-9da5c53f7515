import { BenefitService } from '../services/benefit.service';
export declare class BenefitController {
    private readonly benefitService;
    constructor(benefitService: BenefitService);
    create(createBenefitDto: any): Promise<import("../entities/benefit.entity").Benefit>;
    findAll(): Promise<import("../entities/benefit.entity").Benefit[]>;
    findOne(id: string): Promise<import("../entities/benefit.entity").Benefit>;
    enrollEmployee(enrollDto: {
        employeeId: string;
        benefitId: string;
        enrollmentData: any;
    }): Promise<import("../entities/employee-benefit.entity").EmployeeBenefit>;
    getEmployeeBenefits(employeeId: string): Promise<import("../entities/employee-benefit.entity").EmployeeBenefit[]>;
}
