import { CustomerLoyaltyService } from '../services/customer-loyalty.service';
export declare class CustomerLoyaltyController {
    private readonly customerLoyaltyService;
    constructor(customerLoyaltyService: CustomerLoyaltyService);
    findAll(): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty[]>;
    getLoyaltyStatistics(): Promise<any>;
    getTopLoyaltyCustomers(): Promise<import("../entities/customer.entity").Customer[]>;
    findByCustomer(customerId: string): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty[]>;
    getCustomerLoyaltySummary(customerId: string): Promise<any>;
    findOne(id: string): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty>;
    addPoints(pointsData: {
        customerId: string;
        points: number;
        reason: string;
        orderId?: string;
    }): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty>;
    redeemPoints(redeemData: {
        customerId: string;
        points: number;
        reason: string;
        orderId?: string;
    }): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty>;
    adjustPoints(adjustData: {
        customerId: string;
        adjustment: number;
        reason: string;
    }): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty>;
    expirePoints(expireData: {
        customerId: string;
        points: number;
    }): Promise<import("../entities/customer-loyalty.entity").CustomerLoyalty>;
}
