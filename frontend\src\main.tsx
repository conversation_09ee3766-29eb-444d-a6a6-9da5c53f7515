import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import './i18n'

// Suppress Ant Design React 19 compatibility warning
const originalWarn = console.warn
console.warn = (...args) => {
  if (args[0]?.includes?.('antd: compatible') || args[0]?.includes?.('antd v5 support React is 16 ~ 18')) {
    return
  }
  originalWarn.apply(console, args)
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
