export declare enum ApiKeyStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    EXPIRED = "expired",
    REVOKED = "revoked"
}
export declare class APIKey {
    id: string;
    name: string;
    description: string;
    keyHash: string;
    keyPrefix: string;
    status: ApiKeyStatus;
    userId: string;
    permissions: string[];
    ipWhitelist: string[];
    rateLimit: number;
    expiresAt: Date;
    lastUsedAt: Date;
    usageCount: number;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
