import { TaxService } from '../services/tax.service';
export declare class TaxController {
    private readonly taxService;
    constructor(taxService: TaxService);
    create(createTaxRecordDto: any): Promise<import("../entities/tax-record.entity").TaxRecord>;
    findAll(taxType?: string, status?: string, startDate?: string, endDate?: string): Promise<import("../entities/tax-record.entity").TaxRecord[]>;
    getTaxSummary(year: string): Promise<any>;
    findOne(id: string): Promise<import("../entities/tax-record.entity").TaxRecord>;
    update(id: string, updateTaxRecordDto: any): Promise<import("../entities/tax-record.entity").TaxRecord>;
    remove(id: string): Promise<void>;
    fileReturn(id: string, fileDto: {
        filedBy: string;
        referenceNumber?: string;
    }): Promise<import("../entities/tax-record.entity").TaxRecord>;
    recordPayment(id: string, paymentDto: {
        amount: number;
    }): Promise<import("../entities/tax-record.entity").TaxRecord>;
    calculatePenaltiesAndInterest(id: string): Promise<import("../entities/tax-record.entity").TaxRecord>;
}
