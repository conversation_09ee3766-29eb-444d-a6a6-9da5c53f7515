import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FinancialReport, ReportType, ReportStatus } from '../entities/financial-report.entity';
import { AccountService } from './account.service';
import { TransactionService } from './transaction.service';

@Injectable()
export class FinancialReportService {
  constructor(
    @InjectRepository(FinancialReport)
    private reportRepository: Repository<FinancialReport>,
    private accountService: AccountService,
    private transactionService: TransactionService,
  ) {}

  async create(createReportDto: any): Promise<FinancialReport> {
    const report = this.reportRepository.create({
      ...createReportDto,
      status: ReportStatus.GENERATING,
    });

    const savedReport = await this.reportRepository.save(report);

    // Generate report data asynchronously
    this.generateReportData(savedReport.id);

    return savedReport;
  }

  async findAll(filters?: any): Promise<FinancialReport[]> {
    const queryBuilder = this.reportRepository.createQueryBuilder('report');

    if (filters?.type) {
      queryBuilder.andWhere('report.type = :type', { type: filters.type });
    }

    if (filters?.status) {
      queryBuilder.andWhere('report.status = :status', { status: filters.status });
    }

    return queryBuilder
      .orderBy('report.createdAt', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<FinancialReport> {
    const report = await this.reportRepository.findOne({ where: { id } });

    if (!report) {
      throw new NotFoundException(`Financial report with ID ${id} not found`);
    }

    return report;
  }

  async generateBalanceSheet(startDate: Date, endDate: Date): Promise<any> {
    const accounts = await this.accountService.findAll();
    
    const assets = accounts.filter(acc => acc.type === 'asset');
    const liabilities = accounts.filter(acc => acc.type === 'liability');
    const equity = accounts.filter(acc => acc.type === 'equity');

    const balanceSheet = {
      asOfDate: endDate,
      assets: {
        currentAssets: assets.filter(acc => acc.subType === 'current_asset'),
        fixedAssets: assets.filter(acc => acc.subType === 'fixed_asset'),
        totalAssets: assets.reduce((sum, acc) => sum + acc.balance, 0),
      },
      liabilities: {
        currentLiabilities: liabilities.filter(acc => acc.subType === 'current_liability'),
        longTermLiabilities: liabilities.filter(acc => acc.subType === 'long_term_liability'),
        totalLiabilities: liabilities.reduce((sum, acc) => sum + acc.balance, 0),
      },
      equity: {
        accounts: equity,
        totalEquity: equity.reduce((sum, acc) => sum + acc.balance, 0),
      },
    };

    return balanceSheet;
  }

  async generateIncomeStatement(startDate: Date, endDate: Date): Promise<any> {
    const accounts = await this.accountService.findAll();
    
    const revenue = accounts.filter(acc => acc.type === 'revenue');
    const expenses = accounts.filter(acc => acc.type === 'expense');

    const totalRevenue = revenue.reduce((sum, acc) => sum + acc.balance, 0);
    const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);

    const incomeStatement = {
      period: { startDate, endDate },
      revenue: {
        accounts: revenue,
        total: totalRevenue,
      },
      expenses: {
        operatingExpenses: expenses.filter(acc => acc.subType === 'operating_expense'),
        costOfGoodsSold: expenses.filter(acc => acc.subType === 'cost_of_goods_sold'),
        administrativeExpenses: expenses.filter(acc => acc.subType === 'administrative_expense'),
        total: totalExpenses,
      },
      netIncome: totalRevenue - totalExpenses,
      grossProfit: totalRevenue - expenses.filter(acc => acc.subType === 'cost_of_goods_sold')
        .reduce((sum, acc) => sum + acc.balance, 0),
    };

    return incomeStatement;
  }

  async generateCashFlowStatement(startDate: Date, endDate: Date): Promise<any> {
    // This is a simplified cash flow statement
    // In a real implementation, you'd need to categorize transactions
    const transactions = await this.transactionService.findAll({
      startDate,
      endDate,
      status: 'posted',
    });

    const operatingActivities = transactions.filter(t => 
      ['payment', 'receipt'].includes(t.type)
    );
    
    const investingActivities = transactions.filter(t => 
      t.type === 'transfer' && t.description.toLowerCase().includes('investment')
    );
    
    const financingActivities = transactions.filter(t => 
      t.type === 'transfer' && t.description.toLowerCase().includes('loan')
    );

    const cashFlow = {
      period: { startDate, endDate },
      operatingActivities: {
        transactions: operatingActivities,
        netCash: operatingActivities.reduce((sum, t) => sum + t.amount, 0),
      },
      investingActivities: {
        transactions: investingActivities,
        netCash: investingActivities.reduce((sum, t) => sum + t.amount, 0),
      },
      financingActivities: {
        transactions: financingActivities,
        netCash: financingActivities.reduce((sum, t) => sum + t.amount, 0),
      },
    };

    return cashFlow;
  }

  async scheduleReport(reportConfig: any): Promise<FinancialReport> {
    const report = this.reportRepository.create({
      ...reportConfig,
      isScheduled: true,
      status: ReportStatus.SCHEDULED,
    });

    return this.reportRepository.save(report);
  }

  private async generateReportData(reportId: string): Promise<void> {
    try {
      const report = await this.findOne(reportId);
      let reportData: any;

      switch (report.type) {
        case ReportType.BALANCE_SHEET:
          reportData = await this.generateBalanceSheet(report.startDate, report.endDate);
          break;
        case ReportType.INCOME_STATEMENT:
          reportData = await this.generateIncomeStatement(report.startDate, report.endDate);
          break;
        case ReportType.CASH_FLOW:
          reportData = await this.generateCashFlowStatement(report.startDate, report.endDate);
          break;
        case ReportType.TRIAL_BALANCE:
          reportData = await this.accountService.getTrialBalance(report.endDate);
          break;
        default:
          throw new Error(`Unsupported report type: ${report.type}`);
      }

      report.reportData = JSON.stringify(reportData);
      report.status = ReportStatus.COMPLETED;
      report.generatedAt = new Date();

      await this.reportRepository.save(report);
    } catch (error) {
      const report = await this.findOne(reportId);
      report.status = ReportStatus.FAILED;
      report.errorMessage = error.message;
      await this.reportRepository.save(report);
    }
  }
}
