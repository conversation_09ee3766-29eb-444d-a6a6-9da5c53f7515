"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("../entities/customer.entity");
let CustomerService = class CustomerService {
    customerRepository;
    constructor(customerRepository) {
        this.customerRepository = customerRepository;
    }
    async create(createCustomerDto, tenantId) {
        const customerNumber = await this.generateCustomerNumber();
        const customer = this.customerRepository.create({
            ...createCustomerDto,
            customerNumber,
            tenantId,
        });
        return this.customerRepository.save(customer);
    }
    async findAll(tenantId) {
        return this.customerRepository.find({
            where: { tenantId },
            relations: ['invoices', 'quotations', 'payments'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id, tenantId) {
        const customer = await this.customerRepository.findOne({
            where: { id, tenantId },
            relations: ['invoices', 'quotations', 'payments'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        return customer;
    }
    async update(id, updateCustomerDto, tenantId) {
        const customer = await this.findOne(id, tenantId);
        Object.assign(customer, updateCustomerDto);
        return this.customerRepository.save(customer);
    }
    async remove(id, tenantId) {
        const customer = await this.findOne(id, tenantId);
        await this.customerRepository.remove(customer);
    }
    async updateBalance(customerId, amount, tenantId) {
        const customer = await this.findOne(customerId, tenantId);
        customer.currentBalance += amount;
        await this.customerRepository.save(customer);
    }
    async getCustomerStats(tenantId) {
        const totalCustomers = await this.customerRepository.count({ where: { tenantId } });
        const activeCustomers = await this.customerRepository.count({
            where: { tenantId, status: 'active' }
        });
        const result = await this.customerRepository
            .createQueryBuilder('customer')
            .select('SUM(customer.currentBalance)', 'totalBalance')
            .where('customer.tenantId = :tenantId', { tenantId })
            .getRawOne();
        return {
            totalCustomers,
            activeCustomers,
            totalBalance: parseFloat(result.totalBalance) || 0,
        };
    }
    async generateCustomerNumber() {
        const count = await this.customerRepository.count();
        return `CUST-${(count + 1).toString().padStart(6, '0')}`;
    }
};
exports.CustomerService = CustomerService;
exports.CustomerService = CustomerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CustomerService);
//# sourceMappingURL=customer.service.js.map