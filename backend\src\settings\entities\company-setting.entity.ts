import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum CompanySettingType {
  GENERAL = 'general',
  BRANDING = 'branding',
  BUSINESS = 'business',
  FINANCIAL = 'financial',
  LOCALIZATION = 'localization',
  INTEGRATION = 'integration',
  WORKFLOW = 'workflow',
  SECURITY = 'security',
}

@Entity('company_settings')
export class CompanySetting {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  tenantId: string;

  @Column({ length: 255 })
  key: string;

  @Column({ type: 'text' })
  value: string;

  @Column({
    type: 'enum',
    enum: CompanySettingType,
    default: CompanySettingType.GENERAL,
  })
  type: CompanySettingType;

  @Column({ length: 255, nullable: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: false })
  isEncrypted: boolean;

  @Column({ nullable: true })
  lastModifiedBy: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
