import { TrainingService } from '../services/training.service';
export declare class TrainingController {
    private readonly trainingService;
    constructor(trainingService: TrainingService);
    create(createTrainingDto: any): Promise<import("../entities/training.entity").Training>;
    findAll(employeeId?: string, type?: string, status?: string): Promise<import("../entities/training.entity").Training[]>;
    findOne(id: string): Promise<import("../entities/training.entity").Training>;
    update(id: string, updateTrainingDto: any): Promise<import("../entities/training.entity").Training>;
    completeTraining(id: string, completeDto: {
        score?: number;
        feedback?: string;
    }): Promise<import("../entities/training.entity").Training>;
}
