import { BudgetItem } from './budget-item.entity';
export declare enum BudgetType {
    ANNUAL = "annual",
    QUARTERLY = "quarterly",
    MONTHLY = "monthly",
    PROJECT = "project",
    DEPARTMENT = "department"
}
export declare enum BudgetStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    APPROVED = "approved",
    CLOSED = "closed",
    CANCELLED = "cancelled"
}
export declare class Budget {
    id: string;
    name: string;
    description: string;
    type: BudgetType;
    status: BudgetStatus;
    startDate: Date;
    endDate: Date;
    totalBudgetAmount: number;
    totalActualAmount: number;
    totalVariance: number;
    variancePercentage: number;
    currency: string;
    departmentId: string;
    projectId: string;
    createdBy: string;
    approvedBy: string;
    approvedAt: Date;
    budgetItems: BudgetItem[];
    metadata: any;
    notes: string;
    createdAt: Date;
    updatedAt: Date;
}
