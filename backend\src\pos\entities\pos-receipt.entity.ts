import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PosSale } from './pos-sale.entity';

export enum ReceiptType {
  SALE = 'sale',
  RETURN = 'return',
  EXCHANGE = 'exchange',
  VOID = 'void',
  REPRINT = 'reprint',
  GIFT_RECEIPT = 'gift_receipt',
}

export enum ReceiptFormat {
  THERMAL = 'thermal',
  LASER = 'laser',
  EMAIL = 'email',
  SMS = 'sms',
  DIGITAL = 'digital',
}

@Entity('pos_receipts')
export class PosReceipt {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  saleId: string;

  @ManyToOne(() => PosSale)
  @JoinColumn({ name: 'saleId' })
  sale: PosSale;

  @Column({ length: 50, unique: true })
  receiptNumber: string;

  @Column({
    type: 'enum',
    enum: ReceiptType,
    default: ReceiptType.SALE,
  })
  type: ReceiptType;

  @Column({
    type: 'enum',
    enum: ReceiptFormat,
    default: ReceiptFormat.THERMAL,
  })
  format: ReceiptFormat;

  @Column({ type: 'longtext' })
  content: string;

  @Column({ type: 'json', nullable: true })
  template: any;

  @Column({ default: false })
  isPrinted: boolean;

  @Column({ type: 'timestamp', nullable: true })
  printedAt: Date;

  @Column({ default: false })
  isEmailed: boolean;

  @Column({ type: 'timestamp', nullable: true })
  emailedAt: Date;

  @Column({ length: 200, nullable: true })
  emailAddress: string;

  @Column({ default: false })
  isSms: boolean;

  @Column({ type: 'timestamp', nullable: true })
  smsAt: Date;

  @Column({ length: 20, nullable: true })
  phoneNumber: string;

  @Column({ type: 'json', nullable: true })
  customFields: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
