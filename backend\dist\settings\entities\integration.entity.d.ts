export declare enum IntegrationType {
    API = "api",
    WEBHOOK = "webhook",
    DATABASE = "database",
    FILE = "file",
    EMAIL = "email",
    SMS = "sms",
    PAYMENT = "payment",
    SHIPPING = "shipping",
    ACCOUNTING = "accounting",
    CRM = "crm",
    MARKETING = "marketing",
    SOCIAL_MEDIA = "social_media",
    CUSTOM = "custom"
}
export declare enum IntegrationStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    ERROR = "error",
    TESTING = "testing",
    PENDING = "pending"
}
export declare class Integration {
    id: string;
    name: string;
    description: string;
    type: IntegrationType;
    status: IntegrationStatus;
    provider: string;
    configuration: any;
    credentials: any;
    mapping: any;
    settings: any;
    lastSyncAt: Date;
    lastError: string;
    syncCount: number;
    errorCount: number;
    createdBy: string;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
