import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Employee } from './employee.entity';

export enum TrainingType {
  ORIENTATION = 'orientation',
  SKILL_DEVELOPMENT = 'skill_development',
  COMPLIANCE = 'compliance',
  LEADERSHIP = 'leadership',
  TECHNICAL = 'technical',
  SAFETY = 'safety',
  CERTIFICATION = 'certification',
  WORKSHOP = 'workshop',
  SEMINAR = 'seminar',
  CONFERENCE = 'conference',
}

export enum TrainingStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

export enum TrainingDeliveryMethod {
  IN_PERSON = 'in_person',
  ONLINE = 'online',
  HYBRID = 'hybrid',
  SELF_PACED = 'self_paced',
}

@Entity('hr_training')
export class Training {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  employeeId: string;

  @ManyToOne(() => Employee, employee => employee.trainings)
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column({ length: 200 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TrainingType,
  })
  type: TrainingType;

  @Column({
    type: 'enum',
    enum: TrainingStatus,
    default: TrainingStatus.SCHEDULED,
  })
  status: TrainingStatus;

  @Column({
    type: 'enum',
    enum: TrainingDeliveryMethod,
    default: TrainingDeliveryMethod.IN_PERSON,
  })
  deliveryMethod: TrainingDeliveryMethod;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'time', nullable: true })
  startTime: string;

  @Column({ type: 'time', nullable: true })
  endTime: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  duration: number; // in hours

  @Column({ length: 200, nullable: true })
  location: string;

  @Column({ length: 200, nullable: true })
  instructor: string;

  @Column({ length: 200, nullable: true })
  provider: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  cost: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ default: false })
  isMandatory: boolean;

  @Column({ default: false })
  hasCertification: boolean;

  @Column({ length: 200, nullable: true })
  certificationName: string;

  @Column({ type: 'date', nullable: true })
  certificationExpiryDate: Date;

  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })
  score: number;

  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })
  passingScore: number;

  @Column({ default: false })
  isPassed: boolean;

  @Column({ type: 'date', nullable: true })
  completionDate: Date;

  @Column({ type: 'text', nullable: true })
  feedback: string;

  @Column({ type: 'decimal', precision: 2, scale: 1, nullable: true })
  rating: number; // 1-5 stars

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  prerequisites: string[];

  @Column({ type: 'json', nullable: true })
  learningObjectives: string[];

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
