"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerCreditService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_credit_entity_1 = require("../entities/customer-credit.entity");
const customer_entity_1 = require("../entities/customer.entity");
let CustomerCreditService = class CustomerCreditService {
    creditRepository;
    customerRepository;
    constructor(creditRepository, customerRepository) {
        this.creditRepository = creditRepository;
        this.customerRepository = customerRepository;
    }
    async create(creditData) {
        const credit = this.creditRepository.create(creditData);
        return this.creditRepository.save(credit);
    }
    async findAll() {
        return this.creditRepository.find({
            relations: ['customer'],
            order: { assessmentDate: 'DESC' },
        });
    }
    async findOne(id) {
        const credit = await this.creditRepository.findOne({
            where: { id },
            relations: ['customer'],
        });
        if (!credit) {
            throw new common_1.NotFoundException(`Credit record with ID ${id} not found`);
        }
        return credit;
    }
    async findByCustomer(customerId) {
        return this.creditRepository.find({
            where: { customerId },
            order: { assessmentDate: 'DESC' },
        });
    }
    async getLatestCreditAssessment(customerId) {
        return this.creditRepository.findOne({
            where: { customerId },
            order: { assessmentDate: 'DESC' },
        });
    }
    async createCreditAssessment(customerId, assessmentData) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        let creditScore = assessmentData.creditScore;
        if (!creditScore) {
            creditScore = await this.calculateCreditScore(customerId);
        }
        let riskLevel = assessmentData.riskLevel;
        if (!riskLevel) {
            riskLevel = this.determineRiskLevel(creditScore);
        }
        let creditLimit = assessmentData.creditLimit;
        if (!creditLimit) {
            creditLimit = await this.suggestCreditLimit(customerId, creditScore);
        }
        const creditAssessment = await this.create({
            customerId,
            creditScore,
            creditLimit,
            paymentTerms: assessmentData.paymentTerms || 30,
            riskLevel,
            notes: assessmentData.notes,
            assessedBy: assessmentData.assessedBy,
            assessmentDate: new Date(),
        });
        await this.customerRepository.update(customerId, {
            creditLimit,
            paymentTerms: assessmentData.paymentTerms || 30,
        });
        return creditAssessment;
    }
    async updateCreditLimit(customerId, newLimit, reason, updatedBy) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        const latestAssessment = await this.getLatestCreditAssessment(customerId);
        const newAssessment = await this.create({
            customerId,
            creditScore: latestAssessment?.creditScore || 0,
            creditLimit: newLimit,
            paymentTerms: latestAssessment?.paymentTerms || 30,
            riskLevel: latestAssessment?.riskLevel || 'medium',
            notes: `Credit limit updated: ${reason}`,
            assessedBy: updatedBy,
            assessmentDate: new Date(),
        });
        await this.customerRepository.update(customerId, {
            creditLimit: newLimit,
        });
        return newAssessment;
    }
    async calculateCreditScore(customerId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            return 0;
        }
        let score = 500;
        const paymentScore = this.calculatePaymentScore(customer);
        score += paymentScore * 0.4;
        const utilizationScore = this.calculateUtilizationScore(customer);
        score += utilizationScore * 0.3;
        const relationshipScore = this.calculateRelationshipScore(customer);
        score += relationshipScore * 0.15;
        const purchaseScore = this.calculatePurchaseScore(customer);
        score += purchaseScore * 0.15;
        return Math.min(Math.max(Math.round(score), 300), 850);
    }
    calculatePaymentScore(customer) {
        if (!customer.totalSpent || customer.totalSpent === 0)
            return 0;
        const paymentRatio = 1 - ((customer.currentBalance || 0) / customer.totalSpent);
        return Math.max(0, paymentRatio * 200);
    }
    calculateUtilizationScore(customer) {
        if (!customer.creditLimit || customer.creditLimit === 0)
            return 100;
        const utilization = (customer.currentBalance || 0) / customer.creditLimit;
        if (utilization <= 0.1)
            return 200;
        if (utilization <= 0.3)
            return 150;
        if (utilization <= 0.5)
            return 100;
        if (utilization <= 0.7)
            return 50;
        return 0;
    }
    calculateRelationshipScore(customer) {
        const now = new Date();
        const createdAt = new Date(customer.createdAt);
        const monthsActive = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30);
        return Math.min(monthsActive * 5, 100);
    }
    calculatePurchaseScore(customer) {
        const totalSpent = customer.totalSpent || 0;
        if (totalSpent >= 50000)
            return 100;
        if (totalSpent >= 25000)
            return 80;
        if (totalSpent >= 10000)
            return 60;
        if (totalSpent >= 5000)
            return 40;
        if (totalSpent >= 1000)
            return 20;
        return 0;
    }
    determineRiskLevel(creditScore) {
        if (creditScore >= 750)
            return 'low';
        if (creditScore >= 650)
            return 'medium';
        if (creditScore >= 550)
            return 'high';
        return 'very_high';
    }
    async suggestCreditLimit(customerId, creditScore) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer)
            return 0;
        let baseLimit = 0;
        if (creditScore >= 750)
            baseLimit = 50000;
        else if (creditScore >= 650)
            baseLimit = 25000;
        else if (creditScore >= 550)
            baseLimit = 10000;
        else
            baseLimit = 5000;
        const totalSpent = customer.totalSpent || 0;
        const purchaseMultiplier = Math.min(totalSpent / 10000, 3);
        return Math.round(baseLimit * (1 + purchaseMultiplier));
    }
    async getCreditStatistics() {
        const totalAssessments = await this.creditRepository.count();
        const avgCreditScore = await this.creditRepository
            .createQueryBuilder('credit')
            .select('AVG(credit.creditScore)', 'avg')
            .getRawOne();
        const avgCreditLimit = await this.creditRepository
            .createQueryBuilder('credit')
            .select('AVG(credit.creditLimit)', 'avg')
            .getRawOne();
        const riskDistribution = await this.creditRepository
            .createQueryBuilder('credit')
            .select('credit.riskLevel', 'riskLevel')
            .addSelect('COUNT(*)', 'count')
            .groupBy('credit.riskLevel')
            .getRawMany();
        const highUtilization = await this.customerRepository.count({
            where: { currentBalance: (0, typeorm_2.MoreThan)(0) },
        });
        return {
            totalAssessments,
            averageCreditScore: parseFloat(avgCreditScore.avg) || 0,
            averageCreditLimit: parseFloat(avgCreditLimit.avg) || 0,
            riskDistribution: riskDistribution.map(item => ({
                riskLevel: item.riskLevel,
                count: parseInt(item.count),
            })),
            highUtilizationCustomers: highUtilization,
        };
    }
    async getCustomersRequiringReview() {
        return this.customerRepository.find({
            where: [
                { currentBalance: (0, typeorm_2.MoreThan)(10000) },
            ],
            order: { currentBalance: 'DESC' },
            take: 20,
        });
    }
    async scheduleCreditReview(customerId, reviewDate, reason) {
        return this.create({
            customerId,
            notes: `Credit review scheduled for ${reviewDate.toDateString()}: ${reason}`,
            assessmentDate: new Date(),
            reviewRequired: true,
            reviewDate,
        });
    }
};
exports.CustomerCreditService = CustomerCreditService;
exports.CustomerCreditService = CustomerCreditService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_credit_entity_1.CustomerCredit)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerCreditService);
//# sourceMappingURL=customer-credit.service.js.map