import { Project } from './project.entity';
export declare enum ExpenseStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    APPROVED = "approved",
    REJECTED = "rejected",
    REIMBURSED = "reimbursed"
}
export declare enum ExpenseCategory {
    TRAVEL = "travel",
    MEALS = "meals",
    ACCOMMODATION = "accommodation",
    TRANSPORTATION = "transportation",
    MATERIALS = "materials",
    SOFTWARE = "software",
    EQUIPMENT = "equipment",
    TRAINING = "training",
    CONSULTING = "consulting",
    OTHER = "other"
}
export declare class ProjectExpense {
    id: string;
    projectId: string;
    project: Project;
    userId: string;
    title: string;
    description: string;
    category: ExpenseCategory;
    expenseDate: Date;
    amount: number;
    currency: string;
    status: ExpenseStatus;
    vendor: string;
    receiptNumber: string;
    attachments: string[];
    isReimbursable: boolean;
    submittedBy: string;
    submittedAt: Date;
    approvedBy: string;
    approvedAt: Date;
    approvalNotes: string;
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
