import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { PosTerminal } from './pos-terminal.entity';
import { PosSaleItem } from './pos-sale-item.entity';
import { PosPayment } from './pos-payment.entity';
import { PosDiscount } from './pos-discount.entity';
import { PosTax } from './pos-tax.entity';
import { PosCustomer } from './pos-customer.entity';

export enum SaleStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
  ON_HOLD = 'on_hold',
}

export enum SaleType {
  REGULAR = 'regular',
  RETURN = 'return',
  EXCHANGE = 'exchange',
  LAYAWAY = 'layaway',
  SPECIAL_ORDER = 'special_order',
}

@Entity('pos_sales')
export class PosSale {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  saleNumber: string;

  @Column()
  terminalId: string;

  @ManyToOne(() => PosTerminal)
  @JoinColumn({ name: 'terminalId' })
  terminal: PosTerminal;

  @Column({ nullable: true })
  customerId: string;

  @ManyToOne(() => PosCustomer, { nullable: true })
  @JoinColumn({ name: 'customerId' })
  customer: PosCustomer;

  @Column({
    type: 'enum',
    enum: SaleType,
    default: SaleType.REGULAR,
  })
  type: SaleType;

  @Column({
    type: 'enum',
    enum: SaleStatus,
    default: SaleStatus.PENDING,
  })
  status: SaleStatus;

  @Column({ type: 'timestamp' })
  saleDateTime: Date;

  @Column({ type: 'int', default: 0 })
  totalItems: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  subtotal: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalDiscount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalTax: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  amountPaid: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  changeAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  amountDue: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column()
  cashierId: string;

  @Column({ nullable: true })
  shiftId: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  customerInfo: any;

  @Column({ default: false })
  isVoided: boolean;

  @Column({ nullable: true })
  voidedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  voidedAt: Date;

  @Column({ type: 'text', nullable: true })
  voidReason: string;

  @OneToMany(() => PosSaleItem, item => item.sale, { cascade: true })
  items: PosSaleItem[];

  @OneToMany(() => PosPayment, payment => payment.sale, { cascade: true })
  payments: PosPayment[];

  @OneToMany(() => PosDiscount, discount => discount.sale, { cascade: true })
  discounts: PosDiscount[];

  @OneToMany(() => PosTax, tax => tax.sale, { cascade: true })
  taxes: PosTax[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
