"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReturnedInvoice = void 0;
const typeorm_1 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
const invoice_entity_1 = require("./invoice.entity");
const returned_invoice_item_entity_1 = require("./returned-invoice-item.entity");
let ReturnedInvoice = class ReturnedInvoice {
    id;
    returnNumber;
    customerId;
    customer;
    originalInvoiceId;
    originalInvoice;
    returnDate;
    reason;
    returnType;
    originalAmount;
    returnAmount;
    returnTaxAmount;
    totalReturnAmount;
    status;
    refundMethod;
    notes;
    internalNotes;
    processedBy;
    processedDate;
    refundReference;
    items;
    createdAt;
    updatedAt;
    tenantId;
};
exports.ReturnedInvoice = ReturnedInvoice;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "returnNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => customer_entity_1.Customer, customer => customer),
    (0, typeorm_1.JoinColumn)({ name: 'customerId' }),
    __metadata("design:type", customer_entity_1.Customer)
], ReturnedInvoice.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "originalInvoiceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => invoice_entity_1.Invoice, invoice => invoice),
    (0, typeorm_1.JoinColumn)({ name: 'originalInvoiceId' }),
    __metadata("design:type", invoice_entity_1.Invoice)
], ReturnedInvoice.prototype, "originalInvoice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], ReturnedInvoice.prototype, "returnDate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'full' }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "returnType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoice.prototype, "originalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoice.prototype, "returnAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], ReturnedInvoice.prototype, "returnTaxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoice.prototype, "totalReturnAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'pending' }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'refund' }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "refundMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "internalNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "processedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Object)
], ReturnedInvoice.prototype, "processedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "refundReference", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => returned_invoice_item_entity_1.ReturnedInvoiceItem, item => item.returnedInvoice, { cascade: true }),
    __metadata("design:type", Array)
], ReturnedInvoice.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ReturnedInvoice.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ReturnedInvoice.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoice.prototype, "tenantId", void 0);
exports.ReturnedInvoice = ReturnedInvoice = __decorate([
    (0, typeorm_1.Entity)('returned_invoices')
], ReturnedInvoice);
//# sourceMappingURL=returned-invoice.entity.js.map