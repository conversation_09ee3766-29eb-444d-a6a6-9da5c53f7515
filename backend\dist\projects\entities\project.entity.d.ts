import { ProjectMember } from './project-member.entity';
import { Task } from './task.entity';
import { Milestone } from './milestone.entity';
import { TimeEntry } from './time-entry.entity';
import { ProjectExpense } from './project-expense.entity';
import { ProjectDocument } from './project-document.entity';
export declare enum ProjectStatus {
    PLANNING = "planning",
    ACTIVE = "active",
    ON_HOLD = "on_hold",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    ARCHIVED = "archived"
}
export declare enum ProjectPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export declare enum ProjectType {
    INTERNAL = "internal",
    CLIENT = "client",
    RESEARCH = "research",
    MAINTENANCE = "maintenance",
    DEVELOPMENT = "development",
    MARKETING = "marketing",
    TRAINING = "training"
}
export declare class Project {
    id: string;
    code: string;
    name: string;
    description: string;
    type: ProjectType;
    status: ProjectStatus;
    priority: ProjectPriority;
    startDate: Date;
    endDate: Date;
    actualStartDate: Date;
    actualEndDate: Date;
    budget: number;
    actualCost: number;
    currency: string;
    clientId: string;
    clientName: string;
    managerId: string;
    parentProjectId: string;
    parentProject: Project;
    subProjects: Project[];
    completionPercentage: number;
    estimatedHours: number;
    actualHours: number;
    tags: string[];
    customFields: any;
    notes: string;
    members: ProjectMember[];
    tasks: Task[];
    milestones: Milestone[];
    timeEntries: TimeEntry[];
    expenses: ProjectExpense[];
    documents: ProjectDocument[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
