"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaveService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const leave_entity_1 = require("../entities/leave.entity");
const leave_type_entity_1 = require("../entities/leave-type.entity");
let LeaveService = class LeaveService {
    leaveRepository;
    leaveTypeRepository;
    constructor(leaveRepository, leaveTypeRepository) {
        this.leaveRepository = leaveRepository;
        this.leaveTypeRepository = leaveTypeRepository;
    }
    async create(createLeaveDto) {
        const startDate = new Date(createLeaveDto.startDate);
        const endDate = new Date(createLeaveDto.endDate);
        const daysRequested = this.calculateLeaveDays(startDate, endDate, createLeaveDto.isHalfDay);
        const leave = this.leaveRepository.create({
            ...createLeaveDto,
            daysRequested,
            appliedDate: new Date(),
        });
        return this.leaveRepository.save(leave);
    }
    async findAll(filters) {
        const queryBuilder = this.leaveRepository.createQueryBuilder('leave')
            .leftJoinAndSelect('leave.employee', 'employee')
            .leftJoinAndSelect('leave.leaveType', 'leaveType');
        if (filters?.employeeId) {
            queryBuilder.andWhere('leave.employeeId = :employeeId', { employeeId: filters.employeeId });
        }
        if (filters?.status) {
            queryBuilder.andWhere('leave.status = :status', { status: filters.status });
        }
        if (filters?.leaveTypeId) {
            queryBuilder.andWhere('leave.leaveTypeId = :leaveTypeId', { leaveTypeId: filters.leaveTypeId });
        }
        if (filters?.startDate && filters.endDate) {
            queryBuilder.andWhere('leave.startDate >= :startDate AND leave.endDate <= :endDate', {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        return queryBuilder
            .orderBy('leave.startDate', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const leave = await this.leaveRepository.findOne({
            where: { id },
            relations: ['employee', 'leaveType'],
        });
        if (!leave) {
            throw new common_1.NotFoundException(`Leave request with ID ${id} not found`);
        }
        return leave;
    }
    async update(id, updateLeaveDto) {
        const leave = await this.findOne(id);
        if (leave.status === leave_entity_1.LeaveStatus.APPROVED || leave.status === leave_entity_1.LeaveStatus.TAKEN) {
            throw new common_1.BadRequestException('Cannot update approved or taken leave');
        }
        Object.assign(leave, updateLeaveDto);
        if (updateLeaveDto.startDate || updateLeaveDto.endDate || updateLeaveDto.isHalfDay !== undefined) {
            leave.daysRequested = this.calculateLeaveDays(leave.startDate, leave.endDate, leave.isHalfDay);
        }
        return this.leaveRepository.save(leave);
    }
    async remove(id) {
        const leave = await this.findOne(id);
        if (leave.status === leave_entity_1.LeaveStatus.APPROVED || leave.status === leave_entity_1.LeaveStatus.TAKEN) {
            throw new common_1.BadRequestException('Cannot delete approved or taken leave');
        }
        await this.leaveRepository.remove(leave);
    }
    async approveLeave(id, approvedBy, approvalComments) {
        const leave = await this.findOne(id);
        if (leave.status !== leave_entity_1.LeaveStatus.PENDING) {
            throw new common_1.BadRequestException('Only pending leave requests can be approved');
        }
        leave.status = leave_entity_1.LeaveStatus.APPROVED;
        leave.daysApproved = leave.daysRequested;
        leave.approvedBy = approvedBy;
        leave.approvedAt = new Date();
        leave.approvalComments = approvalComments;
        return this.leaveRepository.save(leave);
    }
    async rejectLeave(id, rejectedBy, rejectionComments) {
        const leave = await this.findOne(id);
        if (leave.status !== leave_entity_1.LeaveStatus.PENDING) {
            throw new common_1.BadRequestException('Only pending leave requests can be rejected');
        }
        leave.status = leave_entity_1.LeaveStatus.REJECTED;
        leave.approvedBy = rejectedBy;
        leave.approvedAt = new Date();
        leave.approvalComments = rejectionComments;
        return this.leaveRepository.save(leave);
    }
    async cancelLeave(id, reason) {
        const leave = await this.findOne(id);
        if (leave.status === leave_entity_1.LeaveStatus.TAKEN) {
            throw new common_1.BadRequestException('Cannot cancel leave that has already been taken');
        }
        leave.status = leave_entity_1.LeaveStatus.CANCELLED;
        if (reason) {
            leave.comments = (leave.comments || '') + `\nCancellation Reason: ${reason}`;
        }
        return this.leaveRepository.save(leave);
    }
    async getLeaveBalance(employeeId, leaveTypeId, year) {
        const currentYear = year || new Date().getFullYear();
        const startDate = new Date(currentYear, 0, 1);
        const endDate = new Date(currentYear, 11, 31);
        const leaveType = await this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } });
        if (!leaveType) {
            throw new common_1.NotFoundException('Leave type not found');
        }
        const approvedLeaves = await this.leaveRepository.find({
            where: {
                employeeId,
                leaveTypeId,
                status: leave_entity_1.LeaveStatus.APPROVED,
                startDate: Between(startDate, endDate),
            },
        });
        const takenLeaves = await this.leaveRepository.find({
            where: {
                employeeId,
                leaveTypeId,
                status: leave_entity_1.LeaveStatus.TAKEN,
                startDate: Between(startDate, endDate),
            },
        });
        const totalApproved = approvedLeaves.reduce((sum, leave) => sum + leave.daysApproved, 0);
        const totalTaken = takenLeaves.reduce((sum, leave) => sum + leave.daysApproved, 0);
        const totalUsed = totalApproved + totalTaken;
        return {
            leaveType: leaveType.name,
            year: currentYear,
            allocated: leaveType.maxDaysPerYear,
            used: totalUsed,
            remaining: Math.max(0, leaveType.maxDaysPerYear - totalUsed),
            pending: totalApproved,
            taken: totalTaken,
        };
    }
    async getLeaveReport(employeeId, year) {
        const currentYear = year || new Date().getFullYear();
        const startDate = new Date(currentYear, 0, 1);
        const endDate = new Date(currentYear, 11, 31);
        const leaves = await this.findAll({
            employeeId,
            startDate,
            endDate,
        });
        const leaveTypes = await this.leaveTypeRepository.find({ where: { isActive: true } });
        const balances = await Promise.all(leaveTypes.map(type => this.getLeaveBalance(employeeId, type.id, year)));
        return {
            employeeId,
            year: currentYear,
            balances,
            leaves,
            summary: {
                totalLeavesTaken: leaves.filter(l => l.status === leave_entity_1.LeaveStatus.TAKEN).length,
                totalDaysTaken: leaves
                    .filter(l => l.status === leave_entity_1.LeaveStatus.TAKEN)
                    .reduce((sum, l) => sum + l.daysApproved, 0),
                pendingRequests: leaves.filter(l => l.status === leave_entity_1.LeaveStatus.PENDING).length,
            },
        };
    }
    async createLeaveType(createLeaveTypeDto) {
        const leaveType = this.leaveTypeRepository.create(createLeaveTypeDto);
        return this.leaveTypeRepository.save(leaveType);
    }
    async findAllLeaveTypes() {
        return this.leaveTypeRepository.find({
            where: { isActive: true },
            order: { name: 'ASC' },
        });
    }
    calculateLeaveDays(startDate, endDate, isHalfDay) {
        const timeDiff = endDate.getTime() - startDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
        return isHalfDay ? 0.5 : daysDiff;
    }
};
exports.LeaveService = LeaveService;
exports.LeaveService = LeaveService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(leave_entity_1.Leave)),
    __param(1, (0, typeorm_1.InjectRepository)(leave_type_entity_1.LeaveType)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], LeaveService);
//# sourceMappingURL=leave.service.js.map