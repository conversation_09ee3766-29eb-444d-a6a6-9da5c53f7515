import { StockMovementService } from '../services/stock-movement.service';
export declare class StockMovementController {
    private readonly stockMovementService;
    constructor(stockMovementService: StockMovementService);
    findAll(): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
    getStatistics(startDate?: string, endDate?: string): Promise<any>;
    getRecentMovements(limit?: string): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
    getMovementsByType(movementType: string): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
    findByProduct(productId: string): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
    findByWarehouse(warehouseId: string): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
    findByDateRange(startDate: string, endDate: string): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
}
