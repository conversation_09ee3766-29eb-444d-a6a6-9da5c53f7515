import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum IncidentSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum IncidentStatus {
  NEW = 'new',
  INVESTIGATING = 'investigating',
  IDENTIFIED = 'identified',
  MONITORING = 'monitoring',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum IncidentCategory {
  HARDWARE_FAILURE = 'hardware_failure',
  SOFTWARE_ISSUE = 'software_issue',
  NETWORK_OUTAGE = 'network_outage',
  SECURITY_BREACH = 'security_breach',
  DATA_LOSS = 'data_loss',
  PERFORMANCE_ISSUE = 'performance_issue',
  SERVICE_UNAVAILABLE = 'service_unavailable',
  OTHER = 'other',
}

@Entity('incidents')
export class Incident {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  incidentNumber: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: IncidentCategory,
  })
  category: IncidentCategory;

  @Column({
    type: 'enum',
    enum: IncidentSeverity,
    default: IncidentSeverity.MEDIUM,
  })
  severity: IncidentSeverity;

  @Column({
    type: 'enum',
    enum: IncidentStatus,
    default: IncidentStatus.NEW,
  })
  status: IncidentStatus;

  @Column()
  reportedBy: string;

  @Column({ length: 255 })
  reporterName: string;

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ type: 'timestamp' })
  reportedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  acknowledgedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  resolvedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  closedAt: Date;

  @Column({ type: 'json', nullable: true })
  affectedServices: string[];

  @Column({ type: 'json', nullable: true })
  affectedUsers: string[];

  @Column({ type: 'text', nullable: true })
  symptoms: string;

  @Column({ type: 'text', nullable: true })
  rootCause: string;

  @Column({ type: 'text', nullable: true })
  resolution: string;

  @Column({ type: 'text', nullable: true })
  workaround: string;

  @Column({ type: 'text', nullable: true })
  preventiveMeasures: string;

  @Column({ type: 'json', nullable: true })
  timeline: any[];

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
