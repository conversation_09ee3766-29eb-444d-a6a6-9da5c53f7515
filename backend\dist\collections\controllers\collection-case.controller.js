"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionCaseController = void 0;
const common_1 = require("@nestjs/common");
const collection_case_service_1 = require("../services/collection-case.service");
const collection_case_entity_1 = require("../entities/collection-case.entity");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CollectionCaseController = class CollectionCaseController {
    collectionCaseService;
    constructor(collectionCaseService) {
        this.collectionCaseService = collectionCaseService;
    }
    async create(createCaseDto) {
        return this.collectionCaseService.create(createCaseDto);
    }
    async findAll(status) {
        if (status) {
            return this.collectionCaseService.findByStatus(status);
        }
        return this.collectionCaseService.findAll();
    }
    async getStatistics() {
        return this.collectionCaseService.getStatistics();
    }
    async getDashboardMetrics() {
        return this.collectionCaseService.getDashboardMetrics();
    }
    async getOverdueCases() {
        return this.collectionCaseService.getOverdueCases();
    }
    async getCasesByPriority(priority) {
        return this.collectionCaseService.getCasesByPriority(priority);
    }
    async findByCustomer(customerId) {
        return this.collectionCaseService.findByCustomer(customerId);
    }
    async findOne(id) {
        return this.collectionCaseService.findOne(id);
    }
    async update(id, updateCaseDto) {
        return this.collectionCaseService.update(id, updateCaseDto);
    }
    async updateStatus(id, statusUpdate) {
        return this.collectionCaseService.updateStatus(id, statusUpdate.status, statusUpdate.notes);
    }
    async assignAgent(id, assignment) {
        return this.collectionCaseService.assignAgent(id, assignment.agentId);
    }
    async escalateCase(id, escalation) {
        return this.collectionCaseService.escalateCase(id, escalation.reason);
    }
    async recordPayment(id, payment) {
        return this.collectionCaseService.recordPayment(id, payment.amount, new Date(payment.paymentDate), payment.notes);
    }
    async remove(id) {
        return this.collectionCaseService.remove(id);
    }
};
exports.CollectionCaseController = CollectionCaseController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Get)('overdue'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "getOverdueCases", null);
__decorate([
    (0, common_1.Get)('priority/:priority'),
    __param(0, (0, common_1.Param)('priority')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "getCasesByPriority", null);
__decorate([
    (0, common_1.Get)('customer/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "findByCustomer", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/assign'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "assignAgent", null);
__decorate([
    (0, common_1.Patch)(':id/escalate'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "escalateCase", null);
__decorate([
    (0, common_1.Post)(':id/payment'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "recordPayment", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionCaseController.prototype, "remove", null);
exports.CollectionCaseController = CollectionCaseController = __decorate([
    (0, common_1.Controller)('collection-cases'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [collection_case_service_1.CollectionCaseService])
], CollectionCaseController);
//# sourceMappingURL=collection-case.controller.js.map