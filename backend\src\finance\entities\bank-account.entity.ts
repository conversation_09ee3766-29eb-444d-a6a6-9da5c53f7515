import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { BankTransaction } from './bank-transaction.entity';

export enum BankAccountType {
  CHECKING = 'checking',
  SAVINGS = 'savings',
  CREDIT = 'credit',
  LOAN = 'loan',
  INVESTMENT = 'investment',
  MONEY_MARKET = 'money_market',
  CD = 'cd',
  OTHER = 'other',
}

export enum BankAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CLOSED = 'closed',
  FROZEN = 'frozen',
}

@Entity('finance_bank_accounts')
export class BankAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  accountName: string;

  @Column({ length: 50 })
  accountNumber: string;

  @Column({ length: 255 })
  bankName: string;

  @Column({ length: 20, nullable: true })
  routingNumber: string;

  @Column({ length: 20, nullable: true })
  swiftCode: string;

  @Column({ length: 20, nullable: true })
  iban: string;

  @Column({
    type: 'enum',
    enum: BankAccountType,
  })
  accountType: BankAccountType;

  @Column({
    type: 'enum',
    enum: BankAccountStatus,
    default: BankAccountStatus.ACTIVE,
  })
  status: BankAccountStatus;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  currentBalance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  availableBalance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  creditLimit: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  interestRate: number;

  @Column({ type: 'date', nullable: true })
  openedDate: Date;

  @Column({ type: 'date', nullable: true })
  closedDate: Date;

  @Column({ type: 'date', nullable: true })
  lastReconciledDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  lastReconciledBalance: number;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ default: true })
  allowOnlineTransactions: boolean;

  @Column({ default: true })
  allowMobileDeposits: boolean;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  bankContactInfo: any;

  @Column({ type: 'json', nullable: true })
  accountHolders: string[];

  @Column({ type: 'json', nullable: true })
  authorizedSigners: string[];

  @OneToMany(() => BankTransaction, transaction => transaction.bankAccount)
  transactions: BankTransaction[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
