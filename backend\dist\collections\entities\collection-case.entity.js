"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionCase = exports.DebtType = exports.CasePriority = exports.CaseStatus = void 0;
const typeorm_1 = require("typeorm");
const collection_activity_entity_1 = require("./collection-activity.entity");
const collection_strategy_entity_1 = require("./collection-strategy.entity");
const collection_agent_entity_1 = require("./collection-agent.entity");
const payment_plan_entity_1 = require("./payment-plan.entity");
const collection_note_entity_1 = require("./collection-note.entity");
const collection_document_entity_1 = require("./collection-document.entity");
const collection_dispute_entity_1 = require("./collection-dispute.entity");
var CaseStatus;
(function (CaseStatus) {
    CaseStatus["NEW"] = "new";
    CaseStatus["ACTIVE"] = "active";
    CaseStatus["ON_HOLD"] = "on_hold";
    CaseStatus["PAYMENT_PLAN"] = "payment_plan";
    CaseStatus["DISPUTED"] = "disputed";
    CaseStatus["LEGAL"] = "legal";
    CaseStatus["CLOSED_PAID"] = "closed_paid";
    CaseStatus["CLOSED_WRITTEN_OFF"] = "closed_written_off";
    CaseStatus["CLOSED_SETTLED"] = "closed_settled";
    CaseStatus["CLOSED_UNCOLLECTABLE"] = "closed_uncollectable";
})(CaseStatus || (exports.CaseStatus = CaseStatus = {}));
var CasePriority;
(function (CasePriority) {
    CasePriority["LOW"] = "low";
    CasePriority["MEDIUM"] = "medium";
    CasePriority["HIGH"] = "high";
    CasePriority["URGENT"] = "urgent";
    CasePriority["CRITICAL"] = "critical";
})(CasePriority || (exports.CasePriority = CasePriority = {}));
var DebtType;
(function (DebtType) {
    DebtType["INVOICE"] = "invoice";
    DebtType["LOAN"] = "loan";
    DebtType["CREDIT_CARD"] = "credit_card";
    DebtType["UTILITY"] = "utility";
    DebtType["MEDICAL"] = "medical";
    DebtType["STUDENT_LOAN"] = "student_loan";
    DebtType["MORTGAGE"] = "mortgage";
    DebtType["OTHER"] = "other";
})(DebtType || (exports.DebtType = DebtType = {}));
let CollectionCase = class CollectionCase {
    id;
    caseNumber;
    customerId;
    customerName;
    customerEmail;
    customerPhone;
    debtType;
    originalAmount;
    currentBalance;
    interestAmount;
    feesAmount;
    totalAmount;
    currency;
    originalDueDate;
    daysOverdue;
    status;
    priority;
    assignedAgentId;
    assignedAgent;
    strategyId;
    strategy;
    lastContactDate;
    nextActionDate;
    nextAction;
    promiseToPayDate;
    promiseToPayAmount;
    contactAttempts;
    successfulContacts;
    totalPayments;
    lastPaymentDate;
    lastPaymentAmount;
    customerNotes;
    internalNotes;
    tags;
    isDisputed;
    isLegal;
    isBankrupt;
    isDeceased;
    doNotCall;
    doNotEmail;
    doNotMail;
    activities;
    paymentPlans;
    notes;
    documents;
    disputes;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionCase = CollectionCase;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionCase.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "caseNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionCase.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CollectionCase.prototype, "customerName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "customerEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "customerPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DebtType,
        default: DebtType.INVOICE,
    }),
    __metadata("design:type", String)
], CollectionCase.prototype, "debtType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "originalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "currentBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "interestAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "feesAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], CollectionCase.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], CollectionCase.prototype, "originalDueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "daysOverdue", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CaseStatus,
        default: CaseStatus.NEW,
    }),
    __metadata("design:type", String)
], CollectionCase.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CasePriority,
        default: CasePriority.MEDIUM,
    }),
    __metadata("design:type", String)
], CollectionCase.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "assignedAgentId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_agent_entity_1.CollectionAgent, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'assignedAgentId' }),
    __metadata("design:type", collection_agent_entity_1.CollectionAgent)
], CollectionCase.prototype, "assignedAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "strategyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_strategy_entity_1.CollectionStrategy, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'strategyId' }),
    __metadata("design:type", collection_strategy_entity_1.CollectionStrategy)
], CollectionCase.prototype, "strategy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionCase.prototype, "lastContactDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionCase.prototype, "nextActionDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "nextAction", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionCase.prototype, "promiseToPayDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "promiseToPayAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "contactAttempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "successfulContacts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "totalPayments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionCase.prototype, "lastPaymentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CollectionCase.prototype, "lastPaymentAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "customerNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionCase.prototype, "internalNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CollectionCase.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "isDisputed", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "isLegal", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "isBankrupt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "isDeceased", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "doNotCall", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "doNotEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCase.prototype, "doNotMail", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => collection_activity_entity_1.CollectionActivity, activity => activity.case),
    __metadata("design:type", Array)
], CollectionCase.prototype, "activities", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => payment_plan_entity_1.PaymentPlan, plan => plan.case),
    __metadata("design:type", Array)
], CollectionCase.prototype, "paymentPlans", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => collection_note_entity_1.CollectionNote, note => note.case),
    __metadata("design:type", Array)
], CollectionCase.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => collection_document_entity_1.CollectionDocument, document => document.case),
    __metadata("design:type", Array)
], CollectionCase.prototype, "documents", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => collection_dispute_entity_1.CollectionDispute, dispute => dispute.case),
    __metadata("design:type", Array)
], CollectionCase.prototype, "disputes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionCase.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionCase.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionCase.prototype, "updatedAt", void 0);
exports.CollectionCase = CollectionCase = __decorate([
    (0, typeorm_1.Entity)('collection_cases')
], CollectionCase);
//# sourceMappingURL=collection-case.entity.js.map