import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { PosSale } from './pos-sale.entity';
import { PosShift } from './pos-shift.entity';

export enum TerminalStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
  OFFLINE = 'offline',
}

export enum TerminalType {
  STANDARD = 'standard',
  MOBILE = 'mobile',
  KIOSK = 'kiosk',
  TABLET = 'tablet',
  HANDHELD = 'handheld',
}

@Entity('pos_terminals')
export class PosTerminal {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  terminalNumber: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TerminalType,
    default: TerminalType.STANDARD,
  })
  type: TerminalType;

  @Column({
    type: 'enum',
    enum: TerminalStatus,
    default: TerminalStatus.ACTIVE,
  })
  status: TerminalStatus;

  @Column({ length: 255, nullable: true })
  location: string;

  @Column({ nullable: true })
  warehouseId: string;

  @Column({ length: 100, nullable: true })
  ipAddress: string;

  @Column({ length: 100, nullable: true })
  macAddress: string;

  @Column({ length: 255, nullable: true })
  deviceModel: string;

  @Column({ length: 255, nullable: true })
  serialNumber: string;

  @Column({ type: 'json', nullable: true })
  printerSettings: any;

  @Column({ type: 'json', nullable: true })
  cashDrawerSettings: any;

  @Column({ type: 'json', nullable: true })
  scannerSettings: any;

  @Column({ type: 'json', nullable: true })
  displaySettings: any;

  @Column({ default: true })
  allowCashPayments: boolean;

  @Column({ default: true })
  allowCardPayments: boolean;

  @Column({ default: false })
  allowMobilePayments: boolean;

  @Column({ default: false })
  allowGiftCards: boolean;

  @Column({ default: false })
  allowLayaway: boolean;

  @Column({ default: false })
  allowReturns: boolean;

  @Column({ default: false })
  allowDiscounts: boolean;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  maxDiscountAmount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  maxDiscountPercentage: number;

  @Column({ type: 'timestamp', nullable: true })
  lastHeartbeat: Date;

  @Column({ type: 'json', nullable: true })
  configuration: any;

  @OneToMany(() => PosSale, sale => sale.terminal)
  sales: PosSale[];

  @OneToMany(() => PosShift, shift => shift.terminal)
  shifts: PosShift[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
