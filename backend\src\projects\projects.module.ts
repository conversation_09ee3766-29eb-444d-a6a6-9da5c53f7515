import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Project } from './entities/project.entity';
import { ProjectMember } from './entities/project-member.entity';
import { Task } from './entities/task.entity';
import { TaskAssignment } from './entities/task-assignment.entity';
import { TaskComment } from './entities/task-comment.entity';
import { TaskAttachment } from './entities/task-attachment.entity';
import { Milestone } from './entities/milestone.entity';
import { TimeEntry } from './entities/time-entry.entity';
import { ProjectExpense } from './entities/project-expense.entity';
import { ProjectDocument } from './entities/project-document.entity';
import { ProjectStatus } from './entities/project-status.entity';

// Services
import { ProjectService } from './services/project.service';
import { TaskService } from './services/task.service';
import { MilestoneService } from './services/milestone.service';
import { TimeTrackingService } from './services/time-tracking.service';
import { ProjectExpenseService } from './services/project-expense.service';
import { ProjectReportService } from './services/project-report.service';

// Controllers
import { ProjectController } from './controllers/project.controller';
import { TaskController } from './controllers/task.controller';
import { MilestoneController } from './controllers/milestone.controller';
import { TimeTrackingController } from './controllers/time-tracking.controller';
import { ProjectExpenseController } from './controllers/project-expense.controller';
import { ProjectReportController } from './controllers/project-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
      ProjectMember,
      Task,
      TaskAssignment,
      TaskComment,
      TaskAttachment,
      Milestone,
      TimeEntry,
      ProjectExpense,
      ProjectDocument,
      ProjectStatus,
    ]),
  ],
  controllers: [
    ProjectController,
    TaskController,
    MilestoneController,
    TimeTrackingController,
    ProjectExpenseController,
    ProjectReportController,
  ],
  providers: [
    ProjectService,
    TaskService,
    MilestoneService,
    TimeTrackingService,
    ProjectExpenseService,
    ProjectReportService,
  ],
  exports: [
    ProjectService,
    TaskService,
    MilestoneService,
    TimeTrackingService,
    ProjectExpenseService,
    ProjectReportService,
  ],
})
export class ProjectsModule {}
