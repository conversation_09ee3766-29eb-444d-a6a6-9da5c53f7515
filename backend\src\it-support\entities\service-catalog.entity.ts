import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum ServiceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated',
  UNDER_DEVELOPMENT = 'under_development',
}

export enum ServiceType {
  BUSINESS_SERVICE = 'business_service',
  TECHNICAL_SERVICE = 'technical_service',
  INFRASTRUCTURE_SERVICE = 'infrastructure_service',
  APPLICATION_SERVICE = 'application_service',
}

@Entity('service_catalog')
export class ServiceCatalog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: ServiceType,
  })
  type: ServiceType;

  @Column({
    type: 'enum',
    enum: ServiceStatus,
    default: ServiceStatus.ACTIVE,
  })
  status: ServiceStatus;

  @Column({ length: 255, nullable: true })
  category: string;

  @Column({ nullable: true })
  serviceOwner: string;

  @Column({ nullable: true })
  businessOwner: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  cost: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'text', nullable: true })
  sla: string;

  @Column({ type: 'json', nullable: true })
  dependencies: string[];

  @Column({ type: 'json', nullable: true })
  supportedBy: string[];

  @Column({ type: 'text', nullable: true })
  requestProcess: string;

  @Column({ type: 'json', nullable: true })
  deliverables: string[];

  @Column({ type: 'int', nullable: true })
  estimatedDeliveryDays: number;

  @Column({ default: true })
  isRequestable: boolean;

  @Column({ default: false })
  requiresApproval: boolean;

  @Column({ type: 'json', nullable: true })
  approvers: string[];

  @Column({ type: 'json', nullable: true })
  requestForm: any;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
