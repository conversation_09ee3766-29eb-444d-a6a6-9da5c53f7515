"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerAnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const customer_analytics_service_1 = require("../services/customer-analytics.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerAnalyticsController = class CustomerAnalyticsController {
    customerAnalyticsService;
    constructor(customerAnalyticsService) {
        this.customerAnalyticsService = customerAnalyticsService;
    }
    async getCustomerOverview() {
        return this.customerAnalyticsService.getCustomerOverview();
    }
    async getCustomerDistribution() {
        return this.customerAnalyticsService.getCustomerDistribution();
    }
    async getFinancialMetrics() {
        return this.customerAnalyticsService.getFinancialMetrics();
    }
    async getCustomerLifecycleMetrics() {
        return this.customerAnalyticsService.getCustomerLifecycleMetrics();
    }
    async getInteractionAnalytics() {
        return this.customerAnalyticsService.getInteractionAnalytics();
    }
    async getSegmentAnalytics() {
        return this.customerAnalyticsService.getSegmentAnalytics();
    }
    async getDashboardMetrics() {
        return this.customerAnalyticsService.getDashboardMetrics();
    }
    async getCustomerHealthScore(customerId) {
        return this.customerAnalyticsService.getCustomerHealthScore(customerId);
    }
};
exports.CustomerAnalyticsController = CustomerAnalyticsController;
__decorate([
    (0, common_1.Get)('overview'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getCustomerOverview", null);
__decorate([
    (0, common_1.Get)('distribution'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getCustomerDistribution", null);
__decorate([
    (0, common_1.Get)('financial-metrics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getFinancialMetrics", null);
__decorate([
    (0, common_1.Get)('lifecycle-metrics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getCustomerLifecycleMetrics", null);
__decorate([
    (0, common_1.Get)('interaction-analytics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getInteractionAnalytics", null);
__decorate([
    (0, common_1.Get)('segment-analytics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getSegmentAnalytics", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Get)('health-score/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerAnalyticsController.prototype, "getCustomerHealthScore", null);
exports.CustomerAnalyticsController = CustomerAnalyticsController = __decorate([
    (0, common_1.Controller)('customer-analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_analytics_service_1.CustomerAnalyticsService])
], CustomerAnalyticsController);
//# sourceMappingURL=customer-analytics.controller.js.map