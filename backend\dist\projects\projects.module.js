"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const project_entity_1 = require("./entities/project.entity");
const project_member_entity_1 = require("./entities/project-member.entity");
const task_entity_1 = require("./entities/task.entity");
const task_assignment_entity_1 = require("./entities/task-assignment.entity");
const task_comment_entity_1 = require("./entities/task-comment.entity");
const task_attachment_entity_1 = require("./entities/task-attachment.entity");
const milestone_entity_1 = require("./entities/milestone.entity");
const time_entry_entity_1 = require("./entities/time-entry.entity");
const project_expense_entity_1 = require("./entities/project-expense.entity");
const project_document_entity_1 = require("./entities/project-document.entity");
const project_status_entity_1 = require("./entities/project-status.entity");
const project_service_1 = require("./services/project.service");
const task_service_1 = require("./services/task.service");
const milestone_service_1 = require("./services/milestone.service");
const time_tracking_service_1 = require("./services/time-tracking.service");
const project_expense_service_1 = require("./services/project-expense.service");
const project_report_service_1 = require("./services/project-report.service");
const project_controller_1 = require("./controllers/project.controller");
const task_controller_1 = require("./controllers/task.controller");
const milestone_controller_1 = require("./controllers/milestone.controller");
const time_tracking_controller_1 = require("./controllers/time-tracking.controller");
const project_expense_controller_1 = require("./controllers/project-expense.controller");
const project_report_controller_1 = require("./controllers/project-report.controller");
let ProjectsModule = class ProjectsModule {
};
exports.ProjectsModule = ProjectsModule;
exports.ProjectsModule = ProjectsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                project_entity_1.Project,
                project_member_entity_1.ProjectMember,
                task_entity_1.Task,
                task_assignment_entity_1.TaskAssignment,
                task_comment_entity_1.TaskComment,
                task_attachment_entity_1.TaskAttachment,
                milestone_entity_1.Milestone,
                time_entry_entity_1.TimeEntry,
                project_expense_entity_1.ProjectExpense,
                project_document_entity_1.ProjectDocument,
                project_status_entity_1.ProjectStatus,
            ]),
        ],
        controllers: [
            project_controller_1.ProjectController,
            task_controller_1.TaskController,
            milestone_controller_1.MilestoneController,
            time_tracking_controller_1.TimeTrackingController,
            project_expense_controller_1.ProjectExpenseController,
            project_report_controller_1.ProjectReportController,
        ],
        providers: [
            project_service_1.ProjectService,
            task_service_1.TaskService,
            milestone_service_1.MilestoneService,
            time_tracking_service_1.TimeTrackingService,
            project_expense_service_1.ProjectExpenseService,
            project_report_service_1.ProjectReportService,
        ],
        exports: [
            project_service_1.ProjectService,
            task_service_1.TaskService,
            milestone_service_1.MilestoneService,
            time_tracking_service_1.TimeTrackingService,
            project_expense_service_1.ProjectExpenseService,
            project_report_service_1.ProjectReportService,
        ],
    })
], ProjectsModule);
//# sourceMappingURL=projects.module.js.map