import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_makefile=__commonJS({"../../node_modules/highlight.js/lib/languages/makefile.js"(exports,module){function makefile(hljs){let VARIABLE={className:"variable",variants:[{begin:"\\$\\("+hljs.UNDERSCORE_IDENT_RE+"\\)",contains:[hljs.BACKSLASH_ESCAPE]},{begin:/\$[@%<?\^\+\*]/}]},QUOTE_STRING={className:"string",begin:/"/,end:/"/,contains:[hljs.BACKSLASH_ESCAPE,VARIABLE]},FUNC={className:"variable",begin:/\$\([\w-]+\s/,end:/\)/,keywords:{built_in:"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value"},contains:[VARIABLE]},ASSIGNMENT={begin:"^"+hljs.UNDERSCORE_IDENT_RE+"\\s*(?=[:+?]?=)"},META={className:"meta",begin:/^\.PHONY:/,end:/$/,keywords:{$pattern:/[\.\w]+/,"meta-keyword":".PHONY"}},TARGET={className:"section",begin:/^[^\s]+:/,end:/$/,contains:[VARIABLE]};return {name:"Makefile",aliases:["mk","mak","make"],keywords:{$pattern:/[\w-]+/,keyword:"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath"},contains:[hljs.HASH_COMMENT_MODE,VARIABLE,QUOTE_STRING,FUNC,ASSIGNMENT,META,TARGET]}}module.exports=makefile;}});var makefileYIU6G5IU = require_makefile();

export { makefileYIU6G5IU as default };
