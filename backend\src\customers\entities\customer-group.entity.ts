import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Customer } from './customer.entity';

export enum GroupType {
  PRICING = 'pricing',
  GEOGRAPHIC = 'geographic',
  INDUSTRY = 'industry',
  SIZE = 'size',
  BEHAVIOR = 'behavior',
  CUSTOM = 'custom',
}

@Entity('customer_groups')
export class CustomerGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: GroupType,
    default: GroupType.CUSTOM,
  })
  type: GroupType;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  discountPercentage: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  creditLimit: number;

  @Column({ type: 'int', nullable: true })
  paymentTerms: number;

  @Column({ type: 'json', nullable: true })
  pricingRules: any;

  @Column({ type: 'json', nullable: true })
  benefits: string[];

  @Column({ type: 'json', nullable: true })
  restrictions: string[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0 })
  sortOrder: number;

  @OneToMany(() => Customer, customer => customer.group)
  customers: Customer[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
