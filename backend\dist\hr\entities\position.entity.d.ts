import { Employee } from './employee.entity';
import { Department } from './department.entity';
export declare enum PositionLevel {
    ENTRY = "entry",
    JUNIOR = "junior",
    SENIOR = "senior",
    LEAD = "lead",
    MANAGER = "manager",
    DIRECTOR = "director",
    VP = "vp",
    C_LEVEL = "c_level"
}
export declare class Position {
    id: string;
    title: string;
    code: string;
    description: string;
    responsibilities: string;
    requirements: string;
    level: PositionLevel;
    departmentId: string;
    department: Department;
    minSalary: number;
    maxSalary: number;
    currency: string;
    isActive: boolean;
    skills: string[];
    benefits: string[];
    employees: Employee[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
