import { CustomerCreditService } from '../services/customer-credit.service';
export declare class CustomerCreditController {
    private readonly customerCreditService;
    constructor(customerCreditService: CustomerCreditService);
    findAll(): Promise<import("../entities/customer-credit.entity").CustomerCredit[]>;
    getCreditStatistics(): Promise<any>;
    getCustomersRequiringReview(): Promise<import("../entities/customer.entity").Customer[]>;
    findByCustomer(customerId: string): Promise<import("../entities/customer-credit.entity").CustomerCredit[]>;
    getLatestCreditAssessment(customerId: string): Promise<import("../entities/customer-credit.entity").CustomerCredit | null>;
    calculateCreditScore(customerId: string): Promise<{
        customerId: string;
        creditScore: number;
    }>;
    findOne(id: string): Promise<import("../entities/customer-credit.entity").CustomerCredit>;
    createCreditAssessment(assessmentData: {
        customerId: string;
        creditScore?: number;
        creditLimit?: number;
        paymentTerms?: number;
        riskLevel?: string;
        notes?: string;
        assessedBy?: string;
    }): Promise<import("../entities/customer-credit.entity").CustomerCredit>;
    updateCreditLimit(updateData: {
        customerId: string;
        newLimit: number;
        reason: string;
        updatedBy?: string;
    }): Promise<import("../entities/customer-credit.entity").CustomerCredit>;
    scheduleCreditReview(reviewData: {
        customerId: string;
        reviewDate: Date;
        reason: string;
    }): Promise<import("../entities/customer-credit.entity").CustomerCredit>;
}
