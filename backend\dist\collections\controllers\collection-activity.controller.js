"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionActivityController = void 0;
const common_1 = require("@nestjs/common");
const collection_activity_service_1 = require("../services/collection-activity.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CollectionActivityController = class CollectionActivityController {
    collectionActivityService;
    constructor(collectionActivityService) {
        this.collectionActivityService = collectionActivityService;
    }
    async create(createActivityDto) {
        return this.collectionActivityService.create(createActivityDto);
    }
    async findAll() {
        return this.collectionActivityService.findAll();
    }
    async getRecentActivities(limit) {
        const limitNum = limit ? parseInt(limit) : 10;
        return this.collectionActivityService.getRecentActivities(limitNum);
    }
    async getScheduledFollowUps() {
        return this.collectionActivityService.getScheduledFollowUps();
    }
    async findByCase(caseId) {
        return this.collectionActivityService.findByCase(caseId);
    }
    async getActivitySummary(caseId) {
        return this.collectionActivityService.getActivitySummary(caseId);
    }
    async findByAgent(agentId) {
        return this.collectionActivityService.findByAgent(agentId);
    }
    async getAgentActivityReport(agentId, startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();
        return this.collectionActivityService.getAgentActivityReport(agentId, start, end);
    }
    async findOne(id) {
        return this.collectionActivityService.findOne(id);
    }
    async logActivity(activityData) {
        return this.collectionActivityService.logActivity(activityData.caseId, activityData.type, activityData.description, activityData.performedBy, activityData.outcome);
    }
    async logCall(callData) {
        return this.collectionActivityService.logCall(callData.caseId, callData.duration, callData.outcome, callData.notes, callData.performedBy);
    }
    async logEmail(emailData) {
        return this.collectionActivityService.logEmail(emailData.caseId, emailData.subject, emailData.outcome, emailData.performedBy);
    }
    async logLetter(letterData) {
        return this.collectionActivityService.logLetter(letterData.caseId, letterData.letterType, letterData.performedBy);
    }
    async logPayment(paymentData) {
        return this.collectionActivityService.logPayment(paymentData.caseId, paymentData.amount, paymentData.paymentMethod, paymentData.performedBy);
    }
    async scheduleFollowUp(followUpData) {
        return this.collectionActivityService.scheduleFollowUp(followUpData.caseId, new Date(followUpData.followUpDate), followUpData.notes, followUpData.performedBy);
    }
    async update(id, updateActivityDto) {
        return this.collectionActivityService.update(id, updateActivityDto);
    }
    async markFollowUpCompleted(id, completion) {
        return this.collectionActivityService.markFollowUpCompleted(id, completion.outcome);
    }
    async remove(id) {
        return this.collectionActivityService.remove(id);
    }
};
exports.CollectionActivityController = CollectionActivityController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('recent'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "getRecentActivities", null);
__decorate([
    (0, common_1.Get)('follow-ups'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "getScheduledFollowUps", null);
__decorate([
    (0, common_1.Get)('case/:caseId'),
    __param(0, (0, common_1.Param)('caseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "findByCase", null);
__decorate([
    (0, common_1.Get)('case/:caseId/summary'),
    __param(0, (0, common_1.Param)('caseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "getActivitySummary", null);
__decorate([
    (0, common_1.Get)('agent/:agentId'),
    __param(0, (0, common_1.Param)('agentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "findByAgent", null);
__decorate([
    (0, common_1.Get)('agent/:agentId/report'),
    __param(0, (0, common_1.Param)('agentId')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "getAgentActivityReport", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('log'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "logActivity", null);
__decorate([
    (0, common_1.Post)('log-call'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "logCall", null);
__decorate([
    (0, common_1.Post)('log-email'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "logEmail", null);
__decorate([
    (0, common_1.Post)('log-letter'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "logLetter", null);
__decorate([
    (0, common_1.Post)('log-payment'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "logPayment", null);
__decorate([
    (0, common_1.Post)('schedule-follow-up'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "scheduleFollowUp", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/complete-follow-up'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "markFollowUpCompleted", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionActivityController.prototype, "remove", null);
exports.CollectionActivityController = CollectionActivityController = __decorate([
    (0, common_1.Controller)('collection-activities'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [collection_activity_service_1.CollectionActivityService])
], CollectionActivityController);
//# sourceMappingURL=collection-activity.controller.js.map