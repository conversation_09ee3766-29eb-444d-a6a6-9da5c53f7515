"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const account_entity_1 = require("../entities/account.entity");
let AccountService = class AccountService {
    accountRepository;
    constructor(accountRepository) {
        this.accountRepository = accountRepository;
    }
    async create(createAccountDto) {
        const accountNumber = await this.generateAccountNumber(createAccountDto.type);
        const account = this.accountRepository.create({
            ...createAccountDto,
            accountNumber,
        });
        return this.accountRepository.save(account);
    }
    async findAll() {
        return this.accountRepository.find({
            relations: ['parentAccount', 'childAccounts'],
            order: { accountNumber: 'ASC' },
        });
    }
    async findOne(id) {
        const account = await this.accountRepository.findOne({
            where: { id },
            relations: ['parentAccount', 'childAccounts', 'debitTransactions', 'creditTransactions'],
        });
        if (!account) {
            throw new common_1.NotFoundException(`Account with ID ${id} not found`);
        }
        return account;
    }
    async findByType(type) {
        return this.accountRepository.find({
            where: { type, isActive: true },
            order: { accountNumber: 'ASC' },
        });
    }
    async findBySubType(subType) {
        return this.accountRepository.find({
            where: { subType, isActive: true },
            order: { accountNumber: 'ASC' },
        });
    }
    async update(id, updateAccountDto) {
        const account = await this.findOne(id);
        if (updateAccountDto.parentAccountId && updateAccountDto.parentAccountId === id) {
            throw new common_1.BadRequestException('Account cannot be its own parent');
        }
        Object.assign(account, updateAccountDto);
        return this.accountRepository.save(account);
    }
    async remove(id) {
        const account = await this.findOne(id);
        if (account.isSystemAccount) {
            throw new common_1.BadRequestException('Cannot delete system account');
        }
        if (account.debitTransactions?.length > 0 || account.creditTransactions?.length > 0) {
            throw new common_1.BadRequestException('Cannot delete account with transactions');
        }
        await this.accountRepository.remove(account);
    }
    async updateBalance(accountId, amount, isDebit) {
        const account = await this.findOne(accountId);
        if (isDebit) {
            account.debitBalance += amount;
        }
        else {
            account.creditBalance += amount;
        }
        switch (account.type) {
            case account_entity_1.AccountType.ASSET:
            case account_entity_1.AccountType.EXPENSE:
                account.balance = account.debitBalance - account.creditBalance;
                break;
            case account_entity_1.AccountType.LIABILITY:
            case account_entity_1.AccountType.EQUITY:
            case account_entity_1.AccountType.REVENUE:
                account.balance = account.creditBalance - account.debitBalance;
                break;
        }
        return this.accountRepository.save(account);
    }
    async getAccountHierarchy() {
        const accounts = await this.accountRepository.find({
            where: { parentAccountId: null },
            relations: ['childAccounts'],
            order: { accountNumber: 'ASC' },
        });
        return this.buildAccountTree(accounts);
    }
    async getTrialBalance(asOfDate) {
        const accounts = await this.accountRepository.find({
            where: { isActive: true },
            order: { accountNumber: 'ASC' },
        });
        const trialBalance = {
            asOfDate: asOfDate || new Date(),
            accounts: accounts.map(account => ({
                accountNumber: account.accountNumber,
                accountName: account.name,
                accountType: account.type,
                debitBalance: account.debitBalance,
                creditBalance: account.creditBalance,
                balance: account.balance,
            })),
            totalDebits: accounts.reduce((sum, acc) => sum + acc.debitBalance, 0),
            totalCredits: accounts.reduce((sum, acc) => sum + acc.creditBalance, 0),
        };
        return trialBalance;
    }
    async generateAccountNumber(type) {
        const prefix = this.getAccountPrefix(type);
        const lastAccount = await this.accountRepository.findOne({
            where: { type },
            order: { accountNumber: 'DESC' },
        });
        let nextNumber = 1;
        if (lastAccount) {
            const lastNumber = parseInt(lastAccount.accountNumber.substring(1));
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
    }
    getAccountPrefix(type) {
        switch (type) {
            case account_entity_1.AccountType.ASSET: return '1';
            case account_entity_1.AccountType.LIABILITY: return '2';
            case account_entity_1.AccountType.EQUITY: return '3';
            case account_entity_1.AccountType.REVENUE: return '4';
            case account_entity_1.AccountType.EXPENSE: return '5';
            default: return '9';
        }
    }
    async buildAccountTree(accounts) {
        for (const account of accounts) {
            if (account.childAccounts?.length > 0) {
                account.childAccounts = await this.buildAccountTree(account.childAccounts);
            }
        }
        return accounts;
    }
};
exports.AccountService = AccountService;
exports.AccountService = AccountService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.Account)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AccountService);
//# sourceMappingURL=account.service.js.map