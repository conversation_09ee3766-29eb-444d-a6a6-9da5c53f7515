import { PosSale } from './pos-sale.entity';
export declare enum DiscountType {
    PERCENTAGE = "percentage",
    FIXED_AMOUNT = "fixed_amount",
    BUY_X_GET_Y = "buy_x_get_y",
    BULK_DISCOUNT = "bulk_discount"
}
export declare enum DiscountScope {
    ITEM = "item",
    TRANSACTION = "transaction",
    CATEGORY = "category"
}
export declare class PosDiscount {
    id: string;
    saleId: string;
    sale: PosSale;
    name: string;
    type: DiscountType;
    scope: DiscountScope;
    amount: number;
    percentage: number;
    appliedBy: string;
    reason: string;
    promotionId: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
