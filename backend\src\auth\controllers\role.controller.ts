import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { RoleService } from '../services/role.service';
import { Role } from '../entities/role.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('roles')
@UseGuards(JwtAuthGuard)
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createRoleDto: Partial<Role>) {
    return this.roleService.create(createRoleDto);
  }

  @Get()
  async findAll(@Query('department') department?: string) {
    if (department) {
      return this.roleService.getRolesByDepartment(department);
    }
    return this.roleService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.roleService.findOne(id);
  }

  @Post('create-defaults')
  async createDefaultRoles() {
    return this.roleService.createDefaultRoles();
  }

  @Post('assign-default-permissions')
  async assignDefaultPermissions() {
    await this.roleService.assignDefaultPermissions();
    return { message: 'Default permissions assigned successfully' };
  }

  @Post(':id/clone')
  async cloneRole(
    @Param('id') id: string,
    @Body() cloneData: { name: string; description?: string },
  ) {
    return this.roleService.cloneRole(id, cloneData.name, cloneData.description);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateRoleDto: Partial<Role>,
  ) {
    return this.roleService.update(id, updateRoleDto);
  }

  @Patch(':id/permissions')
  async assignPermissions(
    @Param('id') id: string,
    @Body() permissionData: { permissionIds: string[] },
  ) {
    return this.roleService.assignPermissions(id, permissionData.permissionIds);
  }

  @Delete(':id/permissions')
  async removePermissions(
    @Param('id') id: string,
    @Body() permissionData: { permissionIds: string[] },
  ) {
    return this.roleService.removePermissions(id, permissionData.permissionIds);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.roleService.remove(id);
  }
}
