import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
import { Location } from './location.entity';
import { Stock } from './stock.entity';
export declare enum MovementType {
    RECEIPT = "receipt",
    ISSUE = "issue",
    TRANSFER = "transfer",
    ADJUSTMENT = "adjustment",
    RETURN = "return",
    DAMAGE = "damage",
    EXPIRY = "expiry",
    SALE = "sale",
    PURCHASE = "purchase",
    PRODUCTION = "production",
    CONSUMPTION = "consumption"
}
export declare enum MovementReason {
    PURCHASE_ORDER = "purchase_order",
    SALES_ORDER = "sales_order",
    STOCK_TRANSFER = "stock_transfer",
    STOCK_ADJUSTMENT = "stock_adjustment",
    PHYSICAL_COUNT = "physical_count",
    DAMAGE_WRITE_OFF = "damage_write_off",
    EXPIRY_WRITE_OFF = "expiry_write_off",
    CUSTOMER_RETURN = "customer_return",
    SUPPLIER_RETURN = "supplier_return",
    PRODUCTION_CONSUMPTION = "production_consumption",
    PRODUCTION_OUTPUT = "production_output",
    MANUAL_ADJUSTMENT = "manual_adjustment"
}
export declare class StockMovement {
    id: string;
    productId: string;
    product: Product;
    warehouseId: string;
    warehouse: Warehouse;
    locationId: string;
    location: Location;
    stockId: string;
    stock: Stock;
    type: MovementType;
    reason: MovementReason;
    quantity: number;
    quantityBefore: number;
    quantityAfter: number;
    unitCost: number;
    totalCost: number;
    currency: string;
    movementDate: Date;
    referenceNumber: string;
    relatedEntityType: string;
    relatedEntityId: string;
    batchNumber: string;
    serialNumber: string;
    expiryDate: Date;
    notes: string;
    performedBy: string;
    approvedBy: string;
    approvedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
