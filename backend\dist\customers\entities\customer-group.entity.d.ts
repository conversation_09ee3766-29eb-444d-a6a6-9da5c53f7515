import { Customer } from './customer.entity';
export declare enum GroupType {
    PRICING = "pricing",
    GEOGRAPHIC = "geographic",
    INDUSTRY = "industry",
    SIZE = "size",
    BEHAVIOR = "behavior",
    CUSTOM = "custom"
}
export declare class CustomerGroup {
    id: string;
    name: string;
    code: string;
    description: string;
    type: GroupType;
    discountPercentage: number;
    creditLimit: number;
    paymentTerms: number;
    pricingRules: any;
    benefits: string[];
    restrictions: string[];
    isActive: boolean;
    sortOrder: number;
    customers: Customer[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
