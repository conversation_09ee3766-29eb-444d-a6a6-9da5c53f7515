import { Employee } from './employee.entity';
import { LeaveType } from './leave-type.entity';
export declare enum LeaveStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    CANCELLED = "cancelled",
    TAKEN = "taken"
}
export declare class Leave {
    id: string;
    employeeId: string;
    employee: Employee;
    leaveTypeId: string;
    leaveType: LeaveType;
    startDate: Date;
    endDate: Date;
    daysRequested: number;
    daysApproved: number;
    status: LeaveStatus;
    reason: string;
    comments: string;
    approvalComments: string;
    approvedBy: string;
    approvedAt: Date;
    appliedDate: Date;
    isHalfDay: boolean;
    isEmergency: boolean;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
