import { CollectionActivityService } from '../services/collection-activity.service';
import { CollectionActivity, ActivityType } from '../entities/collection-activity.entity';
export declare class CollectionActivityController {
    private readonly collectionActivityService;
    constructor(collectionActivityService: CollectionActivityService);
    create(createActivityDto: Partial<CollectionActivity>): Promise<CollectionActivity>;
    findAll(): Promise<CollectionActivity[]>;
    getRecentActivities(limit?: string): Promise<CollectionActivity[]>;
    getScheduledFollowUps(): Promise<CollectionActivity[]>;
    findByCase(caseId: string): Promise<CollectionActivity[]>;
    getActivitySummary(caseId: string): Promise<any>;
    findByAgent(agentId: string): Promise<CollectionActivity[]>;
    getAgentActivityReport(agentId: string, startDate: string, endDate: string): Promise<any>;
    findOne(id: string): Promise<CollectionActivity>;
    logActivity(activityData: {
        caseId: string;
        type: ActivityType;
        description: string;
        performedBy?: string;
        outcome?: string;
    }): Promise<CollectionActivity>;
    logCall(callData: {
        caseId: string;
        duration: number;
        outcome: string;
        notes: string;
        performedBy?: string;
    }): Promise<CollectionActivity>;
    logEmail(emailData: {
        caseId: string;
        subject: string;
        outcome: string;
        performedBy?: string;
    }): Promise<CollectionActivity>;
    logLetter(letterData: {
        caseId: string;
        letterType: string;
        performedBy?: string;
    }): Promise<CollectionActivity>;
    logPayment(paymentData: {
        caseId: string;
        amount: number;
        paymentMethod: string;
        performedBy?: string;
    }): Promise<CollectionActivity>;
    scheduleFollowUp(followUpData: {
        caseId: string;
        followUpDate: Date;
        notes: string;
        performedBy?: string;
    }): Promise<CollectionActivity>;
    update(id: string, updateActivityDto: Partial<CollectionActivity>): Promise<CollectionActivity>;
    markFollowUpCompleted(id: string, completion: {
        outcome?: string;
    }): Promise<CollectionActivity>;
    remove(id: string): Promise<void>;
}
