import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum StrategyType {
  SOFT = 'soft',
  MEDIUM = 'medium',
  AGGRESSIVE = 'aggressive',
  LEGAL = 'legal',
  CUSTOM = 'custom',
}

@Entity('collection_strategies')
export class CollectionStrategy {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: StrategyType,
    default: StrategyType.MEDIUM,
  })
  type: StrategyType;

  @Column({ type: 'json' })
  rules: any; // Collection rules and workflow

  @Column({ type: 'json' })
  actions: any; // Automated actions

  @Column({ type: 'json', nullable: true })
  triggers: any; // Trigger conditions

  @Column({ type: 'int', default: 0 })
  maxContactAttempts: number;

  @Column({ type: 'int', default: 1 })
  contactFrequencyDays: number;

  @Column({ type: 'int', default: 90 })
  escalationDays: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isDefault: boolean;

  @Column()
  createdBy: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
