import { Repository } from 'typeorm';
import { CustomerCredit } from '../entities/customer-credit.entity';
import { Customer } from '../entities/customer.entity';
export declare class CustomerCreditService {
    private creditRepository;
    private customerRepository;
    constructor(creditRepository: Repository<CustomerCredit>, customerRepository: Repository<Customer>);
    create(creditData: Partial<CustomerCredit>): Promise<CustomerCredit>;
    findAll(): Promise<CustomerCredit[]>;
    findOne(id: string): Promise<CustomerCredit>;
    findByCustomer(customerId: string): Promise<CustomerCredit[]>;
    getLatestCreditAssessment(customerId: string): Promise<CustomerCredit | null>;
    createCreditAssessment(customerId: string, assessmentData: {
        creditScore?: number;
        creditLimit?: number;
        paymentTerms?: number;
        riskLevel?: string;
        notes?: string;
        assessedBy?: string;
    }): Promise<CustomerCredit>;
    updateCreditLimit(customerId: string, newLimit: number, reason: string, updatedBy?: string): Promise<CustomerCredit>;
    calculateCreditScore(customerId: string): Promise<number>;
    private calculatePaymentScore;
    private calculateUtilizationScore;
    private calculateRelationshipScore;
    private calculatePurchaseScore;
    private determineRiskLevel;
    private suggestCreditLimit;
    getCreditStatistics(): Promise<any>;
    getCustomersRequiringReview(): Promise<Customer[]>;
    scheduleCreditReview(customerId: string, reviewDate: Date, reason: string): Promise<CustomerCredit>;
}
