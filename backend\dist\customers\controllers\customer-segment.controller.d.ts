import { CustomerSegmentService } from '../services/customer-segment.service';
import { CustomerSegment } from '../entities/customer-segment.entity';
export declare class CustomerSegmentController {
    private readonly customerSegmentService;
    constructor(customerSegmentService: CustomerSegmentService);
    create(createSegmentDto: Partial<CustomerSegment>): Promise<CustomerSegment>;
    findAll(): Promise<CustomerSegment[]>;
    findOne(id: string): Promise<CustomerSegment>;
    getSegmentCustomers(id: string, page?: string, limit?: string): Promise<{
        customers: import("../entities/customer.entity").Customer[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    getSegmentAnalytics(id: string): Promise<any>;
    createHighValueSegment(data: {
        minSpent?: number;
    }): Promise<CustomerSegment>;
    createLoyaltySegment(data: {
        minPoints?: number;
    }): Promise<CustomerSegment>;
    createInactiveSegment(data: {
        daysSinceLastPurchase?: number;
    }): Promise<CustomerSegment>;
    createNewCustomerSegment(data: {
        daysSinceRegistration?: number;
    }): Promise<CustomerSegment>;
    update(id: string, updateSegmentDto: Partial<CustomerSegment>): Promise<CustomerSegment>;
    refreshSegment(id: string): Promise<{
        segmentId: string;
        customerCount: number;
        message: string;
    }>;
    activate(id: string): Promise<CustomerSegment>;
    deactivate(id: string): Promise<CustomerSegment>;
    refreshAllSegments(): Promise<{
        segmentId: string;
        customerCount: number;
    }[]>;
    remove(id: string): Promise<void>;
}
