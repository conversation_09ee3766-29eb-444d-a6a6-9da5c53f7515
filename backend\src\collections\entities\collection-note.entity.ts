import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CollectionCase } from './collection-case.entity';

export enum NoteType {
  GENERAL = 'general',
  CONTACT = 'contact',
  PAYMENT = 'payment',
  DISPUTE = 'dispute',
  LEGAL = 'legal',
  INTERNAL = 'internal',
  CUSTOMER = 'customer',
  SYSTEM = 'system',
}

@Entity('collection_notes')
export class CollectionNote {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  caseId: string;

  @ManyToOne(() => CollectionCase, collectionCase => collectionCase.notes)
  @JoinColumn({ name: 'caseId' })
  case: CollectionCase;

  @Column({
    type: 'enum',
    enum: NoteType,
    default: NoteType.GENERAL,
  })
  type: NoteType;

  @Column({ length: 255, nullable: true })
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column()
  createdBy: string;

  @Column({ default: false })
  isPrivate: boolean;

  @Column({ default: false })
  isPinned: boolean;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
