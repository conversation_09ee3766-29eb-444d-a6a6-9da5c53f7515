"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractTerm = exports.TermType = void 0;
const typeorm_1 = require("typeorm");
const contract_entity_1 = require("./contract.entity");
var TermType;
(function (TermType) {
    TermType["PAYMENT"] = "payment";
    TermType["DELIVERY"] = "delivery";
    TermType["QUALITY"] = "quality";
    TermType["PENALTY"] = "penalty";
    TermType["TERMINATION"] = "termination";
    TermType["CONFIDENTIALITY"] = "confidentiality";
    TermType["LIABILITY"] = "liability";
    TermType["FORCE_MAJEURE"] = "force_majeure";
    TermType["DISPUTE_RESOLUTION"] = "dispute_resolution";
    TermType["GENERAL"] = "general";
})(TermType || (exports.TermType = TermType = {}));
let ContractTerm = class ContractTerm {
    id;
    contractId;
    contract;
    type;
    title;
    content;
    sortOrder;
    isActive;
    metadata;
    createdAt;
    updatedAt;
};
exports.ContractTerm = ContractTerm;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ContractTerm.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ContractTerm.prototype, "contractId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => contract_entity_1.Contract, contract => contract.terms, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'contractId' }),
    __metadata("design:type", contract_entity_1.Contract)
], ContractTerm.prototype, "contract", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TermType,
    }),
    __metadata("design:type", String)
], ContractTerm.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ContractTerm.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ContractTerm.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], ContractTerm.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], ContractTerm.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ContractTerm.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ContractTerm.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ContractTerm.prototype, "updatedAt", void 0);
exports.ContractTerm = ContractTerm = __decorate([
    (0, typeorm_1.Entity)('contract_terms')
], ContractTerm);
//# sourceMappingURL=contract-term.entity.js.map