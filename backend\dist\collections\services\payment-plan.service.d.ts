import { Repository } from 'typeorm';
import { PaymentPlan, PaymentPlanStatus } from '../entities/payment-plan.entity';
import { PaymentPlanInstallment } from '../entities/payment-plan-installment.entity';
export declare class PaymentPlanService {
    private paymentPlanRepository;
    private installmentRepository;
    constructor(paymentPlanRepository: Repository<PaymentPlan>, installmentRepository: Repository<PaymentPlanInstallment>);
    create(planData: Partial<PaymentPlan>): Promise<PaymentPlan>;
    findAll(): Promise<PaymentPlan[]>;
    findOne(id: string): Promise<PaymentPlan>;
    update(id: string, updateData: Partial<PaymentPlan>): Promise<PaymentPlan>;
    remove(id: string): Promise<void>;
    findByCustomer(customerId: string): Promise<PaymentPlan[]>;
    findByStatus(status: PaymentPlanStatus): Promise<PaymentPlan[]>;
    createInstallments(planId: string, numberOfInstallments: number): Promise<PaymentPlanInstallment[]>;
    recordPayment(installmentId: string, amount: number, paymentDate: Date): Promise<PaymentPlanInstallment>;
    private updatePaymentPlanTotals;
    getPaymentPlanSummary(planId: string): Promise<any>;
    private getNextDueDate;
    private getNextDueAmount;
    getOverdueInstallments(): Promise<PaymentPlanInstallment[]>;
    getUpcomingInstallments(days?: number): Promise<PaymentPlanInstallment[]>;
    markPlanAsDefaulted(planId: string, reason: string): Promise<PaymentPlan>;
    getPaymentPlanStatistics(): Promise<any>;
}
