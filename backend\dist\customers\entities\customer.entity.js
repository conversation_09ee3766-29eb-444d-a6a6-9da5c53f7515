"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Customer = exports.CustomerTier = exports.CustomerStatus = exports.CustomerType = void 0;
const typeorm_1 = require("typeorm");
const customer_contact_entity_1 = require("./customer-contact.entity");
const customer_address_entity_1 = require("./customer-address.entity");
const customer_group_entity_1 = require("./customer-group.entity");
const customer_note_entity_1 = require("./customer-note.entity");
const customer_document_entity_1 = require("./customer-document.entity");
const customer_interaction_entity_1 = require("./customer-interaction.entity");
const customer_loyalty_entity_1 = require("./customer-loyalty.entity");
const customer_credit_entity_1 = require("./customer-credit.entity");
const customer_preference_entity_1 = require("./customer-preference.entity");
var CustomerType;
(function (CustomerType) {
    CustomerType["INDIVIDUAL"] = "individual";
    CustomerType["BUSINESS"] = "business";
    CustomerType["GOVERNMENT"] = "government";
    CustomerType["NON_PROFIT"] = "non_profit";
})(CustomerType || (exports.CustomerType = CustomerType = {}));
var CustomerStatus;
(function (CustomerStatus) {
    CustomerStatus["ACTIVE"] = "active";
    CustomerStatus["INACTIVE"] = "inactive";
    CustomerStatus["SUSPENDED"] = "suspended";
    CustomerStatus["BLACKLISTED"] = "blacklisted";
    CustomerStatus["PROSPECT"] = "prospect";
    CustomerStatus["LEAD"] = "lead";
})(CustomerStatus || (exports.CustomerStatus = CustomerStatus = {}));
var CustomerTier;
(function (CustomerTier) {
    CustomerTier["BRONZE"] = "bronze";
    CustomerTier["SILVER"] = "silver";
    CustomerTier["GOLD"] = "gold";
    CustomerTier["PLATINUM"] = "platinum";
    CustomerTier["DIAMOND"] = "diamond";
})(CustomerTier || (exports.CustomerTier = CustomerTier = {}));
let Customer = class Customer {
    id;
    customerNumber;
    type;
    status;
    tier;
    firstName;
    lastName;
    middleName;
    title;
    dateOfBirth;
    gender;
    companyName;
    legalName;
    taxId;
    registrationNumber;
    industry;
    employeeCount;
    annualRevenue;
    primaryEmail;
    primaryPhone;
    website;
    creditLimit;
    currentBalance;
    totalSpent;
    averageOrderValue;
    paymentTerms;
    currency;
    groupId;
    group;
    assignedTo;
    referredBy;
    firstPurchaseDate;
    lastPurchaseDate;
    lastContactDate;
    allowMarketing;
    allowEmail;
    allowSms;
    allowPhone;
    communicationPreferences;
    interests;
    tags;
    loyaltyPoints;
    storeCredit;
    discountPercentage;
    facebookProfile;
    twitterProfile;
    linkedinProfile;
    instagramProfile;
    description;
    profilePicture;
    customFields;
    contacts;
    addresses;
    notes;
    documents;
    interactions;
    loyaltyHistory;
    creditHistory;
    preferences;
    metadata;
    createdAt;
    updatedAt;
};
exports.Customer = Customer;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Customer.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], Customer.prototype, "customerNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CustomerType,
        default: CustomerType.INDIVIDUAL,
    }),
    __metadata("design:type", String)
], Customer.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CustomerStatus,
        default: CustomerStatus.PROSPECT,
    }),
    __metadata("design:type", String)
], Customer.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CustomerTier,
        default: CustomerTier.BRONZE,
    }),
    __metadata("design:type", String)
], Customer.prototype, "tier", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "firstName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "middleName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "dateOfBirth", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "gender", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "companyName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "legalName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "taxId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "registrationNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "industry", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], Customer.prototype, "employeeCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Customer.prototype, "annualRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "primaryEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "primaryPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "website", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Customer.prototype, "creditLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Customer.prototype, "currentBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Customer.prototype, "totalSpent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Customer.prototype, "averageOrderValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 30 }),
    __metadata("design:type", Number)
], Customer.prototype, "paymentTerms", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], Customer.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "groupId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => customer_group_entity_1.CustomerGroup, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'groupId' }),
    __metadata("design:type", customer_group_entity_1.CustomerGroup)
], Customer.prototype, "group", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "referredBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "firstPurchaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "lastPurchaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "lastContactDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Customer.prototype, "allowMarketing", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Customer.prototype, "allowEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Customer.prototype, "allowSms", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Customer.prototype, "allowPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Customer.prototype, "communicationPreferences", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Customer.prototype, "interests", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Customer.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Customer.prototype, "loyaltyPoints", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Customer.prototype, "storeCredit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Customer.prototype, "discountPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "facebookProfile", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "twitterProfile", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "linkedinProfile", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "instagramProfile", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "profilePicture", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Customer.prototype, "customFields", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_contact_entity_1.CustomerContact, contact => contact.customer, { cascade: true }),
    __metadata("design:type", Array)
], Customer.prototype, "contacts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_address_entity_1.CustomerAddress, address => address.customer, { cascade: true }),
    __metadata("design:type", Array)
], Customer.prototype, "addresses", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_note_entity_1.CustomerNote, note => note.customer),
    __metadata("design:type", Array)
], Customer.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_document_entity_1.CustomerDocument, document => document.customer),
    __metadata("design:type", Array)
], Customer.prototype, "documents", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_interaction_entity_1.CustomerInteraction, interaction => interaction.customer),
    __metadata("design:type", Array)
], Customer.prototype, "interactions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_loyalty_entity_1.CustomerLoyalty, loyalty => loyalty.customer),
    __metadata("design:type", Array)
], Customer.prototype, "loyaltyHistory", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_credit_entity_1.CustomerCredit, credit => credit.customer),
    __metadata("design:type", Array)
], Customer.prototype, "creditHistory", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_preference_entity_1.CustomerPreference, preference => preference.customer),
    __metadata("design:type", Array)
], Customer.prototype, "preferences", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Customer.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Customer.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Customer.prototype, "updatedAt", void 0);
exports.Customer = Customer = __decorate([
    (0, typeorm_1.Entity)('customers')
], Customer);
//# sourceMappingURL=customer.entity.js.map