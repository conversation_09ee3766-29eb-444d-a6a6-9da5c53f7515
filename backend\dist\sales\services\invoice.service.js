"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const invoice_entity_1 = require("../entities/invoice.entity");
const invoice_item_entity_1 = require("../entities/invoice-item.entity");
const customer_service_1 = require("./customer.service");
let InvoiceService = class InvoiceService {
    invoiceRepository;
    invoiceItemRepository;
    customerService;
    constructor(invoiceRepository, invoiceItemRepository, customerService) {
        this.invoiceRepository = invoiceRepository;
        this.invoiceItemRepository = invoiceItemRepository;
        this.customerService = customerService;
    }
    async create(createInvoiceDto, tenantId) {
        const invoiceNumber = await this.generateInvoiceNumber();
        const totals = this.calculateInvoiceTotals(createInvoiceDto);
        const invoice = this.invoiceRepository.create({
            ...createInvoiceDto,
            invoiceNumber,
            tenantId,
            ...totals,
        });
        const savedInvoice = await this.invoiceRepository.save(invoice);
        for (const itemDto of createInvoiceDto.items) {
            const item = this.invoiceItemRepository.create({
                ...itemDto,
                invoiceId: savedInvoice.id,
                tenantId,
                taxAmount: this.calculateItemTax(itemDto),
                lineTotal: (itemDto.unitPrice * itemDto.quantity) - (itemDto.discount || 0),
            });
            await this.invoiceItemRepository.save(item);
        }
        await this.customerService.updateBalance(createInvoiceDto.customerId, totals.totalAmount, tenantId);
        return this.findOne(savedInvoice.id, tenantId);
    }
    async findAll(tenantId) {
        return this.invoiceRepository.find({
            where: { tenantId },
            relations: ['customer', 'items'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id, tenantId) {
        const invoice = await this.invoiceRepository.findOne({
            where: { id, tenantId },
            relations: ['customer', 'items', 'payments', 'creditNotes'],
        });
        if (!invoice) {
            throw new common_1.NotFoundException('Invoice not found');
        }
        return invoice;
    }
    async update(id, updateInvoiceDto, tenantId) {
        const invoice = await this.findOne(id, tenantId);
        if (updateInvoiceDto.items) {
            await this.invoiceItemRepository.delete({ invoiceId: id });
            for (const itemDto of updateInvoiceDto.items) {
                const item = this.invoiceItemRepository.create({
                    ...itemDto,
                    invoiceId: id,
                    tenantId,
                    taxAmount: this.calculateItemTax(itemDto),
                    lineTotal: (itemDto.unitPrice * itemDto.quantity) - (itemDto.discount || 0),
                });
                await this.invoiceItemRepository.save(item);
            }
            const totals = this.calculateInvoiceTotals(updateInvoiceDto);
            Object.assign(invoice, totals);
        }
        Object.assign(invoice, updateInvoiceDto);
        return this.invoiceRepository.save(invoice);
    }
    async remove(id, tenantId) {
        const invoice = await this.findOne(id, tenantId);
        await this.invoiceRepository.remove(invoice);
    }
    async updateStatus(id, status, tenantId) {
        const invoice = await this.findOne(id, tenantId);
        invoice.status = status;
        return this.invoiceRepository.save(invoice);
    }
    async getInvoiceStats(tenantId) {
        const totalInvoices = await this.invoiceRepository.count({ where: { tenantId } });
        const paidInvoices = await this.invoiceRepository.count({
            where: { tenantId, status: 'paid' }
        });
        const overdueInvoices = await this.invoiceRepository.count({
            where: { tenantId, status: 'overdue' }
        });
        const result = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .select('SUM(invoice.totalAmount)', 'totalRevenue')
            .addSelect('SUM(invoice.remainingAmount)', 'pendingAmount')
            .where('invoice.tenantId = :tenantId', { tenantId })
            .getRawOne();
        return {
            totalInvoices,
            paidInvoices,
            overdueInvoices,
            totalRevenue: parseFloat(result.totalRevenue) || 0,
            pendingAmount: parseFloat(result.pendingAmount) || 0,
        };
    }
    calculateInvoiceTotals(invoiceDto) {
        const subtotal = invoiceDto.items.reduce((sum, item) => sum + (item.unitPrice * item.quantity) - (item.discount || 0), 0);
        const discountAmount = invoiceDto.discountType === 'percentage'
            ? (subtotal * (invoiceDto.discountValue || 0) / 100)
            : (invoiceDto.discountValue || 0);
        const taxAmount = invoiceDto.items.reduce((sum, item) => sum + this.calculateItemTax(item), 0);
        const totalAmount = subtotal - discountAmount - (invoiceDto.settlementAmount || 0) + taxAmount - (invoiceDto.advancePayment || 0);
        const remainingAmount = totalAmount;
        return {
            subtotal,
            discountAmount,
            taxAmount,
            totalAmount: Math.max(0, totalAmount),
            remainingAmount: Math.max(0, remainingAmount),
            paidAmount: 0,
        };
    }
    calculateItemTax(item) {
        const lineTotal = (item.unitPrice * item.quantity) - (item.discount || 0);
        if (item.taxType === '15%') {
            return lineTotal * 0.15;
        }
        return 0;
    }
    async generateInvoiceNumber() {
        const count = await this.invoiceRepository.count();
        const year = new Date().getFullYear();
        return `INV-${year}-${(count + 1).toString().padStart(3, '0')}`;
    }
};
exports.InvoiceService = InvoiceService;
exports.InvoiceService = InvoiceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoice_entity_1.Invoice)),
    __param(1, (0, typeorm_1.InjectRepository)(invoice_item_entity_1.InvoiceItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        customer_service_1.CustomerService])
], InvoiceService);
//# sourceMappingURL=invoice.service.js.map