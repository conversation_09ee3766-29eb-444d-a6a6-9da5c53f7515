"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankTransaction = exports.BankTransactionStatus = exports.BankTransactionType = void 0;
const typeorm_1 = require("typeorm");
const bank_account_entity_1 = require("./bank-account.entity");
var BankTransactionType;
(function (BankTransactionType) {
    BankTransactionType["DEPOSIT"] = "deposit";
    BankTransactionType["WITHDRAWAL"] = "withdrawal";
    BankTransactionType["TRANSFER"] = "transfer";
    BankTransactionType["FEE"] = "fee";
    BankTransactionType["INTEREST"] = "interest";
    BankTransactionType["CHECK"] = "check";
    BankTransactionType["ACH"] = "ach";
    BankTransactionType["WIRE"] = "wire";
    BankTransactionType["ATM"] = "atm";
    BankTransactionType["DEBIT_CARD"] = "debit_card";
    BankTransactionType["CREDIT_CARD"] = "credit_card";
    BankTransactionType["OTHER"] = "other";
})(BankTransactionType || (exports.BankTransactionType = BankTransactionType = {}));
var BankTransactionStatus;
(function (BankTransactionStatus) {
    BankTransactionStatus["PENDING"] = "pending";
    BankTransactionStatus["CLEARED"] = "cleared";
    BankTransactionStatus["CANCELLED"] = "cancelled";
    BankTransactionStatus["RETURNED"] = "returned";
    BankTransactionStatus["RECONCILED"] = "reconciled";
})(BankTransactionStatus || (exports.BankTransactionStatus = BankTransactionStatus = {}));
let BankTransaction = class BankTransaction {
    id;
    bankAccountId;
    bankAccount;
    transactionId;
    type;
    status;
    transactionDate;
    valueDate;
    amount;
    runningBalance;
    description;
    reference;
    checkNumber;
    payee;
    category;
    relatedTransactionId;
    isReconciled;
    reconciledDate;
    reconciledBy;
    bankData;
    memo;
    tags;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.BankTransaction = BankTransaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], BankTransaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BankTransaction.prototype, "bankAccountId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => bank_account_entity_1.BankAccount, account => account.transactions),
    (0, typeorm_1.JoinColumn)({ name: 'bankAccountId' }),
    __metadata("design:type", bank_account_entity_1.BankAccount)
], BankTransaction.prototype, "bankAccount", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "transactionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BankTransactionType,
    }),
    __metadata("design:type", String)
], BankTransaction.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BankTransactionStatus,
        default: BankTransactionStatus.PENDING,
    }),
    __metadata("design:type", String)
], BankTransaction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], BankTransaction.prototype, "transactionDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], BankTransaction.prototype, "valueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], BankTransaction.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BankTransaction.prototype, "runningBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], BankTransaction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "reference", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "checkNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "payee", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "relatedTransactionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], BankTransaction.prototype, "isReconciled", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], BankTransaction.prototype, "reconciledDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "reconciledBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], BankTransaction.prototype, "bankData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], BankTransaction.prototype, "memo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], BankTransaction.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], BankTransaction.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], BankTransaction.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], BankTransaction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], BankTransaction.prototype, "updatedAt", void 0);
exports.BankTransaction = BankTransaction = __decorate([
    (0, typeorm_1.Entity)('finance_bank_transactions')
], BankTransaction);
//# sourceMappingURL=bank-transaction.entity.js.map