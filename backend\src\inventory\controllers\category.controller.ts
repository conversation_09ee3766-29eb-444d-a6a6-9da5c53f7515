import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { CategoryService } from '../services/category.service';
import { Category } from '../entities/category.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('categories')
@UseGuards(JwtAuthGuard)
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createCategoryDto: Partial<Category>) {
    return this.categoryService.create(createCategoryDto);
  }

  @Get()
  async findAll() {
    return this.categoryService.findAll();
  }

  @Get('tree')
  async getCategoryTree() {
    return this.categoryService.getCategoryTree();
  }

  @Get('root')
  async findRootCategories() {
    return this.categoryService.findRootCategories();
  }

  @Get('statistics')
  async getStatistics() {
    return this.categoryService.getCategoryStatistics();
  }

  @Get('product-count')
  async getProductCountByCategory() {
    return this.categoryService.getProductCountByCategory();
  }

  @Get('search')
  async searchCategories(@Query('q') searchTerm: string) {
    return this.categoryService.searchCategories(searchTerm);
  }

  @Get('parent/:parentId')
  async findByParent(@Param('parentId') parentId: string) {
    return this.categoryService.findByParent(parentId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.categoryService.findOne(id);
  }

  @Get(':id/path')
  async getCategoryPath(@Param('id') id: string) {
    const path = await this.categoryService.getCategoryPath(id);
    return { path };
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCategoryDto: Partial<Category>,
  ) {
    return this.categoryService.update(id, updateCategoryDto);
  }

  @Patch(':id/move')
  async moveCategory(
    @Param('id') id: string,
    @Body() moveData: { newParentId: string | null },
  ) {
    return this.categoryService.moveCategory(id, moveData.newParentId);
  }

  @Post('generate-code')
  async generateCategoryCode(@Body() data: { name: string }) {
    const code = await this.categoryService.generateCategoryCode(data.name);
    return { code };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.categoryService.remove(id);
  }
}
