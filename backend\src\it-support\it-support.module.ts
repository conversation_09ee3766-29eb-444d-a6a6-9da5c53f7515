import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { SupportTicket } from './entities/support-ticket.entity';
import { TicketComment } from './entities/ticket-comment.entity';
import { TicketAttachment } from './entities/ticket-attachment.entity';
import { KnowledgeBase } from './entities/knowledge-base.entity';
import { FAQ } from './entities/faq.entity';
import { Asset } from './entities/asset.entity';
import { AssetMaintenance } from './entities/asset-maintenance.entity';
import { SoftwareLicense } from './entities/software-license.entity';
import { ITRequest } from './entities/it-request.entity';
import { ChangeRequest } from './entities/change-request.entity';
import { Incident } from './entities/incident.entity';
import { ServiceCatalog } from './entities/service-catalog.entity';

// Services
import { SupportTicketService } from './services/support-ticket.service';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { AssetService } from './services/asset.service';
import { SoftwareLicenseService } from './services/software-license.service';
import { ITRequestService } from './services/it-request.service';
import { ChangeRequestService } from './services/change-request.service';
import { IncidentService } from './services/incident.service';
import { ITReportService } from './services/it-report.service';

// Controllers
import { SupportTicketController } from './controllers/support-ticket.controller';
import { KnowledgeBaseController } from './controllers/knowledge-base.controller';
import { AssetController } from './controllers/asset.controller';
import { SoftwareLicenseController } from './controllers/software-license.controller';
import { ITRequestController } from './controllers/it-request.controller';
import { ChangeRequestController } from './controllers/change-request.controller';
import { IncidentController } from './controllers/incident.controller';
import { ITReportController } from './controllers/it-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SupportTicket,
      TicketComment,
      TicketAttachment,
      KnowledgeBase,
      FAQ,
      Asset,
      AssetMaintenance,
      SoftwareLicense,
      ITRequest,
      ChangeRequest,
      Incident,
      ServiceCatalog,
    ]),
  ],
  controllers: [
    SupportTicketController,
    KnowledgeBaseController,
    AssetController,
    SoftwareLicenseController,
    ITRequestController,
    ChangeRequestController,
    IncidentController,
    ITReportController,
  ],
  providers: [
    SupportTicketService,
    KnowledgeBaseService,
    AssetService,
    SoftwareLicenseService,
    ITRequestService,
    ChangeRequestService,
    IncidentService,
    ITReportService,
  ],
  exports: [
    SupportTicketService,
    KnowledgeBaseService,
    AssetService,
    SoftwareLicenseService,
    ITRequestService,
    ChangeRequestService,
    IncidentService,
    ITReportService,
  ],
})
export class ItSupportModule {}
