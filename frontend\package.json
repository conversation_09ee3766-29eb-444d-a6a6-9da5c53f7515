{"name": "ultimate-erp-frontend", "private": true, "version": "1.0.0", "description": "The World's Most Advanced ERP System Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@tanstack/react-query": "^5.77.0", "antd": "^5.25.2", "axios": "^1.9.0", "chart.js": "^4.4.9", "dayjs": "^1.11.13", "i18next": "^25.2.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-router-dom": "^7.6.0", "sqlite3": "^5.1.7", "styled-components": "^6.1.18", "xlsx": "^0.18.5", "tailwindcss": "^3.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "lucide-react": "^0.263.1", "framer-motion": "^10.18.0", "react-hot-toast": "^2.4.1", "crypto-js": "^4.2.0", "jose": "^5.2.0", "@radix-ui/react-slot": "^1.0.2", "@tensorflow/tfjs": "^4.15.0", "ml-matrix": "^6.10.4", "simple-statistics": "^7.8.3", "socket.io-client": "^4.7.4", "pusher-js": "^8.4.0-rc2", "react-beautiful-dnd": "^13.1.1", "react-grid-layout": "^1.4.4", "react-dropzone": "^14.2.3", "sonner": "^1.3.1", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "@types/crypto-js": "^4.2.1", "@tanstack/react-table": "^8.10.0", "recharts": "^2.15.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "@types/d3": "^7.4.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-grid-layout": "^1.3.5", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@testing-library/react": "^14.1.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@storybook/addon-essentials": "^7.6.0", "@storybook/addon-interactions": "^7.6.0", "@storybook/addon-links": "^7.6.0", "@storybook/blocks": "^7.6.0", "@storybook/react": "^7.6.0", "@storybook/react-vite": "^7.6.0", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "husky": "^8.0.3", "lint-staged": "^15.2.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}