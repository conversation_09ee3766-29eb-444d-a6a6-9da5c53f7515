"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BenefitController = void 0;
const common_1 = require("@nestjs/common");
const benefit_service_1 = require("../services/benefit.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let BenefitController = class BenefitController {
    benefitService;
    constructor(benefitService) {
        this.benefitService = benefitService;
    }
    create(createBenefitDto) {
        return this.benefitService.create(createBenefitDto);
    }
    findAll() {
        return this.benefitService.findAll();
    }
    findOne(id) {
        return this.benefitService.findOne(id);
    }
    enrollEmployee(enrollDto) {
        return this.benefitService.enrollEmployee(enrollDto.employeeId, enrollDto.benefitId, enrollDto.enrollmentData);
    }
    getEmployeeBenefits(employeeId) {
        return this.benefitService.getEmployeeBenefits(employeeId);
    }
};
exports.BenefitController = BenefitController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], BenefitController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BenefitController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BenefitController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('enroll'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], BenefitController.prototype, "enrollEmployee", null);
__decorate([
    (0, common_1.Get)('employee/:employeeId'),
    __param(0, (0, common_1.Param)('employeeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BenefitController.prototype, "getEmployeeBenefits", null);
exports.BenefitController = BenefitController = __decorate([
    (0, common_1.Controller)('hr/benefits'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [benefit_service_1.BenefitService])
], BenefitController);
//# sourceMappingURL=benefit.controller.js.map