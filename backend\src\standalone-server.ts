import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>, Controller, Get } from '@nestjs/common';
import { ValidationPipe } from '@nestjs/common';

// Simple business metrics controller
@Controller('analytics')
class AnalyticsController {
  @Get('business-metrics')
  getBusinessMetrics() {
    // Mock data for demonstration
    return {
      totalRevenue: 2500000,
      totalExpenses: 1800000,
      netProfit: 700000,
      profitMargin: 28,
      totalCustomers: 1250,
      activeCustomers: 980,
      customerGrowthRate: 15.5,
      totalOrders: 3420,
      averageOrderValue: 731.58,
      orderGrowthRate: 12.3,
      totalEmployees: 85,
      employeeUtilization: 87.5,
      totalProjects: 45,
      activeProjects: 28,
      projectCompletionRate: 92.5,
      inventoryValue: 450000,
      inventoryTurnover: 6.2,
      cashFlow: 320000,
      accountsReceivable: 180000,
      accountsPayable: 95000,
      currentRatio: 2.1,
      quickRatio: 1.8,
      debtToEquity: 0.35,
      returnOnAssets: 18.5,
      returnOnEquity: 24.2,
      grossMargin: 45.8,
      operatingMargin: 32.1,
      salesGrowthRate: 18.7,
      marketShare: 12.5,
      customerSatisfactionScore: 4.6,
      employeeSatisfactionScore: 4.2,
      systemUptime: 99.8,
      dataAccuracy: 98.5,
      complianceScore: 95.2,
      riskScore: 15.3,
      lastUpdated: new Date().toISOString(),
      period: 'Q4 2024',
      currency: 'USD'
    };
  }

  @Get('health')
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'ZaidanOne Analytics API',
      version: '1.0.0'
    };
  }

  @Get('ai-insights')
  getAIInsights() {
    return {
      insights: [
        "📈 Revenue is trending upward with 85% confidence for next month based on seasonal patterns",
        "⚠️ 23 customers are at high risk of churning - consider targeted retention campaigns",
        "🎯 Product A is your top performer with 40% of total sales - consider expanding this line",
        "📦 8 products need reordering to avoid stockouts within the next 2 weeks",
        "💡 Office supplies expense anomaly detected - 150% above normal, review recent purchases",
        "🔍 Customer acquisition cost decreased by 12% this quarter - marketing efficiency improved",
        "📊 Inventory turnover rate is optimal at 6.2x annually",
        "💰 Cash flow positive for 6 consecutive months - consider expansion opportunities"
      ],
      dashboardData: {
        revenue: {
          current: 125000,
          previous: 118000,
          forecast: {
            value: 135000,
            confidence: 0.85,
            trend: 'up',
            factors: ['Seasonal trends', 'Marketing campaigns', 'Product launches', 'Customer retention']
          }
        },
        customers: {
          total: 1250,
          new: 45,
          churnRisk: 23
        },
        sales: {
          total: 89,
          growth: 12.5,
          topProducts: [
            { name: 'Product A', sales: 25000 },
            { name: 'Product B', sales: 18000 },
            { name: 'Product C', sales: 15000 }
          ]
        },
        inventory: {
          lowStock: 12,
          outOfStock: 3,
          reorderAlerts: 8
        },
        anomalies: [
          {
            type: 'expense',
            description: 'Unusual spike in office supplies expense - 150% above normal',
            severity: 'medium'
          },
          {
            type: 'sales',
            description: 'Unexpected drop in Product D sales - down 25% from last month',
            severity: 'high'
          },
          {
            type: 'customer',
            description: 'Higher than normal customer support tickets in electronics category',
            severity: 'low'
          }
        ]
      },
      lastUpdated: new Date().toISOString()
    };
  }
}

// Simple root controller
@Controller()
class AppController {
  @Get()
  getHello() {
    return {
      message: 'ZaidanOne Management System API',
      version: '1.0.0',
      status: 'running',
      endpoints: {
        businessMetrics: '/api/analytics/business-metrics',
        aiInsights: '/api/analytics/ai-insights',
        health: '/api/analytics/health',
        docs: '/api/docs'
      }
    };
  }
}

// Minimal app module
@Module({
  controllers: [AppController, AnalyticsController],
})
class StandaloneAppModule {}

async function bootstrap() {
  const app = await NestFactory.create(StandaloneAppModule);

  // Enable CORS
  app.enableCors({
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // API prefix
  app.setGlobalPrefix('api');

  const port = process.env.PORT || 3000;
  await app.listen(port);

  console.log(`🚀 Standalone Backend server running on: http://localhost:${port}`);
  console.log(`🔧 Business Metrics Endpoint: http://localhost:${port}/api/analytics/business-metrics`);
  console.log(`❤️  Health Check: http://localhost:${port}/api/analytics/health`);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
