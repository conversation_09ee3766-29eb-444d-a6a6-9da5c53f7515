import { Repository } from 'typeorm';
import { AuditReport } from '../entities/audit-report.entity';
import { AuditFinding } from '../entities/audit-finding.entity';
import { CreateAuditReportDto } from '../dto/create-audit-report.dto';
import { UpdateAuditReportDto } from '../dto/update-audit-report.dto';
import { AuditReportsQueryDto } from '../dto/audit-reports-query.dto';
export declare class AuditReportsService {
    private auditReportRepository;
    private auditFindingRepository;
    constructor(auditReportRepository: Repository<AuditReport>, auditFindingRepository: Repository<AuditFinding>);
    getAuditReports(companyId: string, query: AuditReportsQueryDto): Promise<{
        reports: {
            totalFindings: number;
            criticalFindings: number;
            findings: AuditFinding[];
            id: string;
            companyId: string;
            year: number;
            quarter: number;
            reportType: string;
            status: string;
            department: string;
            scope: string;
            auditCategory: string;
            regulatoryFramework: string[];
            riskLevel: string;
            auditor: string;
            auditScope: string;
            objectives: string;
            methodology: string;
            executiveSummary: string;
            complianceScore: number;
            highFindings: number;
            mediumFindings: number;
            lowFindings: number;
            recommendations: number;
            plannedStartDate: Date;
            plannedEndDate: Date;
            actualStartDate: Date;
            actualEndDate: Date;
            completedDate: Date;
            auditTeam: {
                leadAuditor: string;
                members: string[];
                externalAuditors?: string[];
            };
            auditCriteria: {
                standards: string[];
                regulations: string[];
                policies: string[];
            };
            riskAssessment: {
                inherentRisk: string;
                controlRisk: string;
                detectionRisk: string;
                overallRisk: string;
            };
            samplingMethod: {
                type: string;
                size: number;
                criteria: string;
            };
            limitations: string;
            conclusion: string;
            managementResponse: string;
            attachments: {
                fileName: string;
                fileUrl: string;
                fileType: string;
                uploadDate: Date;
            }[];
            distributionList: {
                name: string;
                email: string;
                role: string;
            }[];
            nextAuditDate: Date;
            followUpActions: {
                action: string;
                responsible: string;
                dueDate: Date;
                status: string;
            }[];
            financialData: {
                totalIncome: number;
                totalExpenses: number;
                netResult: number;
                auditedTransactions: number;
                discrepancies: number;
                materialMisstatements: number;
                departmentBreakdown: {
                    department: string;
                    income: number;
                    expenses: number;
                    accuracy: number;
                }[];
                incomeVerification: {
                    revenueRecognitionCompliant: boolean;
                    invoiceMatchingVerified: boolean;
                    cashReceiptsReconciled: boolean;
                    timingDifferences: number;
                };
                expenseVerification: {
                    purchaseOrderMatching: boolean;
                    payrollCalculationsVerified: boolean;
                    accrualProceduresCompliant: boolean;
                    unauthorizedExpenses: number;
                };
            };
            specializedData: {
                bsaAmlData?: {
                    suspiciousActivityReports: number;
                    currencyTransactionReports: number;
                    customerDueDiligenceReviews: number;
                    sanctionsScreeningResults: number;
                    complianceViolations: number;
                    sarFilingTimeliness: number;
                    ctrAccuracy: number;
                    kycCompleteness: number;
                };
                creditData?: {
                    loansReviewed: number;
                    creditRiskRating: string;
                    portfolioQuality: number;
                    allowanceAdequacy: number;
                    underwritingCompliance: number;
                    concentrationRisk: number;
                    creditPolicyCompliance: number;
                    loanDocumentationScore: number;
                };
                itSecurityData?: {
                    vulnerabilitiesFound: number;
                    securityIncidents: number;
                    complianceGaps: number;
                    penetrationTestResults: string;
                    dataBreachRisk: string;
                    accessControlsScore: number;
                    encryptionCompliance: number;
                    backupRecoveryScore: number;
                };
                operationsData?: {
                    processEfficiency: number;
                    controlDeficiencies: number;
                    procedureCompliance: number;
                    staffingAdequacy: number;
                    branchOperationsScore: number;
                    cashManagementScore: number;
                    customerServiceScore: number;
                };
                trustData?: {
                    fiduciaryCompliance: number;
                    investmentPolicyAdherence: number;
                    clientReportingTimeliness: number;
                    regulatoryFilingCompleteness: number;
                    assetSafeguarding: number;
                    conflictOfInterestManagement: number;
                };
                soxData?: {
                    internalControlsEffectiveness: number;
                    financialReportingAccuracy: number;
                    managementAssessmentScore: number;
                    auditCommitteeOversight: number;
                    disclosureControlsScore: number;
                    fdiciaCertificationStatus: string;
                };
                almData?: {
                    interestRateRiskScore: number;
                    liquidityRiskScore: number;
                    capitalAdequacyRatio: number;
                    assetQualityScore: number;
                    earningsStability: number;
                    managementQuality: number;
                };
            };
            createdBy: string;
            updatedBy: string;
            createdDate: Date;
            updatedDate: Date;
            company: import("../../company/entities/company.entity").Company;
            creator: import("../../user/entities/user.entity").User;
            updater: import("../../user/entities/user.entity").User;
        }[];
        findings: AuditFinding[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        metrics: {
            totalReports: number;
            completedReports: number;
            pendingReports: number;
            averageComplianceScore: number;
            totalFindings: number;
            resolvedFindings: number;
            criticalFindings: number;
            improvementTrend: number;
            financialMetrics: {
                totalAuditedIncome: number;
                totalAuditedExpenses: number;
                totalDiscrepancies: number;
                materialMisstatements: number;
                financialAccuracy: number;
                departmentCoverage: number;
            };
            financialInstitutionMetrics: {
                bsaAmlCompliance: number;
                creditRiskScore: number;
                operationalRiskLevel: string;
                itSecurityScore: number;
                regulatoryViolations: number;
                totalSuspiciousActivities: number;
                networkVulnerabilities: number;
                trustOperationsCompliance: number;
                soxComplianceScore: number;
                enterpriseRiskRating: string;
            };
        };
    }>;
    getAuditMetrics(companyId: string, year?: number): Promise<{
        totalReports: number;
        completedReports: number;
        pendingReports: number;
        averageComplianceScore: number;
        totalFindings: number;
        resolvedFindings: number;
        criticalFindings: number;
        improvementTrend: number;
        financialMetrics: {
            totalAuditedIncome: number;
            totalAuditedExpenses: number;
            totalDiscrepancies: number;
            materialMisstatements: number;
            financialAccuracy: number;
            departmentCoverage: number;
        };
        financialInstitutionMetrics: {
            bsaAmlCompliance: number;
            creditRiskScore: number;
            operationalRiskLevel: string;
            itSecurityScore: number;
            regulatoryViolations: number;
            totalSuspiciousActivities: number;
            networkVulnerabilities: number;
            trustOperationsCompliance: number;
            soxComplianceScore: number;
            enterpriseRiskRating: string;
        };
    }>;
    getAuditReportById(companyId: string, id: string): Promise<{
        totalFindings: number;
        criticalFindings: number;
        findings: AuditFinding[];
        id: string;
        companyId: string;
        year: number;
        quarter: number;
        reportType: string;
        status: string;
        department: string;
        scope: string;
        auditCategory: string;
        regulatoryFramework: string[];
        riskLevel: string;
        auditor: string;
        auditScope: string;
        objectives: string;
        methodology: string;
        executiveSummary: string;
        complianceScore: number;
        highFindings: number;
        mediumFindings: number;
        lowFindings: number;
        recommendations: number;
        plannedStartDate: Date;
        plannedEndDate: Date;
        actualStartDate: Date;
        actualEndDate: Date;
        completedDate: Date;
        auditTeam: {
            leadAuditor: string;
            members: string[];
            externalAuditors?: string[];
        };
        auditCriteria: {
            standards: string[];
            regulations: string[];
            policies: string[];
        };
        riskAssessment: {
            inherentRisk: string;
            controlRisk: string;
            detectionRisk: string;
            overallRisk: string;
        };
        samplingMethod: {
            type: string;
            size: number;
            criteria: string;
        };
        limitations: string;
        conclusion: string;
        managementResponse: string;
        attachments: {
            fileName: string;
            fileUrl: string;
            fileType: string;
            uploadDate: Date;
        }[];
        distributionList: {
            name: string;
            email: string;
            role: string;
        }[];
        nextAuditDate: Date;
        followUpActions: {
            action: string;
            responsible: string;
            dueDate: Date;
            status: string;
        }[];
        financialData: {
            totalIncome: number;
            totalExpenses: number;
            netResult: number;
            auditedTransactions: number;
            discrepancies: number;
            materialMisstatements: number;
            departmentBreakdown: {
                department: string;
                income: number;
                expenses: number;
                accuracy: number;
            }[];
            incomeVerification: {
                revenueRecognitionCompliant: boolean;
                invoiceMatchingVerified: boolean;
                cashReceiptsReconciled: boolean;
                timingDifferences: number;
            };
            expenseVerification: {
                purchaseOrderMatching: boolean;
                payrollCalculationsVerified: boolean;
                accrualProceduresCompliant: boolean;
                unauthorizedExpenses: number;
            };
        };
        specializedData: {
            bsaAmlData?: {
                suspiciousActivityReports: number;
                currencyTransactionReports: number;
                customerDueDiligenceReviews: number;
                sanctionsScreeningResults: number;
                complianceViolations: number;
                sarFilingTimeliness: number;
                ctrAccuracy: number;
                kycCompleteness: number;
            };
            creditData?: {
                loansReviewed: number;
                creditRiskRating: string;
                portfolioQuality: number;
                allowanceAdequacy: number;
                underwritingCompliance: number;
                concentrationRisk: number;
                creditPolicyCompliance: number;
                loanDocumentationScore: number;
            };
            itSecurityData?: {
                vulnerabilitiesFound: number;
                securityIncidents: number;
                complianceGaps: number;
                penetrationTestResults: string;
                dataBreachRisk: string;
                accessControlsScore: number;
                encryptionCompliance: number;
                backupRecoveryScore: number;
            };
            operationsData?: {
                processEfficiency: number;
                controlDeficiencies: number;
                procedureCompliance: number;
                staffingAdequacy: number;
                branchOperationsScore: number;
                cashManagementScore: number;
                customerServiceScore: number;
            };
            trustData?: {
                fiduciaryCompliance: number;
                investmentPolicyAdherence: number;
                clientReportingTimeliness: number;
                regulatoryFilingCompleteness: number;
                assetSafeguarding: number;
                conflictOfInterestManagement: number;
            };
            soxData?: {
                internalControlsEffectiveness: number;
                financialReportingAccuracy: number;
                managementAssessmentScore: number;
                auditCommitteeOversight: number;
                disclosureControlsScore: number;
                fdiciaCertificationStatus: string;
            };
            almData?: {
                interestRateRiskScore: number;
                liquidityRiskScore: number;
                capitalAdequacyRatio: number;
                assetQualityScore: number;
                earningsStability: number;
                managementQuality: number;
            };
        };
        createdBy: string;
        updatedBy: string;
        createdDate: Date;
        updatedDate: Date;
        company: import("../../company/entities/company.entity").Company;
        creator: import("../../user/entities/user.entity").User;
        updater: import("../../user/entities/user.entity").User;
    } | null>;
    createAuditReport(companyId: string, userId: string, createAuditReportDto: CreateAuditReportDto): Promise<AuditReport>;
    updateAuditReport(companyId: string, id: string, userId: string, updateAuditReportDto: UpdateAuditReportDto): Promise<AuditReport>;
    deleteAuditReport(companyId: string, id: string): Promise<import("typeorm").DeleteResult>;
    exportAuditReport(companyId: string, id: string, format: 'pdf' | 'excel'): Promise<{
        fileName: string;
        downloadUrl: string;
        format: "pdf" | "excel";
        size: string;
        generatedAt: Date;
    }>;
    getAuditFindings(companyId: string, reportId: string): Promise<AuditFinding[]>;
    addAuditFinding(companyId: string, reportId: string, userId: string, findingData: any): Promise<AuditFinding[]>;
    updateAuditReportStatus(companyId: string, id: string, userId: string, status: string): Promise<AuditReport>;
    getComplianceTrends(companyId: string, years?: number): Promise<{
        year: number;
        averageComplianceScore: number;
        totalReports: number;
        completedReports: number;
    }[]>;
}
