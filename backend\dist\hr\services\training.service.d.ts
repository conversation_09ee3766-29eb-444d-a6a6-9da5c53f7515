import { Repository } from 'typeorm';
import { Training } from '../entities/training.entity';
export declare class TrainingService {
    private trainingRepository;
    constructor(trainingRepository: Repository<Training>);
    create(createTrainingDto: any): Promise<Training>;
    findAll(filters?: any): Promise<Training[]>;
    findOne(id: string): Promise<Training>;
    update(id: string, updateTrainingDto: any): Promise<Training>;
    completeTraining(id: string, score?: number, feedback?: string): Promise<Training>;
}
