import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PosSale } from './pos-sale.entity';

export enum TaxType {
  SALES_TAX = 'sales_tax',
  VAT = 'vat',
  GST = 'gst',
  EXCISE_TAX = 'excise_tax',
  LUXURY_TAX = 'luxury_tax',
  ENVIRONMENTAL_TAX = 'environmental_tax',
}

@Entity('pos_taxes')
export class PosTax {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  saleId: string;

  @ManyToOne(() => PosSale, sale => sale.taxes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'saleId' })
  sale: PosSale;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: TaxType,
    default: TaxType.SALES_TAX,
  })
  type: TaxType;

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  rate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  taxableAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  taxAmount: number;

  @Column({ default: false })
  isInclusive: boolean;

  @Column({ length: 100, nullable: true })
  jurisdiction: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
