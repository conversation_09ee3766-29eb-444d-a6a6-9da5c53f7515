"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ITRequest = exports.RequestPriority = exports.RequestStatus = exports.ITRequestType = void 0;
const typeorm_1 = require("typeorm");
var ITRequestType;
(function (ITRequestType) {
    ITRequestType["NEW_USER_SETUP"] = "new_user_setup";
    ITRequestType["USER_TERMINATION"] = "user_termination";
    ITRequestType["ACCESS_REQUEST"] = "access_request";
    ITRequestType["SOFTWARE_INSTALLATION"] = "software_installation";
    ITRequestType["HARDWARE_REQUEST"] = "hardware_request";
    ITRequestType["PASSWORD_RESET"] = "password_reset";
    ITRequestType["EMAIL_SETUP"] = "email_setup";
    ITRequestType["PHONE_SETUP"] = "phone_setup";
    ITRequestType["TRAINING"] = "training";
    ITRequestType["OTHER"] = "other";
})(ITRequestType || (exports.ITRequestType = ITRequestType = {}));
var RequestStatus;
(function (RequestStatus) {
    RequestStatus["SUBMITTED"] = "submitted";
    RequestStatus["APPROVED"] = "approved";
    RequestStatus["REJECTED"] = "rejected";
    RequestStatus["IN_PROGRESS"] = "in_progress";
    RequestStatus["COMPLETED"] = "completed";
    RequestStatus["CANCELLED"] = "cancelled";
})(RequestStatus || (exports.RequestStatus = RequestStatus = {}));
var RequestPriority;
(function (RequestPriority) {
    RequestPriority["LOW"] = "low";
    RequestPriority["MEDIUM"] = "medium";
    RequestPriority["HIGH"] = "high";
    RequestPriority["URGENT"] = "urgent";
})(RequestPriority || (exports.RequestPriority = RequestPriority = {}));
let ITRequest = class ITRequest {
    id;
    requestNumber;
    type;
    status;
    priority;
    title;
    description;
    requesterId;
    requesterName;
    requesterEmail;
    assignedTo;
    requestedDate;
    dueDate;
    approvedAt;
    approvedBy;
    completedAt;
    approvalNotes;
    completionNotes;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.ITRequest = ITRequest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ITRequest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], ITRequest.prototype, "requestNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ITRequestType,
    }),
    __metadata("design:type", String)
], ITRequest.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RequestStatus,
        default: RequestStatus.SUBMITTED,
    }),
    __metadata("design:type", String)
], ITRequest.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RequestPriority,
        default: RequestPriority.MEDIUM,
    }),
    __metadata("design:type", String)
], ITRequest.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ITRequest.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ITRequest.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ITRequest.prototype, "requesterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ITRequest.prototype, "requesterName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], ITRequest.prototype, "requesterEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ITRequest.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], ITRequest.prototype, "requestedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], ITRequest.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ITRequest.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ITRequest.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ITRequest.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ITRequest.prototype, "approvalNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ITRequest.prototype, "completionNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ITRequest.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ITRequest.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ITRequest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ITRequest.prototype, "updatedAt", void 0);
exports.ITRequest = ITRequest = __decorate([
    (0, typeorm_1.Entity)('it_requests')
], ITRequest);
//# sourceMappingURL=it-request.entity.js.map