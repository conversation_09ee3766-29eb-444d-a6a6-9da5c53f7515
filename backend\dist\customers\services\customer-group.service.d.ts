import { Repository } from 'typeorm';
import { CustomerGroup } from '../entities/customer-group.entity';
import { Customer } from '../entities/customer.entity';
export declare class CustomerGroupService {
    private groupRepository;
    private customerRepository;
    constructor(groupRepository: Repository<CustomerGroup>, customerRepository: Repository<Customer>);
    create(groupData: Partial<CustomerGroup>): Promise<CustomerGroup>;
    findAll(): Promise<CustomerGroup[]>;
    findOne(id: string): Promise<CustomerGroup>;
    findByCode(code: string): Promise<CustomerGroup>;
    update(id: string, updateData: Partial<CustomerGroup>): Promise<CustomerGroup>;
    remove(id: string): Promise<void>;
    addCustomerToGroup(customerId: string, groupId: string): Promise<Customer>;
    removeCustomerFromGroup(customerId: string): Promise<Customer>;
    getGroupStatistics(groupId: string): Promise<any>;
    bulkAssignCustomers(customerIds: string[], groupId: string): Promise<Customer[]>;
    getGroupsWithCustomerCounts(): Promise<any[]>;
    applyGroupDiscountToCustomers(groupId: string): Promise<void>;
    getActiveGroups(): Promise<CustomerGroup[]>;
    activateGroup(id: string): Promise<CustomerGroup>;
    deactivateGroup(id: string): Promise<CustomerGroup>;
}
