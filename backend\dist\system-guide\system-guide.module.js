"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemGuideModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const guide_entity_1 = require("./entities/guide.entity");
const guide_section_entity_1 = require("./entities/guide-section.entity");
const guide_step_entity_1 = require("./entities/guide-step.entity");
const tutorial_entity_1 = require("./entities/tutorial.entity");
const tutorial_step_entity_1 = require("./entities/tutorial-step.entity");
const help_article_entity_1 = require("./entities/help-article.entity");
const help_category_entity_1 = require("./entities/help-category.entity");
const user_progress_entity_1 = require("./entities/user-progress.entity");
const tooltip_entity_1 = require("./entities/tooltip.entity");
const announcement_entity_1 = require("./entities/announcement.entity");
const feature_flag_entity_1 = require("./entities/feature-flag.entity");
const onboarding_flow_entity_1 = require("./entities/onboarding-flow.entity");
const guide_service_1 = require("./services/guide.service");
const tutorial_service_1 = require("./services/tutorial.service");
const help_article_service_1 = require("./services/help-article.service");
const user_progress_service_1 = require("./services/user-progress.service");
const tooltip_service_1 = require("./services/tooltip.service");
const announcement_service_1 = require("./services/announcement.service");
const feature_flag_service_1 = require("./services/feature-flag.service");
const onboarding_service_1 = require("./services/onboarding.service");
const guide_controller_1 = require("./controllers/guide.controller");
const tutorial_controller_1 = require("./controllers/tutorial.controller");
const help_article_controller_1 = require("./controllers/help-article.controller");
const user_progress_controller_1 = require("./controllers/user-progress.controller");
const tooltip_controller_1 = require("./controllers/tooltip.controller");
const announcement_controller_1 = require("./controllers/announcement.controller");
const feature_flag_controller_1 = require("./controllers/feature-flag.controller");
const onboarding_controller_1 = require("./controllers/onboarding.controller");
let SystemGuideModule = class SystemGuideModule {
};
exports.SystemGuideModule = SystemGuideModule;
exports.SystemGuideModule = SystemGuideModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                guide_entity_1.Guide,
                guide_section_entity_1.GuideSection,
                guide_step_entity_1.GuideStep,
                tutorial_entity_1.Tutorial,
                tutorial_step_entity_1.TutorialStep,
                help_article_entity_1.HelpArticle,
                help_category_entity_1.HelpCategory,
                user_progress_entity_1.UserProgress,
                tooltip_entity_1.Tooltip,
                announcement_entity_1.Announcement,
                feature_flag_entity_1.FeatureFlag,
                onboarding_flow_entity_1.OnboardingFlow,
            ]),
        ],
        controllers: [
            guide_controller_1.GuideController,
            tutorial_controller_1.TutorialController,
            help_article_controller_1.HelpArticleController,
            user_progress_controller_1.UserProgressController,
            tooltip_controller_1.TooltipController,
            announcement_controller_1.AnnouncementController,
            feature_flag_controller_1.FeatureFlagController,
            onboarding_controller_1.OnboardingController,
        ],
        providers: [
            guide_service_1.GuideService,
            tutorial_service_1.TutorialService,
            help_article_service_1.HelpArticleService,
            user_progress_service_1.UserProgressService,
            tooltip_service_1.TooltipService,
            announcement_service_1.AnnouncementService,
            feature_flag_service_1.FeatureFlagService,
            onboarding_service_1.OnboardingService,
        ],
        exports: [
            guide_service_1.GuideService,
            tutorial_service_1.TutorialService,
            help_article_service_1.HelpArticleService,
            user_progress_service_1.UserProgressService,
            tooltip_service_1.TooltipService,
            announcement_service_1.AnnouncementService,
            feature_flag_service_1.FeatureFlagService,
            onboarding_service_1.OnboardingService,
        ],
    })
], SystemGuideModule);
//# sourceMappingURL=system-guide.module.js.map