{"version": 3, "file": "standalone-server.js", "sourceRoot": "", "sources": ["../src/standalone-server.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,uCAA2C;AAC3C,2CAAyD;AACzD,2CAAgD;AAIhD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAEvB,kBAAkB;QAEhB,OAAO;YACL,YAAY,EAAE,OAAO;YACrB,aAAa,EAAE,OAAO;YACtB,SAAS,EAAE,MAAM;YACjB,YAAY,EAAE,EAAE;YAChB,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,GAAG;YACpB,kBAAkB,EAAE,IAAI;YACxB,WAAW,EAAE,IAAI;YACjB,iBAAiB,EAAE,MAAM;YACzB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,EAAE;YAClB,mBAAmB,EAAE,IAAI;YACzB,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;YAClB,qBAAqB,EAAE,IAAI;YAC3B,cAAc,EAAE,MAAM;YACtB,iBAAiB,EAAE,GAAG;YACtB,QAAQ,EAAE,MAAM;YAChB,kBAAkB,EAAE,MAAM;YAC1B,eAAe,EAAE,KAAK;YACtB,YAAY,EAAE,GAAG;YACjB,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,IAAI;YAClB,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,IAAI;YACpB,WAAW,EAAE,IAAI;YACjB,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,IAAI;YACrB,WAAW,EAAE,IAAI;YACjB,yBAAyB,EAAE,GAAG;YAC9B,yBAAyB,EAAE,GAAG;YAC9B,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,IAAI;YAClB,eAAe,EAAE,IAAI;YACrB,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACrC,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;IAGD,SAAS;QACP,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,yBAAyB;YAClC,OAAO,EAAE,OAAO;SACjB,CAAC;IACJ,CAAC;IAGD,aAAa;QACX,OAAO;YACL,QAAQ,EAAE;gBACR,6FAA6F;gBAC7F,sFAAsF;gBACtF,2FAA2F;gBAC3F,0EAA0E;gBAC1E,0FAA0F;gBAC1F,4FAA4F;gBAC5F,wDAAwD;gBACxD,mFAAmF;aACpF;YACD,aAAa,EAAE;gBACb,OAAO,EAAE;oBACP,OAAO,EAAE,MAAM;oBACf,QAAQ,EAAE,MAAM;oBAChB,QAAQ,EAAE;wBACR,KAAK,EAAE,MAAM;wBACb,UAAU,EAAE,IAAI;wBAChB,KAAK,EAAE,IAAI;wBACX,OAAO,EAAE,CAAC,iBAAiB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,oBAAoB,CAAC;qBAC9F;iBACF;gBACD,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;oBACX,GAAG,EAAE,EAAE;oBACP,SAAS,EAAE,EAAE;iBACd;gBACD,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,IAAI;oBACZ,WAAW,EAAE;wBACX,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE;wBACnC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE;wBACnC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE;qBACpC;iBACF;gBACD,SAAS,EAAE;oBACT,QAAQ,EAAE,EAAE;oBACZ,UAAU,EAAE,CAAC;oBACb,aAAa,EAAE,CAAC;iBACjB;gBACD,SAAS,EAAE;oBACT;wBACE,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,8DAA8D;wBAC3E,QAAQ,EAAE,QAAQ;qBACnB;oBACD;wBACE,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,+DAA+D;wBAC5E,QAAQ,EAAE,MAAM;qBACjB;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,WAAW,EAAE,qEAAqE;wBAClF,QAAQ,EAAE,KAAK;qBAChB;iBACF;aACF;YACD,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;IACJ,CAAC;CACF,CAAA;AAtHC;IADC,IAAA,YAAG,EAAC,kBAAkB,CAAC;;;;6DA2CvB;AAGD;IADC,IAAA,YAAG,EAAC,QAAQ,CAAC;;;;oDAQb;AAGD;IADC,IAAA,YAAG,EAAC,aAAa,CAAC;;;;wDA+DlB;AAvHG,mBAAmB;IADxB,IAAA,mBAAU,EAAC,WAAW,CAAC;GAClB,mBAAmB,CAwHxB;AAID,IAAM,aAAa,GAAnB,MAAM,aAAa;IAEjB,QAAQ;QACN,OAAO;YACL,OAAO,EAAE,iCAAiC;YAC1C,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;gBACT,eAAe,EAAE,iCAAiC;gBAClD,UAAU,EAAE,4BAA4B;gBACxC,MAAM,EAAE,uBAAuB;gBAC/B,IAAI,EAAE,WAAW;aAClB;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAbC;IADC,IAAA,YAAG,GAAE;;;;6CAaL;AAdG,aAAa;IADlB,IAAA,mBAAU,GAAE;GACP,aAAa,CAelB;AAMD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;CAAG,CAAA;AAAtB,mBAAmB;IAHxB,IAAA,eAAM,EAAC;QACN,WAAW,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC;KAClD,CAAC;GACI,mBAAmB,CAAG;AAE5B,KAAK,UAAU,SAAS;IACtB,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAG1D,GAAG,CAAC,UAAU,CAAC;QACb,MAAM,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;QAC1D,OAAO,EAAE,wCAAwC;QACjD,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC;IAGH,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAc,CAAC;QACpC,SAAS,EAAE,IAAI;QACf,oBAAoB,EAAE,IAAI;QAC1B,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC,CAAC;IAGJ,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAE3B,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC;IACtC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEvB,OAAO,CAAC,GAAG,CAAC,6DAA6D,IAAI,EAAE,CAAC,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kDAAkD,IAAI,iCAAiC,CAAC,CAAC;IACrG,OAAO,CAAC,GAAG,CAAC,sCAAsC,IAAI,uBAAuB,CAAC,CAAC;AACjF,CAAC;AAED,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IAC1B,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;IAClD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}