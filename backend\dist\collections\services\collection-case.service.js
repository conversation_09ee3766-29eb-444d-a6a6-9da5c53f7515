"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionCaseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const collection_case_entity_1 = require("../entities/collection-case.entity");
const collection_activity_entity_1 = require("../entities/collection-activity.entity");
let CollectionCaseService = class CollectionCaseService {
    collectionCaseRepository;
    collectionActivityRepository;
    constructor(collectionCaseRepository, collectionActivityRepository) {
        this.collectionCaseRepository = collectionCaseRepository;
        this.collectionActivityRepository = collectionActivityRepository;
    }
    async create(caseData) {
        const collectionCase = this.collectionCaseRepository.create(caseData);
        return this.collectionCaseRepository.save(collectionCase);
    }
    async findAll() {
        return this.collectionCaseRepository.find({
            relations: ['customer', 'activities'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const collectionCase = await this.collectionCaseRepository.findOne({
            where: { id },
            relations: ['customer', 'activities'],
        });
        if (!collectionCase) {
            throw new common_1.NotFoundException(`Collection case with ID ${id} not found`);
        }
        return collectionCase;
    }
    async update(id, updateData) {
        await this.collectionCaseRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const collectionCase = await this.findOne(id);
        await this.collectionCaseRepository.remove(collectionCase);
    }
    async findByStatus(status) {
        return this.collectionCaseRepository.find({
            where: { status },
            relations: ['customer'],
        });
    }
    async findByCustomer(customerId) {
        return this.collectionCaseRepository.find({
            where: { customerId },
            relations: ['activities'],
            order: { createdAt: 'DESC' },
        });
    }
    async updateStatus(id, status, notes) {
        const updateData = { status };
        if (notes) {
            await this.collectionActivityRepository.save({
                caseId: id,
                type: 'status_change',
                description: `Status changed to ${status}: ${notes}`,
                activityDate: new Date(),
            });
        }
        await this.collectionCaseRepository.update(id, updateData);
        return this.findOne(id);
    }
    async assignAgent(caseId, agentId) {
        await this.collectionCaseRepository.update(caseId, { assignedTo: agentId });
        await this.collectionActivityRepository.save({
            caseId,
            type: 'assignment',
            description: `Case assigned to agent ${agentId}`,
            activityDate: new Date(),
        });
        return this.findOne(caseId);
    }
    async getStatistics() {
        const total = await this.collectionCaseRepository.count();
        const active = await this.collectionCaseRepository.count({
            where: { status: collection_case_entity_1.CaseStatus.ACTIVE }
        });
        const resolved = await this.collectionCaseRepository.count({
            where: { status: collection_case_entity_1.CaseStatus.RESOLVED }
        });
        const pending = await this.collectionCaseRepository.count({
            where: { status: collection_case_entity_1.CaseStatus.PENDING }
        });
        const financialData = await this.collectionCaseRepository
            .createQueryBuilder('case')
            .select([
            'SUM(case.originalAmount) as totalDebt',
            'SUM(case.collectedAmount) as totalCollected',
            'AVG(case.collectedAmount / case.originalAmount * 100) as avgCollectionRate',
        ])
            .getRawOne();
        return {
            total,
            active,
            resolved,
            pending,
            totalDebt: parseFloat(financialData.totalDebt) || 0,
            totalCollected: parseFloat(financialData.totalCollected) || 0,
            collectionRate: parseFloat(financialData.avgCollectionRate) || 0,
            resolutionRate: total > 0 ? (resolved / total) * 100 : 0,
        };
    }
    async getCasesByPriority(priority) {
        return this.collectionCaseRepository.find({
            where: { priority },
            relations: ['customer'],
            order: { createdAt: 'DESC' },
        });
    }
    async getOverdueCases() {
        const today = new Date();
        return this.collectionCaseRepository
            .createQueryBuilder('case')
            .where('case.dueDate < :today', { today })
            .andWhere('case.status != :resolved', { resolved: collection_case_entity_1.CaseStatus.RESOLVED })
            .getMany();
    }
    async escalateCase(id, reason) {
        const collectionCase = await this.findOne(id);
        await this.collectionCaseRepository.update(id, {
            priority: 'high',
            escalationLevel: (collectionCase.escalationLevel || 0) + 1,
        });
        await this.collectionActivityRepository.save({
            caseId: id,
            type: 'escalation',
            description: `Case escalated: ${reason}`,
            activityDate: new Date(),
        });
        return this.findOne(id);
    }
    async recordPayment(id, amount, paymentDate, notes) {
        const collectionCase = await this.findOne(id);
        const newCollectedAmount = (collectionCase.collectedAmount || 0) + amount;
        await this.collectionCaseRepository.update(id, {
            collectedAmount: newCollectedAmount,
            lastPaymentDate: paymentDate,
        });
        await this.collectionActivityRepository.save({
            caseId: id,
            type: 'payment',
            description: `Payment received: $${amount}${notes ? ` - ${notes}` : ''}`,
            activityDate: paymentDate,
        });
        if (newCollectedAmount >= collectionCase.originalAmount) {
            await this.updateStatus(id, collection_case_entity_1.CaseStatus.RESOLVED, 'Full payment received');
        }
        return this.findOne(id);
    }
    async getDashboardMetrics() {
        const stats = await this.getStatistics();
        const overdueCases = await this.getOverdueCases();
        return {
            ...stats,
            overdueCount: overdueCases.length,
            urgentCases: overdueCases.filter(c => c.priority === 'high').length,
        };
    }
};
exports.CollectionCaseService = CollectionCaseService;
exports.CollectionCaseService = CollectionCaseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(collection_case_entity_1.CollectionCase)),
    __param(1, (0, typeorm_1.InjectRepository)(collection_activity_entity_1.CollectionActivity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CollectionCaseService);
//# sourceMappingURL=collection-case.service.js.map