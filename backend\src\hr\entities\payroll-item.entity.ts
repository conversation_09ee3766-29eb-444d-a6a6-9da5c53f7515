import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Payroll } from './payroll.entity';

export enum PayrollItemType {
  EARNING = 'earning',
  DEDUCTION = 'deduction',
  BENEFIT = 'benefit',
  TAX = 'tax',
}

export enum PayrollItemCategory {
  // Earnings
  BASIC_SALARY = 'basic_salary',
  OVERTIME = 'overtime',
  BONUS = 'bonus',
  COMMISSION = 'commission',
  ALLOWANCE = 'allowance',
  
  // Deductions
  INCOME_TAX = 'income_tax',
  SOCIAL_SECURITY = 'social_security',
  MEDICARE = 'medicare',
  HEALTH_INSURANCE = 'health_insurance',
  RETIREMENT = 'retirement',
  LOAN_REPAYMENT = 'loan_repayment',
  
  // Benefits
  HEALTH_BENEFIT = 'health_benefit',
  DENTAL_BENEFIT = 'dental_benefit',
  VISION_BENEFIT = 'vision_benefit',
  LIFE_INSURANCE = 'life_insurance',
  
  // Other
  OTHER = 'other',
}

@Entity('hr_payroll_items')
export class PayrollItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  payrollId: string;

  @ManyToOne(() => Payroll, payroll => payroll.payrollItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'payrollId' })
  payroll: Payroll;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 50, nullable: true })
  code: string;

  @Column({
    type: 'enum',
    enum: PayrollItemType,
  })
  type: PayrollItemType;

  @Column({
    type: 'enum',
    enum: PayrollItemCategory,
  })
  category: PayrollItemCategory;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  rate: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  hours: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  percentage: number;

  @Column({ default: true })
  isTaxable: boolean;

  @Column({ default: false })
  isStatutory: boolean;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
