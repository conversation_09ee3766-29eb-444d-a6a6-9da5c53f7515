"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const product_entity_1 = require("./entities/product.entity");
const category_entity_1 = require("./entities/category.entity");
const warehouse_entity_1 = require("./entities/warehouse.entity");
const location_entity_1 = require("./entities/location.entity");
const stock_entity_1 = require("./entities/stock.entity");
const stock_movement_entity_1 = require("./entities/stock-movement.entity");
const supplier_entity_1 = require("./entities/supplier.entity");
const purchase_order_entity_1 = require("./entities/purchase-order.entity");
const purchase_order_item_entity_1 = require("./entities/purchase-order-item.entity");
const stock_adjustment_entity_1 = require("./entities/stock-adjustment.entity");
const stock_transfer_entity_1 = require("./entities/stock-transfer.entity");
const inventory_count_entity_1 = require("./entities/inventory-count.entity");
const product_service_1 = require("./services/product.service");
const category_service_1 = require("./services/category.service");
const warehouse_service_1 = require("./services/warehouse.service");
const stock_service_1 = require("./services/stock.service");
const supplier_service_1 = require("./services/supplier.service");
const purchase_order_service_1 = require("./services/purchase-order.service");
const stock_movement_service_1 = require("./services/stock-movement.service");
const inventory_report_service_1 = require("./services/inventory-report.service");
const product_controller_1 = require("./controllers/product.controller");
const category_controller_1 = require("./controllers/category.controller");
const warehouse_controller_1 = require("./controllers/warehouse.controller");
const stock_controller_1 = require("./controllers/stock.controller");
const supplier_controller_1 = require("./controllers/supplier.controller");
const purchase_order_controller_1 = require("./controllers/purchase-order.controller");
const inventory_report_controller_1 = require("./controllers/inventory-report.controller");
let InventoryModule = class InventoryModule {
};
exports.InventoryModule = InventoryModule;
exports.InventoryModule = InventoryModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                product_entity_1.Product,
                category_entity_1.Category,
                warehouse_entity_1.Warehouse,
                location_entity_1.Location,
                stock_entity_1.Stock,
                stock_movement_entity_1.StockMovement,
                supplier_entity_1.Supplier,
                purchase_order_entity_1.PurchaseOrder,
                purchase_order_item_entity_1.PurchaseOrderItem,
                stock_adjustment_entity_1.StockAdjustment,
                stock_transfer_entity_1.StockTransfer,
                inventory_count_entity_1.InventoryCount,
            ]),
        ],
        controllers: [
            product_controller_1.ProductController,
            category_controller_1.CategoryController,
            warehouse_controller_1.WarehouseController,
            stock_controller_1.StockController,
            supplier_controller_1.SupplierController,
            purchase_order_controller_1.PurchaseOrderController,
            inventory_report_controller_1.InventoryReportController,
        ],
        providers: [
            product_service_1.ProductService,
            category_service_1.CategoryService,
            warehouse_service_1.WarehouseService,
            stock_service_1.StockService,
            supplier_service_1.SupplierService,
            purchase_order_service_1.PurchaseOrderService,
            stock_movement_service_1.StockMovementService,
            inventory_report_service_1.InventoryReportService,
        ],
        exports: [
            product_service_1.ProductService,
            category_service_1.CategoryService,
            warehouse_service_1.WarehouseService,
            stock_service_1.StockService,
            supplier_service_1.SupplierService,
            purchase_order_service_1.PurchaseOrderService,
            stock_movement_service_1.StockMovementService,
            inventory_report_service_1.InventoryReportService,
        ],
    })
], InventoryModule);
//# sourceMappingURL=inventory.module.js.map