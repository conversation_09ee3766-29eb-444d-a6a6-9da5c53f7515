"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftwareLicense = exports.LicenseStatus = exports.LicenseType = void 0;
const typeorm_1 = require("typeorm");
var LicenseType;
(function (LicenseType) {
    LicenseType["PERPETUAL"] = "perpetual";
    LicenseType["SUBSCRIPTION"] = "subscription";
    LicenseType["VOLUME"] = "volume";
    LicenseType["OEM"] = "oem";
    LicenseType["TRIAL"] = "trial";
    LicenseType["FREEWARE"] = "freeware";
    LicenseType["OPEN_SOURCE"] = "open_source";
})(LicenseType || (exports.LicenseType = LicenseType = {}));
var LicenseStatus;
(function (LicenseStatus) {
    LicenseStatus["ACTIVE"] = "active";
    LicenseStatus["EXPIRED"] = "expired";
    LicenseStatus["SUSPENDED"] = "suspended";
    LicenseStatus["CANCELLED"] = "cancelled";
    LicenseStatus["TRIAL"] = "trial";
})(LicenseStatus || (exports.LicenseStatus = LicenseStatus = {}));
let SoftwareLicense = class SoftwareLicense {
    id;
    softwareName;
    vendor;
    version;
    type;
    status;
    licenseKey;
    totalLicenses;
    usedLicenses;
    availableLicenses;
    purchaseDate;
    expiryDate;
    cost;
    annualCost;
    currency;
    purchaseOrder;
    description;
    assignedUsers;
    assignedAssets;
    notes;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.SoftwareLicense = SoftwareLicense;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "softwareName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "vendor", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: LicenseType,
    }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: LicenseStatus,
        default: LicenseStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "licenseKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], SoftwareLicense.prototype, "totalLicenses", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], SoftwareLicense.prototype, "usedLicenses", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], SoftwareLicense.prototype, "availableLicenses", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], SoftwareLicense.prototype, "purchaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], SoftwareLicense.prototype, "expiryDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], SoftwareLicense.prototype, "cost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], SoftwareLicense.prototype, "annualCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "purchaseOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], SoftwareLicense.prototype, "assignedUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], SoftwareLicense.prototype, "assignedAssets", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], SoftwareLicense.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], SoftwareLicense.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], SoftwareLicense.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SoftwareLicense.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], SoftwareLicense.prototype, "updatedAt", void 0);
exports.SoftwareLicense = SoftwareLicense = __decorate([
    (0, typeorm_1.Entity)('software_licenses')
], SoftwareLicense);
//# sourceMappingURL=software-license.entity.js.map