export declare enum CallResult {
    CONTACT_MADE = "contact_made",
    NO_ANSWER = "no_answer",
    BUSY = "busy",
    DISCONNECTED = "disconnected",
    WRONG_NUMBER = "wrong_number",
    LEFT_VOICEMAIL = "left_voicemail",
    CALL_BACK_REQUESTED = "call_back_requested",
    REFUSED_TO_TALK = "refused_to_talk",
    HUNG_UP = "hung_up",
    TECHNICAL_ISSUE = "technical_issue"
}
export declare enum CallDisposition {
    PROMISE_TO_PAY = "promise_to_pay",
    PAYMENT_MADE = "payment_made",
    DISPUTE_RAISED = "dispute_raised",
    HARDSHIP_CLAIMED = "hardship_claimed",
    PAYMENT_PLAN_REQUESTED = "payment_plan_requested",
    REFUSED_TO_PAY = "refused_to_pay",
    WILL_CALL_BACK = "will_call_back",
    REQUESTED_VALIDATION = "requested_validation",
    CEASE_AND_DESIST = "cease_and_desist",
    BANKRUPTCY_CLAIMED = "bankruptcy_claimed",
    DECEASED_CLAIMED = "deceased_claimed",
    NOT_RESPONSIBLE = "not_responsible",
    OTHER = "other"
}
export declare class CollectionCall {
    id: string;
    caseId: string;
    callDateTime: Date;
    phoneNumber: string;
    duration: number;
    result: CallResult;
    disposition: CallDisposition;
    agentId: string;
    notes: string;
    summary: string;
    promiseAmount: number;
    promiseDate: Date;
    callbackDate: Date;
    recordingPath: string;
    isRecorded: boolean;
    isInbound: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
