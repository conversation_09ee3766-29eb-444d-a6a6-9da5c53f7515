export declare enum ChangeType {
    STANDARD = "standard",
    NORMAL = "normal",
    EMERGENCY = "emergency",
    MAJOR = "major"
}
export declare enum ChangeStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    APPROVED = "approved",
    REJECTED = "rejected",
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare enum ChangeRisk {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export declare class ChangeRequest {
    id: string;
    changeNumber: string;
    title: string;
    description: string;
    type: ChangeType;
    status: ChangeStatus;
    risk: ChangeRisk;
    requesterId: string;
    requesterName: string;
    assignedTo: string;
    justification: string;
    implementationPlan: string;
    rollbackPlan: string;
    testPlan: string;
    scheduledStart: Date;
    scheduledEnd: Date;
    actualStart: Date;
    actualEnd: Date;
    affectedSystems: string[];
    stakeholders: string[];
    approvedBy: string;
    approvedAt: Date;
    approvalNotes: string;
    implementationNotes: string;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
