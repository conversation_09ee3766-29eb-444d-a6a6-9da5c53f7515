import { RFQ } from './rfq.entity';
export declare enum ResponseStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    UNDER_REVIEW = "under_review",
    ACCEPTED = "accepted",
    REJECTED = "rejected",
    WITHDRAWN = "withdrawn"
}
export declare class RFQResponse {
    id: string;
    rfqId: string;
    rfq: RFQ;
    vendorId: string;
    status: ResponseStatus;
    submittedAt: Date;
    totalPrice: number;
    currency: string;
    deliveryDays: number;
    proposal: string;
    lineItems: any[];
    terms: any;
    attachments: string[];
    evaluationScore: number;
    evaluationNotes: string;
    evaluatedBy: string;
    evaluatedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
