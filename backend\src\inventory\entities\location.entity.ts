import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Warehouse } from './warehouse.entity';
import { Stock } from './stock.entity';

export enum LocationType {
  SHELF = 'shelf',
  BIN = 'bin',
  RACK = 'rack',
  FLOOR = 'floor',
  PALLET = 'pallet',
  COOLER = 'cooler',
  FREEZER = 'freezer',
  QUARANTINE = 'quarantine',
  STAGING = 'staging',
  RECEIVING = 'receiving',
  SHIPPING = 'shipping',
}

@Entity('inventory_locations')
export class Location {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  warehouseId: string;

  @ManyToOne(() => Warehouse, warehouse => warehouse.locations)
  @JoinColumn({ name: 'warehouseId' })
  warehouse: Warehouse;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: LocationType,
    default: LocationType.SHELF,
  })
  type: LocationType;

  @Column({ length: 50, nullable: true })
  aisle: string;

  @Column({ length: 50, nullable: true })
  bay: string;

  @Column({ length: 50, nullable: true })
  level: string;

  @Column({ length: 50, nullable: true })
  position: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxWeight: number; // in kg

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxVolume: number; // in cubic meters

  @Column({ type: 'int', nullable: true })
  maxItems: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isPickable: boolean;

  @Column({ default: false })
  isReceivable: boolean;

  @Column({ type: 'decimal', precision: 8, scale: 6, nullable: true })
  latitude: number;

  @Column({ type: 'decimal', precision: 9, scale: 6, nullable: true })
  longitude: number;

  @Column({ type: 'json', nullable: true })
  environmentalConditions: any; // temperature, humidity, etc.

  @OneToMany(() => Stock, stock => stock.location)
  stocks: Stock[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
