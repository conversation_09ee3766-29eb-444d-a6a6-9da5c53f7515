import { Customer } from './customer.entity';
import { RecurringInvoiceItem } from './recurring-invoice-item.entity';
export declare class RecurringInvoice {
    id: string;
    templateName: string;
    customerId: string;
    customer: Customer;
    frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    startDate: Date;
    endDate: Date;
    nextInvoiceDate: Date;
    lastInvoiceDate: Date;
    invoicesGenerated: number;
    maxInvoices: number;
    subtotal: number;
    discountAmount: number;
    discountType: 'percentage' | 'amount';
    discountValue: number;
    taxAmount: number;
    totalAmount: number;
    status: 'active' | 'paused' | 'completed' | 'cancelled';
    paymentTerms: string;
    notes: string;
    terms: string;
    items: RecurringInvoiceItem[];
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
