import { Customer } from './customer.entity';
export declare enum NoteType {
    GENERAL = "general",
    SALES = "sales",
    SUPPORT = "support",
    BILLING = "billing",
    COMPLAINT = "complaint",
    FEEDBACK = "feedback",
    INTERNAL = "internal",
    ALERT = "alert"
}
export declare class CustomerNote {
    id: string;
    customerId: string;
    customer: Customer;
    type: NoteType;
    title: string;
    content: string;
    createdBy: string;
    isPrivate: boolean;
    isPinned: boolean;
    tags: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
