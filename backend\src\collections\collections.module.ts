import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { CollectionCase } from './entities/collection-case.entity';
import { CollectionActivity } from './entities/collection-activity.entity';
import { CollectionStrategy } from './entities/collection-strategy.entity';
import { CollectionAgent } from './entities/collection-agent.entity';
import { PaymentPlan } from './entities/payment-plan.entity';
import { PaymentPlanInstallment } from './entities/payment-plan-installment.entity';
import { CollectionNote } from './entities/collection-note.entity';
import { CollectionDocument } from './entities/collection-document.entity';
import { CollectionDispute } from './entities/collection-dispute.entity';
import { CollectionLetter } from './entities/collection-letter.entity';
import { CollectionCall } from './entities/collection-call.entity';

// Services
import { CollectionCaseService } from './services/collection-case.service';
import { CollectionActivityService } from './services/collection-activity.service';
import { CollectionStrategyService } from './services/collection-strategy.service';
import { PaymentPlanService } from './services/payment-plan.service';
import { CollectionReportService } from './services/collection-report.service';
import { CollectionAutomationService } from './services/collection-automation.service';

// Controllers
import { CollectionCaseController } from './controllers/collection-case.controller';
import { CollectionActivityController } from './controllers/collection-activity.controller';
import { CollectionStrategyController } from './controllers/collection-strategy.controller';
import { PaymentPlanController } from './controllers/payment-plan.controller';
import { CollectionReportController } from './controllers/collection-report.controller';
import { CollectionAutomationController } from './controllers/collection-automation.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CollectionCase,
      CollectionActivity,
      CollectionStrategy,
      CollectionAgent,
      PaymentPlan,
      PaymentPlanInstallment,
      CollectionNote,
      CollectionDocument,
      CollectionDispute,
      CollectionLetter,
      CollectionCall,
    ]),
  ],
  controllers: [
    CollectionCaseController,
    CollectionActivityController,
    CollectionStrategyController,
    PaymentPlanController,
    CollectionReportController,
    CollectionAutomationController,
  ],
  providers: [
    CollectionCaseService,
    CollectionActivityService,
    CollectionStrategyService,
    PaymentPlanService,
    CollectionReportService,
    CollectionAutomationService,
  ],
  exports: [
    CollectionCaseService,
    CollectionActivityService,
    CollectionStrategyService,
    PaymentPlanService,
    CollectionReportService,
    CollectionAutomationService,
  ],
})
export class CollectionsModule {}
