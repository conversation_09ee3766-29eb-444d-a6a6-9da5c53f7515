"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceCatalog = exports.ServiceType = exports.ServiceStatus = void 0;
const typeorm_1 = require("typeorm");
var ServiceStatus;
(function (ServiceStatus) {
    ServiceStatus["ACTIVE"] = "active";
    ServiceStatus["INACTIVE"] = "inactive";
    ServiceStatus["DEPRECATED"] = "deprecated";
    ServiceStatus["UNDER_DEVELOPMENT"] = "under_development";
})(ServiceStatus || (exports.ServiceStatus = ServiceStatus = {}));
var ServiceType;
(function (ServiceType) {
    ServiceType["BUSINESS_SERVICE"] = "business_service";
    ServiceType["TECHNICAL_SERVICE"] = "technical_service";
    ServiceType["INFRASTRUCTURE_SERVICE"] = "infrastructure_service";
    ServiceType["APPLICATION_SERVICE"] = "application_service";
})(ServiceType || (exports.ServiceType = ServiceType = {}));
let ServiceCatalog = class ServiceCatalog {
    id;
    name;
    description;
    type;
    status;
    category;
    serviceOwner;
    businessOwner;
    cost;
    currency;
    sla;
    dependencies;
    supportedBy;
    requestProcess;
    deliverables;
    estimatedDeliveryDays;
    isRequestable;
    requiresApproval;
    approvers;
    requestForm;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.ServiceCatalog = ServiceCatalog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ServiceType,
    }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ServiceStatus,
        default: ServiceStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "serviceOwner", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "businessOwner", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], ServiceCatalog.prototype, "cost", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "sla", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ServiceCatalog.prototype, "dependencies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ServiceCatalog.prototype, "supportedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ServiceCatalog.prototype, "requestProcess", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ServiceCatalog.prototype, "deliverables", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], ServiceCatalog.prototype, "estimatedDeliveryDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], ServiceCatalog.prototype, "isRequestable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], ServiceCatalog.prototype, "requiresApproval", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ServiceCatalog.prototype, "approvers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ServiceCatalog.prototype, "requestForm", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ServiceCatalog.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ServiceCatalog.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ServiceCatalog.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ServiceCatalog.prototype, "updatedAt", void 0);
exports.ServiceCatalog = ServiceCatalog = __decorate([
    (0, typeorm_1.Entity)('service_catalog')
], ServiceCatalog);
//# sourceMappingURL=service-catalog.entity.js.map