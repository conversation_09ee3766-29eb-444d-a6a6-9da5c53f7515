import { TutorialStep } from './tutorial-step.entity';
export declare enum TutorialType {
    INTERACTIVE = "interactive",
    VIDEO = "video",
    STEP_BY_STEP = "step_by_step",
    WALKTHROUGH = "walkthrough",
    DEMO = "demo"
}
export declare enum TutorialDifficulty {
    BEGINNER = "beginner",
    INTERMEDIATE = "intermediate",
    ADVANCED = "advanced",
    EXPERT = "expert"
}
export declare class Tutorial {
    id: string;
    title: string;
    description: string;
    type: TutorialType;
    difficulty: TutorialDifficulty;
    estimatedDuration: number;
    thumbnailImage: string;
    videoUrl: string;
    prerequisites: string[];
    learningObjectives: string[];
    tags: string[];
    targetRoles: string[];
    isActive: boolean;
    isFeatured: boolean;
    authorId: string;
    viewCount: number;
    completionCount: number;
    rating: number;
    ratingCount: number;
    steps: TutorialStep[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
