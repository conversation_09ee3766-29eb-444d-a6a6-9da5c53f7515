import { Customer } from './customer.entity';
import { QuotationItem } from './quotation-item.entity';
export declare class Quotation {
    id: string;
    quotationNumber: string;
    customerId: string;
    customer: Customer;
    quotationDate: Date;
    validUntil: Date;
    salesOfficer: string;
    subtotal: number;
    discountAmount: number;
    discountType: 'percentage' | 'amount';
    discountValue: number;
    taxAmount: number;
    totalAmount: number;
    status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
    notes: string;
    terms: string;
    convertedToInvoice: string;
    items: QuotationItem[];
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
