import { Employee } from './employee.entity';
export declare enum TrainingType {
    ORIENTATION = "orientation",
    SKILL_DEVELOPMENT = "skill_development",
    COMPLIANCE = "compliance",
    LEADERSHIP = "leadership",
    TECHNICAL = "technical",
    SAFETY = "safety",
    CERTIFICATION = "certification",
    WORKSHOP = "workshop",
    SEMINAR = "seminar",
    CONFERENCE = "conference"
}
export declare enum TrainingStatus {
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    NO_SHOW = "no_show"
}
export declare enum TrainingDeliveryMethod {
    IN_PERSON = "in_person",
    ONLINE = "online",
    HYBRID = "hybrid",
    SELF_PACED = "self_paced"
}
export declare class Training {
    id: string;
    employeeId: string;
    employee: Employee;
    title: string;
    description: string;
    type: TrainingType;
    status: TrainingStatus;
    deliveryMethod: TrainingDeliveryMethod;
    startDate: Date;
    endDate: Date;
    startTime: string;
    endTime: string;
    duration: number;
    location: string;
    instructor: string;
    provider: string;
    cost: number;
    currency: string;
    isMandatory: boolean;
    hasCertification: boolean;
    certificationName: string;
    certificationExpiryDate: Date;
    score: number;
    passingScore: number;
    isPassed: boolean;
    completionDate: Date;
    feedback: string;
    rating: number;
    attachments: string[];
    prerequisites: string[];
    learningObjectives: string[];
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
