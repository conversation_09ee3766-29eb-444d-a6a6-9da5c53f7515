"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Account = exports.AccountSubType = exports.AccountType = void 0;
const typeorm_1 = require("typeorm");
const transaction_entity_1 = require("./transaction.entity");
var AccountType;
(function (AccountType) {
    AccountType["ASSET"] = "asset";
    AccountType["LIABILITY"] = "liability";
    AccountType["EQUITY"] = "equity";
    AccountType["REVENUE"] = "revenue";
    AccountType["EXPENSE"] = "expense";
})(AccountType || (exports.AccountType = AccountType = {}));
var AccountSubType;
(function (AccountSubType) {
    AccountSubType["CURRENT_ASSET"] = "current_asset";
    AccountSubType["FIXED_ASSET"] = "fixed_asset";
    AccountSubType["CASH"] = "cash";
    AccountSubType["ACCOUNTS_RECEIVABLE"] = "accounts_receivable";
    AccountSubType["INVENTORY"] = "inventory";
    AccountSubType["CURRENT_LIABILITY"] = "current_liability";
    AccountSubType["LONG_TERM_LIABILITY"] = "long_term_liability";
    AccountSubType["ACCOUNTS_PAYABLE"] = "accounts_payable";
    AccountSubType["OWNERS_EQUITY"] = "owners_equity";
    AccountSubType["RETAINED_EARNINGS"] = "retained_earnings";
    AccountSubType["SALES_REVENUE"] = "sales_revenue";
    AccountSubType["SERVICE_REVENUE"] = "service_revenue";
    AccountSubType["OTHER_REVENUE"] = "other_revenue";
    AccountSubType["OPERATING_EXPENSE"] = "operating_expense";
    AccountSubType["COST_OF_GOODS_SOLD"] = "cost_of_goods_sold";
    AccountSubType["ADMINISTRATIVE_EXPENSE"] = "administrative_expense";
})(AccountSubType || (exports.AccountSubType = AccountSubType = {}));
let Account = class Account {
    id;
    accountNumber;
    name;
    description;
    type;
    subType;
    balance;
    debitBalance;
    creditBalance;
    isActive;
    isSystemAccount;
    parentAccountId;
    parentAccount;
    childAccounts;
    debitTransactions;
    creditTransactions;
    currency;
    metadata;
    createdAt;
    updatedAt;
};
exports.Account = Account;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Account.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, unique: true }),
    __metadata("design:type", String)
], Account.prototype, "accountNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Account.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Account.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AccountType,
    }),
    __metadata("design:type", String)
], Account.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AccountSubType,
    }),
    __metadata("design:type", String)
], Account.prototype, "subType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Account.prototype, "balance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Account.prototype, "debitBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Account.prototype, "creditBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Account.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Account.prototype, "isSystemAccount", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Account.prototype, "parentAccountId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Account, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'parentAccountId' }),
    __metadata("design:type", Account)
], Account.prototype, "parentAccount", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Account, account => account.parentAccount),
    __metadata("design:type", Array)
], Account.prototype, "childAccounts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => transaction_entity_1.Transaction, transaction => transaction.debitAccount),
    __metadata("design:type", Array)
], Account.prototype, "debitTransactions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => transaction_entity_1.Transaction, transaction => transaction.creditAccount),
    __metadata("design:type", Array)
], Account.prototype, "creditTransactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, nullable: true }),
    __metadata("design:type", String)
], Account.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Account.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Account.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Account.prototype, "updatedAt", void 0);
exports.Account = Account = __decorate([
    (0, typeorm_1.Entity)('finance_accounts')
], Account);
//# sourceMappingURL=account.entity.js.map