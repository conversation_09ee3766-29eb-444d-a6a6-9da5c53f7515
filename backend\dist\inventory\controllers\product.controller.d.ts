import { ProductService } from '../services/product.service';
import { Product } from '../entities/product.entity';
export declare class ProductController {
    private readonly productService;
    constructor(productService: ProductService);
    create(createProductDto: Partial<Product>): Promise<Product>;
    findAll(): Promise<Product[]>;
    getStatistics(): Promise<any>;
    getLowStockProducts(threshold?: string): Promise<Product[]>;
    getOutOfStockProducts(): Promise<Product[]>;
    getTopSellingProducts(limit?: string): Promise<Product[]>;
    searchProducts(searchTerm: string): Promise<Product[]>;
    findByCategory(categoryId: string): Promise<Product[]>;
    findBySku(sku: string): Promise<Product>;
    findByBarcode(barcode: string): Promise<Product>;
    findOne(id: string): Promise<Product>;
    update(id: string, updateProductDto: Partial<Product>): Promise<Product>;
    bulkUpdatePrices(updates: Array<{
        id: string;
        salePrice: number;
        costPrice?: number;
    }>): Promise<{
        message: string;
    }>;
    updateStock(id: string, stockUpdate: {
        warehouseId: string;
        quantity: number;
    }): Promise<{
        message: string;
    }>;
    reserveStock(id: string, reservation: {
        warehouseId: string;
        quantity: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    releaseStock(id: string, release: {
        warehouseId: string;
        quantity: number;
    }): Promise<{
        message: string;
    }>;
    generateSku(data: {
        categoryCode: string;
    }): Promise<{
        sku: string;
    }>;
    remove(id: string): Promise<void>;
}
