"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReturnedInvoiceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const returned_invoice_entity_1 = require("../entities/returned-invoice.entity");
const returned_invoice_item_entity_1 = require("../entities/returned-invoice-item.entity");
let ReturnedInvoiceService = class ReturnedInvoiceService {
    returnedInvoiceRepository;
    returnedInvoiceItemRepository;
    constructor(returnedInvoiceRepository, returnedInvoiceItemRepository) {
        this.returnedInvoiceRepository = returnedInvoiceRepository;
        this.returnedInvoiceItemRepository = returnedInvoiceItemRepository;
    }
    async create(createReturnedInvoiceDto, tenantId) {
        const returnNumber = await this.generateReturnNumber();
        const returnedInvoice = this.returnedInvoiceRepository.create({
            ...createReturnedInvoiceDto,
            returnNumber,
            tenantId,
            returnDate: new Date(createReturnedInvoiceDto.returnDate),
            processedDate: createReturnedInvoiceDto.processedDate ? new Date(createReturnedInvoiceDto.processedDate) : null,
        });
        const savedReturnedInvoice = await this.returnedInvoiceRepository.save(returnedInvoice);
        for (const itemDto of createReturnedInvoiceDto.items) {
            const item = this.returnedInvoiceItemRepository.create({
                ...itemDto,
                returnedInvoiceId: savedReturnedInvoice.id,
                tenantId,
            });
            await this.returnedInvoiceItemRepository.save(item);
        }
        return this.findOne(savedReturnedInvoice.id, tenantId);
    }
    async findAll(tenantId, filters) {
        const queryBuilder = this.returnedInvoiceRepository
            .createQueryBuilder('returnedInvoice')
            .leftJoinAndSelect('returnedInvoice.customer', 'customer')
            .leftJoinAndSelect('returnedInvoice.originalInvoice', 'originalInvoice')
            .leftJoinAndSelect('returnedInvoice.items', 'items')
            .where('returnedInvoice.tenantId = :tenantId', { tenantId })
            .orderBy('returnedInvoice.createdAt', 'DESC');
        if (filters?.startDate && filters?.endDate) {
            queryBuilder.andWhere('returnedInvoice.returnDate BETWEEN :startDate AND :endDate', {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        if (filters?.customerId) {
            queryBuilder.andWhere('returnedInvoice.customerId = :customerId', {
                customerId: filters.customerId,
            });
        }
        if (filters?.status) {
            queryBuilder.andWhere('returnedInvoice.status = :status', {
                status: filters.status,
            });
        }
        if (filters?.search) {
            queryBuilder.andWhere('(returnedInvoice.returnNumber ILIKE :search OR customer.name ILIKE :search OR originalInvoice.invoiceNumber ILIKE :search)', { search: `%${filters.search}%` });
        }
        return queryBuilder.getMany();
    }
    async findOne(id, tenantId) {
        const returnedInvoice = await this.returnedInvoiceRepository.findOne({
            where: { id, tenantId },
            relations: ['customer', 'originalInvoice', 'items'],
        });
        if (!returnedInvoice) {
            throw new common_1.NotFoundException('Returned invoice not found');
        }
        return returnedInvoice;
    }
    async update(id, updateReturnedInvoiceDto, tenantId) {
        const returnedInvoice = await this.findOne(id, tenantId);
        if (updateReturnedInvoiceDto.items) {
            await this.returnedInvoiceItemRepository.delete({ returnedInvoiceId: id });
            for (const itemDto of updateReturnedInvoiceDto.items) {
                const item = this.returnedInvoiceItemRepository.create({
                    ...itemDto,
                    returnedInvoiceId: id,
                    tenantId,
                });
                await this.returnedInvoiceItemRepository.save(item);
            }
        }
        Object.assign(returnedInvoice, updateReturnedInvoiceDto);
        if (updateReturnedInvoiceDto.returnDate) {
            returnedInvoice.returnDate = new Date(updateReturnedInvoiceDto.returnDate);
        }
        if (updateReturnedInvoiceDto.processedDate) {
            returnedInvoice.processedDate = new Date(updateReturnedInvoiceDto.processedDate);
        }
        return this.returnedInvoiceRepository.save(returnedInvoice);
    }
    async remove(id, tenantId) {
        const returnedInvoice = await this.findOne(id, tenantId);
        await this.returnedInvoiceRepository.remove(returnedInvoice);
    }
    async updateStatus(id, status, tenantId) {
        const returnedInvoice = await this.findOne(id, tenantId);
        returnedInvoice.status = status;
        if (status === 'completed' && !returnedInvoice.processedDate) {
            returnedInvoice.processedDate = new Date();
        }
        return this.returnedInvoiceRepository.save(returnedInvoice);
    }
    async getReturnedInvoiceStats(tenantId) {
        const totalReturns = await this.returnedInvoiceRepository.count({ where: { tenantId } });
        const pendingReturns = await this.returnedInvoiceRepository.count({
            where: { tenantId, status: 'pending' }
        });
        const completedReturns = await this.returnedInvoiceRepository.count({
            where: { tenantId, status: 'completed' }
        });
        const result = await this.returnedInvoiceRepository
            .createQueryBuilder('returnedInvoice')
            .select('SUM(returnedInvoice.totalReturnAmount)', 'totalReturnAmount')
            .where('returnedInvoice.tenantId = :tenantId', { tenantId })
            .getRawOne();
        return {
            totalReturns,
            pendingReturns,
            completedReturns,
            totalReturnAmount: parseFloat(result.totalReturnAmount) || 0,
        };
    }
    async generateReturnNumber() {
        const count = await this.returnedInvoiceRepository.count();
        return `RET-${(count + 1).toString().padStart(6, '0')}`;
    }
    async exportData(tenantId, filters) {
        const returnedInvoices = await this.findAll(tenantId, filters);
        return returnedInvoices.map(returnedInvoice => ({
            returnNumber: returnedInvoice.returnNumber,
            customerName: returnedInvoice.customer.name,
            originalInvoiceNumber: returnedInvoice.originalInvoice.invoiceNumber,
            returnDate: returnedInvoice.returnDate,
            originalAmount: returnedInvoice.originalAmount,
            returnAmount: returnedInvoice.returnAmount,
            totalReturnAmount: returnedInvoice.totalReturnAmount,
            status: returnedInvoice.status,
            reason: returnedInvoice.reason,
            refundMethod: returnedInvoice.refundMethod,
            processedBy: returnedInvoice.processedBy,
            processedDate: returnedInvoice.processedDate,
        }));
    }
};
exports.ReturnedInvoiceService = ReturnedInvoiceService;
exports.ReturnedInvoiceService = ReturnedInvoiceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(returned_invoice_entity_1.ReturnedInvoice)),
    __param(1, (0, typeorm_1.InjectRepository)(returned_invoice_item_entity_1.ReturnedInvoiceItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], ReturnedInvoiceService);
//# sourceMappingURL=returned-invoice.service.js.map