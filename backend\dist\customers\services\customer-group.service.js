"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerGroupService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_group_entity_1 = require("../entities/customer-group.entity");
const customer_entity_1 = require("../entities/customer.entity");
let CustomerGroupService = class CustomerGroupService {
    groupRepository;
    customerRepository;
    constructor(groupRepository, customerRepository) {
        this.groupRepository = groupRepository;
        this.customerRepository = customerRepository;
    }
    async create(groupData) {
        if (groupData.code) {
            const existingGroup = await this.groupRepository.findOne({
                where: { code: groupData.code },
            });
            if (existingGroup) {
                throw new common_1.BadRequestException(`Group with code ${groupData.code} already exists`);
            }
        }
        const group = this.groupRepository.create(groupData);
        return this.groupRepository.save(group);
    }
    async findAll() {
        return this.groupRepository.find({
            relations: ['customers'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const group = await this.groupRepository.findOne({
            where: { id },
            relations: ['customers'],
        });
        if (!group) {
            throw new common_1.NotFoundException(`Customer group with ID ${id} not found`);
        }
        return group;
    }
    async findByCode(code) {
        const group = await this.groupRepository.findOne({
            where: { code },
            relations: ['customers'],
        });
        if (!group) {
            throw new common_1.NotFoundException(`Customer group with code ${code} not found`);
        }
        return group;
    }
    async update(id, updateData) {
        const group = await this.findOne(id);
        if (updateData.code && updateData.code !== group.code) {
            const existingGroup = await this.groupRepository.findOne({
                where: { code: updateData.code },
            });
            if (existingGroup) {
                throw new common_1.BadRequestException(`Group with code ${updateData.code} already exists`);
            }
        }
        await this.groupRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const group = await this.findOne(id);
        const customerCount = await this.customerRepository.count({
            where: { groupId: id },
        });
        if (customerCount > 0) {
            throw new common_1.BadRequestException(`Cannot delete group with ${customerCount} customers. Move customers to another group first.`);
        }
        await this.groupRepository.remove(group);
    }
    async addCustomerToGroup(customerId, groupId) {
        const group = await this.findOne(groupId);
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        customer.groupId = groupId;
        return this.customerRepository.save(customer);
    }
    async removeCustomerFromGroup(customerId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        customer.groupId = null;
        return this.customerRepository.save(customer);
    }
    async getGroupStatistics(groupId) {
        const group = await this.findOne(groupId);
        const customers = await this.customerRepository.find({
            where: { groupId },
        });
        const totalCustomers = customers.length;
        const activeCustomers = customers.filter(c => c.status === 'active').length;
        const totalSpent = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0);
        const averageSpent = totalCustomers > 0 ? totalSpent / totalCustomers : 0;
        const totalLoyaltyPoints = customers.reduce((sum, c) => sum + (c.loyaltyPoints || 0), 0);
        const tierDistribution = customers.reduce((acc, customer) => {
            acc[customer.tier] = (acc[customer.tier] || 0) + 1;
            return acc;
        }, {});
        const typeDistribution = customers.reduce((acc, customer) => {
            acc[customer.type] = (acc[customer.type] || 0) + 1;
            return acc;
        }, {});
        return {
            groupId,
            groupName: group.name,
            totalCustomers,
            activeCustomers,
            inactiveCustomers: totalCustomers - activeCustomers,
            totalSpent,
            averageSpent,
            totalLoyaltyPoints,
            tierDistribution,
            typeDistribution,
            discountPercentage: group.discountPercentage,
            specialOffers: group.specialOffers,
        };
    }
    async bulkAssignCustomers(customerIds, groupId) {
        const group = await this.findOne(groupId);
        const customers = await this.customerRepository.findByIds(customerIds);
        if (customers.length !== customerIds.length) {
            throw new common_1.BadRequestException('Some customers not found');
        }
        await this.customerRepository.update({ id: (0, typeorm_2.In)(customerIds) }, { groupId });
        return this.customerRepository.findByIds(customerIds);
    }
    async getGroupsWithCustomerCounts() {
        const groups = await this.groupRepository
            .createQueryBuilder('group')
            .leftJoin('group.customers', 'customer')
            .addSelect('COUNT(customer.id)', 'customerCount')
            .groupBy('group.id')
            .getRawAndEntities();
        return groups.entities.map((group, index) => ({
            ...group,
            customerCount: parseInt(groups.raw[index].customerCount) || 0,
        }));
    }
    async applyGroupDiscountToCustomers(groupId) {
        const group = await this.findOne(groupId);
        if (group.discountPercentage && group.discountPercentage > 0) {
            await this.customerRepository.update({ groupId }, { discountPercentage: group.discountPercentage });
        }
    }
    async getActiveGroups() {
        return this.groupRepository.find({
            where: { isActive: true },
            relations: ['customers'],
            order: { name: 'ASC' },
        });
    }
    async activateGroup(id) {
        await this.groupRepository.update(id, { isActive: true });
        return this.findOne(id);
    }
    async deactivateGroup(id) {
        await this.groupRepository.update(id, { isActive: false });
        return this.findOne(id);
    }
};
exports.CustomerGroupService = CustomerGroupService;
exports.CustomerGroupService = CustomerGroupService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_group_entity_1.CustomerGroup)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerGroupService);
//# sourceMappingURL=customer-group.service.js.map