import { BudgetService } from '../services/budget.service';
export declare class BudgetController {
    private readonly budgetService;
    constructor(budgetService: BudgetService);
    create(createBudgetDto: any): Promise<import("../entities/budget.entity").Budget>;
    findAll(type?: string, status?: string, departmentId?: string): Promise<import("../entities/budget.entity").Budget[]>;
    findOne(id: string): Promise<import("../entities/budget.entity").Budget>;
    update(id: string, updateBudgetDto: any): Promise<import("../entities/budget.entity").Budget>;
    remove(id: string): Promise<void>;
    addBudgetItem(id: string, createBudgetItemDto: any): Promise<import("../entities/budget-item.entity").BudgetItem>;
    updateBudgetItem(itemId: string, updateBudgetItemDto: any): Promise<import("../entities/budget-item.entity").BudgetItem>;
    removeBudgetItem(itemId: string): Promise<void>;
    approveBudget(id: string, approveDto: {
        approvedBy: string;
    }): Promise<import("../entities/budget.entity").Budget>;
    activateBudget(id: string): Promise<import("../entities/budget.entity").Budget>;
    getBudgetVarianceReport(id: string): Promise<any>;
}
