import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Customer } from './customer.entity';

export enum NoteType {
  GENERAL = 'general',
  SALES = 'sales',
  SUPPORT = 'support',
  BILLING = 'billing',
  COMPLAINT = 'complaint',
  FEEDBACK = 'feedback',
  INTERNAL = 'internal',
  ALERT = 'alert',
}

@Entity('customer_notes')
export class CustomerNote {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer.notes)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({
    type: 'enum',
    enum: NoteType,
    default: NoteType.GENERAL,
  })
  type: NoteType;

  @Column({ length: 255, nullable: true })
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column()
  createdBy: string;

  @Column({ default: false })
  isPrivate: boolean;

  @Column({ default: false })
  isPinned: boolean;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
