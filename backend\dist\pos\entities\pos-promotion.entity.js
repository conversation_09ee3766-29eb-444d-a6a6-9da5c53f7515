"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosPromotion = exports.PromotionStatus = exports.PromotionType = void 0;
const typeorm_1 = require("typeorm");
var PromotionType;
(function (PromotionType) {
    PromotionType["PERCENTAGE_DISCOUNT"] = "percentage_discount";
    PromotionType["FIXED_DISCOUNT"] = "fixed_discount";
    PromotionType["BUY_X_GET_Y"] = "buy_x_get_y";
    PromotionType["FREE_SHIPPING"] = "free_shipping";
    PromotionType["BUNDLE_DEAL"] = "bundle_deal";
    PromotionType["LOYALTY_POINTS"] = "loyalty_points";
    PromotionType["CASHBACK"] = "cashback";
})(PromotionType || (exports.PromotionType = PromotionType = {}));
var PromotionStatus;
(function (PromotionStatus) {
    PromotionStatus["ACTIVE"] = "active";
    PromotionStatus["INACTIVE"] = "inactive";
    PromotionStatus["SCHEDULED"] = "scheduled";
    PromotionStatus["EXPIRED"] = "expired";
    PromotionStatus["PAUSED"] = "paused";
})(PromotionStatus || (exports.PromotionStatus = PromotionStatus = {}));
let PosPromotion = class PosPromotion {
    id;
    code;
    name;
    description;
    type;
    status;
    startDate;
    endDate;
    discountAmount;
    discountPercentage;
    minimumPurchase;
    maximumDiscount;
    usageLimit;
    usageCount;
    usageLimitPerCustomer;
    applicableProducts;
    applicableCategories;
    excludedProducts;
    excludedCategories;
    customerSegments;
    conditions;
    isStackable;
    requiresCouponCode;
    priority;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosPromotion = PosPromotion;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosPromotion.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PosPromotion.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PosPromotion.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosPromotion.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PromotionType,
    }),
    __metadata("design:type", String)
], PosPromotion.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PromotionStatus,
        default: PromotionStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], PosPromotion.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], PosPromotion.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], PosPromotion.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "discountAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "discountPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "minimumPurchase", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "maximumDiscount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "usageLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "usageCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "usageLimitPerCustomer", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], PosPromotion.prototype, "applicableProducts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], PosPromotion.prototype, "applicableCategories", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], PosPromotion.prototype, "excludedProducts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], PosPromotion.prototype, "excludedCategories", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], PosPromotion.prototype, "customerSegments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosPromotion.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], PosPromotion.prototype, "isStackable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosPromotion.prototype, "requiresCouponCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PosPromotion.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosPromotion.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosPromotion.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosPromotion.prototype, "updatedAt", void 0);
exports.PosPromotion = PosPromotion = __decorate([
    (0, typeorm_1.Entity)('pos_promotions')
], PosPromotion);
//# sourceMappingURL=pos-promotion.entity.js.map