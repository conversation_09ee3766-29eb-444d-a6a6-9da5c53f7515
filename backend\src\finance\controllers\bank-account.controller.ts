import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { BankAccountService } from '../services/bank-account.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/bank-accounts')
@UseGuards(JwtAuthGuard)
export class BankAccountController {
  constructor(private readonly bankAccountService: BankAccountService) {}

  @Post()
  create(@Body() createBankAccountDto: any) {
    return this.bankAccountService.create(createBankAccountDto);
  }

  @Get()
  findAll() {
    return this.bankAccountService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.bankAccountService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateBankAccountDto: any) {
    return this.bankAccountService.update(id, updateBankAccountDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.bankAccountService.remove(id);
  }

  @Post(':id/transactions')
  addTransaction(@Param('id') id: string, @Body() transactionData: any) {
    return this.bankAccountService.addTransaction(id, transactionData);
  }

  @Post(':id/import-transactions')
  importTransactions(@Param('id') id: string, @Body() importData: { transactions: any[] }) {
    return this.bankAccountService.importTransactions(id, importData.transactions);
  }

  @Post(':id/reconcile')
  reconcileAccount(
    @Param('id') id: string,
    @Body() reconcileData: { reconciledBalance: number; reconciledBy: string }
  ) {
    return this.bankAccountService.reconcileAccount(
      id,
      reconcileData.reconciledBalance,
      reconcileData.reconciledBy
    );
  }

  @Get(':id/statement')
  getAccountStatement(
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    return this.bankAccountService.getAccountStatement(id, start, end);
  }

  @Get(':id/unreconciled-transactions')
  getUnreconciledTransactions(@Param('id') id: string) {
    return this.bankAccountService.getUnreconciledTransactions(id);
  }

  @Post(':id/set-default')
  setDefaultAccount(@Param('id') id: string) {
    return this.bankAccountService.setDefaultAccount(id);
  }
}
