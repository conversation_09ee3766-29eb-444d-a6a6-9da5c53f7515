import { Repository } from 'typeorm';
import { CustomerInteraction } from '../entities/customer-interaction.entity';
import { Customer } from '../entities/customer.entity';
export declare class CustomerInteractionService {
    private interactionRepository;
    private customerRepository;
    constructor(interactionRepository: Repository<CustomerInteraction>, customerRepository: Repository<Customer>);
    create(interactionData: Partial<CustomerInteraction>): Promise<CustomerInteraction>;
    findAll(options?: {
        customerId?: string;
        type?: string;
        channel?: string;
        startDate?: Date;
        endDate?: Date;
        page?: number;
        limit?: number;
    }): Promise<{
        interactions: CustomerInteraction[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<CustomerInteraction>;
    findByCustomer(customerId: string): Promise<CustomerInteraction[]>;
    update(id: string, updateData: Partial<CustomerInteraction>): Promise<CustomerInteraction>;
    remove(id: string): Promise<void>;
    logCall(customerId: string, data: {
        duration?: number;
        outcome?: string;
        notes?: string;
        followUpRequired?: boolean;
        followUpDate?: Date;
        contactedBy?: string;
    }): Promise<CustomerInteraction>;
    logEmail(customerId: string, data: {
        subject?: string;
        outcome?: string;
        notes?: string;
        followUpRequired?: boolean;
        followUpDate?: Date;
        contactedBy?: string;
    }): Promise<CustomerInteraction>;
    logMeeting(customerId: string, data: {
        location?: string;
        duration?: number;
        outcome?: string;
        notes?: string;
        followUpRequired?: boolean;
        followUpDate?: Date;
        contactedBy?: string;
    }): Promise<CustomerInteraction>;
    getInteractionStats(customerId?: string): Promise<any>;
    getUpcomingFollowUps(days?: number): Promise<CustomerInteraction[]>;
    markFollowUpCompleted(id: string, notes?: string): Promise<CustomerInteraction>;
    getInteractionTimeline(customerId: string, days?: number): Promise<CustomerInteraction[]>;
    getInteractionSummary(customerId: string): Promise<any>;
}
