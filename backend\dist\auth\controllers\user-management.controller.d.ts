import { UserManagementService, CreateUserDto, UpdateUserDto } from '../services/user-management.service';
import { UserStatus } from '../../user/entities/user.entity';
export declare class UserManagementController {
    private readonly userManagementService;
    constructor(userManagementService: UserManagementService);
    createUser(createUserDto: CreateUserDto): Promise<import("../../user/entities/user.entity").User>;
    findAllUsers(roleId?: string, department?: string, status?: UserStatus, searchTerm?: string): Promise<import("../../user/entities/user.entity").User[]>;
    getUserStatistics(): Promise<any>;
    exportUsers(): Promise<any[]>;
    getUsersWithDepartmentAccess(department: string): Promise<import("../../user/entities/user.entity").User[]>;
    findUser(id: string): Promise<import("../../user/entities/user.entity").User>;
    getUserPermissions(id: string): Promise<import("../entities/permission.entity").Permission[]>;
    checkUserPermission(id: string, permissionCheck: {
        module: string;
        action: string;
        resource: string;
    }): Promise<import("../services/user-management.service").UserPermissionCheck>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<import("../../user/entities/user.entity").User>;
    changePassword(id: string, passwordData: {
        newPassword: string;
    }): Promise<{
        message: string;
    }>;
    activateUser(id: string): Promise<import("../../user/entities/user.entity").User>;
    deactivateUser(id: string): Promise<import("../../user/entities/user.entity").User>;
    suspendUser(id: string): Promise<import("../../user/entities/user.entity").User>;
    assignRole(id: string, roleData: {
        roleId: string;
    }): Promise<import("../../user/entities/user.entity").User>;
    assignAdditionalPermissions(id: string, permissionData: {
        permissionIds: string[];
    }): Promise<import("../../user/entities/user.entity").User>;
    removeAdditionalPermissions(id: string, permissionData: {
        permissionIds: string[];
    }): Promise<import("../../user/entities/user.entity").User>;
    bulkUpdateUsers(bulkUpdateData: {
        userIds: string[];
        updateData: Partial<UpdateUserDto>;
    }): Promise<{
        message: string;
    }>;
    deleteUser(id: string): Promise<void>;
}
