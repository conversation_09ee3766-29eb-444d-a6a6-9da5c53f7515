import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
import { Location } from './location.entity';
import { StockMovement } from './stock-movement.entity';
export declare enum StockStatus {
    AVAILABLE = "available",
    RESERVED = "reserved",
    DAMAGED = "damaged",
    EXPIRED = "expired",
    QUARANTINE = "quarantine",
    IN_TRANSIT = "in_transit"
}
export declare class Stock {
    id: string;
    productId: string;
    product: Product;
    warehouseId: string;
    warehouse: Warehouse;
    locationId: string;
    location: Location;
    quantityOnHand: number;
    quantityReserved: number;
    quantityAvailable: number;
    quantityInTransit: number;
    quantityDamaged: number;
    status: StockStatus;
    averageCost: number;
    lastCost: number;
    lastReceivedDate: Date;
    lastIssuedDate: Date;
    expiryDate: Date;
    manufactureDate: Date;
    batchNumber: string;
    serialNumber: string;
    lotNumbers: string[];
    stockMovements: StockMovement[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
