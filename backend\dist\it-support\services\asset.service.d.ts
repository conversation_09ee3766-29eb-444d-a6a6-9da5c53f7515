import { Repository } from 'typeorm';
import { Asset, AssetStatus } from '../entities/asset.entity';
import { AssetAssignment } from '../entities/asset-assignment.entity';
export declare class AssetService {
    private assetRepository;
    private assetAssignmentRepository;
    constructor(assetRepository: Repository<Asset>, assetAssignmentRepository: Repository<AssetAssignment>);
    create(assetData: Partial<Asset>): Promise<Asset>;
    findAll(): Promise<Asset[]>;
    findOne(id: string): Promise<Asset>;
    update(id: string, updateData: Partial<Asset>): Promise<Asset>;
    remove(id: string): Promise<void>;
    findByStatus(status: AssetStatus): Promise<Asset[]>;
    findByCategory(category: string): Promise<Asset[]>;
    findByLocation(location: string): Promise<Asset[]>;
    findByAssetTag(assetTag: string): Promise<Asset>;
    assignAsset(assetId: string, assignedToId: string, assignedById: string, notes?: string): Promise<AssetAssignment>;
    unassignAsset(assetId: string, unassignedById: string, notes?: string): Promise<void>;
    getAssetHistory(assetId: string): Promise<AssetAssignment[]>;
    getUserAssets(userId: string): Promise<Asset[]>;
    searchAssets(searchTerm: string): Promise<Asset[]>;
    getAssetStatistics(): Promise<any>;
    getAssetsByCategory(): Promise<Array<{
        category: string;
        count: number;
        value: number;
    }>>;
    getAssetsNearingWarrantyExpiry(days?: number): Promise<Asset[]>;
    updateAssetStatus(assetId: string, status: AssetStatus, notes?: string): Promise<Asset>;
    retireAsset(assetId: string, retiredById: string, reason: string): Promise<Asset>;
    private generateAssetTag;
    getDashboardMetrics(): Promise<any>;
}
