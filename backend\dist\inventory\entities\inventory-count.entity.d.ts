import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
export declare enum CountStatus {
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare enum CountType {
    FULL = "full",
    PARTIAL = "partial",
    CYCLE = "cycle",
    SPOT = "spot"
}
export declare class InventoryCount {
    id: string;
    countNumber: string;
    type: CountType;
    status: CountStatus;
    warehouseId: string;
    warehouse: Warehouse;
    productId: string;
    product: Product;
    scheduledDate: Date;
    startDate: Date;
    completedDate: Date;
    systemQuantity: number;
    countedQuantity: number;
    variance: number;
    varianceValue: number;
    notes: string;
    countedBy: string;
    verifiedBy: string;
    verifiedAt: Date;
    adjustmentCreated: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
