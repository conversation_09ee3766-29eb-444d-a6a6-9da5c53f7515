import { Repository } from 'typeorm';
import { KnowledgeBaseArticle } from '../entities/knowledge-base-article.entity';
export declare class KnowledgeBaseService {
    private articleRepository;
    constructor(articleRepository: Repository<KnowledgeBaseArticle>);
    create(articleData: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle>;
    findAll(): Promise<KnowledgeBaseArticle[]>;
    findOne(id: string): Promise<KnowledgeBaseArticle>;
    update(id: string, updateData: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle>;
    remove(id: string): Promise<void>;
    findByCategory(category: string): Promise<KnowledgeBaseArticle[]>;
    findByTags(tags: string[]): Promise<KnowledgeBaseArticle[]>;
    searchArticles(searchTerm: string): Promise<KnowledgeBaseArticle[]>;
    getPublishedArticles(): Promise<KnowledgeBaseArticle[]>;
    getDraftArticles(): Promise<KnowledgeBaseArticle[]>;
    publishArticle(id: string): Promise<KnowledgeBaseArticle>;
    unpublishArticle(id: string): Promise<KnowledgeBaseArticle>;
    getPopularArticles(limit?: number): Promise<KnowledgeBaseArticle[]>;
    getRecentArticles(limit?: number): Promise<KnowledgeBaseArticle[]>;
    getArticlesByAuthor(authorId: string): Promise<KnowledgeBaseArticle[]>;
    getCategories(): Promise<string[]>;
    getAllTags(): Promise<string[]>;
    getArticleStatistics(): Promise<any>;
    rateArticle(articleId: string, rating: number): Promise<KnowledgeBaseArticle>;
    getRelatedArticles(articleId: string, limit?: number): Promise<KnowledgeBaseArticle[]>;
}
