import { PaymentService } from '../services/payment.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    create(createPaymentDto: CreatePaymentDto, req: any): Promise<import("../entities/payment.entity").Payment>;
    findAll(req: any, customerId?: string, invoiceId?: string): Promise<import("../entities/payment.entity").Payment[]>;
    getStats(req: any): Promise<{
        totalPayments: number;
        receivedPayments: number;
        pendingPayments: number;
        totalAmount: number;
        averageAmount: number;
        methodStats: any[];
    }>;
    findOne(id: string, req: any): Promise<import("../entities/payment.entity").Payment>;
    update(id: string, updatePaymentDto: Partial<CreatePaymentDto>, req: any): Promise<import("../entities/payment.entity").Payment>;
    remove(id: string, req: any): Promise<void>;
    updateStatus(id: string, body: {
        status: string;
    }, req: any): Promise<import("../entities/payment.entity").Payment>;
}
