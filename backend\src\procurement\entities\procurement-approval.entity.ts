import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ProcurementRequest } from './procurement-request.entity';

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELEGATED = 'delegated',
}

export enum ApprovalLevel {
  SUPERVISOR = 'supervisor',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  VP = 'vp',
  CEO = 'ceo',
  BOARD = 'board',
}

@Entity('procurement_approvals')
export class ProcurementApproval {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  requestId: string;

  @ManyToOne(() => ProcurementRequest, request => request.approvals)
  @JoinColumn({ name: 'requestId' })
  request: ProcurementRequest;

  @Column({
    type: 'enum',
    enum: ApprovalLevel,
  })
  level: ApprovalLevel;

  @Column({
    type: 'enum',
    enum: ApprovalStatus,
    default: ApprovalStatus.PENDING,
  })
  status: ApprovalStatus;

  @Column()
  approverId: string;

  @Column({ type: 'int', default: 0 })
  sequence: number;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ nullable: true })
  delegatedTo: string;

  @Column({ type: 'timestamp', nullable: true })
  delegatedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
