import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { VendorContact } from './vendor-contact.entity';
import { VendorEvaluation } from './vendor-evaluation.entity';
import { Contract } from './contract.entity';

export enum VendorStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLACKLISTED = 'blacklisted',
  PENDING_APPROVAL = 'pending_approval',
}

export enum VendorType {
  SUPPLIER = 'supplier',
  SERVICE_PROVIDER = 'service_provider',
  CONTRACTOR = 'contractor',
  CONSULTANT = 'consultant',
  DISTRIBUTOR = 'distributor',
  MANUFACTURER = 'manufacturer',
}

@Entity('vendors')
export class Vendor {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  vendorNumber: string;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 255, nullable: true })
  legalName: string;

  @Column({
    type: 'enum',
    enum: VendorType,
    default: VendorType.SUPPLIER,
  })
  type: VendorType;

  @Column({
    type: 'enum',
    enum: VendorStatus,
    default: VendorStatus.ACTIVE,
  })
  status: VendorStatus;

  @Column({ length: 50, nullable: true })
  taxId: string;

  @Column({ length: 50, nullable: true })
  registrationNumber: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ length: 100, nullable: true })
  city: string;

  @Column({ length: 100, nullable: true })
  state: string;

  @Column({ length: 20, nullable: true })
  zipCode: string;

  @Column({ length: 100, nullable: true })
  country: string;

  @Column({ length: 20, nullable: true })
  phone: string;

  @Column({ length: 20, nullable: true })
  fax: string;

  @Column({ length: 200, nullable: true })
  email: string;

  @Column({ length: 255, nullable: true })
  website: string;

  @Column({ type: 'json', nullable: true })
  categories: string[];

  @Column({ type: 'json', nullable: true })
  certifications: string[];

  @Column({ type: 'int', default: 30 })
  paymentTerms: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  creditLimit: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  currentBalance: number;

  @Column({ type: 'int', default: 7 })
  leadTimeDays: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  minimumOrderAmount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  discountPercentage: number;

  @Column({ type: 'decimal', precision: 2, scale: 1, nullable: true })
  rating: number;

  @Column({ type: 'json', nullable: true })
  bankDetails: any;

  @Column({ type: 'json', nullable: true })
  insuranceDetails: any;

  @Column({ type: 'json', nullable: true })
  qualityMetrics: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @OneToMany(() => VendorContact, contact => contact.vendor, { cascade: true })
  contacts: VendorContact[];

  @OneToMany(() => VendorEvaluation, evaluation => evaluation.vendor)
  evaluations: VendorEvaluation[];

  @OneToMany(() => Contract, contract => contract.vendor)
  contracts: Contract[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
