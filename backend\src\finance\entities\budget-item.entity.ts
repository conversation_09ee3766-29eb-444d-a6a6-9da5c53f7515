import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Budget } from './budget.entity';
import { Account } from './account.entity';

export enum BudgetItemType {
  REVENUE = 'revenue',
  EXPENSE = 'expense',
  CAPITAL = 'capital',
}

export enum BudgetPeriod {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUALLY = 'annually',
}

@Entity('finance_budget_items')
export class BudgetItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  budgetId: string;

  @ManyToOne(() => Budget, budget => budget.budgetItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'budgetId' })
  budget: Budget;

  @Column()
  accountId: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'accountId' })
  account: Account;

  @Column({ length: 255 })
  itemName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: BudgetItemType,
  })
  type: BudgetItemType;

  @Column({
    type: 'enum',
    enum: BudgetPeriod,
    default: BudgetPeriod.MONTHLY,
  })
  period: BudgetPeriod;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  budgetedAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  actualAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  variance: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  variancePercentage: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  encumberedAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  availableAmount: number;

  @Column({ type: 'json', nullable: true })
  monthlyBreakdown: any; // For detailed monthly planning

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
