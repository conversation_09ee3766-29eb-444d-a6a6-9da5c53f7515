import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, Like } from 'typeorm';
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { AccountService } from './account.service';

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private accountService: AccountService,
  ) {}

  async create(createTransactionDto: any): Promise<Transaction> {
    // Validate accounts exist
    await this.accountService.findOne(createTransactionDto.debitAccountId);
    await this.accountService.findOne(createTransactionDto.creditAccountId);

    // Generate transaction number
    const transactionNumber = await this.generateTransactionNumber();

    const transaction = this.transactionRepository.create({
      ...createTransactionDto,
      transactionNumber,
      baseCurrencyAmount: createTransactionDto.amount * (createTransactionDto.exchangeRate || 1),
    });

    const savedTransaction = await this.transactionRepository.save(transaction);

    // Update account balances if transaction is posted
    if (savedTransaction.status === TransactionStatus.POSTED) {
      await this.updateAccountBalances(savedTransaction);
    }

    return savedTransaction;
  }

  async findAll(filters?: any): Promise<Transaction[]> {
    const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.debitAccount', 'debitAccount')
      .leftJoinAndSelect('transaction.creditAccount', 'creditAccount');

    if (filters?.startDate && filters?.endDate) {
      queryBuilder.andWhere('transaction.transactionDate BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    if (filters?.type) {
      queryBuilder.andWhere('transaction.type = :type', { type: filters.type });
    }

    if (filters?.status) {
      queryBuilder.andWhere('transaction.status = :status', { status: filters.status });
    }

    if (filters?.accountId) {
      queryBuilder.andWhere(
        '(transaction.debitAccountId = :accountId OR transaction.creditAccountId = :accountId)',
        { accountId: filters.accountId }
      );
    }

    return queryBuilder
      .orderBy('transaction.transactionDate', 'DESC')
      .addOrderBy('transaction.createdAt', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { id },
      relations: ['debitAccount', 'creditAccount'],
    });

    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found`);
    }

    return transaction;
  }

  async update(id: string, updateTransactionDto: any): Promise<Transaction> {
    const transaction = await this.findOne(id);

    if (transaction.status === TransactionStatus.POSTED) {
      throw new BadRequestException('Cannot update posted transaction');
    }

    Object.assign(transaction, updateTransactionDto);

    if (updateTransactionDto.amount || updateTransactionDto.exchangeRate) {
      transaction.baseCurrencyAmount = transaction.amount * (transaction.exchangeRate || 1);
    }

    return this.transactionRepository.save(transaction);
  }

  async remove(id: string): Promise<void> {
    const transaction = await this.findOne(id);

    if (transaction.status === TransactionStatus.POSTED) {
      throw new BadRequestException('Cannot delete posted transaction');
    }

    await this.transactionRepository.remove(transaction);
  }

  async postTransaction(id: string, approvedBy?: string): Promise<Transaction> {
    const transaction = await this.findOne(id);

    if (transaction.status === TransactionStatus.POSTED) {
      throw new BadRequestException('Transaction is already posted');
    }

    transaction.status = TransactionStatus.POSTED;
    transaction.approvedBy = approvedBy;
    transaction.approvedAt = new Date();

    const savedTransaction = await this.transactionRepository.save(transaction);

    // Update account balances
    await this.updateAccountBalances(savedTransaction);

    return savedTransaction;
  }

  async reverseTransaction(id: string, reason: string, reversedBy?: string): Promise<Transaction> {
    const originalTransaction = await this.findOne(id);

    if (originalTransaction.status !== TransactionStatus.POSTED) {
      throw new BadRequestException('Can only reverse posted transactions');
    }

    // Create reversal transaction
    const reversalTransaction = this.transactionRepository.create({
      type: TransactionType.ADJUSTMENT,
      status: TransactionStatus.POSTED,
      transactionDate: new Date(),
      description: `Reversal of ${originalTransaction.transactionNumber}: ${reason}`,
      reference: originalTransaction.transactionNumber,
      amount: originalTransaction.amount,
      debitAccountId: originalTransaction.creditAccountId, // Swap accounts
      creditAccountId: originalTransaction.debitAccountId,
      currency: originalTransaction.currency,
      exchangeRate: originalTransaction.exchangeRate,
      baseCurrencyAmount: originalTransaction.baseCurrencyAmount,
      relatedEntityType: 'transaction',
      relatedEntityId: originalTransaction.id,
      approvedBy: reversedBy,
      approvedAt: new Date(),
      transactionNumber: await this.generateTransactionNumber(),
    });

    const savedReversal = await this.transactionRepository.save(reversalTransaction);

    // Update original transaction status
    originalTransaction.status = TransactionStatus.REVERSED;
    await this.transactionRepository.save(originalTransaction);

    // Update account balances
    await this.updateAccountBalances(savedReversal);

    return savedReversal;
  }

  async getAccountLedger(accountId: string, startDate?: Date, endDate?: Date): Promise<any> {
    const account = await this.accountService.findOne(accountId);

    const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
      .where('(transaction.debitAccountId = :accountId OR transaction.creditAccountId = :accountId)',
        { accountId })
      .andWhere('transaction.status = :status', { status: TransactionStatus.POSTED });

    if (startDate && endDate) {
      queryBuilder.andWhere('transaction.transactionDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const transactions = await queryBuilder
      .orderBy('transaction.transactionDate', 'ASC')
      .addOrderBy('transaction.createdAt', 'ASC')
      .getMany();

    let runningBalance = 0;
    const ledgerEntries = transactions.map(transaction => {
      const isDebit = transaction.debitAccountId === accountId;
      const amount = isDebit ? transaction.amount : -transaction.amount;
      runningBalance += amount;

      return {
        date: transaction.transactionDate,
        transactionNumber: transaction.transactionNumber,
        description: transaction.description,
        reference: transaction.reference,
        debit: isDebit ? transaction.amount : 0,
        credit: !isDebit ? transaction.amount : 0,
        balance: runningBalance,
      };
    });

    return {
      account: {
        id: account.id,
        accountNumber: account.accountNumber,
        name: account.name,
        type: account.type,
      },
      period: { startDate, endDate },
      entries: ledgerEntries,
      finalBalance: runningBalance,
    };
  }

  private async updateAccountBalances(transaction: Transaction): Promise<void> {
    // Update debit account
    await this.accountService.updateBalance(
      transaction.debitAccountId,
      transaction.amount,
      true
    );

    // Update credit account
    await this.accountService.updateBalance(
      transaction.creditAccountId,
      transaction.amount,
      false
    );
  }

  private async generateTransactionNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');

    const prefix = `TXN-${year}${month}-`;

    const lastTransaction = await this.transactionRepository.findOne({
      where: { transactionNumber: Like(`${prefix}%`) },
      order: { transactionNumber: 'DESC' },
    });

    let nextNumber = 1;
    if (lastTransaction) {
      const lastNumber = parseInt(lastTransaction.transactionNumber.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
  }
}
