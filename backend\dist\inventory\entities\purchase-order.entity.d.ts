import { Supplier } from './supplier.entity';
import { PurchaseOrderItem } from './purchase-order-item.entity';
export declare enum PurchaseOrderStatus {
    DRAFT = "draft",
    PENDING = "pending",
    APPROVED = "approved",
    SENT = "sent",
    PARTIALLY_RECEIVED = "partially_received",
    RECEIVED = "received",
    CANCELLED = "cancelled",
    CLOSED = "closed"
}
export declare enum PurchaseOrderType {
    STANDARD = "standard",
    BLANKET = "blanket",
    CONTRACT = "contract",
    PLANNED = "planned"
}
export declare class PurchaseOrder {
    id: string;
    orderNumber: string;
    type: PurchaseOrderType;
    status: PurchaseOrderStatus;
    supplierId: string;
    supplier: Supplier;
    orderDate: Date;
    expectedDeliveryDate: Date;
    actualDeliveryDate: Date;
    subtotal: number;
    taxAmount: number;
    shippingAmount: number;
    discountAmount: number;
    totalAmount: number;
    currency: string;
    exchangeRate: number;
    shippingAddress: string;
    billingAddress: string;
    terms: string;
    notes: string;
    requestedBy: string;
    approvedBy: string;
    approvedAt: Date;
    receivedBy: string;
    receivedAt: Date;
    items: PurchaseOrderItem[];
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
