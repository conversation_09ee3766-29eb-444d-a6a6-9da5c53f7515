import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission, PermissionModule, PermissionAction } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';

@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) {}

  async createDefaultPermissions(): Promise<Permission[]> {
    const permissions = this.getDefaultPermissions();
    const createdPermissions = [];

    for (const permissionData of permissions) {
      const existing = await this.permissionRepository.findOne({
        where: {
          module: permissionData.module,
          action: permissionData.action,
          resource: permissionData.resource,
        },
      });

      if (!existing) {
        const permission = this.permissionRepository.create(permissionData);
        const saved = await this.permissionRepository.save(permission);
        createdPermissions.push(saved);
      }
    }

    return createdPermissions;
  }

  private getDefaultPermissions(): Partial<Permission>[] {
    return [
      // ANALYTICS DEPARTMENT
      {
        module: PermissionModule.ANALYTICS,
        action: PermissionAction.READ,
        resource: 'dashboard',
        name: 'View Analytics Dashboard',
        description: 'Access to view analytics dashboard and basic metrics',
      },
      {
        module: PermissionModule.ANALYTICS,
        action: PermissionAction.VIEW_REPORTS,
        resource: 'reports',
        name: 'View Analytics Reports',
        description: 'Access to view detailed analytics reports',
      },
      {
        module: PermissionModule.ANALYTICS,
        action: PermissionAction.GENERATE_REPORTS,
        resource: 'reports',
        name: 'Generate Analytics Reports',
        description: 'Ability to generate custom analytics reports',
      },
      {
        module: PermissionModule.ANALYTICS,
        action: PermissionAction.EXPORT,
        resource: 'data',
        name: 'Export Analytics Data',
        description: 'Export analytics data to various formats',
      },

      // CUSTOMERS DEPARTMENT
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.CREATE,
        resource: 'customer',
        name: 'Create Customer',
        description: 'Add new customers to the system',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.READ,
        resource: 'customer',
        name: 'View Customer',
        description: 'View customer information and details',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.UPDATE,
        resource: 'customer',
        name: 'Update Customer',
        description: 'Edit customer information and details',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.DELETE,
        resource: 'customer',
        name: 'Delete Customer',
        description: 'Remove customers from the system',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.CREATE,
        resource: 'interaction',
        name: 'Log Customer Interaction',
        description: 'Record interactions with customers',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.READ,
        resource: 'interaction',
        name: 'View Customer Interactions',
        description: 'View customer interaction history',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.CREATE,
        resource: 'segment',
        name: 'Create Customer Segment',
        description: 'Create customer segments for marketing',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.MANAGE_SETTINGS,
        resource: 'loyalty',
        name: 'Manage Customer Loyalty',
        description: 'Manage customer loyalty programs and points',
      },
      {
        module: PermissionModule.CUSTOMERS,
        action: PermissionAction.ADJUST,
        resource: 'credit',
        name: 'Adjust Customer Credit',
        description: 'Modify customer credit limits and terms',
      },

      // COLLECTIONS DEPARTMENT
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.CREATE,
        resource: 'case',
        name: 'Create Collection Case',
        description: 'Create new collection cases for overdue accounts',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.READ,
        resource: 'case',
        name: 'View Collection Cases',
        description: 'View collection case details and status',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.UPDATE,
        resource: 'case',
        name: 'Update Collection Case',
        description: 'Update collection case information and status',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.ASSIGN,
        resource: 'case',
        name: 'Assign Collection Case',
        description: 'Assign collection cases to agents',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.ESCALATE,
        resource: 'case',
        name: 'Escalate Collection Case',
        description: 'Escalate collection cases to higher priority',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.CLOSE,
        resource: 'case',
        name: 'Close Collection Case',
        description: 'Close resolved collection cases',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.CREATE,
        resource: 'activity',
        name: 'Log Collection Activity',
        description: 'Record collection activities and communications',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.CREATE,
        resource: 'payment_plan',
        name: 'Create Payment Plan',
        description: 'Set up payment plans for customers',
      },
      {
        module: PermissionModule.COLLECTIONS,
        action: PermissionAction.PROCESS_PAYMENT,
        resource: 'payment',
        name: 'Process Collection Payment',
        description: 'Process payments against collection cases',
      },

      // FINANCE DEPARTMENT
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.CREATE,
        resource: 'transaction',
        name: 'Create Financial Transaction',
        description: 'Record financial transactions',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.READ,
        resource: 'transaction',
        name: 'View Financial Transactions',
        description: 'View financial transaction details',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.APPROVE,
        resource: 'transaction',
        name: 'Approve Financial Transaction',
        description: 'Approve pending financial transactions',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.CREATE,
        resource: 'invoice',
        name: 'Create Invoice',
        description: 'Generate customer invoices',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.UPDATE,
        resource: 'invoice',
        name: 'Update Invoice',
        description: 'Modify invoice details',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.VOID,
        resource: 'invoice',
        name: 'Void Invoice',
        description: 'Cancel or void invoices',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.PROCESS_PAYMENT,
        resource: 'payment',
        name: 'Process Payment',
        description: 'Process customer payments',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.REFUND,
        resource: 'payment',
        name: 'Process Refund',
        description: 'Process customer refunds',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.VIEW_REPORTS,
        resource: 'reports',
        name: 'View Financial Reports',
        description: 'Access financial reports and statements',
      },
      {
        module: PermissionModule.FINANCE,
        action: PermissionAction.GENERATE_REPORTS,
        resource: 'reports',
        name: 'Generate Financial Reports',
        description: 'Create custom financial reports',
      },

      // HR DEPARTMENT
      {
        module: PermissionModule.HR,
        action: PermissionAction.CREATE,
        resource: 'employee',
        name: 'Create Employee',
        description: 'Add new employees to the system',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.READ,
        resource: 'employee',
        name: 'View Employee',
        description: 'View employee information and records',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.UPDATE,
        resource: 'employee',
        name: 'Update Employee',
        description: 'Edit employee information and records',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.DELETE,
        resource: 'employee',
        name: 'Delete Employee',
        description: 'Remove employees from the system',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.READ,
        resource: 'payroll',
        name: 'View Payroll',
        description: 'Access payroll information',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.PROCESS_PAYMENT,
        resource: 'payroll',
        name: 'Process Payroll',
        description: 'Process employee payroll',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.CREATE,
        resource: 'leave',
        name: 'Manage Leave Requests',
        description: 'Create and manage employee leave requests',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.APPROVE,
        resource: 'leave',
        name: 'Approve Leave Requests',
        description: 'Approve or reject employee leave requests',
      },
      {
        module: PermissionModule.HR,
        action: PermissionAction.CREATE,
        resource: 'performance',
        name: 'Manage Performance Reviews',
        description: 'Create and manage employee performance reviews',
      },

      // INVENTORY DEPARTMENT
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.CREATE,
        resource: 'product',
        name: 'Create Product',
        description: 'Add new products to inventory',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.READ,
        resource: 'product',
        name: 'View Product',
        description: 'View product information and details',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.UPDATE,
        resource: 'product',
        name: 'Update Product',
        description: 'Edit product information and pricing',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.DELETE,
        resource: 'product',
        name: 'Delete Product',
        description: 'Remove products from inventory',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.ADJUST,
        resource: 'stock',
        name: 'Adjust Stock',
        description: 'Adjust product stock levels',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.READ,
        resource: 'stock',
        name: 'View Stock Levels',
        description: 'View current stock levels and availability',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.CREATE,
        resource: 'purchase_order',
        name: 'Create Purchase Order',
        description: 'Create purchase orders for suppliers',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.APPROVE,
        resource: 'purchase_order',
        name: 'Approve Purchase Order',
        description: 'Approve purchase orders',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.CREATE,
        resource: 'supplier',
        name: 'Manage Suppliers',
        description: 'Add and manage supplier information',
      },
      {
        module: PermissionModule.INVENTORY,
        action: PermissionAction.CREATE,
        resource: 'warehouse',
        name: 'Manage Warehouses',
        description: 'Create and manage warehouse locations',
      },

      // IT SUPPORT DEPARTMENT
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.CREATE,
        resource: 'ticket',
        name: 'Create Support Ticket',
        description: 'Create new IT support tickets',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.READ,
        resource: 'ticket',
        name: 'View Support Tickets',
        description: 'View IT support ticket details',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.UPDATE,
        resource: 'ticket',
        name: 'Update Support Ticket',
        description: 'Update IT support ticket information',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.ASSIGN,
        resource: 'ticket',
        name: 'Assign Support Ticket',
        description: 'Assign tickets to IT support agents',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.ESCALATE,
        resource: 'ticket',
        name: 'Escalate Support Ticket',
        description: 'Escalate tickets to higher support levels',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.CLOSE,
        resource: 'ticket',
        name: 'Close Support Ticket',
        description: 'Close resolved support tickets',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.CREATE,
        resource: 'asset',
        name: 'Manage IT Assets',
        description: 'Create and manage IT assets',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.ASSIGN,
        resource: 'asset',
        name: 'Assign IT Assets',
        description: 'Assign IT assets to employees',
      },
      {
        module: PermissionModule.IT_SUPPORT,
        action: PermissionAction.CREATE,
        resource: 'knowledge_base',
        name: 'Manage Knowledge Base',
        description: 'Create and manage knowledge base articles',
      },

      // POS DEPARTMENT
      {
        module: PermissionModule.POS,
        action: PermissionAction.CREATE,
        resource: 'sale',
        name: 'Process Sale',
        description: 'Process customer sales transactions',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.READ,
        resource: 'sale',
        name: 'View Sales',
        description: 'View sales transaction details',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.VOID,
        resource: 'sale',
        name: 'Void Sale',
        description: 'Cancel or void sales transactions',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.REFUND,
        resource: 'sale',
        name: 'Process Refund',
        description: 'Process customer refunds',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.PROCESS_PAYMENT,
        resource: 'payment',
        name: 'Process Payment',
        description: 'Process various payment methods',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.READ,
        resource: 'cash_register',
        name: 'Access Cash Register',
        description: 'Access and operate cash register',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.MANAGE_SETTINGS,
        resource: 'cash_register',
        name: 'Manage Cash Register',
        description: 'Open/close cash register and manage cash',
      },
      {
        module: PermissionModule.POS,
        action: PermissionAction.PRINT,
        resource: 'receipt',
        name: 'Print Receipts',
        description: 'Print customer receipts',
      },

      // PROCUREMENT DEPARTMENT
      {
        module: PermissionModule.PROCUREMENT,
        action: PermissionAction.CREATE,
        resource: 'purchase_request',
        name: 'Create Purchase Request',
        description: 'Create new purchase requests',
      },
      {
        module: PermissionModule.PROCUREMENT,
        action: PermissionAction.APPROVE,
        resource: 'purchase_request',
        name: 'Approve Purchase Request',
        description: 'Approve purchase requests',
      },
      {
        module: PermissionModule.PROCUREMENT,
        action: PermissionAction.CREATE,
        resource: 'vendor',
        name: 'Manage Vendors',
        description: 'Add and manage vendor information',
      },
      {
        module: PermissionModule.PROCUREMENT,
        action: PermissionAction.CREATE,
        resource: 'rfq',
        name: 'Manage RFQ',
        description: 'Create and manage Request for Quotations',
      },
      {
        module: PermissionModule.PROCUREMENT,
        action: PermissionAction.CREATE,
        resource: 'contract',
        name: 'Manage Contracts',
        description: 'Create and manage procurement contracts',
      },

      // PROJECTS DEPARTMENT
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.CREATE,
        resource: 'project',
        name: 'Create Project',
        description: 'Create new projects',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.READ,
        resource: 'project',
        name: 'View Projects',
        description: 'View project details and status',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.UPDATE,
        resource: 'project',
        name: 'Update Project',
        description: 'Edit project information and settings',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.DELETE,
        resource: 'project',
        name: 'Delete Project',
        description: 'Remove projects from the system',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.CREATE,
        resource: 'task',
        name: 'Manage Tasks',
        description: 'Create and manage project tasks',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.ASSIGN,
        resource: 'task',
        name: 'Assign Tasks',
        description: 'Assign tasks to team members',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.CREATE,
        resource: 'milestone',
        name: 'Manage Milestones',
        description: 'Create and manage project milestones',
      },
      {
        module: PermissionModule.PROJECTS,
        action: PermissionAction.ASSIGN,
        resource: 'resource',
        name: 'Manage Resources',
        description: 'Assign and manage project resources',
      },

      // SALES DEPARTMENT
      {
        module: PermissionModule.SALES,
        action: PermissionAction.CREATE,
        resource: 'lead',
        name: 'Create Lead',
        description: 'Add new sales leads',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.READ,
        resource: 'lead',
        name: 'View Leads',
        description: 'View sales lead information',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.UPDATE,
        resource: 'lead',
        name: 'Update Lead',
        description: 'Edit sales lead information',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.ASSIGN,
        resource: 'lead',
        name: 'Assign Lead',
        description: 'Assign leads to sales representatives',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.CREATE,
        resource: 'opportunity',
        name: 'Manage Opportunities',
        description: 'Create and manage sales opportunities',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.CREATE,
        resource: 'quote',
        name: 'Create Quote',
        description: 'Generate customer quotes',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.APPROVE,
        resource: 'quote',
        name: 'Approve Quote',
        description: 'Approve customer quotes',
      },
      {
        module: PermissionModule.SALES,
        action: PermissionAction.CREATE,
        resource: 'order',
        name: 'Create Sales Order',
        description: 'Create customer sales orders',
      },

      // SETTINGS DEPARTMENT
      {
        module: PermissionModule.SETTINGS,
        action: PermissionAction.MANAGE_SETTINGS,
        resource: 'system',
        name: 'Manage System Settings',
        description: 'Configure system-wide settings',
      },
      {
        module: PermissionModule.SETTINGS,
        action: PermissionAction.CONFIGURE,
        resource: 'company',
        name: 'Configure Company Settings',
        description: 'Configure company information and settings',
      },
      {
        module: PermissionModule.SETTINGS,
        action: PermissionAction.MANAGE_SETTINGS,
        resource: 'integration',
        name: 'Manage Integrations',
        description: 'Configure third-party integrations',
      },
      {
        module: PermissionModule.SETTINGS,
        action: PermissionAction.BACKUP,
        resource: 'data',
        name: 'Backup Data',
        description: 'Create system backups',
      },
      {
        module: PermissionModule.SETTINGS,
        action: PermissionAction.RESTORE,
        resource: 'data',
        name: 'Restore Data',
        description: 'Restore system from backups',
      },

      // USER MANAGEMENT
      {
        module: PermissionModule.USER_MANAGEMENT,
        action: PermissionAction.MANAGE_USERS,
        resource: 'user',
        name: 'Manage Users',
        description: 'Create, edit, and manage user accounts',
      },
      {
        module: PermissionModule.USER_MANAGEMENT,
        action: PermissionAction.MANAGE_ROLES,
        resource: 'role',
        name: 'Manage Roles',
        description: 'Create and manage user roles',
      },
      {
        module: PermissionModule.USER_MANAGEMENT,
        action: PermissionAction.MANAGE_PERMISSIONS,
        resource: 'permission',
        name: 'Manage Permissions',
        description: 'Configure user permissions',
      },
      {
        module: PermissionModule.USER_MANAGEMENT,
        action: PermissionAction.AUDIT,
        resource: 'activity',
        name: 'View Audit Logs',
        description: 'Access user activity audit logs',
      },
    ];
  }

  async findAll(): Promise<Permission[]> {
    return this.permissionRepository.find({
      order: { module: 'ASC', resource: 'ASC', action: 'ASC' },
    });
  }

  async findByModule(module: PermissionModule): Promise<Permission[]> {
    return this.permissionRepository.find({
      where: { module },
      order: { resource: 'ASC', action: 'ASC' },
    });
  }

  async getPermissionsByRole(roleId: string): Promise<Permission[]> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });

    return role?.permissions || [];
  }

  async getGroupedPermissions(): Promise<any> {
    const permissions = await this.findAll();
    const grouped = {};

    permissions.forEach(permission => {
      if (!grouped[permission.module]) {
        grouped[permission.module] = {};
      }
      if (!grouped[permission.module][permission.resource]) {
        grouped[permission.module][permission.resource] = [];
      }
      grouped[permission.module][permission.resource].push(permission);
    });

    return grouped;
  }
}
