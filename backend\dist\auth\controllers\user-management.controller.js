"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManagementController = void 0;
const common_1 = require("@nestjs/common");
const user_management_service_1 = require("../services/user-management.service");
const user_entity_1 = require("../../user/entities/user.entity");
const jwt_auth_guard_1 = require("../guards/jwt-auth.guard");
let UserManagementController = class UserManagementController {
    userManagementService;
    constructor(userManagementService) {
        this.userManagementService = userManagementService;
    }
    async createUser(createUserDto) {
        return this.userManagementService.createUser(createUserDto);
    }
    async findAllUsers(roleId, department, status, searchTerm) {
        if (searchTerm) {
            return this.userManagementService.searchUsers(searchTerm);
        }
        if (roleId) {
            return this.userManagementService.getUsersByRole(roleId);
        }
        if (department) {
            return this.userManagementService.getUsersByDepartment(department);
        }
        return this.userManagementService.findAllUsers();
    }
    async getUserStatistics() {
        return this.userManagementService.getUserStatistics();
    }
    async exportUsers() {
        return this.userManagementService.exportUsers();
    }
    async getUsersWithDepartmentAccess(department) {
        return this.userManagementService.getUsersWithDepartmentAccess(department);
    }
    async findUser(id) {
        return this.userManagementService.findUserById(id);
    }
    async getUserPermissions(id) {
        return this.userManagementService.getUserPermissions(id);
    }
    async checkUserPermission(id, permissionCheck) {
        return this.userManagementService.checkUserPermission(id, permissionCheck.module, permissionCheck.action, permissionCheck.resource);
    }
    async updateUser(id, updateUserDto) {
        return this.userManagementService.updateUser(id, updateUserDto);
    }
    async changePassword(id, passwordData) {
        await this.userManagementService.changeUserPassword(id, passwordData.newPassword);
        return { message: 'Password updated successfully' };
    }
    async activateUser(id) {
        return this.userManagementService.activateUser(id);
    }
    async deactivateUser(id) {
        return this.userManagementService.deactivateUser(id);
    }
    async suspendUser(id) {
        return this.userManagementService.suspendUser(id);
    }
    async assignRole(id, roleData) {
        return this.userManagementService.assignRole(id, roleData.roleId);
    }
    async assignAdditionalPermissions(id, permissionData) {
        return this.userManagementService.assignAdditionalPermissions(id, permissionData.permissionIds);
    }
    async removeAdditionalPermissions(id, permissionData) {
        return this.userManagementService.removeAdditionalPermissions(id, permissionData.permissionIds);
    }
    async bulkUpdateUsers(bulkUpdateData) {
        await this.userManagementService.bulkUpdateUsers(bulkUpdateData.userIds, bulkUpdateData.updateData);
        return { message: 'Users updated successfully' };
    }
    async deleteUser(id) {
        return this.userManagementService.deleteUser(id);
    }
};
exports.UserManagementController = UserManagementController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "createUser", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('role')),
    __param(1, (0, common_1.Query)('department')),
    __param(2, (0, common_1.Query)('status')),
    __param(3, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "findAllUsers", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getUserStatistics", null);
__decorate([
    (0, common_1.Get)('export'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "exportUsers", null);
__decorate([
    (0, common_1.Get)('department/:department/access'),
    __param(0, (0, common_1.Param)('department')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getUsersWithDepartmentAccess", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "findUser", null);
__decorate([
    (0, common_1.Get)(':id/permissions'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getUserPermissions", null);
__decorate([
    (0, common_1.Post)(':id/check-permission'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "checkUserPermission", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Patch)(':id/password'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Patch)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "activateUser", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "deactivateUser", null);
__decorate([
    (0, common_1.Patch)(':id/suspend'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "suspendUser", null);
__decorate([
    (0, common_1.Patch)(':id/role'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "assignRole", null);
__decorate([
    (0, common_1.Patch)(':id/additional-permissions'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "assignAdditionalPermissions", null);
__decorate([
    (0, common_1.Delete)(':id/additional-permissions'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "removeAdditionalPermissions", null);
__decorate([
    (0, common_1.Post)('bulk-update'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "bulkUpdateUsers", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "deleteUser", null);
exports.UserManagementController = UserManagementController = __decorate([
    (0, common_1.Controller)('user-management'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [user_management_service_1.UserManagementService])
], UserManagementController);
//# sourceMappingURL=user-management.controller.js.map