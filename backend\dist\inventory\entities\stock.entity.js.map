{"version": 3, "file": "stock.entity.js", "sourceRoot": "", "sources": ["../../../src/inventory/entities/stock.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAUiB;AACjB,qDAA2C;AAC3C,yDAA+C;AAC/C,uDAA6C;AAC7C,mEAAwD;AAExD,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,sCAAuB,CAAA;IACvB,oCAAqB,CAAA;IACrB,kCAAmB,CAAA;IACnB,kCAAmB,CAAA;IACnB,wCAAyB,CAAA;IACzB,wCAAyB,CAAA;AAC3B,CAAC,EAPW,WAAW,2BAAX,WAAW,QAOtB;AAIM,IAAM,KAAK,GAAX,MAAM,KAAK;IAEhB,EAAE,CAAS;IAGX,SAAS,CAAS;IAIlB,OAAO,CAAU;IAGjB,WAAW,CAAS;IAIpB,SAAS,CAAY;IAGrB,UAAU,CAAS;IAInB,QAAQ,CAAW;IAGnB,cAAc,CAAS;IAGvB,gBAAgB,CAAS;IAGzB,iBAAiB,CAAS;IAG1B,iBAAiB,CAAS;IAG1B,eAAe,CAAS;IAOxB,MAAM,CAAc;IAGpB,WAAW,CAAS;IAGpB,QAAQ,CAAS;IAGjB,gBAAgB,CAAO;IAGvB,cAAc,CAAO;IAGrB,UAAU,CAAO;IAGjB,eAAe,CAAO;IAGtB,WAAW,CAAS;IAGpB,YAAY,CAAS;IAGrB,UAAU,CAAW;IAGrB,cAAc,CAAkB;IAGhC,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AArFY,sBAAK;AAEhB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;iCACpB;AAGX;IADC,IAAA,gBAAM,GAAE;;wCACS;AAIlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,wBAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;IACnD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;8BACzB,wBAAO;sCAAC;AAGjB;IADC,IAAA,gBAAM,GAAE;;0CACW;AAIpB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,4BAAS,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;IACzD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;8BACzB,4BAAS;wCAAC;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCACR;AAInB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,0BAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC1E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,0BAAQ;uCAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CACb;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CACX;AAGzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDACV;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDACV;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;8CACZ;AAOxB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,WAAW,CAAC,SAAS;KAC/B,CAAC;;qCACkB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACjD;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACpD;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACvB,IAAI;+CAAC;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACzB,IAAI;6CAAC;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAC7B,IAAI;yCAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACxB,IAAI;8CAAC;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACpB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CACnB;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCACpB;AAGrB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,qCAAa,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;;6CAC3B;AAGhC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;wCAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;wCAAC;gBApFL,KAAK;IAFjB,IAAA,gBAAM,EAAC,iBAAiB,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;GACvD,KAAK,CAqFjB"}