import { Project } from './project.entity';
export declare enum DocumentType {
    SPECIFICATION = "specification",
    DESIGN = "design",
    CONTRACT = "contract",
    PROPOSAL = "proposal",
    REPORT = "report",
    PRESENTATION = "presentation",
    MANUAL = "manual",
    TEMPLATE = "template",
    OTHER = "other"
}
export declare class ProjectDocument {
    id: string;
    projectId: string;
    project: Project;
    name: string;
    description: string;
    type: DocumentType;
    fileName: string;
    originalName: string;
    filePath: string;
    mimeType: string;
    fileSize: number;
    version: string;
    uploadedBy: string;
    tags: string[];
    isActive: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
