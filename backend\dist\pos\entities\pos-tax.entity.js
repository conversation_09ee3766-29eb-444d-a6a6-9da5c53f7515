"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosTax = exports.TaxType = void 0;
const typeorm_1 = require("typeorm");
const pos_sale_entity_1 = require("./pos-sale.entity");
var TaxType;
(function (TaxType) {
    TaxType["SALES_TAX"] = "sales_tax";
    TaxType["VAT"] = "vat";
    TaxType["GST"] = "gst";
    TaxType["EXCISE_TAX"] = "excise_tax";
    TaxType["LUXURY_TAX"] = "luxury_tax";
    TaxType["ENVIRONMENTAL_TAX"] = "environmental_tax";
})(TaxType || (exports.TaxType = TaxType = {}));
let PosTax = class PosTax {
    id;
    saleId;
    sale;
    name;
    type;
    rate;
    taxableAmount;
    taxAmount;
    isInclusive;
    jurisdiction;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosTax = PosTax;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosTax.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosTax.prototype, "saleId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_sale_entity_1.PosSale, sale => sale.taxes, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'saleId' }),
    __metadata("design:type", pos_sale_entity_1.PosSale)
], PosTax.prototype, "sale", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PosTax.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TaxType,
        default: TaxType.SALES_TAX,
    }),
    __metadata("design:type", String)
], PosTax.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 4 }),
    __metadata("design:type", Number)
], PosTax.prototype, "rate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PosTax.prototype, "taxableAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PosTax.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosTax.prototype, "isInclusive", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], PosTax.prototype, "jurisdiction", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTax.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosTax.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosTax.prototype, "updatedAt", void 0);
exports.PosTax = PosTax = __decorate([
    (0, typeorm_1.Entity)('pos_taxes')
], PosTax);
//# sourceMappingURL=pos-tax.entity.js.map