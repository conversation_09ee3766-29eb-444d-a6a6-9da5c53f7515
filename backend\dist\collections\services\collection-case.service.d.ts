import { Repository } from 'typeorm';
import { CollectionCase, CaseStatus } from '../entities/collection-case.entity';
import { CollectionActivity } from '../entities/collection-activity.entity';
export declare class CollectionCaseService {
    private collectionCaseRepository;
    private collectionActivityRepository;
    constructor(collectionCaseRepository: Repository<CollectionCase>, collectionActivityRepository: Repository<CollectionActivity>);
    create(caseData: Partial<CollectionCase>): Promise<CollectionCase>;
    findAll(): Promise<CollectionCase[]>;
    findOne(id: string): Promise<CollectionCase>;
    update(id: string, updateData: Partial<CollectionCase>): Promise<CollectionCase>;
    remove(id: string): Promise<void>;
    findByStatus(status: CaseStatus): Promise<CollectionCase[]>;
    findByCustomer(customerId: string): Promise<CollectionCase[]>;
    updateStatus(id: string, status: CaseStatus, notes?: string): Promise<CollectionCase>;
    assignAgent(caseId: string, agentId: string): Promise<CollectionCase>;
    getStatistics(): Promise<any>;
    getCasesByPriority(priority: string): Promise<CollectionCase[]>;
    getOverdueCases(): Promise<CollectionCase[]>;
    escalateCase(id: string, reason: string): Promise<CollectionCase>;
    recordPayment(id: string, amount: number, paymentDate: Date, notes?: string): Promise<CollectionCase>;
    getDashboardMetrics(): Promise<any>;
}
