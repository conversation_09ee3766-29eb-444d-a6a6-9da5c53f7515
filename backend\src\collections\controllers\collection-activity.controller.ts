import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { CollectionActivityService } from '../services/collection-activity.service';
import { CollectionActivity, ActivityType } from '../entities/collection-activity.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('collection-activities')
@UseGuards(JwtAuthGuard)
export class CollectionActivityController {
  constructor(private readonly collectionActivityService: CollectionActivityService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createActivityDto: Partial<CollectionActivity>) {
    return this.collectionActivityService.create(createActivityDto);
  }

  @Get()
  async findAll() {
    return this.collectionActivityService.findAll();
  }

  @Get('recent')
  async getRecentActivities(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 10;
    return this.collectionActivityService.getRecentActivities(limitNum);
  }

  @Get('follow-ups')
  async getScheduledFollowUps() {
    return this.collectionActivityService.getScheduledFollowUps();
  }

  @Get('case/:caseId')
  async findByCase(@Param('caseId') caseId: string) {
    return this.collectionActivityService.findByCase(caseId);
  }

  @Get('case/:caseId/summary')
  async getActivitySummary(@Param('caseId') caseId: string) {
    return this.collectionActivityService.getActivitySummary(caseId);
  }

  @Get('agent/:agentId')
  async findByAgent(@Param('agentId') agentId: string) {
    return this.collectionActivityService.findByAgent(agentId);
  }

  @Get('agent/:agentId/report')
  async getAgentActivityReport(
    @Param('agentId') agentId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    return this.collectionActivityService.getAgentActivityReport(agentId, start, end);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.collectionActivityService.findOne(id);
  }

  @Post('log')
  async logActivity(@Body() activityData: {
    caseId: string;
    type: ActivityType;
    description: string;
    performedBy?: string;
    outcome?: string;
  }) {
    return this.collectionActivityService.logActivity(
      activityData.caseId,
      activityData.type,
      activityData.description,
      activityData.performedBy,
      activityData.outcome,
    );
  }

  @Post('log-call')
  async logCall(@Body() callData: {
    caseId: string;
    duration: number;
    outcome: string;
    notes: string;
    performedBy?: string;
  }) {
    return this.collectionActivityService.logCall(
      callData.caseId,
      callData.duration,
      callData.outcome,
      callData.notes,
      callData.performedBy,
    );
  }

  @Post('log-email')
  async logEmail(@Body() emailData: {
    caseId: string;
    subject: string;
    outcome: string;
    performedBy?: string;
  }) {
    return this.collectionActivityService.logEmail(
      emailData.caseId,
      emailData.subject,
      emailData.outcome,
      emailData.performedBy,
    );
  }

  @Post('log-letter')
  async logLetter(@Body() letterData: {
    caseId: string;
    letterType: string;
    performedBy?: string;
  }) {
    return this.collectionActivityService.logLetter(
      letterData.caseId,
      letterData.letterType,
      letterData.performedBy,
    );
  }

  @Post('log-payment')
  async logPayment(@Body() paymentData: {
    caseId: string;
    amount: number;
    paymentMethod: string;
    performedBy?: string;
  }) {
    return this.collectionActivityService.logPayment(
      paymentData.caseId,
      paymentData.amount,
      paymentData.paymentMethod,
      paymentData.performedBy,
    );
  }

  @Post('schedule-follow-up')
  async scheduleFollowUp(@Body() followUpData: {
    caseId: string;
    followUpDate: Date;
    notes: string;
    performedBy?: string;
  }) {
    return this.collectionActivityService.scheduleFollowUp(
      followUpData.caseId,
      new Date(followUpData.followUpDate),
      followUpData.notes,
      followUpData.performedBy,
    );
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateActivityDto: Partial<CollectionActivity>,
  ) {
    return this.collectionActivityService.update(id, updateActivityDto);
  }

  @Patch(':id/complete-follow-up')
  async markFollowUpCompleted(
    @Param('id') id: string,
    @Body() completion: { outcome?: string },
  ) {
    return this.collectionActivityService.markFollowUpCompleted(
      id,
      completion.outcome,
    );
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.collectionActivityService.remove(id);
  }
}
