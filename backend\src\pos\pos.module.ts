import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { PosTerminal } from './entities/pos-terminal.entity';
import { PosSale } from './entities/pos-sale.entity';
import { PosSaleItem } from './entities/pos-sale-item.entity';
import { PosPayment } from './entities/pos-payment.entity';
import { PosDiscount } from './entities/pos-discount.entity';
import { PosTax } from './entities/pos-tax.entity';
import { PosShift } from './entities/pos-shift.entity';
import { PosCashDrawer } from './entities/pos-cash-drawer.entity';
import { PosReceipt } from './entities/pos-receipt.entity';
import { PosReturn } from './entities/pos-return.entity';
import { PosPromotion } from './entities/pos-promotion.entity';
import { PosCustomer } from './entities/pos-customer.entity';

// Services
import { PosTerminalService } from './services/pos-terminal.service';
import { PosSaleService } from './services/pos-sale.service';
import { PosPaymentService } from './services/pos-payment.service';
import { PosShiftService } from './services/pos-shift.service';
import { PosCashDrawerService } from './services/pos-cash-drawer.service';
import { PosReceiptService } from './services/pos-receipt.service';
import { PosReturnService } from './services/pos-return.service';
import { PosPromotionService } from './services/pos-promotion.service';
import { PosReportService } from './services/pos-report.service';

// Controllers
import { PosTerminalController } from './controllers/pos-terminal.controller';
import { PosSaleController } from './controllers/pos-sale.controller';
import { PosPaymentController } from './controllers/pos-payment.controller';
import { PosShiftController } from './controllers/pos-shift.controller';
import { PosCashDrawerController } from './controllers/pos-cash-drawer.controller';
import { PosReceiptController } from './controllers/pos-receipt.controller';
import { PosReturnController } from './controllers/pos-return.controller';
import { PosPromotionController } from './controllers/pos-promotion.controller';
import { PosReportController } from './controllers/pos-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PosTerminal,
      PosSale,
      PosSaleItem,
      PosPayment,
      PosDiscount,
      PosTax,
      PosShift,
      PosCashDrawer,
      PosReceipt,
      PosReturn,
      PosPromotion,
      PosCustomer,
    ]),
  ],
  controllers: [
    PosTerminalController,
    PosSaleController,
    PosPaymentController,
    PosShiftController,
    PosCashDrawerController,
    PosReceiptController,
    PosReturnController,
    PosPromotionController,
    PosReportController,
  ],
  providers: [
    PosTerminalService,
    PosSaleService,
    PosPaymentService,
    PosShiftService,
    PosCashDrawerService,
    PosReceiptService,
    PosReturnService,
    PosPromotionService,
    PosReportService,
  ],
  exports: [
    PosTerminalService,
    PosSaleService,
    PosPaymentService,
    PosShiftService,
    PosCashDrawerService,
    PosReceiptService,
    PosReturnService,
    PosPromotionService,
    PosReportService,
  ],
})
export class PosModule {}
