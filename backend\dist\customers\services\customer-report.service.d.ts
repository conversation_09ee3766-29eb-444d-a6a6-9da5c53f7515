import { Repository } from 'typeorm';
import { Customer } from '../entities/customer.entity';
import { CustomerInteraction } from '../entities/customer-interaction.entity';
import { CustomerSegment } from '../entities/customer-segment.entity';
export declare class CustomerReportService {
    private customerRepository;
    private interactionRepository;
    private segmentRepository;
    constructor(customerRepository: Repository<Customer>, interactionRepository: Repository<CustomerInteraction>, segmentRepository: Repository<CustomerSegment>);
    generateCustomerSummaryReport(): Promise<any>;
    generateCustomerSegmentReport(): Promise<any>;
    generateInteractionReport(startDate: Date, endDate: Date): Promise<any>;
    generateCustomerLifecycleReport(): Promise<any>;
    generateTopCustomersReport(limit?: number): Promise<any>;
    generateCustomerHealthReport(): Promise<any>;
    private getSegmentAnalytics;
    private getDaysBetween;
    exportCustomerData(format?: 'csv' | 'excel'): Promise<any>;
}
