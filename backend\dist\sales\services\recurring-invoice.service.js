"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecurringInvoiceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const recurring_invoice_entity_1 = require("../entities/recurring-invoice.entity");
const recurring_invoice_item_entity_1 = require("../entities/recurring-invoice-item.entity");
let RecurringInvoiceService = class RecurringInvoiceService {
    recurringInvoiceRepository;
    recurringInvoiceItemRepository;
    constructor(recurringInvoiceRepository, recurringInvoiceItemRepository) {
        this.recurringInvoiceRepository = recurringInvoiceRepository;
        this.recurringInvoiceItemRepository = recurringInvoiceItemRepository;
    }
    async create(createRecurringInvoiceDto, tenantId) {
        const totals = this.calculateRecurringInvoiceTotals(createRecurringInvoiceDto);
        const recurringInvoice = this.recurringInvoiceRepository.create({
            ...createRecurringInvoiceDto,
            tenantId,
            ...totals,
        });
        const result = await this.recurringInvoiceRepository.save(recurringInvoice);
        const savedRecurringInvoice = Array.isArray(result) ? result[0] : result;
        if (createRecurringInvoiceDto.items) {
            for (const itemDto of createRecurringInvoiceDto.items) {
                const item = this.recurringInvoiceItemRepository.create({
                    ...itemDto,
                    recurringInvoiceId: savedRecurringInvoice.id,
                    tenantId,
                    taxAmount: this.calculateItemTax(itemDto),
                    lineTotal: (itemDto.unitPrice * itemDto.quantity) - (itemDto.discount || 0),
                });
                await this.recurringInvoiceItemRepository.save(item);
            }
        }
        return this.findOne(savedRecurringInvoice.id, tenantId);
    }
    async findAll(tenantId) {
        return this.recurringInvoiceRepository.find({
            where: { tenantId },
            relations: ['customer', 'items'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id, tenantId) {
        const recurringInvoice = await this.recurringInvoiceRepository.findOne({
            where: { id, tenantId },
            relations: ['customer', 'items'],
        });
        if (!recurringInvoice) {
            throw new common_1.NotFoundException('Recurring invoice not found');
        }
        return recurringInvoice;
    }
    async update(id, updateRecurringInvoiceDto, tenantId) {
        const recurringInvoice = await this.findOne(id, tenantId);
        if (updateRecurringInvoiceDto.items) {
            await this.recurringInvoiceItemRepository.delete({ recurringInvoiceId: id });
            for (const itemDto of updateRecurringInvoiceDto.items) {
                const item = this.recurringInvoiceItemRepository.create({
                    ...itemDto,
                    recurringInvoiceId: id,
                    tenantId,
                    taxAmount: this.calculateItemTax(itemDto),
                    lineTotal: (itemDto.unitPrice * itemDto.quantity) - (itemDto.discount || 0),
                });
                await this.recurringInvoiceItemRepository.save(item);
            }
            const totals = this.calculateRecurringInvoiceTotals(updateRecurringInvoiceDto);
            Object.assign(recurringInvoice, totals);
        }
        Object.assign(recurringInvoice, updateRecurringInvoiceDto);
        return this.recurringInvoiceRepository.save(recurringInvoice);
    }
    async remove(id, tenantId) {
        const recurringInvoice = await this.findOne(id, tenantId);
        await this.recurringInvoiceRepository.remove(recurringInvoice);
    }
    async updateStatus(id, status, tenantId) {
        const recurringInvoice = await this.findOne(id, tenantId);
        recurringInvoice.status = status;
        return this.recurringInvoiceRepository.save(recurringInvoice);
    }
    async generateInvoice(id, tenantId) {
        const recurringInvoice = await this.findOne(id, tenantId);
        if (recurringInvoice.status !== 'active') {
            throw new Error('Only active recurring invoices can generate invoices');
        }
        recurringInvoice.invoicesGenerated += 1;
        recurringInvoice.lastInvoiceDate = new Date();
        const nextDate = new Date(recurringInvoice.nextInvoiceDate);
        switch (recurringInvoice.frequency) {
            case 'weekly':
                nextDate.setDate(nextDate.getDate() + 7);
                break;
            case 'monthly':
                nextDate.setMonth(nextDate.getMonth() + 1);
                break;
            case 'quarterly':
                nextDate.setMonth(nextDate.getMonth() + 3);
                break;
            case 'yearly':
                nextDate.setFullYear(nextDate.getFullYear() + 1);
                break;
        }
        recurringInvoice.nextInvoiceDate = nextDate;
        if (recurringInvoice.maxInvoices && recurringInvoice.invoicesGenerated >= recurringInvoice.maxInvoices) {
            recurringInvoice.status = 'completed';
        }
        await this.recurringInvoiceRepository.save(recurringInvoice);
        return { success: true, invoiceId: 'generated' };
    }
    async getRecurringInvoiceStats(tenantId) {
        const totalRecurringInvoices = await this.recurringInvoiceRepository.count({ where: { tenantId } });
        const activeRecurringInvoices = await this.recurringInvoiceRepository.count({
            where: { tenantId, status: 'active' }
        });
        const pausedRecurringInvoices = await this.recurringInvoiceRepository.count({
            where: { tenantId, status: 'paused' }
        });
        const result = await this.recurringInvoiceRepository
            .createQueryBuilder('recurringInvoice')
            .select('SUM(recurringInvoice.totalAmount)', 'totalValue')
            .addSelect('SUM(recurringInvoice.invoicesGenerated)', 'totalGenerated')
            .where('recurringInvoice.tenantId = :tenantId', { tenantId })
            .getRawOne();
        const frequencyStats = await this.recurringInvoiceRepository
            .createQueryBuilder('recurringInvoice')
            .select('recurringInvoice.frequency', 'frequency')
            .addSelect('COUNT(*)', 'count')
            .where('recurringInvoice.tenantId = :tenantId', { tenantId })
            .groupBy('recurringInvoice.frequency')
            .getRawMany();
        return {
            totalRecurringInvoices,
            activeRecurringInvoices,
            pausedRecurringInvoices,
            totalValue: parseFloat(result.totalValue) || 0,
            totalGenerated: parseInt(result.totalGenerated) || 0,
            frequencyStats,
        };
    }
    calculateRecurringInvoiceTotals(recurringInvoiceDto) {
        if (!recurringInvoiceDto.items || recurringInvoiceDto.items.length === 0) {
            return {
                subtotal: 0,
                discountAmount: 0,
                taxAmount: 0,
                totalAmount: 0,
            };
        }
        const subtotal = recurringInvoiceDto.items.reduce((sum, item) => sum + (item.unitPrice * item.quantity) - (item.discount || 0), 0);
        const discountAmount = recurringInvoiceDto.discountType === 'percentage'
            ? (subtotal * (recurringInvoiceDto.discountValue || 0) / 100)
            : (recurringInvoiceDto.discountValue || 0);
        const taxAmount = recurringInvoiceDto.items.reduce((sum, item) => sum + this.calculateItemTax(item), 0);
        const totalAmount = subtotal - discountAmount + taxAmount;
        return {
            subtotal,
            discountAmount,
            taxAmount,
            totalAmount: Math.max(0, totalAmount),
        };
    }
    calculateItemTax(item) {
        const lineTotal = (item.unitPrice * item.quantity) - (item.discount || 0);
        if (item.taxType === '15%') {
            return lineTotal * 0.15;
        }
        return 0;
    }
};
exports.RecurringInvoiceService = RecurringInvoiceService;
exports.RecurringInvoiceService = RecurringInvoiceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(recurring_invoice_entity_1.RecurringInvoice)),
    __param(1, (0, typeorm_1.InjectRepository)(recurring_invoice_item_entity_1.RecurringInvoiceItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], RecurringInvoiceService);
//# sourceMappingURL=recurring-invoice.service.js.map