import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';

export enum AdjustmentType {
  INCREASE = 'increase',
  DECREASE = 'decrease',
}

export enum AdjustmentReason {
  PHYSICAL_COUNT = 'physical_count',
  DAMAGE = 'damage',
  EXPIRY = 'expiry',
  THEFT = 'theft',
  LOSS = 'loss',
  FOUND = 'found',
  CORRECTION = 'correction',
  WRITE_OFF = 'write_off',
  OTHER = 'other',
}

@Entity('inventory_stock_adjustments')
export class StockAdjustment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  adjustmentNumber: string;

  @Column()
  productId: string;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column()
  warehouseId: string;

  @ManyToOne(() => Warehouse)
  @JoinColumn({ name: 'warehouseId' })
  warehouse: Warehouse;

  @Column({
    type: 'enum',
    enum: AdjustmentType,
  })
  type: AdjustmentType;

  @Column({
    type: 'enum',
    enum: AdjustmentReason,
  })
  reason: AdjustmentReason;

  @Column({ type: 'int' })
  quantityBefore: number;

  @Column({ type: 'int' })
  quantityAdjusted: number;

  @Column({ type: 'int' })
  quantityAfter: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  unitCost: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalCost: number;

  @Column({ type: 'date' })
  adjustmentDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  performedBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
