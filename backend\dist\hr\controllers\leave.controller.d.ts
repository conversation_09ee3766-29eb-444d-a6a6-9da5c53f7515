import { LeaveService } from '../services/leave.service';
export declare class LeaveController {
    private readonly leaveService;
    constructor(leaveService: LeaveService);
    create(createLeaveDto: any): Promise<import("../entities/leave.entity").Leave>;
    findAll(employeeId?: string, status?: string, leaveTypeId?: string, startDate?: string, endDate?: string): Promise<import("../entities/leave.entity").Leave[]>;
    findAllLeaveTypes(): Promise<import("../entities/leave-type.entity").LeaveType[]>;
    createLeaveType(createLeaveTypeDto: any): Promise<import("../entities/leave-type.entity").LeaveType>;
    getLeaveBalance(employeeId: string, leaveTypeId: string, year?: string): Promise<any>;
    getLeaveReport(employeeId: string, year?: string): Promise<any>;
    findOne(id: string): Promise<import("../entities/leave.entity").Leave>;
    update(id: string, updateLeaveDto: any): Promise<import("../entities/leave.entity").Leave>;
    remove(id: string): Promise<void>;
    approveLeave(id: string, approveDto: {
        approvedBy: string;
        approvalComments?: string;
    }): Promise<import("../entities/leave.entity").Leave>;
    rejectLeave(id: string, rejectDto: {
        rejectedBy: string;
        rejectionComments: string;
    }): Promise<import("../entities/leave.entity").Leave>;
    cancelLeave(id: string, cancelDto: {
        reason?: string;
    }): Promise<import("../entities/leave.entity").Leave>;
}
