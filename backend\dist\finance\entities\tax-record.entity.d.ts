export declare enum TaxType {
    INCOME_TAX = "income_tax",
    SALES_TAX = "sales_tax",
    VAT = "vat",
    PAYROLL_TAX = "payroll_tax",
    PROPERTY_TAX = "property_tax",
    EXCISE_TAX = "excise_tax",
    CUSTOMS_DUTY = "customs_duty",
    OTHER = "other"
}
export declare enum TaxStatus {
    CALCULATED = "calculated",
    FILED = "filed",
    PAID = "paid",
    OVERDUE = "overdue",
    AMENDED = "amended"
}
export declare class TaxRecord {
    id: string;
    taxRecordNumber: string;
    taxType: TaxType;
    status: TaxStatus;
    taxPeriodStart: Date;
    taxPeriodEnd: Date;
    dueDate: Date;
    taxableAmount: number;
    taxRate: number;
    taxAmount: number;
    penaltyAmount: number;
    interestAmount: number;
    totalAmount: number;
    paidAmount: number;
    outstandingAmount: number;
    currency: string;
    taxAuthority: string;
    referenceNumber: string;
    attachments: string[];
    filedDate: Date;
    paidDate: Date;
    filedBy: string;
    notes: string;
    calculations: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
