{"version": 3, "file": "tenant-connection.service.js", "sourceRoot": "", "sources": ["../../src/tenant/tenant-connection.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,qCAAwD;AACxD,+DAAoE;AAG7D,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAId;IAHH,MAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAC3D,WAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;IAEzD,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAEpD,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QACxC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC3C,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,IAAA,yCAAuB,EAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC,MAA2B,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,QAAQ,EAAE,CAAC,CAAC;YAC7D,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC;QAE/E,IAAI,CAAC,WAAW,EAAE,CAAC;YAEjB,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAClE,MAAM,gBAAgB,CAAC,WAAW,EAAE,CAAC;gBACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oDAAoD,QAAQ,EAAE,CAAC,CAAC;gBAChF,OAAO;YACT,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;gBACjF,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAGD,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,UAAmB;YACzB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;YACvC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;YACvC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;YAC/C,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;YAC/C,QAAQ,EAAE,UAAU;SACrB,CAAC;QAEF,MAAM,gBAAgB,GAAG,IAAI,oBAAU,CAAC,YAAY,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,MAAM,gBAAgB,CAAC,UAAU,EAAE,CAAC;YAEpC,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,QAAQ,EAAE,CAAC;YAGhF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,KAAK,CACzC,8CAA8C,EAC9C,CAAC,YAAY,CAAC,CACf,CAAC;YAEF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAExB,MAAM,gBAAgB,CAAC,KAAK,CAAC,oBAAoB,YAAY,GAAG,CAAC,CAAC;gBAClE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,YAAY,EAAE,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,YAAY,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAGjC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAClE,MAAM,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,YAAY,EAAE,CAAC,CAAC;QAE1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1E,IAAI,gBAAgB,CAAC,aAAa,EAAE,CAAC;gBACnC,MAAM,gBAAgB,CAAC,OAAO,EAAE,CAAC;YACnC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAClE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CACrC,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;CACF,CAAA;AA1GY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAKwB,sBAAa;GAJrC,uBAAuB,CA0GnC"}