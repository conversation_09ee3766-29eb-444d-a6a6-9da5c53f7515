import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { CompanyModule } from './company/company.module';
import { TenantModule } from './tenant/tenant.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { SalesModule } from './sales/sales.module';
import { AnalyticsModule } from './analytics/analytics.module';
// Temporarily disabled modules with missing files
// import { FinanceModule } from './finance/finance.module';
// import { HrModule } from './hr/hr.module';
// import { InventoryModule } from './inventory/inventory.module';
// import { ProjectsModule } from './projects/projects.module';
// import { PosModule } from './pos/pos.module';
import { CustomersModule } from './customers/customers.module';
// import { CollectionsModule } from './collections/collections.module';
// import { ProcurementModule } from './procurement/procurement.module';
// import { ItSupportModule } from './it-support/it-support.module';
// import { SettingsModule } from './settings/settings.module';
// import { SystemGuideModule } from './system-guide/system-guide.module';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => getDatabaseConfig(configService),
      inject: [ConfigService],
    }),
    AuthModule,
    UserModule,
    CompanyModule,
    TenantModule,
    DashboardModule,
    SalesModule,
    AnalyticsModule,
    // Temporarily disabled modules with missing files
    // FinanceModule,
    // HrModule,
    // InventoryModule,
    // ProjectsModule,
    // PosModule,
    CustomersModule,
    // CollectionsModule,
    // ProcurementModule,
    // ItSupportModule,
    // SettingsModule,
    // SystemGuideModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
