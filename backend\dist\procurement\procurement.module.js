"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcurementModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const procurement_request_entity_1 = require("./entities/procurement-request.entity");
const procurement_item_entity_1 = require("./entities/procurement-item.entity");
const vendor_entity_1 = require("./entities/vendor.entity");
const vendor_contact_entity_1 = require("./entities/vendor-contact.entity");
const vendor_evaluation_entity_1 = require("./entities/vendor-evaluation.entity");
const contract_entity_1 = require("./entities/contract.entity");
const contract_term_entity_1 = require("./entities/contract-term.entity");
const rfq_entity_1 = require("./entities/rfq.entity");
const rfq_response_entity_1 = require("./entities/rfq-response.entity");
const procurement_approval_entity_1 = require("./entities/procurement-approval.entity");
const procurement_category_entity_1 = require("./entities/procurement-category.entity");
const procurement_request_service_1 = require("./services/procurement-request.service");
const vendor_service_1 = require("./services/vendor.service");
const contract_service_1 = require("./services/contract.service");
const rfq_service_1 = require("./services/rfq.service");
const procurement_approval_service_1 = require("./services/procurement-approval.service");
const procurement_report_service_1 = require("./services/procurement-report.service");
const procurement_request_controller_1 = require("./controllers/procurement-request.controller");
const vendor_controller_1 = require("./controllers/vendor.controller");
const contract_controller_1 = require("./controllers/contract.controller");
const rfq_controller_1 = require("./controllers/rfq.controller");
const procurement_approval_controller_1 = require("./controllers/procurement-approval.controller");
const procurement_report_controller_1 = require("./controllers/procurement-report.controller");
let ProcurementModule = class ProcurementModule {
};
exports.ProcurementModule = ProcurementModule;
exports.ProcurementModule = ProcurementModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                procurement_request_entity_1.ProcurementRequest,
                procurement_item_entity_1.ProcurementItem,
                vendor_entity_1.Vendor,
                vendor_contact_entity_1.VendorContact,
                vendor_evaluation_entity_1.VendorEvaluation,
                contract_entity_1.Contract,
                contract_term_entity_1.ContractTerm,
                rfq_entity_1.RFQ,
                rfq_response_entity_1.RFQResponse,
                procurement_approval_entity_1.ProcurementApproval,
                procurement_category_entity_1.ProcurementCategory,
            ]),
        ],
        controllers: [
            procurement_request_controller_1.ProcurementRequestController,
            vendor_controller_1.VendorController,
            contract_controller_1.ContractController,
            rfq_controller_1.RFQController,
            procurement_approval_controller_1.ProcurementApprovalController,
            procurement_report_controller_1.ProcurementReportController,
        ],
        providers: [
            procurement_request_service_1.ProcurementRequestService,
            vendor_service_1.VendorService,
            contract_service_1.ContractService,
            rfq_service_1.RFQService,
            procurement_approval_service_1.ProcurementApprovalService,
            procurement_report_service_1.ProcurementReportService,
        ],
        exports: [
            procurement_request_service_1.ProcurementRequestService,
            vendor_service_1.VendorService,
            contract_service_1.ContractService,
            rfq_service_1.RFQService,
            procurement_approval_service_1.ProcurementApprovalService,
            procurement_report_service_1.ProcurementReportService,
        ],
    })
], ProcurementModule);
//# sourceMappingURL=procurement.module.js.map