import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';

@Entity('procurement_categories')
export class ProcurementCategory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  parentCategoryId: string;

  @ManyToOne(() => ProcurementCategory, { nullable: true })
  @JoinColumn({ name: 'parentCategoryId' })
  parentCategory: ProcurementCategory;

  @OneToMany(() => ProcurementCategory, category => category.parentCategory)
  childCategories: ProcurementCategory[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0 })
  sortOrder: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  budgetLimit: number;

  @Column({ type: 'json', nullable: true })
  approvalWorkflow: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
