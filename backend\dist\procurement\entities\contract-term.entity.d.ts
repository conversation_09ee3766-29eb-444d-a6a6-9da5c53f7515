import { Contract } from './contract.entity';
export declare enum TermType {
    PAYMENT = "payment",
    DELIVERY = "delivery",
    QUALITY = "quality",
    PENALTY = "penalty",
    TERMINATION = "termination",
    CONFIDENTIALITY = "confidentiality",
    LIABILITY = "liability",
    FORCE_MAJEURE = "force_majeure",
    DISPUTE_RESOLUTION = "dispute_resolution",
    GENERAL = "general"
}
export declare class ContractTerm {
    id: string;
    contractId: string;
    contract: Contract;
    type: TermType;
    title: string;
    content: string;
    sortOrder: number;
    isActive: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
