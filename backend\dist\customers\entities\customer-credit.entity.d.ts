import { Customer } from './customer.entity';
export declare enum CreditTransactionType {
    CREDIT = "credit",
    DEBIT = "debit",
    ADJUSTMENT = "adjustment",
    REFUND = "refund",
    PAYMENT = "payment",
    TRANSFER = "transfer"
}
export declare enum CreditStatus {
    ACTIVE = "active",
    EXPIRED = "expired",
    USED = "used",
    CANCELLED = "cancelled"
}
export declare class CustomerCredit {
    id: string;
    customerId: string;
    customer: Customer;
    type: CreditTransactionType;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    status: CreditStatus;
    description: string;
    reference: string;
    relatedEntityType: string;
    relatedEntityId: string;
    expiryDate: Date;
    processedBy: string;
    approvedBy: string;
    approvedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
