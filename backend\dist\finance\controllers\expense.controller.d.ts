import { ExpenseService } from '../services/expense.service';
export declare class ExpenseController {
    private readonly expenseService;
    constructor(expenseService: ExpenseService);
    create(createExpenseDto: any): Promise<import("../entities/expense.entity").Expense>;
    findAll(status?: string, type?: string, employeeId?: string, departmentId?: string, startDate?: string, endDate?: string): Promise<import("../entities/expense.entity").Expense[]>;
    getExpenseReport(status?: string, type?: string, employeeId?: string, departmentId?: string, startDate?: string, endDate?: string): Promise<any>;
    findAllCategories(): Promise<import("../entities/expense-category.entity").ExpenseCategory[]>;
    createCategory(createCategoryDto: any): Promise<import("../entities/expense-category.entity").ExpenseCategory>;
    findCategory(id: string): Promise<import("../entities/expense-category.entity").ExpenseCategory>;
    findOne(id: string): Promise<import("../entities/expense.entity").Expense>;
    update(id: string, updateExpenseDto: any): Promise<import("../entities/expense.entity").Expense>;
    remove(id: string): Promise<void>;
    submitExpense(id: string, submitDto: {
        submittedBy: string;
    }): Promise<import("../entities/expense.entity").Expense>;
    approveExpense(id: string, approveDto: {
        approvedBy: string;
        notes?: string;
    }): Promise<import("../entities/expense.entity").Expense>;
    rejectExpense(id: string, rejectDto: {
        rejectedBy: string;
        notes: string;
    }): Promise<import("../entities/expense.entity").Expense>;
    markAsPaid(id: string): Promise<import("../entities/expense.entity").Expense>;
}
