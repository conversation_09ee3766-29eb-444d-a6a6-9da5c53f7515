import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Customer } from './entities/customer.entity';
import { CustomerContact } from './entities/customer-contact.entity';
import { CustomerAddress } from './entities/customer-address.entity';
import { CustomerGroup } from './entities/customer-group.entity';
import { CustomerNote } from './entities/customer-note.entity';
import { CustomerDocument } from './entities/customer-document.entity';
import { CustomerInteraction } from './entities/customer-interaction.entity';
import { CustomerSegment } from './entities/customer-segment.entity';
import { CustomerLoyalty } from './entities/customer-loyalty.entity';
import { CustomerCredit } from './entities/customer-credit.entity';
import { CustomerPreference } from './entities/customer-preference.entity';

// Services
import { CustomerService } from './services/customer.service';
import { CustomerGroupService } from './services/customer-group.service';
import { CustomerInteractionService } from './services/customer-interaction.service';
import { CustomerSegmentService } from './services/customer-segment.service';
import { CustomerLoyaltyService } from './services/customer-loyalty.service';
import { CustomerCreditService } from './services/customer-credit.service';
import { CustomerReportService } from './services/customer-report.service';

// Controllers
import { CustomerController } from './controllers/customer.controller';
import { CustomerGroupController } from './controllers/customer-group.controller';
import { CustomerInteractionController } from './controllers/customer-interaction.controller';
import { CustomerSegmentController } from './controllers/customer-segment.controller';
import { CustomerLoyaltyController } from './controllers/customer-loyalty.controller';
import { CustomerCreditController } from './controllers/customer-credit.controller';
import { CustomerReportController } from './controllers/customer-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Customer,
      CustomerContact,
      CustomerAddress,
      CustomerGroup,
      CustomerNote,
      CustomerDocument,
      CustomerInteraction,
      CustomerSegment,
      CustomerLoyalty,
      CustomerCredit,
      CustomerPreference,
    ]),
  ],
  controllers: [
    CustomerController,
    CustomerGroupController,
    CustomerInteractionController,
    CustomerSegmentController,
    CustomerLoyaltyController,
    CustomerCreditController,
    CustomerReportController,
  ],
  providers: [
    CustomerService,
    CustomerGroupService,
    CustomerInteractionService,
    CustomerSegmentService,
    CustomerLoyaltyService,
    CustomerCreditService,
    CustomerReportService,
  ],
  exports: [
    CustomerService,
    CustomerGroupService,
    CustomerInteractionService,
    CustomerSegmentService,
    CustomerLoyaltyService,
    CustomerCreditService,
    CustomerReportService,
  ],
})
export class CustomersModule {}
