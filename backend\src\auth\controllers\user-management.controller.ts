import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { UserManagementService, CreateUserDto, UpdateUserDto } from '../services/user-management.service';
import { UserStatus } from '../../user/entities/user.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('user-management')
@UseGuards(JwtAuthGuard)
export class UserManagementController {
  constructor(private readonly userManagementService: UserManagementService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createUser(@Body() createUserDto: CreateUserDto) {
    return this.userManagementService.createUser(createUserDto);
  }

  @Get()
  async findAllUsers(
    @Query('role') roleId?: string,
    @Query('department') department?: string,
    @Query('status') status?: UserStatus,
    @Query('search') searchTerm?: string,
  ) {
    if (searchTerm) {
      return this.userManagementService.searchUsers(searchTerm);
    }
    if (roleId) {
      return this.userManagementService.getUsersByRole(roleId);
    }
    if (department) {
      return this.userManagementService.getUsersByDepartment(department);
    }
    return this.userManagementService.findAllUsers();
  }

  @Get('statistics')
  async getUserStatistics() {
    return this.userManagementService.getUserStatistics();
  }

  @Get('export')
  async exportUsers() {
    return this.userManagementService.exportUsers();
  }

  @Get('department/:department/access')
  async getUsersWithDepartmentAccess(@Param('department') department: string) {
    return this.userManagementService.getUsersWithDepartmentAccess(department);
  }

  @Get(':id')
  async findUser(@Param('id') id: string) {
    return this.userManagementService.findUserById(id);
  }

  @Get(':id/permissions')
  async getUserPermissions(@Param('id') id: string) {
    return this.userManagementService.getUserPermissions(id);
  }

  @Post(':id/check-permission')
  async checkUserPermission(
    @Param('id') id: string,
    @Body() permissionCheck: {
      module: string;
      action: string;
      resource: string;
    },
  ) {
    return this.userManagementService.checkUserPermission(
      id,
      permissionCheck.module,
      permissionCheck.action,
      permissionCheck.resource,
    );
  }

  @Patch(':id')
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.userManagementService.updateUser(id, updateUserDto);
  }

  @Patch(':id/password')
  async changePassword(
    @Param('id') id: string,
    @Body() passwordData: { newPassword: string },
  ) {
    await this.userManagementService.changeUserPassword(id, passwordData.newPassword);
    return { message: 'Password updated successfully' };
  }

  @Patch(':id/activate')
  async activateUser(@Param('id') id: string) {
    return this.userManagementService.activateUser(id);
  }

  @Patch(':id/deactivate')
  async deactivateUser(@Param('id') id: string) {
    return this.userManagementService.deactivateUser(id);
  }

  @Patch(':id/suspend')
  async suspendUser(@Param('id') id: string) {
    return this.userManagementService.suspendUser(id);
  }

  @Patch(':id/role')
  async assignRole(
    @Param('id') id: string,
    @Body() roleData: { roleId: string },
  ) {
    return this.userManagementService.assignRole(id, roleData.roleId);
  }

  @Patch(':id/additional-permissions')
  async assignAdditionalPermissions(
    @Param('id') id: string,
    @Body() permissionData: { permissionIds: string[] },
  ) {
    return this.userManagementService.assignAdditionalPermissions(
      id,
      permissionData.permissionIds,
    );
  }

  @Delete(':id/additional-permissions')
  async removeAdditionalPermissions(
    @Param('id') id: string,
    @Body() permissionData: { permissionIds: string[] },
  ) {
    return this.userManagementService.removeAdditionalPermissions(
      id,
      permissionData.permissionIds,
    );
  }

  @Post('bulk-update')
  async bulkUpdateUsers(
    @Body() bulkUpdateData: {
      userIds: string[];
      updateData: Partial<UpdateUserDto>;
    },
  ) {
    await this.userManagementService.bulkUpdateUsers(
      bulkUpdateData.userIds,
      bulkUpdateData.updateData,
    );
    return { message: 'Users updated successfully' };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteUser(@Param('id') id: string) {
    return this.userManagementService.deleteUser(id);
  }
}
