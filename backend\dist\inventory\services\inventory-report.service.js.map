{"version": 3, "file": "inventory-report.service.js", "sourceRoot": "", "sources": ["../../../src/inventory/services/inventory-report.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,+DAAqD;AACrD,2DAAiD;AACjD,6EAAkE;AAClE,6EAAkE;AAG3D,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGvB;IAEA;IAEA;IAEA;IARV,YAEU,iBAAsC,EAEtC,eAAkC,EAElC,uBAAkD,EAElD,uBAAkD;QANlD,sBAAiB,GAAjB,iBAAiB,CAAqB;QAEtC,oBAAe,GAAf,eAAe,CAAmB;QAElC,4BAAuB,GAAvB,uBAAuB,CAA2B;QAElD,4BAAuB,GAAvB,uBAAuB,CAA2B;IACzD,CAAC;IAEJ,KAAK,CAAC,gCAAgC;QACpC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe;aACzC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;aAC7C,iBAAiB,CAAC,iBAAiB,EAAE,WAAW,CAAC;aACjD,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC;aACjD,OAAO,EAAE,CAAC;QAEb,MAAM,MAAM,GAAG;YACb,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,UAAU,EAAE,SAAS,CAAC,MAAM;YAC5B,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,EAAE;YACd,UAAU,EAAE,EAAE;YACd,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;YAC3D,MAAM,CAAC,aAAa,IAAI,KAAK,CAAC,QAAQ,CAAC;YACvC,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC;YAG/B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;oBACxC,QAAQ,EAAE,CAAC;oBACX,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACT,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;YACnE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YAGnD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,IAAI,eAAe,CAAC;YACrE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG;oBAChC,QAAQ,EAAE,CAAC;oBACX,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACT,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YAG3C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;gBAChB,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;gBAC3B,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;gBAC/B,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG;gBACtB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI;gBAC/B,QAAQ,EAAE,YAAY;gBACtB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS;gBAClC,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe;aAC7C,kBAAkB,CAAC,OAAO,CAAC;aAC3B,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;aAC7C,iBAAiB,CAAC,iBAAiB,EAAE,WAAW,CAAC;aACjD,KAAK,CAAC,iDAAiD,CAAC;aACxD,QAAQ,CAAC,6BAA6B,CAAC;aACvC,OAAO,EAAE,CAAC;QAEb,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe;aAC/C,kBAAkB,CAAC,OAAO,CAAC;aAC3B,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;aAC7C,iBAAiB,CAAC,iBAAiB,EAAE,WAAW,CAAC;aACjD,KAAK,CAAC,6BAA6B,CAAC;aACpC,OAAO,EAAE,CAAC;QAEb,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,eAAe;aAC9C,kBAAkB,CAAC,OAAO,CAAC;aAC3B,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;aAC7C,iBAAiB,CAAC,iBAAiB,EAAE,WAAW,CAAC;aACjD,KAAK,CAAC,wCAAwC,CAAC;aAC/C,OAAO,EAAE,CAAC;QAEb,OAAO;YACL,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,OAAO,EAAE;gBACP,aAAa,EAAE,aAAa,CAAC,MAAM;gBACnC,eAAe,EAAE,eAAe,CAAC,MAAM;gBACvC,cAAc,EAAE,cAAc,CAAC,MAAM;aACtC;YACD,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACzC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;gBAC/B,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG;gBACtB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI;gBAC/B,YAAY,EAAE,KAAK,CAAC,iBAAiB;gBACrC,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,YAAY;gBACxC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,iBAAiB;aAC9D,CAAC,CAAC;YACH,eAAe,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC7C,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;gBAC/B,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG;gBACtB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI;gBAC/B,aAAa,EAAE,KAAK,CAAC,SAAS;aAC/B,CAAC,CAAC;YACH,cAAc,EAAE,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC3C,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;gBAC/B,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG;gBACtB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI;gBAC/B,YAAY,EAAE,KAAK,CAAC,QAAQ;gBAC5B,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa;gBACrC,MAAM,EAAE,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa;aACrD,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAe,EAAE,OAAa;QACzD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB;aACjD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,iBAAiB,CAAC,kBAAkB,EAAE,SAAS,CAAC;aAChD,iBAAiB,CAAC,oBAAoB,EAAE,WAAW,CAAC;aACpD,KAAK,CAAC,uDAAuD,EAAE;YAC9D,SAAS;YACT,OAAO;SACR,CAAC;aACD,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC;aACxC,OAAO,EAAE,CAAC;QAEb,MAAM,OAAO,GAAG;YACd,cAAc,EAAE,SAAS,CAAC,MAAM;YAChC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,MAAM;YAC1D,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM;YAC5D,eAAe,EAAE,SAAS;iBACvB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC;iBAC5B,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1C,gBAAgB,EAAE,SAAS;iBACxB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC;iBAC7B,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC3C,CAAC;QAEF,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC5C,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG;oBACvC,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,CAAC;iBACjB,CAAC;YACJ,CAAC;YACD,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YAClD,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,aAAa,IAAI,QAAQ,CAAC,QAAQ,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;YAC9B,OAAO;YACP,eAAe;YACf,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAI,EAAE,QAAQ,CAAC,YAAY;gBAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;gBAC9B,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI;gBAClC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,SAAe,EAAE,OAAa;QAC9D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB;aAC9C,kBAAkB,CAAC,IAAI,CAAC;aACxB,iBAAiB,CAAC,aAAa,EAAE,UAAU,CAAC;aAC5C,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC;aACtC,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;aAC7C,KAAK,CAAC,8CAA8C,EAAE;YACrD,SAAS;YACT,OAAO;SACR,CAAC;aACD,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;aAC/B,OAAO,EAAE,CAAC;QAEb,MAAM,OAAO,GAAG;YACd,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC/D,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;YAChE,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,MAAM;YAClE,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM;SACrE,CAAC;QAEF,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;YACzC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;gBACnC,eAAe,CAAC,YAAY,CAAC,GAAG;oBAC9B,UAAU,EAAE,CAAC;oBACb,UAAU,EAAE,CAAC;iBACd,CAAC;YACJ,CAAC;YACD,eAAe,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;YAC9C,eAAe,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;YAC9B,OAAO;YACP,eAAe;YACf,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC3B,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI;gBAC7B,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM;gBAC7B,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,yBAAyB;QAG7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe;aACzC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;aAC7C,OAAO,EAAE,CAAC;QAEb,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC5C,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;YAC3B,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;YAC/B,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG;YACtB,KAAK,EAAE,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS;SAChD,CAAC,CAAC,CAAC;QAGJ,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAEhD,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC5E,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,MAAM,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACrD,eAAe,IAAI,OAAO,CAAC,KAAK,CAAC;YACjC,MAAM,oBAAoB,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;YAElE,IAAI,cAAc,GAAG,GAAG,CAAC;YACzB,IAAI,oBAAoB,IAAI,EAAE,EAAE,CAAC;gBAC/B,cAAc,GAAG,GAAG,CAAC;YACvB,CAAC;iBAAM,IAAI,oBAAoB,IAAI,EAAE,EAAE,CAAC;gBACtC,cAAc,GAAG,GAAG,CAAC;YACvB,CAAC;YAED,OAAO;gBACL,GAAG,OAAO;gBACV,cAAc;gBACd,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,GAAG,CAAC,GAAG,GAAG;aACnE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,GAAG,CAAC,CAAC,MAAM;YACvE,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,GAAG,CAAC,CAAC,MAAM;YACvE,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,GAAG,CAAC,CAAC,MAAM;SACxE,CAAC;QAEF,OAAO;YACL,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,OAAO;YACP,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;IACJ,CAAC;CACF,CAAA;AA/RY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;IAEzB,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;IAEvB,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;qCALL,oBAAU;QAEZ,oBAAU;QAEF,oBAAU;QAEV,oBAAU;GATlC,sBAAsB,CA+RlC"}