"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnboardingFlow = exports.OnboardingStatus = exports.OnboardingType = void 0;
const typeorm_1 = require("typeorm");
var OnboardingType;
(function (OnboardingType) {
    OnboardingType["USER_ONBOARDING"] = "user_onboarding";
    OnboardingType["FEATURE_ONBOARDING"] = "feature_onboarding";
    OnboardingType["ROLE_ONBOARDING"] = "role_onboarding";
    OnboardingType["MODULE_ONBOARDING"] = "module_onboarding";
    OnboardingType["CUSTOM"] = "custom";
})(OnboardingType || (exports.OnboardingType = OnboardingType = {}));
var OnboardingStatus;
(function (OnboardingStatus) {
    OnboardingStatus["ACTIVE"] = "active";
    OnboardingStatus["INACTIVE"] = "inactive";
    OnboardingStatus["DRAFT"] = "draft";
    OnboardingStatus["TESTING"] = "testing";
    OnboardingStatus["ARCHIVED"] = "archived";
})(OnboardingStatus || (exports.OnboardingStatus = OnboardingStatus = {}));
let OnboardingFlow = class OnboardingFlow {
    id;
    name;
    description;
    type;
    status;
    steps;
    triggers;
    targetRoles;
    targetPages;
    conditions;
    isSkippable;
    isRepeatable;
    priority;
    estimatedDuration;
    createdBy;
    lastModifiedBy;
    completionCount;
    skipCount;
    completionRate;
    metadata;
    createdAt;
    updatedAt;
};
exports.OnboardingFlow = OnboardingFlow;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: OnboardingType,
        default: OnboardingType.USER_ONBOARDING,
    }),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: OnboardingStatus,
        default: OnboardingStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Array)
], OnboardingFlow.prototype, "steps", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], OnboardingFlow.prototype, "triggers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], OnboardingFlow.prototype, "targetRoles", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], OnboardingFlow.prototype, "targetPages", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], OnboardingFlow.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], OnboardingFlow.prototype, "isSkippable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], OnboardingFlow.prototype, "isRepeatable", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], OnboardingFlow.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], OnboardingFlow.prototype, "estimatedDuration", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], OnboardingFlow.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], OnboardingFlow.prototype, "completionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], OnboardingFlow.prototype, "skipCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], OnboardingFlow.prototype, "completionRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], OnboardingFlow.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], OnboardingFlow.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], OnboardingFlow.prototype, "updatedAt", void 0);
exports.OnboardingFlow = OnboardingFlow = __decorate([
    (0, typeorm_1.Entity)('onboarding_flows')
], OnboardingFlow);
//# sourceMappingURL=onboarding-flow.entity.js.map