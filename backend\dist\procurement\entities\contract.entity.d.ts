import { Vendor } from './vendor.entity';
import { ContractTerm } from './contract-term.entity';
export declare enum ContractStatus {
    DRAFT = "draft",
    PENDING_APPROVAL = "pending_approval",
    ACTIVE = "active",
    EXPIRED = "expired",
    TERMINATED = "terminated",
    CANCELLED = "cancelled",
    RENEWED = "renewed"
}
export declare enum ContractType {
    PURCHASE_AGREEMENT = "purchase_agreement",
    SERVICE_AGREEMENT = "service_agreement",
    MASTER_AGREEMENT = "master_agreement",
    FRAMEWORK_AGREEMENT = "framework_agreement",
    NDA = "nda",
    SLA = "sla",
    OTHER = "other"
}
export declare class Contract {
    id: string;
    contractNumber: string;
    title: string;
    description: string;
    type: ContractType;
    status: ContractStatus;
    vendorId: string;
    vendor: Vendor;
    startDate: Date;
    endDate: Date;
    totalValue: number;
    currency: string;
    renewalPeriodMonths: number;
    autoRenewal: boolean;
    noticePeriodDays: number;
    paymentTerms: string;
    deliveryTerms: string;
    penaltyClause: string;
    attachments: string[];
    createdBy: string;
    approvedBy: string;
    approvedAt: Date;
    signedBy: string;
    signedAt: Date;
    terms: ContractTerm[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
