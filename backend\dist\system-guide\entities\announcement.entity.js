"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Announcement = exports.AnnouncementStatus = exports.AnnouncementPriority = exports.AnnouncementType = void 0;
const typeorm_1 = require("typeorm");
var AnnouncementType;
(function (AnnouncementType) {
    AnnouncementType["GENERAL"] = "general";
    AnnouncementType["FEATURE_RELEASE"] = "feature_release";
    AnnouncementType["MAINTENANCE"] = "maintenance";
    AnnouncementType["SECURITY"] = "security";
    AnnouncementType["PROMOTION"] = "promotion";
    AnnouncementType["TRAINING"] = "training";
    AnnouncementType["POLICY"] = "policy";
    AnnouncementType["URGENT"] = "urgent";
})(AnnouncementType || (exports.AnnouncementType = AnnouncementType = {}));
var AnnouncementPriority;
(function (AnnouncementPriority) {
    AnnouncementPriority["LOW"] = "low";
    AnnouncementPriority["MEDIUM"] = "medium";
    AnnouncementPriority["HIGH"] = "high";
    AnnouncementPriority["CRITICAL"] = "critical";
})(AnnouncementPriority || (exports.AnnouncementPriority = AnnouncementPriority = {}));
var AnnouncementStatus;
(function (AnnouncementStatus) {
    AnnouncementStatus["DRAFT"] = "draft";
    AnnouncementStatus["SCHEDULED"] = "scheduled";
    AnnouncementStatus["PUBLISHED"] = "published";
    AnnouncementStatus["EXPIRED"] = "expired";
    AnnouncementStatus["ARCHIVED"] = "archived";
})(AnnouncementStatus || (exports.AnnouncementStatus = AnnouncementStatus = {}));
let Announcement = class Announcement {
    id;
    title;
    content;
    summary;
    type;
    priority;
    status;
    publishAt;
    expiresAt;
    targetAudience;
    targetRoles;
    targetDepartments;
    isPinned;
    requiresAcknowledgment;
    sendEmail;
    sendPush;
    bannerImage;
    actionButtonText;
    actionButtonUrl;
    createdBy;
    viewCount;
    acknowledgmentCount;
    metadata;
    createdAt;
    updatedAt;
};
exports.Announcement = Announcement;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Announcement.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Announcement.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Announcement.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Announcement.prototype, "summary", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AnnouncementType,
        default: AnnouncementType.GENERAL,
    }),
    __metadata("design:type", String)
], Announcement.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AnnouncementPriority,
        default: AnnouncementPriority.MEDIUM,
    }),
    __metadata("design:type", String)
], Announcement.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AnnouncementStatus,
        default: AnnouncementStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Announcement.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Announcement.prototype, "publishAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Announcement.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Announcement.prototype, "targetAudience", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Announcement.prototype, "targetRoles", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Announcement.prototype, "targetDepartments", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Announcement.prototype, "isPinned", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Announcement.prototype, "requiresAcknowledgment", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Announcement.prototype, "sendEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Announcement.prototype, "sendPush", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], Announcement.prototype, "bannerImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Announcement.prototype, "actionButtonText", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], Announcement.prototype, "actionButtonUrl", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Announcement.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Announcement.prototype, "viewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Announcement.prototype, "acknowledgmentCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Announcement.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Announcement.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Announcement.prototype, "updatedAt", void 0);
exports.Announcement = Announcement = __decorate([
    (0, typeorm_1.Entity)('announcements')
], Announcement);
//# sourceMappingURL=announcement.entity.js.map