import { Employee } from './employee.entity';
import { Benefit } from './benefit.entity';
export declare enum EnrollmentStatus {
    ENROLLED = "enrolled",
    PENDING = "pending",
    DECLINED = "declined",
    TERMINATED = "terminated",
    SUSPENDED = "suspended"
}
export declare class EmployeeBenefit {
    id: string;
    employeeId: string;
    employee: Employee;
    benefitId: string;
    benefit: Benefit;
    status: EnrollmentStatus;
    enrollmentDate: Date;
    effectiveDate: Date;
    terminationDate: Date;
    employeeContribution: number;
    employerContribution: number;
    coverageAmount: number;
    dependents: any[];
    beneficiaries: any[];
    enrollmentChoices: any;
    notes: string;
    enrolledBy: string;
    enrolledAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
