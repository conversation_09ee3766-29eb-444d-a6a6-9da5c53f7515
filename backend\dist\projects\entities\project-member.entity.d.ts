import { Project } from './project.entity';
export declare enum ProjectRole {
    MANAGER = "manager",
    LEAD = "lead",
    DEVELOPER = "developer",
    DESIGNER = "designer",
    TESTER = "tester",
    ANALYST = "analyst",
    STAKEHOLDER = "stakeholder",
    CLIENT = "client",
    OBSERVER = "observer"
}
export declare class ProjectMember {
    id: string;
    projectId: string;
    project: Project;
    userId: string;
    role: ProjectRole;
    joinedDate: Date;
    leftDate: Date;
    isActive: boolean;
    hourlyRate: number;
    currency: string;
    permissions: string[];
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
