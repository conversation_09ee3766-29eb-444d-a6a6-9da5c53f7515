import { GuideSection } from './guide-section.entity';
export declare enum StepType {
    TEXT = "text",
    IMAGE = "image",
    VIDEO = "video",
    CODE = "code",
    INTERACTIVE = "interactive",
    CHECKLIST = "checklist",
    WARNING = "warning",
    TIP = "tip",
    NOTE = "note"
}
export declare class GuideStep {
    id: string;
    sectionId: string;
    section: GuideSection;
    title: string;
    content: string;
    type: StepType;
    sortOrder: number;
    media: any;
    interactive: any;
    isVisible: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
