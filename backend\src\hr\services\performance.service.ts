import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Performance, PerformanceStatus } from '../entities/performance.entity';

@Injectable()
export class PerformanceService {
  constructor(
    @InjectRepository(Performance)
    private performanceRepository: Repository<Performance>,
  ) {}

  async create(createPerformanceDto: any): Promise<Performance> {
    const performance = this.performanceRepository.create(createPerformanceDto);
    return this.performanceRepository.save(performance);
  }

  async findAll(filters?: any): Promise<Performance[]> {
    const queryBuilder = this.performanceRepository.createQueryBuilder('performance')
      .leftJoinAndSelect('performance.employee', 'employee');

    if (filters?.employeeId) {
      queryBuilder.andWhere('performance.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters?.type) {
      queryBuilder.andWhere('performance.type = :type', { type: filters.type });
    }

    if (filters?.status) {
      queryBuilder.andWhere('performance.status = :status', { status: filters.status });
    }

    return queryBuilder
      .orderBy('performance.reviewPeriodStart', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Performance> {
    const performance = await this.performanceRepository.findOne({
      where: { id },
      relations: ['employee'],
    });

    if (!performance) {
      throw new NotFoundException(`Performance review with ID ${id} not found`);
    }

    return performance;
  }

  async update(id: string, updatePerformanceDto: any): Promise<Performance> {
    const performance = await this.findOne(id);
    Object.assign(performance, updatePerformanceDto);
    return this.performanceRepository.save(performance);
  }

  async submitReview(id: string): Promise<Performance> {
    const performance = await this.findOne(id);
    performance.status = PerformanceStatus.COMPLETED;
    return this.performanceRepository.save(performance);
  }

  async approveReview(id: string, approvedBy: string): Promise<Performance> {
    const performance = await this.findOne(id);
    performance.status = PerformanceStatus.APPROVED;
    performance.approvedBy = approvedBy;
    performance.approvedAt = new Date();
    return this.performanceRepository.save(performance);
  }
}
