"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditReportsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const audit_report_entity_1 = require("../entities/audit-report.entity");
const audit_finding_entity_1 = require("../entities/audit-finding.entity");
let AuditReportsService = class AuditReportsService {
    auditReportRepository;
    auditFindingRepository;
    constructor(auditReportRepository, auditFindingRepository) {
        this.auditReportRepository = auditReportRepository;
        this.auditFindingRepository = auditFindingRepository;
    }
    async getAuditReports(companyId, query) {
        const { year, department, status, reportType, page = 1, limit = 10, } = query;
        const queryBuilder = this.auditReportRepository
            .createQueryBuilder('report')
            .where('report.companyId = :companyId', { companyId })
            .leftJoinAndSelect('report.findings', 'findings');
        if (year) {
            queryBuilder.andWhere('report.year = :year', { year });
        }
        if (department) {
            queryBuilder.andWhere('report.department = :department', { department });
        }
        if (status) {
            queryBuilder.andWhere('report.status = :status', { status });
        }
        if (reportType) {
            queryBuilder.andWhere('report.reportType = :reportType', { reportType });
        }
        const [reports, total] = await queryBuilder
            .orderBy('report.createdDate', 'DESC')
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();
        const reportsWithFindings = await Promise.all(reports.map(async (report) => {
            const findings = await this.auditFindingRepository.find({
                where: { auditReportId: report.id },
                order: { createdDate: 'DESC' },
            });
            return {
                ...report,
                totalFindings: findings.length,
                criticalFindings: findings.filter(f => f.severity === 'Critical').length,
                findings: findings,
            };
        }));
        const allFindings = await this.auditFindingRepository
            .createQueryBuilder('finding')
            .leftJoin('finding.auditReport', 'report')
            .where('report.companyId = :companyId', { companyId })
            .andWhere(year ? 'report.year = :year' : '1=1', { year })
            .andWhere(department ? 'report.department = :department' : '1=1', { department })
            .getMany();
        return {
            reports: reportsWithFindings,
            findings: allFindings,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            metrics: await this.getAuditMetrics(companyId, year),
        };
    }
    async getAuditMetrics(companyId, year) {
        const currentYear = year || new Date().getFullYear();
        const queryBuilder = this.auditReportRepository
            .createQueryBuilder('report')
            .where('report.companyId = :companyId', { companyId });
        if (year) {
            queryBuilder.andWhere('report.year = :year', { year });
        }
        const reports = await queryBuilder.getMany();
        const findingsQuery = this.auditFindingRepository
            .createQueryBuilder('finding')
            .leftJoin('finding.auditReport', 'report')
            .where('report.companyId = :companyId', { companyId });
        if (year) {
            findingsQuery.andWhere('report.year = :year', { year });
        }
        const findings = await findingsQuery.getMany();
        const totalReports = reports.length;
        const completedReports = reports.filter(r => r.status === 'Completed' || r.status === 'Published').length;
        const pendingReports = reports.filter(r => r.status === 'Draft' || r.status === 'In Review').length;
        const averageComplianceScore = reports.length > 0
            ? Math.round(reports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / reports.length)
            : 0;
        const totalFindings = findings.length;
        const resolvedFindings = findings.filter(f => f.status === 'Resolved' || f.status === 'Closed').length;
        const criticalFindings = findings.filter(f => f.severity === 'Critical').length;
        const financialReports = reports.filter(r => r.reportType === 'Financial' ||
            r.reportType === 'Comprehensive' ||
            r.scope === 'Financial Only' ||
            r.scope === 'Comprehensive');
        const totalAuditedIncome = financialReports.reduce((sum, r) => sum + (r.financialData?.totalIncome || 0), 0);
        const totalAuditedExpenses = financialReports.reduce((sum, r) => sum + (r.financialData?.totalExpenses || 0), 0);
        const totalDiscrepancies = financialReports.reduce((sum, r) => sum + (r.financialData?.discrepancies || 0), 0);
        const materialMisstatements = financialReports.reduce((sum, r) => sum + (r.financialData?.materialMisstatements || 0), 0);
        const financialAccuracy = financialReports.length > 0
            ? Math.round(financialReports.reduce((sum, r) => {
                const totalTransactions = r.financialData?.auditedTransactions || 1;
                const discrepancies = r.financialData?.discrepancies || 0;
                return sum + ((totalTransactions - discrepancies) / totalTransactions * 100);
            }, 0) / financialReports.length)
            : 0;
        const departmentCoverage = reports.filter(r => r.scope === 'All Departments' || r.scope === 'Comprehensive').length;
        const bsaAmlReports = reports.filter(r => r.reportType === 'BSA_AML');
        const creditReports = reports.filter(r => r.reportType === 'Credit_Review');
        const itSecurityReports = reports.filter(r => r.reportType === 'IT_Security' || r.reportType === 'Network_Penetration');
        const soxReports = reports.filter(r => r.reportType === 'SOX_FDICIA');
        const trustReports = reports.filter(r => r.reportType === 'Trust_Operations');
        const bsaAmlCompliance = bsaAmlReports.length > 0
            ? Math.round(bsaAmlReports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / bsaAmlReports.length)
            : 0;
        const creditRiskScore = creditReports.length > 0
            ? Math.round(creditReports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / creditReports.length)
            : 0;
        const itSecurityScore = itSecurityReports.length > 0
            ? Math.round(itSecurityReports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / itSecurityReports.length)
            : 0;
        const soxComplianceScore = soxReports.length > 0
            ? Math.round(soxReports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / soxReports.length)
            : 0;
        const trustOperationsCompliance = trustReports.length > 0
            ? Math.round(trustReports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / trustReports.length)
            : 0;
        const regulatoryViolations = reports.reduce((sum, r) => sum + (r.specializedData?.bsaAmlData?.complianceViolations || 0), 0);
        const totalSuspiciousActivities = reports.reduce((sum, r) => sum + (r.specializedData?.bsaAmlData?.suspiciousActivityReports || 0), 0);
        const networkVulnerabilities = reports.reduce((sum, r) => sum + (r.specializedData?.itSecurityData?.vulnerabilitiesFound || 0), 0);
        const operationalRiskLevel = criticalFindings > 10 ? 'High' :
            criticalFindings > 5 ? 'Medium' : 'Low';
        const enterpriseRiskRating = averageComplianceScore < 70 ? 'High' :
            averageComplianceScore < 85 ? 'Medium' : 'Low';
        const previousYearReports = await this.auditReportRepository.find({
            where: {
                companyId,
                year: currentYear - 1,
            },
        });
        const previousYearAvgScore = previousYearReports.length > 0
            ? previousYearReports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / previousYearReports.length
            : 0;
        const improvementTrend = previousYearAvgScore > 0
            ? Math.round(((averageComplianceScore - previousYearAvgScore) / previousYearAvgScore) * 100)
            : 0;
        return {
            totalReports,
            completedReports,
            pendingReports,
            averageComplianceScore,
            totalFindings,
            resolvedFindings,
            criticalFindings,
            improvementTrend,
            financialMetrics: {
                totalAuditedIncome,
                totalAuditedExpenses,
                totalDiscrepancies,
                materialMisstatements,
                financialAccuracy,
                departmentCoverage,
            },
            financialInstitutionMetrics: {
                bsaAmlCompliance,
                creditRiskScore,
                operationalRiskLevel,
                itSecurityScore,
                regulatoryViolations,
                totalSuspiciousActivities,
                networkVulnerabilities,
                trustOperationsCompliance,
                soxComplianceScore,
                enterpriseRiskRating,
            },
        };
    }
    async getAuditReportById(companyId, id) {
        const report = await this.auditReportRepository.findOne({
            where: { id, companyId },
            relations: ['findings'],
        });
        if (!report) {
            return null;
        }
        const findings = await this.auditFindingRepository.find({
            where: { auditReportId: id },
            order: { createdDate: 'DESC' },
        });
        return {
            ...report,
            totalFindings: findings.length,
            criticalFindings: findings.filter(f => f.severity === 'Critical').length,
            findings,
        };
    }
    async createAuditReport(companyId, userId, createAuditReportDto) {
        const report = this.auditReportRepository.create({
            ...createAuditReportDto,
            companyId,
            createdBy: userId,
            createdDate: new Date(),
            status: 'Draft',
            complianceScore: 0,
        });
        return await this.auditReportRepository.save(report);
    }
    async updateAuditReport(companyId, id, userId, updateAuditReportDto) {
        const report = await this.auditReportRepository.findOne({
            where: { id, companyId },
        });
        if (!report) {
            throw new Error('Audit report not found');
        }
        Object.assign(report, updateAuditReportDto);
        report.updatedBy = userId;
        report.updatedDate = new Date();
        return await this.auditReportRepository.save(report);
    }
    async deleteAuditReport(companyId, id) {
        const result = await this.auditReportRepository.delete({
            id,
            companyId,
        });
        if (result.affected === 0) {
            throw new Error('Audit report not found');
        }
        return result;
    }
    async exportAuditReport(companyId, id, format) {
        const report = await this.getAuditReportById(companyId, id);
        if (!report) {
            throw new Error('Audit report not found');
        }
        const fileName = `audit-report-${report.year}-${report.department}-${Date.now()}.${format}`;
        const downloadUrl = `/downloads/${fileName}`;
        return {
            fileName,
            downloadUrl,
            format,
            size: '2.5 MB',
            generatedAt: new Date(),
        };
    }
    async getAuditFindings(companyId, reportId) {
        const report = await this.auditReportRepository.findOne({
            where: { id: reportId, companyId },
        });
        if (!report) {
            throw new Error('Audit report not found');
        }
        return await this.auditFindingRepository.find({
            where: { auditReportId: reportId },
            order: { createdDate: 'DESC' },
        });
    }
    async addAuditFinding(companyId, reportId, userId, findingData) {
        const report = await this.auditReportRepository.findOne({
            where: { id: reportId, companyId },
        });
        if (!report) {
            throw new Error('Audit report not found');
        }
        const finding = this.auditFindingRepository.create({
            ...findingData,
            auditReportId: reportId,
            createdBy: userId,
            createdDate: new Date(),
            status: 'Open',
        });
        return await this.auditFindingRepository.save(finding);
    }
    async updateAuditReportStatus(companyId, id, userId, status) {
        const report = await this.auditReportRepository.findOne({
            where: { id, companyId },
        });
        if (!report) {
            throw new Error('Audit report not found');
        }
        report.status = status;
        report.updatedBy = userId;
        report.updatedDate = new Date();
        if (status === 'Completed' || status === 'Published') {
            report.completedDate = new Date();
        }
        return await this.auditReportRepository.save(report);
    }
    async getComplianceTrends(companyId, years = 3) {
        const currentYear = new Date().getFullYear();
        const trends = [];
        for (let i = 0; i < years; i++) {
            const year = currentYear - i;
            const reports = await this.auditReportRepository.find({
                where: { companyId, year },
            });
            const averageScore = reports.length > 0
                ? Math.round(reports.reduce((sum, r) => sum + (r.complianceScore || 0), 0) / reports.length)
                : 0;
            trends.push({
                year,
                averageComplianceScore: averageScore,
                totalReports: reports.length,
                completedReports: reports.filter(r => r.status === 'Completed' || r.status === 'Published').length,
            });
        }
        return trends.reverse();
    }
};
exports.AuditReportsService = AuditReportsService;
exports.AuditReportsService = AuditReportsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(audit_report_entity_1.AuditReport)),
    __param(1, (0, typeorm_1.InjectRepository)(audit_finding_entity_1.AuditFinding)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AuditReportsService);
//# sourceMappingURL=audit-reports.service.js.map