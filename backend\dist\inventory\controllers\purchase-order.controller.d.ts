import { PurchaseOrderService } from '../services/purchase-order.service';
import { PurchaseOrder } from '../entities/purchase-order.entity';
import { PurchaseOrderItem } from '../entities/purchase-order-item.entity';
export declare class PurchaseOrderController {
    private readonly purchaseOrderService;
    constructor(purchaseOrderService: PurchaseOrderService);
    create(createPurchaseOrderDto: Partial<PurchaseOrder>): Promise<PurchaseOrder>;
    findAll(status?: string): Promise<PurchaseOrder[]>;
    getStatistics(): Promise<any>;
    findBySupplier(supplierId: string): Promise<PurchaseOrder[]>;
    findOne(id: string): Promise<PurchaseOrder>;
    generateOrderNumber(): Promise<{
        orderNumber: string;
    }>;
    addItem(id: string, createItemDto: Partial<PurchaseOrderItem>): Promise<PurchaseOrderItem>;
    updateItem(itemId: string, updateItemDto: Partial<PurchaseOrderItem>): Promise<PurchaseOrderItem>;
    removeItem(itemId: string): Promise<void>;
    receiveOrder(id: string, receiveData: {
        warehouseId: string;
    }): Promise<PurchaseOrder>;
    partialReceive(id: string, receiveData: {
        warehouseId: string;
        receivedItems: Array<{
            itemId: string;
            receivedQuantity: number;
        }>;
    }): Promise<PurchaseOrder>;
    cancelOrder(id: string, cancelData: {
        reason: string;
    }): Promise<PurchaseOrder>;
    update(id: string, updatePurchaseOrderDto: Partial<PurchaseOrder>): Promise<PurchaseOrder>;
    remove(id: string): Promise<void>;
}
