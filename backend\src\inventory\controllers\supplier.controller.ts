import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { SupplierService } from '../services/supplier.service';
import { Supplier } from '../entities/supplier.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('suppliers')
@UseGuards(JwtAuthGuard)
export class SupplierController {
  constructor(private readonly supplierService: SupplierService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createSupplierDto: Partial<Supplier>) {
    return this.supplierService.create(createSupplierDto);
  }

  @Get()
  async findAll() {
    return this.supplierService.findAll();
  }

  @Get('active')
  async getActiveSuppliers() {
    return this.supplierService.getActiveSuppliers();
  }

  @Get('statistics')
  async getStatistics() {
    return this.supplierService.getSupplierStatistics();
  }

  @Get('search')
  async searchSuppliers(@Query('q') searchTerm: string) {
    return this.supplierService.searchSuppliers(searchTerm);
  }

  @Get('code/:code')
  async findByCode(@Param('code') code: string) {
    return this.supplierService.findByCode(code);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.supplierService.findOne(id);
  }

  @Post('generate-code')
  async generateSupplierCode(@Body() data: { name: string }) {
    const code = await this.supplierService.generateSupplierCode(data.name);
    return { code };
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateSupplierDto: Partial<Supplier>,
  ) {
    return this.supplierService.update(id, updateSupplierDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.supplierService.remove(id);
  }
}
