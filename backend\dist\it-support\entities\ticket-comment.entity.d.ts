import { SupportTicket } from './support-ticket.entity';
export declare enum CommentType {
    PUBLIC = "public",
    INTERNAL = "internal",
    SYSTEM = "system"
}
export declare class TicketComment {
    id: string;
    ticketId: string;
    ticket: SupportTicket;
    content: string;
    type: CommentType;
    authorId: string;
    authorName: string;
    isEdited: boolean;
    editedAt: Date;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
