"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosCashDrawer = exports.CashDrawerActivity = void 0;
const typeorm_1 = require("typeorm");
const pos_shift_entity_1 = require("./pos-shift.entity");
var CashDrawerActivity;
(function (CashDrawerActivity) {
    CashDrawerActivity["OPEN"] = "open";
    CashDrawerActivity["CLOSE"] = "close";
    CashDrawerActivity["CASH_IN"] = "cash_in";
    CashDrawerActivity["CASH_OUT"] = "cash_out";
    CashDrawerActivity["PAYOUT"] = "payout";
    CashDrawerActivity["DROP"] = "drop";
    CashDrawerActivity["COUNT"] = "count";
    CashDrawerActivity["RECONCILE"] = "reconcile";
})(CashDrawerActivity || (exports.CashDrawerActivity = CashDrawerActivity = {}));
let PosCashDrawer = class PosCashDrawer {
    id;
    shiftId;
    shift;
    activity;
    amount;
    balanceBefore;
    balanceAfter;
    reason;
    notes;
    performedBy;
    timestamp;
    denominationBreakdown;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosCashDrawer = PosCashDrawer;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosCashDrawer.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosCashDrawer.prototype, "shiftId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_shift_entity_1.PosShift, shift => shift.cashDrawerActivities),
    (0, typeorm_1.JoinColumn)({ name: 'shiftId' }),
    __metadata("design:type", pos_shift_entity_1.PosShift)
], PosCashDrawer.prototype, "shift", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CashDrawerActivity,
    }),
    __metadata("design:type", String)
], PosCashDrawer.prototype, "activity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PosCashDrawer.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosCashDrawer.prototype, "balanceBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosCashDrawer.prototype, "balanceAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosCashDrawer.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosCashDrawer.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosCashDrawer.prototype, "performedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], PosCashDrawer.prototype, "timestamp", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosCashDrawer.prototype, "denominationBreakdown", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosCashDrawer.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosCashDrawer.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosCashDrawer.prototype, "updatedAt", void 0);
exports.PosCashDrawer = PosCashDrawer = __decorate([
    (0, typeorm_1.Entity)('pos_cash_drawer')
], PosCashDrawer);
//# sourceMappingURL=pos-cash-drawer.entity.js.map