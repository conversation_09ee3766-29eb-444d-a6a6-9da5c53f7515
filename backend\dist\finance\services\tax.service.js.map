{"version": 3, "file": "tax.service.js", "sourceRoot": "", "sources": ["../../../src/finance/services/tax.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAA2C;AAC3C,qEAA8E;AAGvE,IAAM,UAAU,GAAhB,MAAM,UAAU;IAGX;IAFV,YAEU,mBAA0C;QAA1C,wBAAmB,GAAnB,mBAAmB,CAAuB;IACjD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,kBAAuB;QAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAEvF,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAChD,GAAG,kBAAkB;YACrB,eAAe;YACf,SAAS,EAAE,kBAAkB,CAAC,aAAa,GAAG,CAAC,kBAAkB,CAAC,OAAO,GAAG,GAAG,CAAC;SACjF,CAAC,CAAC;QAGH,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/G,SAAS,CAAC,iBAAiB,GAAG,SAAS,CAAC,WAAW,CAAC;QAEpD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAa;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAE9E,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YACrB,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,YAAY,CAAC,QAAQ,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YAC3C,YAAY,CAAC,QAAQ,CAAC,+EAA+E,EAAE;gBACrG,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,YAAY;aAChB,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC;aACnC,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAE5E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,kBAAuB;QAC9C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QAG7C,IAAI,kBAAkB,CAAC,aAAa,IAAI,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACnE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,aAAa,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;QAC5E,CAAC;QAED,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/G,SAAS,CAAC,iBAAiB,GAAG,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC;QAE3E,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzC,IAAI,SAAS,CAAC,MAAM,KAAK,6BAAS,CAAC,KAAK,IAAI,SAAS,CAAC,MAAM,KAAK,6BAAS,CAAC,IAAI,EAAE,CAAC;YAChF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,OAAe,EAAE,eAAwB;QACpE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzC,SAAS,CAAC,MAAM,GAAG,6BAAS,CAAC,KAAK,CAAC;QACnC,SAAS,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;QAC5B,IAAI,eAAe,EAAE,CAAC;YACpB,SAAS,CAAC,eAAe,GAAG,eAAe,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,aAAqB;QACnD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzC,SAAS,CAAC,UAAU,IAAI,aAAa,CAAC;QACtC,SAAS,CAAC,iBAAiB,GAAG,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC;QAE3E,IAAI,SAAS,CAAC,iBAAiB,IAAI,CAAC,EAAE,CAAC;YACrC,SAAS,CAAC,MAAM,GAAG,6BAAS,CAAC,IAAI,CAAC;YAClC,SAAS,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,EAAU;QAC5C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QAEzB,IAAI,KAAK,GAAG,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK,6BAAS,CAAC,IAAI,EAAE,CAAC;YACrE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAGxG,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;YAClD,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,SAAS,GAAG,IAAI,GAAG,aAAa,CAAC;YAGrE,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,SAAS,GAAG,KAAK,GAAG,aAAa,CAAC;YAEvE,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,cAAc,CAAC;YACjG,SAAS,CAAC,iBAAiB,GAAG,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC;YAE3E,IAAI,SAAS,CAAC,MAAM,KAAK,6BAAS,CAAC,KAAK,EAAE,CAAC;gBACzC,SAAS,CAAC,MAAM,GAAG,6BAAS,CAAC,OAAO,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAEvC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACpC,SAAS;YACT,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG;YACd,IAAI;YACJ,iBAAiB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAChF,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;YACzE,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACvF,cAAc,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YACxF,aAAa,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YACxF,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAC1C,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;YACxC,cAAc,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,6BAAS,CAAC,OAAO,CAAC;SACjF,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,OAAgB;QACpD,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,GAAG,UAAU,IAAI,IAAI,GAAG,CAAC;QAExC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YACxD,KAAK,EAAE,EAAE,eAAe,EAAE,IAAA,cAAI,EAAC,GAAG,MAAM,GAAG,CAAC,EAAE;YAC9C,KAAK,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE;SACnC,CAAC,CAAC;QAEH,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,UAAU,GAAG,UAAU,GAAG,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,GAAG,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC9D,CAAC;IAEO,gBAAgB,CAAC,OAAgB;QACvC,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,2BAAO,CAAC,UAAU,CAAC,CAAC,OAAO,IAAI,CAAC;YACrC,KAAK,2BAAO,CAAC,SAAS,CAAC,CAAC,OAAO,IAAI,CAAC;YACpC,KAAK,2BAAO,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC;YAC/B,KAAK,2BAAO,CAAC,WAAW,CAAC,CAAC,OAAO,IAAI,CAAC;YACtC,KAAK,2BAAO,CAAC,YAAY,CAAC,CAAC,OAAO,MAAM,CAAC;YACzC,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,OAAoB;QACzC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG;oBACpB,KAAK,EAAE,CAAC;oBACR,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,CAAC;oBACZ,gBAAgB,EAAE,CAAC;iBACpB,CAAC;YACJ,CAAC;YACD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5B,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC;YACjD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,gBAAgB,IAAI,MAAM,CAAC,iBAAiB,CAAC;YACjE,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,aAAa,CAAC,OAAoB;QACxC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxB,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG;oBACnB,KAAK,EAAE,CAAC;oBACR,WAAW,EAAE,CAAC;iBACf,CAAC;YACJ,CAAC;YACD,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;YAC3B,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC;YACrD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;CACF,CAAA;AA1NY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,6BAAS,CAAC,CAAA;qCACC,oBAAU;GAH9B,UAAU,CA0NtB"}