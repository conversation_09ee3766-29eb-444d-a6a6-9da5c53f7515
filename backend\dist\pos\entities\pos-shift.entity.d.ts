import { PosTerminal } from './pos-terminal.entity';
import { PosCashDrawer } from './pos-cash-drawer.entity';
export declare enum ShiftStatus {
    OPEN = "open",
    CLOSED = "closed",
    SUSPENDED = "suspended"
}
export declare class PosShift {
    id: string;
    shiftNumber: string;
    terminalId: string;
    terminal: PosTerminal;
    cashierId: string;
    status: ShiftStatus;
    startTime: Date;
    endTime: Date;
    openingCash: number;
    closingCash: number;
    expectedCash: number;
    cashVariance: number;
    totalTransactions: number;
    totalSales: number;
    totalReturns: number;
    totalDiscounts: number;
    totalTax: number;
    cashSales: number;
    cardSales: number;
    otherPayments: number;
    voidedTransactions: number;
    voidedAmount: number;
    notes: string;
    closedBy: string;
    closedAt: Date;
    cashDrawerActivities: PosCashDrawer[];
    paymentSummary: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
