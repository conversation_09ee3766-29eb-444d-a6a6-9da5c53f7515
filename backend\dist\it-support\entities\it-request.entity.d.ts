export declare enum ITRequestType {
    NEW_USER_SETUP = "new_user_setup",
    USER_TERMINATION = "user_termination",
    ACCESS_REQUEST = "access_request",
    SOFTWARE_INSTALLATION = "software_installation",
    HARDWARE_REQUEST = "hardware_request",
    PASSWORD_RESET = "password_reset",
    EMAIL_SETUP = "email_setup",
    PHONE_SETUP = "phone_setup",
    TRAINING = "training",
    OTHER = "other"
}
export declare enum RequestStatus {
    SUBMITTED = "submitted",
    APPROVED = "approved",
    REJECTED = "rejected",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare enum RequestPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class ITRequest {
    id: string;
    requestNumber: string;
    type: ITRequestType;
    status: RequestStatus;
    priority: RequestPriority;
    title: string;
    description: string;
    requesterId: string;
    requesterName: string;
    requesterEmail: string;
    assignedTo: string;
    requestedDate: Date;
    dueDate: Date;
    approvedAt: Date;
    approvedBy: string;
    completedAt: Date;
    approvalNotes: string;
    completionNotes: string;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
