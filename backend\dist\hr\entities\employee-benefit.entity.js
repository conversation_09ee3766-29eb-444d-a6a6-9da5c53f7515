"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeBenefit = exports.EnrollmentStatus = void 0;
const typeorm_1 = require("typeorm");
const employee_entity_1 = require("./employee.entity");
const benefit_entity_1 = require("./benefit.entity");
var EnrollmentStatus;
(function (EnrollmentStatus) {
    EnrollmentStatus["ENROLLED"] = "enrolled";
    EnrollmentStatus["PENDING"] = "pending";
    EnrollmentStatus["DECLINED"] = "declined";
    EnrollmentStatus["TERMINATED"] = "terminated";
    EnrollmentStatus["SUSPENDED"] = "suspended";
})(EnrollmentStatus || (exports.EnrollmentStatus = EnrollmentStatus = {}));
let EmployeeBenefit = class EmployeeBenefit {
    id;
    employeeId;
    employee;
    benefitId;
    benefit;
    status;
    enrollmentDate;
    effectiveDate;
    terminationDate;
    employeeContribution;
    employerContribution;
    coverageAmount;
    dependents;
    beneficiaries;
    enrollmentChoices;
    notes;
    enrolledBy;
    enrolledAt;
    metadata;
    createdAt;
    updatedAt;
};
exports.EmployeeBenefit = EmployeeBenefit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EmployeeBenefit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EmployeeBenefit.prototype, "employeeId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => employee_entity_1.Employee, employee => employee.benefits),
    (0, typeorm_1.JoinColumn)({ name: 'employeeId' }),
    __metadata("design:type", employee_entity_1.Employee)
], EmployeeBenefit.prototype, "employee", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EmployeeBenefit.prototype, "benefitId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => benefit_entity_1.Benefit, benefit => benefit.employeeBenefits),
    (0, typeorm_1.JoinColumn)({ name: 'benefitId' }),
    __metadata("design:type", benefit_entity_1.Benefit)
], EmployeeBenefit.prototype, "benefit", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EnrollmentStatus,
        default: EnrollmentStatus.PENDING,
    }),
    __metadata("design:type", String)
], EmployeeBenefit.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], EmployeeBenefit.prototype, "enrollmentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], EmployeeBenefit.prototype, "effectiveDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], EmployeeBenefit.prototype, "terminationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], EmployeeBenefit.prototype, "employeeContribution", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], EmployeeBenefit.prototype, "employerContribution", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], EmployeeBenefit.prototype, "coverageAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], EmployeeBenefit.prototype, "dependents", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], EmployeeBenefit.prototype, "beneficiaries", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], EmployeeBenefit.prototype, "enrollmentChoices", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], EmployeeBenefit.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], EmployeeBenefit.prototype, "enrolledBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], EmployeeBenefit.prototype, "enrolledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], EmployeeBenefit.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], EmployeeBenefit.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], EmployeeBenefit.prototype, "updatedAt", void 0);
exports.EmployeeBenefit = EmployeeBenefit = __decorate([
    (0, typeorm_1.Entity)('hr_employee_benefits')
], EmployeeBenefit);
//# sourceMappingURL=employee-benefit.entity.js.map