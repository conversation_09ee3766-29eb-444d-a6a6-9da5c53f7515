import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum CallResult {
  CONTACT_MADE = 'contact_made',
  NO_ANSWER = 'no_answer',
  BUSY = 'busy',
  DISCONNECTED = 'disconnected',
  WRONG_NUMBER = 'wrong_number',
  LEFT_VOICEMAIL = 'left_voicemail',
  CALL_BACK_REQUESTED = 'call_back_requested',
  REFUSED_TO_TALK = 'refused_to_talk',
  HUNG_UP = 'hung_up',
  TECHNICAL_ISSUE = 'technical_issue',
}

export enum CallDisposition {
  PROMISE_TO_PAY = 'promise_to_pay',
  PAYMENT_MADE = 'payment_made',
  DISPUTE_RAISED = 'dispute_raised',
  HARDSHIP_CLAIMED = 'hardship_claimed',
  PAYMENT_PLAN_REQUESTED = 'payment_plan_requested',
  REFUSED_TO_PAY = 'refused_to_pay',
  WILL_CALL_BACK = 'will_call_back',
  REQUESTED_VALIDATION = 'requested_validation',
  CEASE_AND_DESIST = 'cease_and_desist',
  BANKRUPTCY_CLAIMED = 'bankruptcy_claimed',
  DECEASED_CLAIMED = 'deceased_claimed',
  NOT_RESPONSIBLE = 'not_responsible',
  OTHER = 'other',
}

@Entity('collection_calls')
export class CollectionCall {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  caseId: string;

  @Column({ type: 'timestamp' })
  callDateTime: Date;

  @Column({ length: 20 })
  phoneNumber: string;

  @Column({ type: 'int' })
  duration: number; // in seconds

  @Column({
    type: 'enum',
    enum: CallResult,
  })
  result: CallResult;

  @Column({
    type: 'enum',
    enum: CallDisposition,
    nullable: true,
  })
  disposition: CallDisposition;

  @Column()
  agentId: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  summary: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  promiseAmount: number;

  @Column({ type: 'date', nullable: true })
  promiseDate: Date;

  @Column({ type: 'date', nullable: true })
  callbackDate: Date;

  @Column({ length: 500, nullable: true })
  recordingPath: string;

  @Column({ default: false })
  isRecorded: boolean;

  @Column({ default: false })
  isInbound: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
