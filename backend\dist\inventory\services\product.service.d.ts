import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { Category } from '../entities/category.entity';
import { Stock } from '../entities/stock.entity';
export declare class ProductService {
    private productRepository;
    private categoryRepository;
    private stockRepository;
    constructor(productRepository: Repository<Product>, categoryRepository: Repository<Category>, stockRepository: Repository<Stock>);
    create(productData: Partial<Product>): Promise<Product>;
    findAll(): Promise<Product[]>;
    findOne(id: string): Promise<Product>;
    update(id: string, updateData: Partial<Product>): Promise<Product>;
    remove(id: string): Promise<void>;
    findByCategory(categoryId: string): Promise<Product[]>;
    findBySku(sku: string): Promise<Product>;
    findByBarcode(barcode: string): Promise<Product>;
    searchProducts(searchTerm: string): Promise<Product[]>;
    getLowStockProducts(threshold?: number): Promise<Product[]>;
    getOutOfStockProducts(): Promise<Product[]>;
    updateStock(productId: string, warehouseId: string, quantity: number): Promise<void>;
    reserveStock(productId: string, warehouseId: string, quantity: number): Promise<boolean>;
    releaseStock(productId: string, warehouseId: string, quantity: number): Promise<void>;
    getProductStatistics(): Promise<any>;
    getTopSellingProducts(limit?: number): Promise<Product[]>;
    bulkUpdatePrices(updates: Array<{
        id: string;
        salePrice: number;
        costPrice?: number;
    }>): Promise<void>;
    generateSku(categoryCode: string): Promise<string>;
}
