import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ProjectMember } from './project-member.entity';
import { Task } from './task.entity';
import { Milestone } from './milestone.entity';
import { TimeEntry } from './time-entry.entity';
import { ProjectExpense } from './project-expense.entity';
import { ProjectDocument } from './project-document.entity';

export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ARCHIVED = 'archived',
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum ProjectType {
  INTERNAL = 'internal',
  CLIENT = 'client',
  RESEARCH = 'research',
  MAINTENANCE = 'maintenance',
  DEVELOPMENT = 'development',
  MARKETING = 'marketing',
  TRAINING = 'training',
}

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ProjectType,
    default: ProjectType.INTERNAL,
  })
  type: ProjectType;

  @Column({
    type: 'enum',
    enum: ProjectStatus,
    default: ProjectStatus.PLANNING,
  })
  status: ProjectStatus;

  @Column({
    type: 'enum',
    enum: ProjectPriority,
    default: ProjectPriority.MEDIUM,
  })
  priority: ProjectPriority;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'date', nullable: true })
  actualStartDate: Date;

  @Column({ type: 'date', nullable: true })
  actualEndDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  budget: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  actualCost: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ nullable: true })
  clientId: string;

  @Column({ length: 255, nullable: true })
  clientName: string;

  @Column({ nullable: true })
  managerId: string;

  @Column({ nullable: true })
  parentProjectId: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'parentProjectId' })
  parentProject: Project;

  @OneToMany(() => Project, project => project.parentProject)
  subProjects: Project[];

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  completionPercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  estimatedHours: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  actualHours: number;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  customFields: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @OneToMany(() => ProjectMember, member => member.project, { cascade: true })
  members: ProjectMember[];

  @OneToMany(() => Task, task => task.project)
  tasks: Task[];

  @OneToMany(() => Milestone, milestone => milestone.project)
  milestones: Milestone[];

  @OneToMany(() => TimeEntry, timeEntry => timeEntry.project)
  timeEntries: TimeEntry[];

  @OneToMany(() => ProjectExpense, expense => expense.project)
  expenses: ProjectExpense[];

  @OneToMany(() => ProjectDocument, document => document.project)
  documents: ProjectDocument[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
