import { ReturnedInvoiceService } from '../services/returned-invoice.service';
import { CreateReturnedInvoiceDto } from '../dto/create-returned-invoice.dto';
export declare class ReturnedInvoiceController {
    private readonly returnedInvoiceService;
    constructor(returnedInvoiceService: ReturnedInvoiceService);
    create(createReturnedInvoiceDto: CreateReturnedInvoiceDto, req: any): Promise<import("../entities/returned-invoice.entity").ReturnedInvoice>;
    findAll(req: any, query: any): Promise<import("../entities/returned-invoice.entity").ReturnedInvoice[]>;
    getStats(req: any): Promise<{
        totalReturns: number;
        pendingReturns: number;
        completedReturns: number;
        totalReturnAmount: number;
    }>;
    exportData(req: any, query: any): Promise<{
        success: boolean;
        data: {
            returnNumber: string;
            customerName: string;
            originalInvoiceNumber: string;
            returnDate: Date;
            originalAmount: number;
            returnAmount: number;
            totalReturnAmount: number;
            status: "pending" | "cancelled" | "completed" | "processing";
            reason: string;
            refundMethod: "refund" | "credit_note" | "exchange";
            processedBy: string;
            processedDate: Date | null;
        }[];
        message: string;
    }>;
    findOne(id: string, req: any): Promise<import("../entities/returned-invoice.entity").ReturnedInvoice>;
    update(id: string, updateReturnedInvoiceDto: Partial<CreateReturnedInvoiceDto>, req: any): Promise<import("../entities/returned-invoice.entity").ReturnedInvoice>;
    updateStatus(id: string, status: string, req: any): Promise<import("../entities/returned-invoice.entity").ReturnedInvoice>;
    remove(id: string, req: any): Promise<void>;
}
