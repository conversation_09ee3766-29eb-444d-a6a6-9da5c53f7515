import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ON_BREAK = 'on_break',
  TRAINING = 'training',
  SUSPENDED = 'suspended',
}

@Entity('collection_agents')
export class CollectionAgent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column({ length: 50, unique: true })
  agentNumber: string;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 200 })
  email: string;

  @Column({ length: 20, nullable: true })
  phone: string;

  @Column({
    type: 'enum',
    enum: AgentStatus,
    default: AgentStatus.ACTIVE,
  })
  status: AgentStatus;

  @Column({ type: 'date' })
  hireDate: Date;

  @Column({ type: 'int', default: 0 })
  maxCases: number;

  @Column({ type: 'int', default: 0 })
  currentCases: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalCollected: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  collectionRate: number;

  @Column({ type: 'int', default: 0 })
  totalCalls: number;

  @Column({ type: 'int', default: 0 })
  successfulCalls: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  callSuccessRate: number;

  @Column({ type: 'json', nullable: true })
  specializations: string[];

  @Column({ type: 'json', nullable: true })
  permissions: string[];

  @Column({ type: 'json', nullable: true })
  workingHours: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
