import { ProcurementRequest } from './procurement-request.entity';
export declare enum ApprovalStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    DELEGATED = "delegated"
}
export declare enum ApprovalLevel {
    SUPERVISOR = "supervisor",
    MANAGER = "manager",
    DIRECTOR = "director",
    VP = "vp",
    CEO = "ceo",
    BOARD = "board"
}
export declare class ProcurementApproval {
    id: string;
    requestId: string;
    request: ProcurementRequest;
    level: ApprovalLevel;
    status: ApprovalStatus;
    approverId: string;
    sequence: number;
    approvedAt: Date;
    comments: string;
    delegatedTo: string;
    delegatedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
