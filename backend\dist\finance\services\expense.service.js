"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpenseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const expense_entity_1 = require("../entities/expense.entity");
const expense_category_entity_1 = require("../entities/expense-category.entity");
let ExpenseService = class ExpenseService {
    expenseRepository;
    categoryRepository;
    constructor(expenseRepository, categoryRepository) {
        this.expenseRepository = expenseRepository;
        this.categoryRepository = categoryRepository;
    }
    async create(createExpenseDto) {
        const expenseNumber = await this.generateExpenseNumber();
        const expense = this.expenseRepository.create({
            ...createExpenseDto,
            expenseNumber,
            baseCurrencyAmount: createExpenseDto.amount * (createExpenseDto.exchangeRate || 1),
            totalAmount: createExpenseDto.amount + (createExpenseDto.taxAmount || 0),
        });
        return this.expenseRepository.save(expense);
    }
    async findAll(filters) {
        const queryBuilder = this.expenseRepository.createQueryBuilder('expense')
            .leftJoinAndSelect('expense.category', 'category')
            .leftJoinAndSelect('expense.account', 'account');
        if (filters?.status) {
            queryBuilder.andWhere('expense.status = :status', { status: filters.status });
        }
        if (filters?.type) {
            queryBuilder.andWhere('expense.type = :type', { type: filters.type });
        }
        if (filters?.employeeId) {
            queryBuilder.andWhere('expense.employeeId = :employeeId', { employeeId: filters.employeeId });
        }
        if (filters?.departmentId) {
            queryBuilder.andWhere('expense.departmentId = :departmentId', { departmentId: filters.departmentId });
        }
        if (filters?.startDate && filters?.endDate) {
            queryBuilder.andWhere('expense.expenseDate BETWEEN :startDate AND :endDate', {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        return queryBuilder
            .orderBy('expense.expenseDate', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const expense = await this.expenseRepository.findOne({
            where: { id },
            relations: ['category', 'account'],
        });
        if (!expense) {
            throw new common_1.NotFoundException(`Expense with ID ${id} not found`);
        }
        return expense;
    }
    async update(id, updateExpenseDto) {
        const expense = await this.findOne(id);
        if (expense.status === expense_entity_1.ExpenseStatus.PAID) {
            throw new common_1.BadRequestException('Cannot update paid expense');
        }
        Object.assign(expense, updateExpenseDto);
        if (updateExpenseDto.amount || updateExpenseDto.taxAmount) {
            expense.totalAmount = expense.amount + (expense.taxAmount || 0);
        }
        if (updateExpenseDto.amount || updateExpenseDto.exchangeRate) {
            expense.baseCurrencyAmount = expense.amount * (expense.exchangeRate || 1);
        }
        return this.expenseRepository.save(expense);
    }
    async remove(id) {
        const expense = await this.findOne(id);
        if (expense.status === expense_entity_1.ExpenseStatus.PAID) {
            throw new common_1.BadRequestException('Cannot delete paid expense');
        }
        await this.expenseRepository.remove(expense);
    }
    async submitExpense(id, submittedBy) {
        const expense = await this.findOne(id);
        if (expense.status !== expense_entity_1.ExpenseStatus.DRAFT) {
            throw new common_1.BadRequestException('Only draft expenses can be submitted');
        }
        expense.status = expense_entity_1.ExpenseStatus.SUBMITTED;
        expense.submittedBy = submittedBy;
        expense.submittedAt = new Date();
        return this.expenseRepository.save(expense);
    }
    async approveExpense(id, approvedBy, notes) {
        const expense = await this.findOne(id);
        if (expense.status !== expense_entity_1.ExpenseStatus.SUBMITTED) {
            throw new common_1.BadRequestException('Only submitted expenses can be approved');
        }
        expense.status = expense_entity_1.ExpenseStatus.APPROVED;
        expense.approvedBy = approvedBy;
        expense.approvedAt = new Date();
        expense.approvalNotes = notes;
        return this.expenseRepository.save(expense);
    }
    async rejectExpense(id, rejectedBy, notes) {
        const expense = await this.findOne(id);
        if (expense.status !== expense_entity_1.ExpenseStatus.SUBMITTED) {
            throw new common_1.BadRequestException('Only submitted expenses can be rejected');
        }
        expense.status = expense_entity_1.ExpenseStatus.REJECTED;
        expense.approvedBy = rejectedBy;
        expense.approvedAt = new Date();
        expense.approvalNotes = notes;
        return this.expenseRepository.save(expense);
    }
    async markAsPaid(id) {
        const expense = await this.findOne(id);
        if (expense.status !== expense_entity_1.ExpenseStatus.APPROVED) {
            throw new common_1.BadRequestException('Only approved expenses can be marked as paid');
        }
        expense.status = expense_entity_1.ExpenseStatus.PAID;
        return this.expenseRepository.save(expense);
    }
    async getExpenseReport(filters) {
        const expenses = await this.findAll(filters);
        const report = {
            totalExpenses: expenses.length,
            totalAmount: expenses.reduce((sum, exp) => sum + exp.totalAmount, 0),
            byStatus: this.groupByField(expenses, 'status'),
            byType: this.groupByField(expenses, 'type'),
            byCategory: this.groupByCategory(expenses),
            byEmployee: this.groupByField(expenses, 'employeeId'),
            byDepartment: this.groupByField(expenses, 'departmentId'),
            expenses: expenses.map(exp => ({
                id: exp.id,
                expenseNumber: exp.expenseNumber,
                title: exp.title,
                type: exp.type,
                status: exp.status,
                amount: exp.amount,
                totalAmount: exp.totalAmount,
                expenseDate: exp.expenseDate,
                category: exp.category?.name,
                vendor: exp.vendor,
            })),
        };
        return report;
    }
    async createCategory(createCategoryDto) {
        const category = this.categoryRepository.create(createCategoryDto);
        return this.categoryRepository.save(category);
    }
    async findAllCategories() {
        return this.categoryRepository.find({
            where: { isActive: true },
            relations: ['parentCategory', 'childCategories', 'defaultAccount'],
            order: { name: 'ASC' },
        });
    }
    async findCategory(id) {
        const category = await this.categoryRepository.findOne({
            where: { id },
            relations: ['parentCategory', 'childCategories', 'defaultAccount', 'expenses'],
        });
        if (!category) {
            throw new common_1.NotFoundException(`Expense category with ID ${id} not found`);
        }
        return category;
    }
    async generateExpenseNumber() {
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const prefix = `EXP-${year}${month}-`;
        const lastExpense = await this.expenseRepository.findOne({
            where: { expenseNumber: (0, typeorm_2.Like)(`${prefix}%`) },
            order: { expenseNumber: 'DESC' },
        });
        let nextNumber = 1;
        if (lastExpense) {
            const lastNumber = parseInt(lastExpense.expenseNumber.split('-')[2]);
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
    }
    groupByField(expenses, field) {
        return expenses.reduce((acc, expense) => {
            const key = expense[field] || 'Unknown';
            if (!acc[key]) {
                acc[key] = { count: 0, amount: 0 };
            }
            acc[key].count++;
            acc[key].amount += expense.totalAmount;
            return acc;
        }, {});
    }
    groupByCategory(expenses) {
        return expenses.reduce((acc, expense) => {
            const key = expense.category?.name || 'Uncategorized';
            if (!acc[key]) {
                acc[key] = { count: 0, amount: 0 };
            }
            acc[key].count++;
            acc[key].amount += expense.totalAmount;
            return acc;
        }, {});
    }
};
exports.ExpenseService = ExpenseService;
exports.ExpenseService = ExpenseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(expense_entity_1.Expense)),
    __param(1, (0, typeorm_1.InjectRepository)(expense_category_entity_1.ExpenseCategory)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], ExpenseService);
//# sourceMappingURL=expense.service.js.map