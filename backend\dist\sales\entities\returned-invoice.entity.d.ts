import { Customer } from './customer.entity';
import { Invoice } from './invoice.entity';
import { ReturnedInvoiceItem } from './returned-invoice-item.entity';
export declare class ReturnedInvoice {
    id: string;
    returnNumber: string;
    customerId: string;
    customer: Customer;
    originalInvoiceId: string;
    originalInvoice: Invoice;
    returnDate: Date;
    reason: string;
    returnType: 'full' | 'partial';
    originalAmount: number;
    returnAmount: number;
    returnTaxAmount: number;
    totalReturnAmount: number;
    status: 'pending' | 'processing' | 'completed' | 'cancelled';
    refundMethod: 'refund' | 'credit_note' | 'exchange';
    notes: string;
    internalNotes: string;
    processedBy: string;
    processedDate: Date;
    refundReference: string;
    items: ReturnedInvoiceItem[];
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
