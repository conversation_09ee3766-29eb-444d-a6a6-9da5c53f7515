import { Employee } from './employee.entity';
export declare class Department {
    id: string;
    name: string;
    code: string;
    description: string;
    parentDepartmentId: string;
    parentDepartment: Department;
    childDepartments: Department[];
    managerId: string;
    manager: Employee;
    employees: Employee[];
    budget: number;
    location: string;
    phone: string;
    email: string;
    isActive: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
