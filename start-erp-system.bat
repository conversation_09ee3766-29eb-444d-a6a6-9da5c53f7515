@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: ZaidanOne Ultimate ERP Management System - Windows Startup Script
:: =============================================================================
:: This script automates the startup and management of the world's most
:: advanced ERP system with AI-powered features and enterprise-grade security
:: =============================================================================

title ZaidanOne Ultimate ERP System - Startup Manager

:: Color codes for better visual experience
color 0A

echo.
echo ===============================================================================
echo                    ZAIDANONE ULTIMATE ERP MANAGEMENT SYSTEM
echo                          World's Most Advanced ERP Platform
echo ===============================================================================
echo.
echo [INFO] Starting the Ultimate ERP System with AI-Powered Features...
echo [INFO] Enterprise-Grade Security and Multi-Tenant Architecture
echo.

:: Check if Node.js is installed
echo [CHECK] Verifying Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH!
    echo [INFO] Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if npm is installed
echo [CHECK] Verifying npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed or not in PATH!
    echo [INFO] Please install npm (usually comes with Node.js)
    pause
    exit /b 1
)

echo [SUCCESS] Node.js and npm are properly installed!
echo.

:: Display system information
echo [INFO] System Information:
echo [INFO] Node.js Version: 
node --version
echo [INFO] npm Version: 
npm --version
echo.

:: Check if directories exist
if not exist "frontend" (
    echo [ERROR] Frontend directory not found!
    echo [INFO] Please ensure you're running this script from the project root directory
    pause
    exit /b 1
)

if not exist "backend" (
    echo [ERROR] Backend directory not found!
    echo [INFO] Please ensure you're running this script from the project root directory
    pause
    exit /b 1
)

echo [SUCCESS] Project directories found!
echo.

:: Menu system
:MENU
cls
echo.
echo ===============================================================================
echo                    ZAIDANONE ULTIMATE ERP SYSTEM - CONTROL PANEL
echo ===============================================================================
echo.
echo Please select an option:
echo.
echo [1] Start Full System (Frontend + Backend)
echo [2] Start Frontend Only (Port 5173)
echo [3] Start Backend Only (Port 3001)
echo [4] Install/Update Dependencies
echo [5] Build Production Version
echo [6] Run Tests
echo [7] View System Status
echo [8] Stop All Services
echo [9] Clean and Reinstall
echo [0] Exit
echo.
set /p choice="Enter your choice (0-9): "

if "%choice%"=="1" goto START_FULL
if "%choice%"=="2" goto START_FRONTEND
if "%choice%"=="3" goto START_BACKEND
if "%choice%"=="4" goto INSTALL_DEPS
if "%choice%"=="5" goto BUILD_PROD
if "%choice%"=="6" goto RUN_TESTS
if "%choice%"=="7" goto SYSTEM_STATUS
if "%choice%"=="8" goto STOP_SERVICES
if "%choice%"=="9" goto CLEAN_INSTALL
if "%choice%"=="0" goto EXIT

echo [ERROR] Invalid choice. Please try again.
timeout /t 2 >nul
goto MENU

:START_FULL
echo.
echo [INFO] Starting Full ERP System...
echo [INFO] This will start both Frontend (Port 5173) and Backend (Port 3001)
echo.

:: Start backend in a new window
echo [INFO] Starting Backend Server...
start "ZaidanOne ERP - Backend Server" cmd /k "cd backend && npm run start:dev"

:: Wait a moment for backend to start
timeout /t 3 >nul

:: Start frontend in a new window
echo [INFO] Starting Frontend Server...
start "ZaidanOne ERP - Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo [SUCCESS] ERP System is starting up!
echo [INFO] Backend: http://localhost:3001
echo [INFO] Frontend: http://localhost:5173
echo [INFO] Please wait for both servers to fully initialize...
echo.
echo [INFO] The system will automatically open in your browser once ready.
echo.
pause
goto MENU

:START_FRONTEND
echo.
echo [INFO] Starting Frontend Only...
echo [INFO] Frontend will be available at: http://localhost:5173
echo.

cd frontend
start "ZaidanOne ERP - Frontend Server" cmd /k "npm run dev"
cd ..

echo [SUCCESS] Frontend server is starting...
echo [INFO] Check the new window for startup progress
echo.
pause
goto MENU

:START_BACKEND
echo.
echo [INFO] Starting Backend Only...
echo [INFO] Backend API will be available at: http://localhost:3001
echo.

cd backend
start "ZaidanOne ERP - Backend Server" cmd /k "npm run start:dev"
cd ..

echo [SUCCESS] Backend server is starting...
echo [INFO] Check the new window for startup progress
echo.
pause
goto MENU

:INSTALL_DEPS
echo.
echo [INFO] Installing/Updating Dependencies...
echo.

echo [INFO] Installing Frontend Dependencies...
cd frontend
npm install --legacy-peer-deps
if errorlevel 1 (
    echo [ERROR] Frontend dependency installation failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [INFO] Installing Backend Dependencies...
cd backend
npm install
if errorlevel 1 (
    echo [ERROR] Backend dependency installation failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [SUCCESS] All dependencies installed successfully!
echo.
pause
goto MENU

:BUILD_PROD
echo.
echo [INFO] Building Production Version...
echo.

echo [INFO] Building Frontend...
cd frontend
npm run build
if errorlevel 1 (
    echo [ERROR] Frontend build failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [INFO] Building Backend...
cd backend
npm run build
if errorlevel 1 (
    echo [ERROR] Backend build failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [SUCCESS] Production build completed successfully!
echo [INFO] Built files are ready for deployment
echo.
pause
goto MENU

:RUN_TESTS
echo.
echo [INFO] Running Tests...
echo.

echo [INFO] Running Frontend Tests...
cd frontend
npm run test
cd ..

echo [INFO] Running Backend Tests...
cd backend
npm run test
cd ..

echo [SUCCESS] Tests completed!
echo.
pause
goto MENU

:SYSTEM_STATUS
echo.
echo [INFO] Checking System Status...
echo.

:: Check if ports are in use
echo [INFO] Checking port availability...
netstat -an | findstr ":5173" >nul
if not errorlevel 1 (
    echo [STATUS] Frontend (Port 5173): RUNNING
) else (
    echo [STATUS] Frontend (Port 5173): STOPPED
)

netstat -an | findstr ":3001" >nul
if not errorlevel 1 (
    echo [STATUS] Backend (Port 3001): RUNNING
) else (
    echo [STATUS] Backend (Port 3001): STOPPED
)

echo.
echo [INFO] System Resources:
echo [INFO] Available Memory: 
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | findstr "="

echo.
pause
goto MENU

:STOP_SERVICES
echo.
echo [INFO] Stopping All ERP Services...
echo.

:: Kill Node.js processes (be careful with this)
taskkill /f /im node.exe >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] All Node.js processes stopped
) else (
    echo [INFO] No Node.js processes were running
)

echo [SUCCESS] All services stopped!
echo.
pause
goto MENU

:CLEAN_INSTALL
echo.
echo [WARNING] This will delete all node_modules and reinstall everything!
echo [WARNING] This may take several minutes to complete.
echo.
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto MENU

echo.
echo [INFO] Cleaning and reinstalling...

echo [INFO] Cleaning Frontend...
cd frontend
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
npm install --legacy-peer-deps
cd ..

echo [INFO] Cleaning Backend...
cd backend
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
npm install
cd ..

echo [SUCCESS] Clean installation completed!
echo.
pause
goto MENU

:EXIT
echo.
echo [INFO] Thank you for using ZaidanOne Ultimate ERP System!
echo [INFO] The world's most advanced enterprise platform.
echo.
echo [INFO] For support, visit: https://github.com/zaidanone/erp-system
echo.
pause
exit /b 0

:: Error handling
:ERROR
echo [ERROR] An unexpected error occurred!
echo [INFO] Please check the error messages above and try again.
pause
goto MENU
