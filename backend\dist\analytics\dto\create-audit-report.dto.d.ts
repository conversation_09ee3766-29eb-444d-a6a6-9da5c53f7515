declare class AuditTeamDto {
    leadAuditor: string;
    members: string[];
    externalAuditors?: string[];
}
declare class AuditCriteriaDto {
    standards: string[];
    regulations: string[];
    policies: string[];
}
declare class RiskAssessmentDto {
    inherentRisk: string;
    controlRisk: string;
    detectionRisk: string;
    overallRisk: string;
}
declare class SamplingMethodDto {
    type: string;
    size: number;
    criteria: string;
}
export declare class CreateAuditReportDto {
    year: number;
    quarter?: number;
    reportType: string;
    department: string;
    scope: string;
    auditCategory?: string;
    regulatoryFramework?: string[];
    riskLevel?: string;
    auditor: string;
    objectives?: string;
    methodology?: string;
    executiveSummary?: string;
    complianceScore?: number;
    plannedStartDate?: string;
    plannedEndDate?: string;
    actualStartDate?: string;
    actualEndDate?: string;
    auditTeam?: AuditTeamDto;
    auditCriteria?: AuditCriteriaDto;
    riskAssessment?: RiskAssessmentDto;
    samplingMethod?: SamplingMethodDto;
    limitations?: string;
    conclusion?: string;
    managementResponse?: string;
    nextAuditDate?: string;
}
export {};
