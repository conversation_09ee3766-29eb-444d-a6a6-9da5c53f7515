import { CollectionActivity } from './collection-activity.entity';
import { CollectionStrategy } from './collection-strategy.entity';
import { CollectionAgent } from './collection-agent.entity';
import { PaymentPlan } from './payment-plan.entity';
import { CollectionNote } from './collection-note.entity';
import { CollectionDocument } from './collection-document.entity';
import { CollectionDispute } from './collection-dispute.entity';
export declare enum CaseStatus {
    NEW = "new",
    ACTIVE = "active",
    ON_HOLD = "on_hold",
    PAYMENT_PLAN = "payment_plan",
    DISPUTED = "disputed",
    LEGAL = "legal",
    CLOSED_PAID = "closed_paid",
    CLOSED_WRITTEN_OFF = "closed_written_off",
    CLOSED_SETTLED = "closed_settled",
    CLOSED_UNCOLLECTABLE = "closed_uncollectable"
}
export declare enum CasePriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent",
    CRITICAL = "critical"
}
export declare enum DebtType {
    INVOICE = "invoice",
    LOAN = "loan",
    CREDIT_CARD = "credit_card",
    UTILITY = "utility",
    MEDICAL = "medical",
    STUDENT_LOAN = "student_loan",
    MORTGAGE = "mortgage",
    OTHER = "other"
}
export declare class CollectionCase {
    id: string;
    caseNumber: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    debtType: DebtType;
    originalAmount: number;
    currentBalance: number;
    interestAmount: number;
    feesAmount: number;
    totalAmount: number;
    currency: string;
    originalDueDate: Date;
    daysOverdue: number;
    status: CaseStatus;
    priority: CasePriority;
    assignedAgentId: string;
    assignedAgent: CollectionAgent;
    strategyId: string;
    strategy: CollectionStrategy;
    lastContactDate: Date;
    nextActionDate: Date;
    nextAction: string;
    promiseToPayDate: Date;
    promiseToPayAmount: number;
    contactAttempts: number;
    successfulContacts: number;
    totalPayments: number;
    lastPaymentDate: Date;
    lastPaymentAmount: number;
    customerNotes: string;
    internalNotes: string;
    tags: string[];
    isDisputed: boolean;
    isLegal: boolean;
    isBankrupt: boolean;
    isDeceased: boolean;
    doNotCall: boolean;
    doNotEmail: boolean;
    doNotMail: boolean;
    activities: CollectionActivity[];
    paymentPlans: PaymentPlan[];
    notes: CollectionNote[];
    documents: CollectionDocument[];
    disputes: CollectionDispute[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
