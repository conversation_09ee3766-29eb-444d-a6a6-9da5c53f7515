"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const transaction_entity_1 = require("../entities/transaction.entity");
const account_service_1 = require("./account.service");
let TransactionService = class TransactionService {
    transactionRepository;
    accountService;
    constructor(transactionRepository, accountService) {
        this.transactionRepository = transactionRepository;
        this.accountService = accountService;
    }
    async create(createTransactionDto) {
        await this.accountService.findOne(createTransactionDto.debitAccountId);
        await this.accountService.findOne(createTransactionDto.creditAccountId);
        const transactionNumber = await this.generateTransactionNumber();
        const transaction = this.transactionRepository.create({
            ...createTransactionDto,
            transactionNumber,
            baseCurrencyAmount: createTransactionDto.amount * (createTransactionDto.exchangeRate || 1),
        });
        const savedTransaction = await this.transactionRepository.save(transaction);
        if (savedTransaction.status === transaction_entity_1.TransactionStatus.POSTED) {
            await this.updateAccountBalances(savedTransaction);
        }
        return savedTransaction;
    }
    async findAll(filters) {
        const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
            .leftJoinAndSelect('transaction.debitAccount', 'debitAccount')
            .leftJoinAndSelect('transaction.creditAccount', 'creditAccount');
        if (filters?.startDate && filters?.endDate) {
            queryBuilder.andWhere('transaction.transactionDate BETWEEN :startDate AND :endDate', {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        if (filters?.type) {
            queryBuilder.andWhere('transaction.type = :type', { type: filters.type });
        }
        if (filters?.status) {
            queryBuilder.andWhere('transaction.status = :status', { status: filters.status });
        }
        if (filters?.accountId) {
            queryBuilder.andWhere('(transaction.debitAccountId = :accountId OR transaction.creditAccountId = :accountId)', { accountId: filters.accountId });
        }
        return queryBuilder
            .orderBy('transaction.transactionDate', 'DESC')
            .addOrderBy('transaction.createdAt', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const transaction = await this.transactionRepository.findOne({
            where: { id },
            relations: ['debitAccount', 'creditAccount'],
        });
        if (!transaction) {
            throw new common_1.NotFoundException(`Transaction with ID ${id} not found`);
        }
        return transaction;
    }
    async update(id, updateTransactionDto) {
        const transaction = await this.findOne(id);
        if (transaction.status === transaction_entity_1.TransactionStatus.POSTED) {
            throw new common_1.BadRequestException('Cannot update posted transaction');
        }
        Object.assign(transaction, updateTransactionDto);
        if (updateTransactionDto.amount || updateTransactionDto.exchangeRate) {
            transaction.baseCurrencyAmount = transaction.amount * (transaction.exchangeRate || 1);
        }
        return this.transactionRepository.save(transaction);
    }
    async remove(id) {
        const transaction = await this.findOne(id);
        if (transaction.status === transaction_entity_1.TransactionStatus.POSTED) {
            throw new common_1.BadRequestException('Cannot delete posted transaction');
        }
        await this.transactionRepository.remove(transaction);
    }
    async postTransaction(id, approvedBy) {
        const transaction = await this.findOne(id);
        if (transaction.status === transaction_entity_1.TransactionStatus.POSTED) {
            throw new common_1.BadRequestException('Transaction is already posted');
        }
        transaction.status = transaction_entity_1.TransactionStatus.POSTED;
        transaction.approvedBy = approvedBy;
        transaction.approvedAt = new Date();
        const savedTransaction = await this.transactionRepository.save(transaction);
        await this.updateAccountBalances(savedTransaction);
        return savedTransaction;
    }
    async reverseTransaction(id, reason, reversedBy) {
        const originalTransaction = await this.findOne(id);
        if (originalTransaction.status !== transaction_entity_1.TransactionStatus.POSTED) {
            throw new common_1.BadRequestException('Can only reverse posted transactions');
        }
        const reversalTransaction = this.transactionRepository.create({
            type: transaction_entity_1.TransactionType.ADJUSTMENT,
            status: transaction_entity_1.TransactionStatus.POSTED,
            transactionDate: new Date(),
            description: `Reversal of ${originalTransaction.transactionNumber}: ${reason}`,
            reference: originalTransaction.transactionNumber,
            amount: originalTransaction.amount,
            debitAccountId: originalTransaction.creditAccountId,
            creditAccountId: originalTransaction.debitAccountId,
            currency: originalTransaction.currency,
            exchangeRate: originalTransaction.exchangeRate,
            baseCurrencyAmount: originalTransaction.baseCurrencyAmount,
            relatedEntityType: 'transaction',
            relatedEntityId: originalTransaction.id,
            approvedBy: reversedBy,
            approvedAt: new Date(),
            transactionNumber: await this.generateTransactionNumber(),
        });
        const savedReversal = await this.transactionRepository.save(reversalTransaction);
        originalTransaction.status = transaction_entity_1.TransactionStatus.REVERSED;
        await this.transactionRepository.save(originalTransaction);
        await this.updateAccountBalances(savedReversal);
        return savedReversal;
    }
    async getAccountLedger(accountId, startDate, endDate) {
        const account = await this.accountService.findOne(accountId);
        const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
            .where('(transaction.debitAccountId = :accountId OR transaction.creditAccountId = :accountId)', { accountId })
            .andWhere('transaction.status = :status', { status: transaction_entity_1.TransactionStatus.POSTED });
        if (startDate && endDate) {
            queryBuilder.andWhere('transaction.transactionDate BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        const transactions = await queryBuilder
            .orderBy('transaction.transactionDate', 'ASC')
            .addOrderBy('transaction.createdAt', 'ASC')
            .getMany();
        let runningBalance = 0;
        const ledgerEntries = transactions.map(transaction => {
            const isDebit = transaction.debitAccountId === accountId;
            const amount = isDebit ? transaction.amount : -transaction.amount;
            runningBalance += amount;
            return {
                date: transaction.transactionDate,
                transactionNumber: transaction.transactionNumber,
                description: transaction.description,
                reference: transaction.reference,
                debit: isDebit ? transaction.amount : 0,
                credit: !isDebit ? transaction.amount : 0,
                balance: runningBalance,
            };
        });
        return {
            account: {
                id: account.id,
                accountNumber: account.accountNumber,
                name: account.name,
                type: account.type,
            },
            period: { startDate, endDate },
            entries: ledgerEntries,
            finalBalance: runningBalance,
        };
    }
    async updateAccountBalances(transaction) {
        await this.accountService.updateBalance(transaction.debitAccountId, transaction.amount, true);
        await this.accountService.updateBalance(transaction.creditAccountId, transaction.amount, false);
    }
    async generateTransactionNumber() {
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const prefix = `TXN-${year}${month}-`;
        const lastTransaction = await this.transactionRepository.findOne({
            where: { transactionNumber: (0, typeorm_2.Like)(`${prefix}%`) },
            order: { transactionNumber: 'DESC' },
        });
        let nextNumber = 1;
        if (lastTransaction) {
            const lastNumber = parseInt(lastTransaction.transactionNumber.split('-')[2]);
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
    }
};
exports.TransactionService = TransactionService;
exports.TransactionService = TransactionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.Transaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        account_service_1.AccountService])
], TransactionService);
//# sourceMappingURL=transaction.service.js.map