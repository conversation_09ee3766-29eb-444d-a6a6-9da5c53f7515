import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PayrollService } from '../services/payroll.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/payroll')
@UseGuards(JwtAuthGuard)
export class PayrollController {
  constructor(private readonly payrollService: PayrollService) {}

  @Post()
  create(@Body() createPayrollDto: any) {
    return this.payrollService.create(createPayrollDto);
  }

  @Get()
  findAll(
    @Query('employeeId') employeeId?: string,
    @Query('status') status?: string,
    @Query('payPeriod') payPeriod?: string
  ) {
    const filters: any = {};
    if (employeeId) filters.employeeId = employeeId;
    if (status) filters.status = status;
    if (payPeriod) filters.payPeriod = payPeriod;

    return this.payrollService.findAll(filters);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.payrollService.findOne(id);
  }

  @Post(':id/calculate')
  calculatePayroll(@Param('id') id: string) {
    return this.payrollService.calculatePayroll(id);
  }

  @Post(':id/approve')
  approvePayroll(@Param('id') id: string, @Body() approveDto: { approvedBy: string }) {
    return this.payrollService.approvePayroll(id, approveDto.approvedBy);
  }

  @Post(':id/process-payment')
  processPayment(@Param('id') id: string, @Body() paymentDto: { paidBy: string }) {
    return this.payrollService.processPayment(id, paymentDto.paidBy);
  }

  @Post(':id/items')
  addPayrollItem(@Param('id') id: string, @Body() createPayrollItemDto: any) {
    return this.payrollService.addPayrollItem(id, createPayrollItemDto);
  }
}
