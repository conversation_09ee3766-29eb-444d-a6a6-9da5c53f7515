"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const category_entity_1 = require("../entities/category.entity");
let CategoryService = class CategoryService {
    categoryRepository;
    constructor(categoryRepository) {
        this.categoryRepository = categoryRepository;
    }
    async create(categoryData) {
        const category = this.categoryRepository.create(categoryData);
        return this.categoryRepository.save(category);
    }
    async findAll() {
        return this.categoryRepository.find({
            relations: ['parent', 'children', 'products'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const category = await this.categoryRepository.findOne({
            where: { id },
            relations: ['parent', 'children', 'products'],
        });
        if (!category) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found`);
        }
        return category;
    }
    async update(id, updateData) {
        await this.categoryRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const category = await this.findOne(id);
        if (category.products && category.products.length > 0) {
            throw new Error('Cannot delete category with existing products');
        }
        if (category.children && category.children.length > 0) {
            throw new Error('Cannot delete category with subcategories');
        }
        await this.categoryRepository.remove(category);
    }
    async findRootCategories() {
        return this.categoryRepository.find({
            where: { parentId: null },
            relations: ['children'],
            order: { name: 'ASC' },
        });
    }
    async findByParent(parentId) {
        return this.categoryRepository.find({
            where: { parentId },
            relations: ['children', 'products'],
            order: { name: 'ASC' },
        });
    }
    async getCategoryTree() {
        const rootCategories = await this.findRootCategories();
        for (const category of rootCategories) {
            await this.loadCategoryTree(category);
        }
        return rootCategories;
    }
    async loadCategoryTree(category) {
        const children = await this.findByParent(category.id);
        category.children = children;
        for (const child of children) {
            await this.loadCategoryTree(child);
        }
    }
    async getCategoryPath(categoryId) {
        const category = await this.findOne(categoryId);
        const path = [category.name];
        let current = category;
        while (current.parent) {
            current = await this.findOne(current.parent.id);
            path.unshift(current.name);
        }
        return path;
    }
    async getCategoryStatistics() {
        const totalCategories = await this.categoryRepository.count();
        const rootCategories = await this.categoryRepository.count({ where: { parentId: null } });
        const categoriesWithProducts = await this.categoryRepository
            .createQueryBuilder('category')
            .leftJoin('category.products', 'products')
            .where('products.id IS NOT NULL')
            .getCount();
        return {
            totalCategories,
            rootCategories,
            subcategories: totalCategories - rootCategories,
            categoriesWithProducts,
            emptyCategoriesCount: totalCategories - categoriesWithProducts,
        };
    }
    async searchCategories(searchTerm) {
        return this.categoryRepository
            .createQueryBuilder('category')
            .leftJoinAndSelect('category.parent', 'parent')
            .leftJoinAndSelect('category.children', 'children')
            .where('category.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('category.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('category.name', 'ASC')
            .getMany();
    }
    async moveCategory(categoryId, newParentId) {
        const category = await this.findOne(categoryId);
        if (newParentId) {
            const newParent = await this.findOne(newParentId);
            if (await this.isDescendant(newParent, category)) {
                throw new Error('Cannot move category to its own descendant');
            }
        }
        category.parentId = newParentId;
        await this.categoryRepository.save(category);
        return this.findOne(categoryId);
    }
    async isDescendant(potentialDescendant, ancestor) {
        if (potentialDescendant.id === ancestor.id) {
            return true;
        }
        if (!potentialDescendant.parent) {
            return false;
        }
        const parent = await this.findOne(potentialDescendant.parent.id);
        return this.isDescendant(parent, ancestor);
    }
    async generateCategoryCode(name) {
        const baseCode = name.substring(0, 3).toUpperCase();
        const count = await this.categoryRepository.count();
        const sequence = (count + 1).toString().padStart(3, '0');
        return `${baseCode}${sequence}`;
    }
    async getProductCountByCategory() {
        const result = await this.categoryRepository
            .createQueryBuilder('category')
            .leftJoin('category.products', 'products')
            .select([
            'category.id as categoryId',
            'category.name as categoryName',
            'COUNT(products.id) as productCount',
        ])
            .groupBy('category.id, category.name')
            .orderBy('productCount', 'DESC')
            .getRawMany();
        return result.map(row => ({
            categoryId: row.categoryId,
            categoryName: row.categoryName,
            productCount: parseInt(row.productCount),
        }));
    }
};
exports.CategoryService = CategoryService;
exports.CategoryService = CategoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CategoryService);
//# sourceMappingURL=category.service.js.map