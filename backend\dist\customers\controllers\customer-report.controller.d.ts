import { CustomerReportService } from '../services/customer-report.service';
export declare class CustomerReportController {
    private readonly customerReportService;
    constructor(customerReportService: CustomerReportService);
    getCustomerSummaryReport(): Promise<any>;
    getCustomerSegmentReport(): Promise<any>;
    getInteractionReport(startDate: string, endDate: string): Promise<any>;
    getCustomerLifecycleReport(): Promise<any>;
    getTopCustomersReport(limit?: string): Promise<any>;
    getCustomerHealthReport(): Promise<any>;
    exportCustomerData(format?: 'csv' | 'excel'): Promise<any>;
}
