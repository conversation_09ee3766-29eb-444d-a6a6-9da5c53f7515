/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/engine/dataset_stub" />
/**
 * Stub interfaces and classes for testing tf.LayersModel.fitDataset().
 *
 * TODO(cais, soergel): Remove this in favor of actual interfaces and classes
 *   when ready.
 */
export declare abstract class LazyIterator<T> {
    abstract next(): Promise<IteratorResult<T>>;
}
export declare abstract class Dataset<T> {
    abstract iterator(): Promise<LazyIterator<T>>;
    size: number;
}
