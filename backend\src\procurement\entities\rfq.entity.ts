import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { RFQResponse } from './rfq-response.entity';

export enum RFQStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  RESPONSES_RECEIVED = 'responses_received',
  EVALUATION_IN_PROGRESS = 'evaluation_in_progress',
  AWARDED = 'awarded',
  CANCELLED = 'cancelled',
  CLOSED = 'closed',
}

@Entity('rfqs')
export class RFQ {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  rfqNumber: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: RFQStatus,
    default: RFQStatus.DRAFT,
  })
  status: RFQStatus;

  @Column({ type: 'date' })
  publishDate: Date;

  @Column({ type: 'date' })
  responseDeadline: Date;

  @Column({ type: 'date', nullable: true })
  awardDate: Date;

  @Column({ type: 'json' })
  requirements: any;

  @Column({ type: 'json', nullable: true })
  evaluationCriteria: any;

  @Column({ type: 'json', nullable: true })
  invitedVendors: string[];

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column()
  createdBy: string;

  @Column({ nullable: true })
  awardedTo: string;

  @Column({ type: 'text', nullable: true })
  awardReason: string;

  @OneToMany(() => RFQResponse, response => response.rfq)
  responses: RFQResponse[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
