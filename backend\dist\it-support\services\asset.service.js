"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
const asset_assignment_entity_1 = require("../entities/asset-assignment.entity");
let AssetService = class AssetService {
    assetRepository;
    assetAssignmentRepository;
    constructor(assetRepository, assetAssignmentRepository) {
        this.assetRepository = assetRepository;
        this.assetAssignmentRepository = assetAssignmentRepository;
    }
    async create(assetData) {
        const assetTag = await this.generateAssetTag();
        const asset = this.assetRepository.create({
            ...assetData,
            assetTag,
            status: asset_entity_1.AssetStatus.AVAILABLE,
            createdAt: new Date(),
        });
        return this.assetRepository.save(asset);
    }
    async findAll() {
        return this.assetRepository.find({
            relations: ['currentAssignment', 'currentAssignment.assignedTo'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const asset = await this.assetRepository.findOne({
            where: { id },
            relations: ['currentAssignment', 'currentAssignment.assignedTo', 'assignments'],
        });
        if (!asset) {
            throw new common_1.NotFoundException(`Asset with ID ${id} not found`);
        }
        return asset;
    }
    async update(id, updateData) {
        await this.assetRepository.update(id, {
            ...updateData,
            updatedAt: new Date(),
        });
        return this.findOne(id);
    }
    async remove(id) {
        const asset = await this.findOne(id);
        if (asset.currentAssignment) {
            throw new Error('Cannot delete asset that is currently assigned');
        }
        await this.assetRepository.remove(asset);
    }
    async findByStatus(status) {
        return this.assetRepository.find({
            where: { status },
            relations: ['currentAssignment', 'currentAssignment.assignedTo'],
            order: { name: 'ASC' },
        });
    }
    async findByCategory(category) {
        return this.assetRepository.find({
            where: { category },
            relations: ['currentAssignment', 'currentAssignment.assignedTo'],
            order: { name: 'ASC' },
        });
    }
    async findByLocation(location) {
        return this.assetRepository.find({
            where: { location },
            relations: ['currentAssignment', 'currentAssignment.assignedTo'],
            order: { name: 'ASC' },
        });
    }
    async findByAssetTag(assetTag) {
        const asset = await this.assetRepository.findOne({
            where: { assetTag },
            relations: ['currentAssignment', 'currentAssignment.assignedTo'],
        });
        if (!asset) {
            throw new common_1.NotFoundException(`Asset with tag ${assetTag} not found`);
        }
        return asset;
    }
    async assignAsset(assetId, assignedToId, assignedById, notes) {
        const asset = await this.findOne(assetId);
        if (asset.status !== asset_entity_1.AssetStatus.AVAILABLE) {
            throw new Error('Asset is not available for assignment');
        }
        if (asset.currentAssignment) {
            await this.unassignAsset(assetId, assignedById, 'Reassigned to new user');
        }
        const assignment = this.assetAssignmentRepository.create({
            assetId,
            assignedToId,
            assignedById,
            assignedAt: new Date(),
            notes,
        });
        const savedAssignment = await this.assetAssignmentRepository.save(assignment);
        await this.assetRepository.update(assetId, {
            status: asset_entity_1.AssetStatus.ASSIGNED,
            currentAssignmentId: savedAssignment.id,
        });
        return savedAssignment;
    }
    async unassignAsset(assetId, unassignedById, notes) {
        const asset = await this.findOne(assetId);
        if (!asset.currentAssignment) {
            throw new Error('Asset is not currently assigned');
        }
        await this.assetAssignmentRepository.update(asset.currentAssignment.id, {
            unassignedAt: new Date(),
            unassignedById,
            unassignmentNotes: notes,
        });
        await this.assetRepository.update(assetId, {
            status: asset_entity_1.AssetStatus.AVAILABLE,
            currentAssignmentId: null,
        });
    }
    async getAssetHistory(assetId) {
        return this.assetAssignmentRepository.find({
            where: { assetId },
            relations: ['assignedTo', 'assignedBy', 'unassignedBy'],
            order: { assignedAt: 'DESC' },
        });
    }
    async getUserAssets(userId) {
        const assignments = await this.assetAssignmentRepository.find({
            where: { assignedToId: userId, unassignedAt: null },
            relations: ['asset'],
        });
        return assignments.map(assignment => assignment.asset);
    }
    async searchAssets(searchTerm) {
        return this.assetRepository
            .createQueryBuilder('asset')
            .leftJoinAndSelect('asset.currentAssignment', 'assignment')
            .leftJoinAndSelect('assignment.assignedTo', 'user')
            .where('asset.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('asset.assetTag ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('asset.serialNumber ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('asset.model ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('asset.name', 'ASC')
            .getMany();
    }
    async getAssetStatistics() {
        const totalAssets = await this.assetRepository.count();
        const availableAssets = await this.assetRepository.count({ where: { status: asset_entity_1.AssetStatus.AVAILABLE } });
        const assignedAssets = await this.assetRepository.count({ where: { status: asset_entity_1.AssetStatus.ASSIGNED } });
        const maintenanceAssets = await this.assetRepository.count({ where: { status: asset_entity_1.AssetStatus.MAINTENANCE } });
        const retiredAssets = await this.assetRepository.count({ where: { status: asset_entity_1.AssetStatus.RETIRED } });
        const totalValue = await this.assetRepository
            .createQueryBuilder('asset')
            .select('SUM(asset.purchasePrice)', 'totalValue')
            .getRawOne();
        return {
            totalAssets,
            availableAssets,
            assignedAssets,
            maintenanceAssets,
            retiredAssets,
            totalValue: parseFloat(totalValue.totalValue) || 0,
            utilizationRate: totalAssets > 0 ? (assignedAssets / totalAssets) * 100 : 0,
        };
    }
    async getAssetsByCategory() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select([
            'asset.category as category',
            'COUNT(asset.id) as count',
            'SUM(asset.purchasePrice) as value',
        ])
            .groupBy('asset.category')
            .orderBy('count', 'DESC')
            .getRawMany();
        return result.map(row => ({
            category: row.category,
            count: parseInt(row.count),
            value: parseFloat(row.value) || 0,
        }));
    }
    async getAssetsNearingWarrantyExpiry(days = 30) {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + days);
        return this.assetRepository
            .createQueryBuilder('asset')
            .leftJoinAndSelect('asset.currentAssignment', 'assignment')
            .leftJoinAndSelect('assignment.assignedTo', 'user')
            .where('asset.warrantyExpiry <= :futureDate', { futureDate })
            .andWhere('asset.warrantyExpiry > :now', { now: new Date() })
            .orderBy('asset.warrantyExpiry', 'ASC')
            .getMany();
    }
    async updateAssetStatus(assetId, status, notes) {
        await this.assetRepository.update(assetId, {
            status,
            ...(notes && { notes }),
            updatedAt: new Date(),
        });
        return this.findOne(assetId);
    }
    async retireAsset(assetId, retiredById, reason) {
        const asset = await this.findOne(assetId);
        if (asset.currentAssignment) {
            await this.unassignAsset(assetId, retiredById, 'Asset retired');
        }
        await this.assetRepository.update(assetId, {
            status: asset_entity_1.AssetStatus.RETIRED,
            retiredAt: new Date(),
            retirementReason: reason,
            updatedAt: new Date(),
        });
        return this.findOne(assetId);
    }
    async generateAssetTag() {
        const count = await this.assetRepository.count();
        const sequence = (count + 1).toString().padStart(6, '0');
        const year = new Date().getFullYear();
        return `AST-${year}-${sequence}`;
    }
    async getDashboardMetrics() {
        const stats = await this.getAssetStatistics();
        const nearingExpiry = await this.getAssetsNearingWarrantyExpiry();
        return {
            ...stats,
            warrantyExpiringCount: nearingExpiry.length,
        };
    }
};
exports.AssetService = AssetService;
exports.AssetService = AssetService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(asset_assignment_entity_1.AssetAssignment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AssetService);
//# sourceMappingURL=asset.service.js.map