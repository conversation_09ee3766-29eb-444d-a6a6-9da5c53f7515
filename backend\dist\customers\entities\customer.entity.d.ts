import { CustomerContact } from './customer-contact.entity';
import { CustomerAddress } from './customer-address.entity';
import { CustomerGroup } from './customer-group.entity';
import { CustomerNote } from './customer-note.entity';
import { CustomerDocument } from './customer-document.entity';
import { CustomerInteraction } from './customer-interaction.entity';
import { CustomerLoyalty } from './customer-loyalty.entity';
import { CustomerCredit } from './customer-credit.entity';
import { CustomerPreference } from './customer-preference.entity';
export declare enum CustomerType {
    INDIVIDUAL = "individual",
    BUSINESS = "business",
    GOVERNMENT = "government",
    NON_PROFIT = "non_profit"
}
export declare enum CustomerStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    BLACKLISTED = "blacklisted",
    PROSPECT = "prospect",
    LEAD = "lead"
}
export declare enum CustomerTier {
    BRONZE = "bronze",
    SILVER = "silver",
    GOLD = "gold",
    PLATINUM = "platinum",
    DIAMOND = "diamond"
}
export declare class Customer {
    id: string;
    customerNumber: string;
    type: CustomerType;
    status: CustomerStatus;
    tier: CustomerTier;
    firstName: string;
    lastName: string;
    middleName: string;
    title: string;
    dateOfBirth: Date;
    gender: string;
    companyName: string;
    legalName: string;
    taxId: string;
    registrationNumber: string;
    industry: string;
    employeeCount: number;
    annualRevenue: number;
    primaryEmail: string;
    primaryPhone: string;
    website: string;
    creditLimit: number;
    currentBalance: number;
    totalSpent: number;
    averageOrderValue: number;
    paymentTerms: number;
    currency: string;
    groupId: string;
    group: CustomerGroup;
    assignedTo: string;
    referredBy: string;
    firstPurchaseDate: Date;
    lastPurchaseDate: Date;
    lastContactDate: Date;
    allowMarketing: boolean;
    allowEmail: boolean;
    allowSms: boolean;
    allowPhone: boolean;
    communicationPreferences: any;
    interests: string[];
    tags: string[];
    loyaltyPoints: number;
    storeCredit: number;
    discountPercentage: number;
    facebookProfile: string;
    twitterProfile: string;
    linkedinProfile: string;
    instagramProfile: string;
    description: string;
    profilePicture: string;
    customFields: any;
    contacts: CustomerContact[];
    addresses: CustomerAddress[];
    notes: CustomerNote[];
    documents: CustomerDocument[];
    interactions: CustomerInteraction[];
    loyaltyHistory: CustomerLoyalty[];
    creditHistory: CustomerCredit[];
    preferences: CustomerPreference[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
