"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const warehouse_entity_1 = require("../entities/warehouse.entity");
const location_entity_1 = require("../entities/location.entity");
const stock_entity_1 = require("../entities/stock.entity");
let WarehouseService = class WarehouseService {
    warehouseRepository;
    locationRepository;
    stockRepository;
    constructor(warehouseRepository, locationRepository, stockRepository) {
        this.warehouseRepository = warehouseRepository;
        this.locationRepository = locationRepository;
        this.stockRepository = stockRepository;
    }
    async create(warehouseData) {
        const warehouse = this.warehouseRepository.create(warehouseData);
        return this.warehouseRepository.save(warehouse);
    }
    async findAll() {
        return this.warehouseRepository.find({
            relations: ['locations', 'stocks', 'stocks.product'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const warehouse = await this.warehouseRepository.findOne({
            where: { id },
            relations: ['locations', 'stocks', 'stocks.product'],
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID ${id} not found`);
        }
        return warehouse;
    }
    async update(id, updateData) {
        await this.warehouseRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const warehouse = await this.findOne(id);
        if (warehouse.stocks && warehouse.stocks.length > 0) {
            const hasStock = warehouse.stocks.some(stock => stock.quantity > 0);
            if (hasStock) {
                throw new Error('Cannot delete warehouse with existing stock');
            }
        }
        await this.warehouseRepository.remove(warehouse);
    }
    async findByCode(code) {
        const warehouse = await this.warehouseRepository.findOne({
            where: { code },
            relations: ['locations', 'stocks'],
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with code ${code} not found`);
        }
        return warehouse;
    }
    async getActiveWarehouses() {
        return this.warehouseRepository.find({
            where: { isActive: true },
            relations: ['locations'],
            order: { name: 'ASC' },
        });
    }
    async createLocation(warehouseId, locationData) {
        const warehouse = await this.findOne(warehouseId);
        const location = this.locationRepository.create({
            ...locationData,
            warehouseId: warehouse.id,
        });
        return this.locationRepository.save(location);
    }
    async getWarehouseLocations(warehouseId) {
        return this.locationRepository.find({
            where: { warehouseId },
            order: { zone: 'ASC', aisle: 'ASC', shelf: 'ASC' },
        });
    }
    async getWarehouseStock(warehouseId) {
        return this.stockRepository.find({
            where: { warehouseId },
            relations: ['product', 'product.category'],
            order: { 'product.name': 'ASC' },
        });
    }
    async getWarehouseStockValue(warehouseId) {
        const result = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoin('stock.product', 'product')
            .where('stock.warehouseId = :warehouseId', { warehouseId })
            .select('SUM(stock.quantity * product.costPrice)', 'totalValue')
            .getRawOne();
        return parseFloat(result.totalValue) || 0;
    }
    async getWarehouseCapacityUtilization(warehouseId) {
        const warehouse = await this.findOne(warehouseId);
        const totalStock = warehouse.stocks.reduce((sum, stock) => sum + stock.quantity, 0);
        const utilizationPercentage = warehouse.capacity > 0
            ? (totalStock / warehouse.capacity) * 100
            : 0;
        return {
            warehouseId: warehouse.id,
            warehouseName: warehouse.name,
            capacity: warehouse.capacity,
            currentStock: totalStock,
            availableCapacity: Math.max(0, warehouse.capacity - totalStock),
            utilizationPercentage: Math.round(utilizationPercentage * 100) / 100,
        };
    }
    async getWarehouseStatistics() {
        const totalWarehouses = await this.warehouseRepository.count();
        const activeWarehouses = await this.warehouseRepository.count({ where: { isActive: true } });
        const totalCapacity = await this.warehouseRepository
            .createQueryBuilder('warehouse')
            .select('SUM(warehouse.capacity)', 'totalCapacity')
            .getRawOne();
        const totalStock = await this.stockRepository
            .createQueryBuilder('stock')
            .select('SUM(stock.quantity)', 'totalStock')
            .getRawOne();
        const totalValue = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoin('stock.product', 'product')
            .select('SUM(stock.quantity * product.costPrice)', 'totalValue')
            .getRawOne();
        return {
            totalWarehouses,
            activeWarehouses,
            inactiveWarehouses: totalWarehouses - activeWarehouses,
            totalCapacity: parseInt(totalCapacity.totalCapacity) || 0,
            totalStock: parseInt(totalStock.totalStock) || 0,
            totalStockValue: parseFloat(totalValue.totalValue) || 0,
            averageUtilization: totalCapacity.totalCapacity > 0
                ? ((totalStock.totalStock / totalCapacity.totalCapacity) * 100)
                : 0,
        };
    }
    async searchWarehouses(searchTerm) {
        return this.warehouseRepository
            .createQueryBuilder('warehouse')
            .where('warehouse.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('warehouse.code ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('warehouse.address ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('warehouse.name', 'ASC')
            .getMany();
    }
    async getWarehousesByRegion(region) {
        return this.warehouseRepository.find({
            where: { region },
            relations: ['locations'],
            order: { name: 'ASC' },
        });
    }
    async transferStock(fromWarehouseId, toWarehouseId, productId, quantity) {
        const fromStock = await this.stockRepository.findOne({
            where: { warehouseId: fromWarehouseId, productId },
        });
        if (!fromStock || fromStock.availableQuantity < quantity) {
            return {
                success: false,
                message: 'Insufficient stock in source warehouse',
            };
        }
        fromStock.quantity -= quantity;
        fromStock.availableQuantity -= quantity;
        await this.stockRepository.save(fromStock);
        let toStock = await this.stockRepository.findOne({
            where: { warehouseId: toWarehouseId, productId },
        });
        if (toStock) {
            toStock.quantity += quantity;
            toStock.availableQuantity += quantity;
        }
        else {
            toStock = this.stockRepository.create({
                warehouseId: toWarehouseId,
                productId,
                quantity,
                reservedQuantity: 0,
                availableQuantity: quantity,
            });
        }
        await this.stockRepository.save(toStock);
        return {
            success: true,
            message: `Successfully transferred ${quantity} units`,
        };
    }
    async generateWarehouseCode(name) {
        const baseCode = name.substring(0, 3).toUpperCase();
        const count = await this.warehouseRepository.count();
        const sequence = (count + 1).toString().padStart(3, '0');
        return `WH-${baseCode}${sequence}`;
    }
};
exports.WarehouseService = WarehouseService;
exports.WarehouseService = WarehouseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(warehouse_entity_1.Warehouse)),
    __param(1, (0, typeorm_1.InjectRepository)(location_entity_1.Location)),
    __param(2, (0, typeorm_1.InjectRepository)(stock_entity_1.Stock)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], WarehouseService);
//# sourceMappingURL=warehouse.service.js.map