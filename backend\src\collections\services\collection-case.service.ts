import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CollectionCase, CaseStatus } from '../entities/collection-case.entity';
import { CollectionActivity } from '../entities/collection-activity.entity';

@Injectable()
export class CollectionCaseService {
  constructor(
    @InjectRepository(CollectionCase)
    private collectionCaseRepository: Repository<CollectionCase>,
    @InjectRepository(CollectionActivity)
    private collectionActivityRepository: Repository<CollectionActivity>,
  ) {}

  async create(caseData: Partial<CollectionCase>): Promise<CollectionCase> {
    const collectionCase = this.collectionCaseRepository.create(caseData);
    return this.collectionCaseRepository.save(collectionCase);
  }

  async findAll(): Promise<CollectionCase[]> {
    return this.collectionCaseRepository.find({
      relations: ['customer', 'activities'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<CollectionCase> {
    const collectionCase = await this.collectionCaseRepository.findOne({
      where: { id },
      relations: ['customer', 'activities'],
    });

    if (!collectionCase) {
      throw new NotFoundException(`Collection case with ID ${id} not found`);
    }

    return collectionCase;
  }

  async update(id: string, updateData: Partial<CollectionCase>): Promise<CollectionCase> {
    await this.collectionCaseRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const collectionCase = await this.findOne(id);
    await this.collectionCaseRepository.remove(collectionCase);
  }

  async findByStatus(status: CaseStatus): Promise<CollectionCase[]> {
    return this.collectionCaseRepository.find({
      where: { status },
      relations: ['customer'],
    });
  }

  async findByCustomer(customerId: string): Promise<CollectionCase[]> {
    return this.collectionCaseRepository.find({
      where: { customerId },
      relations: ['activities'],
      order: { createdAt: 'DESC' },
    });
  }

  async updateStatus(id: string, status: CaseStatus, notes?: string): Promise<CollectionCase> {
    const updateData: any = { status };
    if (notes) {
      // Log status change as activity
      await this.collectionActivityRepository.save({
        caseId: id,
        type: 'status_change',
        description: `Status changed to ${status}: ${notes}`,
        activityDate: new Date(),
      });
    }
    
    await this.collectionCaseRepository.update(id, updateData);
    return this.findOne(id);
  }

  async assignAgent(caseId: string, agentId: string): Promise<CollectionCase> {
    await this.collectionCaseRepository.update(caseId, { assignedTo: agentId });
    
    // Log assignment as activity
    await this.collectionActivityRepository.save({
      caseId,
      type: 'assignment',
      description: `Case assigned to agent ${agentId}`,
      activityDate: new Date(),
    });
    
    return this.findOne(caseId);
  }

  async getStatistics(): Promise<any> {
    const total = await this.collectionCaseRepository.count();
    const active = await this.collectionCaseRepository.count({ 
      where: { status: CaseStatus.ACTIVE } 
    });
    const resolved = await this.collectionCaseRepository.count({ 
      where: { status: CaseStatus.RESOLVED } 
    });
    const pending = await this.collectionCaseRepository.count({ 
      where: { status: CaseStatus.PENDING } 
    });

    // Calculate total debt and collected amounts
    const financialData = await this.collectionCaseRepository
      .createQueryBuilder('case')
      .select([
        'SUM(case.originalAmount) as totalDebt',
        'SUM(case.collectedAmount) as totalCollected',
        'AVG(case.collectedAmount / case.originalAmount * 100) as avgCollectionRate',
      ])
      .getRawOne();

    return {
      total,
      active,
      resolved,
      pending,
      totalDebt: parseFloat(financialData.totalDebt) || 0,
      totalCollected: parseFloat(financialData.totalCollected) || 0,
      collectionRate: parseFloat(financialData.avgCollectionRate) || 0,
      resolutionRate: total > 0 ? (resolved / total) * 100 : 0,
    };
  }

  async getCasesByPriority(priority: string): Promise<CollectionCase[]> {
    return this.collectionCaseRepository.find({
      where: { priority },
      relations: ['customer'],
      order: { createdAt: 'DESC' },
    });
  }

  async getOverdueCases(): Promise<CollectionCase[]> {
    const today = new Date();
    return this.collectionCaseRepository
      .createQueryBuilder('case')
      .where('case.dueDate < :today', { today })
      .andWhere('case.status != :resolved', { resolved: CaseStatus.RESOLVED })
      .getMany();
  }

  async escalateCase(id: string, reason: string): Promise<CollectionCase> {
    const collectionCase = await this.findOne(id);
    
    // Update priority and escalation level
    await this.collectionCaseRepository.update(id, {
      priority: 'high',
      escalationLevel: (collectionCase.escalationLevel || 0) + 1,
    });

    // Log escalation activity
    await this.collectionActivityRepository.save({
      caseId: id,
      type: 'escalation',
      description: `Case escalated: ${reason}`,
      activityDate: new Date(),
    });

    return this.findOne(id);
  }

  async recordPayment(id: string, amount: number, paymentDate: Date, notes?: string): Promise<CollectionCase> {
    const collectionCase = await this.findOne(id);
    const newCollectedAmount = (collectionCase.collectedAmount || 0) + amount;
    
    await this.collectionCaseRepository.update(id, {
      collectedAmount: newCollectedAmount,
      lastPaymentDate: paymentDate,
    });

    // Log payment activity
    await this.collectionActivityRepository.save({
      caseId: id,
      type: 'payment',
      description: `Payment received: $${amount}${notes ? ` - ${notes}` : ''}`,
      activityDate: paymentDate,
    });

    // Check if case should be resolved
    if (newCollectedAmount >= collectionCase.originalAmount) {
      await this.updateStatus(id, CaseStatus.RESOLVED, 'Full payment received');
    }

    return this.findOne(id);
  }

  async getDashboardMetrics(): Promise<any> {
    const stats = await this.getStatistics();
    const overdueCases = await this.getOverdueCases();
    
    return {
      ...stats,
      overdueCount: overdueCases.length,
      urgentCases: overdueCases.filter(c => c.priority === 'high').length,
    };
  }
}
