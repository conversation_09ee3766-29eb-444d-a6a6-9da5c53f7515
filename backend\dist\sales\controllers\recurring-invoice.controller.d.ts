import { RecurringInvoiceService } from '../services/recurring-invoice.service';
export declare class RecurringInvoiceController {
    private readonly recurringInvoiceService;
    constructor(recurringInvoiceService: RecurringInvoiceService);
    create(createRecurringInvoiceDto: any, req: any): Promise<import("../entities/recurring-invoice.entity").RecurringInvoice>;
    findAll(req: any): Promise<import("../entities/recurring-invoice.entity").RecurringInvoice[]>;
    getStats(req: any): Promise<{
        totalRecurringInvoices: number;
        activeRecurringInvoices: number;
        pausedRecurringInvoices: number;
        totalValue: number;
        totalGenerated: number;
        frequencyStats: any[];
    }>;
    findOne(id: string, req: any): Promise<import("../entities/recurring-invoice.entity").RecurringInvoice>;
    update(id: string, updateRecurringInvoiceDto: any, req: any): Promise<import("../entities/recurring-invoice.entity").RecurringInvoice>;
    remove(id: string, req: any): Promise<void>;
    updateStatus(id: string, body: {
        status: string;
    }, req: any): Promise<import("../entities/recurring-invoice.entity").RecurringInvoice>;
    generateInvoice(id: string, req: any): Promise<{
        success: boolean;
        invoiceId?: string;
    }>;
}
