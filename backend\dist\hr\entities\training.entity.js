"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Training = exports.TrainingDeliveryMethod = exports.TrainingStatus = exports.TrainingType = void 0;
const typeorm_1 = require("typeorm");
const employee_entity_1 = require("./employee.entity");
var TrainingType;
(function (TrainingType) {
    TrainingType["ORIENTATION"] = "orientation";
    TrainingType["SKILL_DEVELOPMENT"] = "skill_development";
    TrainingType["COMPLIANCE"] = "compliance";
    TrainingType["LEADERSHIP"] = "leadership";
    TrainingType["TECHNICAL"] = "technical";
    TrainingType["SAFETY"] = "safety";
    TrainingType["CERTIFICATION"] = "certification";
    TrainingType["WORKSHOP"] = "workshop";
    TrainingType["SEMINAR"] = "seminar";
    TrainingType["CONFERENCE"] = "conference";
})(TrainingType || (exports.TrainingType = TrainingType = {}));
var TrainingStatus;
(function (TrainingStatus) {
    TrainingStatus["SCHEDULED"] = "scheduled";
    TrainingStatus["IN_PROGRESS"] = "in_progress";
    TrainingStatus["COMPLETED"] = "completed";
    TrainingStatus["CANCELLED"] = "cancelled";
    TrainingStatus["NO_SHOW"] = "no_show";
})(TrainingStatus || (exports.TrainingStatus = TrainingStatus = {}));
var TrainingDeliveryMethod;
(function (TrainingDeliveryMethod) {
    TrainingDeliveryMethod["IN_PERSON"] = "in_person";
    TrainingDeliveryMethod["ONLINE"] = "online";
    TrainingDeliveryMethod["HYBRID"] = "hybrid";
    TrainingDeliveryMethod["SELF_PACED"] = "self_paced";
})(TrainingDeliveryMethod || (exports.TrainingDeliveryMethod = TrainingDeliveryMethod = {}));
let Training = class Training {
    id;
    employeeId;
    employee;
    title;
    description;
    type;
    status;
    deliveryMethod;
    startDate;
    endDate;
    startTime;
    endTime;
    duration;
    location;
    instructor;
    provider;
    cost;
    currency;
    isMandatory;
    hasCertification;
    certificationName;
    certificationExpiryDate;
    score;
    passingScore;
    isPassed;
    completionDate;
    feedback;
    rating;
    attachments;
    prerequisites;
    learningObjectives;
    notes;
    metadata;
    createdAt;
    updatedAt;
};
exports.Training = Training;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Training.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Training.prototype, "employeeId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => employee_entity_1.Employee, employee => employee.trainings),
    (0, typeorm_1.JoinColumn)({ name: 'employeeId' }),
    __metadata("design:type", employee_entity_1.Employee)
], Training.prototype, "employee", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], Training.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TrainingType,
    }),
    __metadata("design:type", String)
], Training.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TrainingStatus,
        default: TrainingStatus.SCHEDULED,
    }),
    __metadata("design:type", String)
], Training.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TrainingDeliveryMethod,
        default: TrainingDeliveryMethod.IN_PERSON,
    }),
    __metadata("design:type", String)
], Training.prototype, "deliveryMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Training.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Training.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'time', nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'time', nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Training.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "instructor", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Training.prototype, "cost", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], Training.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Training.prototype, "isMandatory", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Training.prototype, "hasCertification", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "certificationName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Training.prototype, "certificationExpiryDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], Training.prototype, "score", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], Training.prototype, "passingScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Training.prototype, "isPassed", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Training.prototype, "completionDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "feedback", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 2, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], Training.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Training.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Training.prototype, "prerequisites", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Training.prototype, "learningObjectives", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Training.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Training.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Training.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Training.prototype, "updatedAt", void 0);
exports.Training = Training = __decorate([
    (0, typeorm_1.Entity)('hr_training')
], Training);
//# sourceMappingURL=training.entity.js.map