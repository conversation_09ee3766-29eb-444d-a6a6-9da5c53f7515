"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const app_demo_module_1 = require("./app-demo.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_demo_module_1.AppDemoModule);
    app.enableCors({
        origin: process.env.FRONTEND_URL || 'http://localhost:5173',
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    const port = process.env.PORT || 3000;
    await app.listen(port);
    console.log(`🚀 ZaidanOne Management System Backend (DEMO MODE) running on port ${port}`);
    console.log(`📝 Note: This is demo mode with in-memory storage. Data will be lost on restart.`);
    console.log(`🔗 Frontend: http://localhost:5173`);
    console.log(`🔗 Backend: http://localhost:${port}`);
}
bootstrap();
//# sourceMappingURL=main-demo.js.map