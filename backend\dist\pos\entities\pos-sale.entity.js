"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosSale = exports.SaleType = exports.SaleStatus = void 0;
const typeorm_1 = require("typeorm");
const pos_terminal_entity_1 = require("./pos-terminal.entity");
const pos_sale_item_entity_1 = require("./pos-sale-item.entity");
const pos_payment_entity_1 = require("./pos-payment.entity");
const pos_discount_entity_1 = require("./pos-discount.entity");
const pos_tax_entity_1 = require("./pos-tax.entity");
const pos_customer_entity_1 = require("./pos-customer.entity");
var SaleStatus;
(function (SaleStatus) {
    SaleStatus["PENDING"] = "pending";
    SaleStatus["COMPLETED"] = "completed";
    SaleStatus["CANCELLED"] = "cancelled";
    SaleStatus["REFUNDED"] = "refunded";
    SaleStatus["PARTIALLY_REFUNDED"] = "partially_refunded";
    SaleStatus["ON_HOLD"] = "on_hold";
})(SaleStatus || (exports.SaleStatus = SaleStatus = {}));
var SaleType;
(function (SaleType) {
    SaleType["REGULAR"] = "regular";
    SaleType["RETURN"] = "return";
    SaleType["EXCHANGE"] = "exchange";
    SaleType["LAYAWAY"] = "layaway";
    SaleType["SPECIAL_ORDER"] = "special_order";
})(SaleType || (exports.SaleType = SaleType = {}));
let PosSale = class PosSale {
    id;
    saleNumber;
    terminalId;
    terminal;
    customerId;
    customer;
    type;
    status;
    saleDateTime;
    totalItems;
    subtotal;
    totalDiscount;
    totalTax;
    totalAmount;
    amountPaid;
    changeAmount;
    amountDue;
    currency;
    cashierId;
    shiftId;
    notes;
    customerInfo;
    isVoided;
    voidedBy;
    voidedAt;
    voidReason;
    items;
    payments;
    discounts;
    taxes;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosSale = PosSale;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosSale.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PosSale.prototype, "saleNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosSale.prototype, "terminalId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_terminal_entity_1.PosTerminal),
    (0, typeorm_1.JoinColumn)({ name: 'terminalId' }),
    __metadata("design:type", pos_terminal_entity_1.PosTerminal)
], PosSale.prototype, "terminal", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosSale.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_customer_entity_1.PosCustomer, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'customerId' }),
    __metadata("design:type", pos_customer_entity_1.PosCustomer)
], PosSale.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SaleType,
        default: SaleType.REGULAR,
    }),
    __metadata("design:type", String)
], PosSale.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SaleStatus,
        default: SaleStatus.PENDING,
    }),
    __metadata("design:type", String)
], PosSale.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], PosSale.prototype, "saleDateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "totalItems", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "subtotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "totalDiscount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "totalTax", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "amountPaid", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "changeAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosSale.prototype, "amountDue", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], PosSale.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosSale.prototype, "cashierId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosSale.prototype, "shiftId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosSale.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosSale.prototype, "customerInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosSale.prototype, "isVoided", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosSale.prototype, "voidedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosSale.prototype, "voidedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosSale.prototype, "voidReason", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_sale_item_entity_1.PosSaleItem, item => item.sale, { cascade: true }),
    __metadata("design:type", Array)
], PosSale.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_payment_entity_1.PosPayment, payment => payment.sale, { cascade: true }),
    __metadata("design:type", Array)
], PosSale.prototype, "payments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_discount_entity_1.PosDiscount, discount => discount.sale, { cascade: true }),
    __metadata("design:type", Array)
], PosSale.prototype, "discounts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_tax_entity_1.PosTax, tax => tax.sale, { cascade: true }),
    __metadata("design:type", Array)
], PosSale.prototype, "taxes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosSale.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosSale.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosSale.prototype, "updatedAt", void 0);
exports.PosSale = PosSale = __decorate([
    (0, typeorm_1.Entity)('pos_sales')
], PosSale);
//# sourceMappingURL=pos-sale.entity.js.map