import { Budget } from './budget.entity';
import { Account } from './account.entity';
export declare enum BudgetItemType {
    REVENUE = "revenue",
    EXPENSE = "expense",
    CAPITAL = "capital"
}
export declare enum BudgetPeriod {
    MONTHLY = "monthly",
    QUARTERLY = "quarterly",
    ANNUALLY = "annually"
}
export declare class BudgetItem {
    id: string;
    budgetId: string;
    budget: Budget;
    accountId: string;
    account: Account;
    itemName: string;
    description: string;
    type: BudgetItemType;
    period: BudgetPeriod;
    budgetedAmount: number;
    actualAmount: number;
    variance: number;
    variancePercentage: number;
    encumberedAmount: number;
    availableAmount: number;
    monthlyBreakdown: any;
    notes: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
