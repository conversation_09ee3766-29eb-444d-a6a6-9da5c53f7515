import { Customer } from './customer.entity';
export declare enum LoyaltyTransactionType {
    EARNED = "earned",
    REDEEMED = "redeemed",
    EXPIRED = "expired",
    ADJUSTED = "adjusted",
    BONUS = "bonus",
    PENALTY = "penalty"
}
export declare class CustomerLoyalty {
    id: string;
    customerId: string;
    customer: Customer;
    type: LoyaltyTransactionType;
    points: number;
    balanceBefore: number;
    balanceAfter: number;
    description: string;
    reference: string;
    relatedEntityType: string;
    relatedEntityId: string;
    expiryDate: Date;
    processedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
