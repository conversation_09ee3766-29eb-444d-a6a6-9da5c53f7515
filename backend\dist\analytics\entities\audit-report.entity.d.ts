import { Company } from '../../company/entities/company.entity';
import { User } from '../../user/entities/user.entity';
import { AuditFinding } from './audit-finding.entity';
export declare class AuditReport {
    id: string;
    companyId: string;
    year: number;
    quarter: number;
    reportType: string;
    status: string;
    department: string;
    scope: string;
    auditCategory: string;
    regulatoryFramework: string[];
    riskLevel: string;
    auditor: string;
    auditScope: string;
    objectives: string;
    methodology: string;
    executiveSummary: string;
    complianceScore: number;
    totalFindings: number;
    criticalFindings: number;
    highFindings: number;
    mediumFindings: number;
    lowFindings: number;
    recommendations: number;
    plannedStartDate: Date;
    plannedEndDate: Date;
    actualStartDate: Date;
    actualEndDate: Date;
    completedDate: Date;
    auditTeam: {
        leadAuditor: string;
        members: string[];
        externalAuditors?: string[];
    };
    auditCriteria: {
        standards: string[];
        regulations: string[];
        policies: string[];
    };
    riskAssessment: {
        inherentRisk: string;
        controlRisk: string;
        detectionRisk: string;
        overallRisk: string;
    };
    samplingMethod: {
        type: string;
        size: number;
        criteria: string;
    };
    limitations: string;
    conclusion: string;
    managementResponse: string;
    attachments: {
        fileName: string;
        fileUrl: string;
        fileType: string;
        uploadDate: Date;
    }[];
    distributionList: {
        name: string;
        email: string;
        role: string;
    }[];
    nextAuditDate: Date;
    followUpActions: {
        action: string;
        responsible: string;
        dueDate: Date;
        status: string;
    }[];
    financialData: {
        totalIncome: number;
        totalExpenses: number;
        netResult: number;
        auditedTransactions: number;
        discrepancies: number;
        materialMisstatements: number;
        departmentBreakdown: {
            department: string;
            income: number;
            expenses: number;
            accuracy: number;
        }[];
        incomeVerification: {
            revenueRecognitionCompliant: boolean;
            invoiceMatchingVerified: boolean;
            cashReceiptsReconciled: boolean;
            timingDifferences: number;
        };
        expenseVerification: {
            purchaseOrderMatching: boolean;
            payrollCalculationsVerified: boolean;
            accrualProceduresCompliant: boolean;
            unauthorizedExpenses: number;
        };
    };
    specializedData: {
        bsaAmlData?: {
            suspiciousActivityReports: number;
            currencyTransactionReports: number;
            customerDueDiligenceReviews: number;
            sanctionsScreeningResults: number;
            complianceViolations: number;
            sarFilingTimeliness: number;
            ctrAccuracy: number;
            kycCompleteness: number;
        };
        creditData?: {
            loansReviewed: number;
            creditRiskRating: string;
            portfolioQuality: number;
            allowanceAdequacy: number;
            underwritingCompliance: number;
            concentrationRisk: number;
            creditPolicyCompliance: number;
            loanDocumentationScore: number;
        };
        itSecurityData?: {
            vulnerabilitiesFound: number;
            securityIncidents: number;
            complianceGaps: number;
            penetrationTestResults: string;
            dataBreachRisk: string;
            accessControlsScore: number;
            encryptionCompliance: number;
            backupRecoveryScore: number;
        };
        operationsData?: {
            processEfficiency: number;
            controlDeficiencies: number;
            procedureCompliance: number;
            staffingAdequacy: number;
            branchOperationsScore: number;
            cashManagementScore: number;
            customerServiceScore: number;
        };
        trustData?: {
            fiduciaryCompliance: number;
            investmentPolicyAdherence: number;
            clientReportingTimeliness: number;
            regulatoryFilingCompleteness: number;
            assetSafeguarding: number;
            conflictOfInterestManagement: number;
        };
        soxData?: {
            internalControlsEffectiveness: number;
            financialReportingAccuracy: number;
            managementAssessmentScore: number;
            auditCommitteeOversight: number;
            disclosureControlsScore: number;
            fdiciaCertificationStatus: string;
        };
        almData?: {
            interestRateRiskScore: number;
            liquidityRiskScore: number;
            capitalAdequacyRatio: number;
            assetQualityScore: number;
            earningsStability: number;
            managementQuality: number;
        };
    };
    createdBy: string;
    updatedBy: string;
    createdDate: Date;
    updatedDate: Date;
    company: Company;
    creator: User;
    updater: User;
    findings: AuditFinding[];
}
