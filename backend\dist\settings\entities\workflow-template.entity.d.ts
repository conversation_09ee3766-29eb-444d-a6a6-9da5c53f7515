export declare enum WorkflowType {
    APPROVAL = "approval",
    NOTIFICATION = "notification",
    AUTOMATION = "automation",
    ESCALATION = "escalation",
    CUSTOM = "custom"
}
export declare enum WorkflowStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    ARCHIVED = "archived"
}
export declare class WorkflowTemplate {
    id: string;
    name: string;
    description: string;
    type: WorkflowType;
    status: WorkflowStatus;
    definition: any;
    triggers: any;
    variables: string[];
    isSystem: boolean;
    createdBy: string;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
