export declare class CreateCreditNoteItemDto {
    lineNumber: number;
    description: string;
    productCode?: string;
    unitPrice: number;
    quantity: number;
    taxType?: string;
    unit?: string;
    notes?: string;
}
export declare class CreateCreditNoteDto {
    customerId: string;
    invoiceId?: string;
    creditNoteDate: string;
    reason: string;
    type: 'return' | 'discount' | 'error' | 'other';
    notes?: string;
    items: CreateCreditNoteItemDto[];
}
