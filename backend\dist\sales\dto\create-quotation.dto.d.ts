export declare class CreateQuotationItemDto {
    lineNumber: number;
    description: string;
    productCode?: string;
    unitPrice: number;
    quantity: number;
    discount?: number;
    taxType?: string;
    unit?: string;
    notes?: string;
}
export declare class CreateQuotationDto {
    customerId: string;
    quotationDate: string;
    validUntil: string;
    salesOfficer?: string;
    discountType?: 'percentage' | 'amount';
    discountValue?: number;
    notes?: string;
    terms?: string;
    items: CreateQuotationItemDto[];
}
