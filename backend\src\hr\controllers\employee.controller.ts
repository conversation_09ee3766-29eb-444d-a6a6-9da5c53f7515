import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { EmployeeService } from '../services/employee.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/employees')
@UseGuards(JwtAuthGuard)
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Post()
  create(@Body() createEmployeeDto: any) {
    return this.employeeService.create(createEmployeeDto);
  }

  @Get()
  findAll(
    @Query('status') status?: string,
    @Query('departmentId') departmentId?: string,
    @Query('positionId') positionId?: string,
    @Query('managerId') managerId?: string,
    @Query('search') search?: string
  ) {
    const filters: any = {};
    if (status) filters.status = status;
    if (departmentId) filters.departmentId = departmentId;
    if (positionId) filters.positionId = positionId;
    if (managerId) filters.managerId = managerId;
    if (search) filters.search = search;

    return this.employeeService.findAll(filters);
  }

  @Get('stats')
  getEmployeeStats() {
    return this.employeeService.getEmployeeStats();
  }

  @Get('hierarchy')
  getEmployeeHierarchy(@Query('managerId') managerId?: string) {
    return this.employeeService.getEmployeeHierarchy(managerId);
  }

  @Get('search')
  searchEmployees(@Query('q') searchTerm: string) {
    return this.employeeService.searchEmployees(searchTerm);
  }

  @Get('employee-number/:employeeNumber')
  findByEmployeeNumber(@Param('employeeNumber') employeeNumber: string) {
    return this.employeeService.findByEmployeeNumber(employeeNumber);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.employeeService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateEmployeeDto: any) {
    return this.employeeService.update(id, updateEmployeeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.employeeService.remove(id);
  }

  @Post(':id/terminate')
  terminate(
    @Param('id') id: string,
    @Body() terminateDto: { terminationDate: string; reason?: string }
  ) {
    return this.employeeService.terminate(
      id,
      new Date(terminateDto.terminationDate),
      terminateDto.reason
    );
  }

  @Post(':id/reactivate')
  reactivate(@Param('id') id: string) {
    return this.employeeService.reactivate(id);
  }
}
