import { InventoryReportService } from '../services/inventory-report.service';
export declare class InventoryReportController {
    private readonly inventoryReportService;
    constructor(inventoryReportService: InventoryReportService);
    generateInventoryValuationReport(): Promise<any>;
    generateStockLevelReport(): Promise<any>;
    generateMovementReport(startDate: string, endDate: string): Promise<any>;
    generatePurchaseOrderReport(startDate: string, endDate: string): Promise<any>;
    generateABCAnalysisReport(): Promise<any>;
}
