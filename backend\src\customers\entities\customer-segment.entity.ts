import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum SegmentType {
  DEMOGRAPHIC = 'demographic',
  BEHAVIORAL = 'behavioral',
  GEOGRAPHIC = 'geographic',
  PSYCHOGRAPHIC = 'psychographic',
  TRANSACTIONAL = 'transactional',
  CUSTOM = 'custom',
}

@Entity('customer_segments')
export class CustomerSegment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SegmentType,
    default: SegmentType.CUSTOM,
  })
  type: SegmentType;

  @Column({ type: 'json' })
  criteria: any; // Segmentation rules and conditions

  @Column({ type: 'int', default: 0 })
  customerCount: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isAutoUpdate: boolean; // Automatically update based on criteria

  @Column({ type: 'timestamp', nullable: true })
  lastUpdated: Date;

  @Column()
  createdBy: string;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
