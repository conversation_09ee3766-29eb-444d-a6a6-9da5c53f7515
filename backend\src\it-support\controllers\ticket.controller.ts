import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { TicketService } from '../services/ticket.service';
import { Ticket, TicketStatus, TicketPriority } from '../entities/ticket.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('tickets')
@UseGuards(JwtAuthGuard)
export class TicketController {
  constructor(private readonly ticketService: TicketService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createTicketDto: Partial<Ticket>) {
    return this.ticketService.create(createTicketDto);
  }

  @Get()
  async findAll(
    @Query('status') status?: TicketStatus,
    @Query('priority') priority?: TicketPriority,
    @Query('assignee') assigneeId?: string,
    @Query('creator') creatorId?: string,
  ) {
    if (status) {
      return this.ticketService.findByStatus(status);
    }
    if (priority) {
      return this.ticketService.findByPriority(priority);
    }
    if (assigneeId) {
      return this.ticketService.findByAssignee(assigneeId);
    }
    if (creatorId) {
      return this.ticketService.findByCreator(creatorId);
    }
    return this.ticketService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.ticketService.getTicketStatistics();
  }

  @Get('dashboard')
  async getDashboardMetrics() {
    return this.ticketService.getDashboardMetrics();
  }

  @Get('overdue')
  async getOverdueTickets() {
    return this.ticketService.getOverdueTickets();
  }

  @Get('search')
  async searchTickets(@Query('q') searchTerm: string) {
    return this.ticketService.searchTickets(searchTerm);
  }

  @Get('category/:category')
  async getTicketsByCategory(@Param('category') category: string) {
    return this.ticketService.getTicketsByCategory(category);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.ticketService.findOne(id);
  }

  @Get(':id/comments')
  async getTicketComments(@Param('id') id: string) {
    return this.ticketService.getTicketComments(id);
  }

  @Post(':id/comments')
  async addComment(
    @Param('id') id: string,
    @Body() commentData: { content: string; authorId: string },
  ) {
    return this.ticketService.addComment(id, commentData.content, commentData.authorId);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTicketDto: Partial<Ticket>,
  ) {
    return this.ticketService.update(id, updateTicketDto);
  }

  @Patch(':id/assign')
  async assignTicket(
    @Param('id') id: string,
    @Body() assignmentData: { assigneeId: string },
  ) {
    return this.ticketService.assignTicket(id, assignmentData.assigneeId);
  }

  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body() statusData: { status: TicketStatus; userId?: string },
  ) {
    return this.ticketService.updateStatus(id, statusData.status, statusData.userId);
  }

  @Patch(':id/priority')
  async updatePriority(
    @Param('id') id: string,
    @Body() priorityData: { priority: TicketPriority; userId?: string },
  ) {
    return this.ticketService.updatePriority(id, priorityData.priority, priorityData.userId);
  }

  @Post(':id/escalate')
  async escalateTicket(
    @Param('id') id: string,
    @Body() escalationData: { reason: string; userId?: string },
  ) {
    return this.ticketService.escalateTicket(id, escalationData.reason, escalationData.userId);
  }

  @Post(':id/close')
  async closeTicket(
    @Param('id') id: string,
    @Body() closeData: { resolution: string; userId?: string },
  ) {
    return this.ticketService.closeTicket(id, closeData.resolution, closeData.userId);
  }

  @Post(':id/reopen')
  async reopenTicket(
    @Param('id') id: string,
    @Body() reopenData: { reason: string; userId?: string },
  ) {
    return this.ticketService.reopenTicket(id, reopenData.reason, reopenData.userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.ticketService.remove(id);
  }
}
