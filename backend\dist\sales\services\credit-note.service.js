"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditNoteService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const credit_note_entity_1 = require("../entities/credit-note.entity");
const credit_note_item_entity_1 = require("../entities/credit-note-item.entity");
let CreditNoteService = class CreditNoteService {
    creditNoteRepository;
    creditNoteItemRepository;
    constructor(creditNoteRepository, creditNoteItemRepository) {
        this.creditNoteRepository = creditNoteRepository;
        this.creditNoteItemRepository = creditNoteItemRepository;
    }
    async create(createCreditNoteDto, tenantId) {
        const creditNoteNumber = await this.generateCreditNoteNumber();
        const totals = this.calculateCreditNoteTotals(createCreditNoteDto);
        const creditNote = this.creditNoteRepository.create({
            ...createCreditNoteDto,
            creditNoteNumber,
            tenantId,
            ...totals,
        });
        const savedCreditNote = await this.creditNoteRepository.save(creditNote);
        for (const itemDto of createCreditNoteDto.items) {
            const item = this.creditNoteItemRepository.create({
                ...itemDto,
                creditNoteId: savedCreditNote.id,
                tenantId,
                taxAmount: this.calculateItemTax(itemDto),
                lineTotal: (itemDto.unitPrice * itemDto.quantity),
            });
            await this.creditNoteItemRepository.save(item);
        }
        return this.findOne(savedCreditNote.id, tenantId);
    }
    async findAll(tenantId) {
        return this.creditNoteRepository.find({
            where: { tenantId },
            relations: ['customer', 'items', 'invoice'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id, tenantId) {
        const creditNote = await this.creditNoteRepository.findOne({
            where: { id, tenantId },
            relations: ['customer', 'items', 'invoice'],
        });
        if (!creditNote) {
            throw new common_1.NotFoundException('Credit note not found');
        }
        return creditNote;
    }
    async update(id, updateCreditNoteDto, tenantId) {
        const creditNote = await this.findOne(id, tenantId);
        if (updateCreditNoteDto.items) {
            await this.creditNoteItemRepository.delete({ creditNoteId: id });
            for (const itemDto of updateCreditNoteDto.items) {
                const item = this.creditNoteItemRepository.create({
                    ...itemDto,
                    creditNoteId: id,
                    tenantId,
                    taxAmount: this.calculateItemTax(itemDto),
                    lineTotal: (itemDto.unitPrice * itemDto.quantity),
                });
                await this.creditNoteItemRepository.save(item);
            }
            const totals = this.calculateCreditNoteTotals(updateCreditNoteDto);
            Object.assign(creditNote, totals);
        }
        Object.assign(creditNote, updateCreditNoteDto);
        return this.creditNoteRepository.save(creditNote);
    }
    async remove(id, tenantId) {
        const creditNote = await this.findOne(id, tenantId);
        await this.creditNoteRepository.remove(creditNote);
    }
    async updateStatus(id, status, tenantId) {
        const creditNote = await this.findOne(id, tenantId);
        creditNote.status = status;
        return this.creditNoteRepository.save(creditNote);
    }
    async getCreditNoteStats(tenantId) {
        const totalCreditNotes = await this.creditNoteRepository.count({ where: { tenantId } });
        const issuedCreditNotes = await this.creditNoteRepository.count({
            where: { tenantId, status: 'issued' }
        });
        const appliedCreditNotes = await this.creditNoteRepository.count({
            where: { tenantId, status: 'applied' }
        });
        const result = await this.creditNoteRepository
            .createQueryBuilder('creditNote')
            .select('SUM(creditNote.totalAmount)', 'totalAmount')
            .where('creditNote.tenantId = :tenantId', { tenantId })
            .getRawOne();
        const typeStats = await this.creditNoteRepository
            .createQueryBuilder('creditNote')
            .select('creditNote.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .addSelect('SUM(creditNote.totalAmount)', 'amount')
            .where('creditNote.tenantId = :tenantId', { tenantId })
            .groupBy('creditNote.type')
            .getRawMany();
        return {
            totalCreditNotes,
            issuedCreditNotes,
            appliedCreditNotes,
            totalAmount: parseFloat(result.totalAmount) || 0,
            typeStats,
        };
    }
    calculateCreditNoteTotals(creditNoteDto) {
        const subtotal = creditNoteDto.items.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
        const taxAmount = creditNoteDto.items.reduce((sum, item) => sum + this.calculateItemTax(item), 0);
        const totalAmount = subtotal + taxAmount;
        return {
            subtotal,
            taxAmount,
            totalAmount: Math.max(0, totalAmount),
        };
    }
    calculateItemTax(item) {
        const lineTotal = (item.unitPrice * item.quantity);
        if (item.taxType === '15%') {
            return lineTotal * 0.15;
        }
        return 0;
    }
    async generateCreditNoteNumber() {
        const count = await this.creditNoteRepository.count();
        const year = new Date().getFullYear();
        return `CN-${year}-${(count + 1).toString().padStart(3, '0')}`;
    }
};
exports.CreditNoteService = CreditNoteService;
exports.CreditNoteService = CreditNoteService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(credit_note_entity_1.CreditNote)),
    __param(1, (0, typeorm_1.InjectRepository)(credit_note_item_entity_1.CreditNoteItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CreditNoteService);
//# sourceMappingURL=credit-note.service.js.map