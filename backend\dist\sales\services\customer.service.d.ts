import { Repository } from 'typeorm';
import { Customer } from '../entities/customer.entity';
import { CreateCustomerDto } from '../dto/create-customer.dto';
export declare class CustomerService {
    private customerRepository;
    constructor(customerRepository: Repository<Customer>);
    create(createCustomerDto: CreateCustomerDto, tenantId: string): Promise<Customer>;
    findAll(tenantId: string): Promise<Customer[]>;
    findOne(id: string, tenantId: string): Promise<Customer>;
    update(id: string, updateCustomerDto: Partial<CreateCustomerDto>, tenantId: string): Promise<Customer>;
    remove(id: string, tenantId: string): Promise<void>;
    updateBalance(customerId: string, amount: number, tenantId: string): Promise<void>;
    getCustomerStats(tenantId: string): Promise<{
        totalCustomers: number;
        activeCustomers: number;
        totalBalance: number;
    }>;
    private generateCustomerNumber;
}
