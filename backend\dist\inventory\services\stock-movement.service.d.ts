import { Repository } from 'typeorm';
import { StockMovement } from '../entities/stock-movement.entity';
export declare class StockMovementService {
    private stockMovementRepository;
    constructor(stockMovementRepository: Repository<StockMovement>);
    findAll(): Promise<StockMovement[]>;
    findByProduct(productId: string): Promise<StockMovement[]>;
    findByWarehouse(warehouseId: string): Promise<StockMovement[]>;
    findByDateRange(startDate: Date, endDate: Date): Promise<StockMovement[]>;
    getMovementStatistics(startDate?: Date, endDate?: Date): Promise<any>;
    getMovementsByType(movementType: string): Promise<StockMovement[]>;
    getRecentMovements(limit?: number): Promise<StockMovement[]>;
}
