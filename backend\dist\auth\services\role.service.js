"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const role_entity_1 = require("../entities/role.entity");
const permission_entity_1 = require("../entities/permission.entity");
let RoleService = class RoleService {
    roleRepository;
    permissionRepository;
    constructor(roleRepository, permissionRepository) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
    }
    async create(roleData) {
        const role = this.roleRepository.create(roleData);
        return this.roleRepository.save(role);
    }
    async findAll() {
        return this.roleRepository.find({
            relations: ['permissions', 'users'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const role = await this.roleRepository.findOne({
            where: { id },
            relations: ['permissions', 'users'],
        });
        if (!role) {
            throw new common_1.NotFoundException(`Role with ID ${id} not found`);
        }
        return role;
    }
    async update(id, updateData) {
        await this.roleRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const role = await this.findOne(id);
        if (role.isSystemRole) {
            throw new Error('Cannot delete system role');
        }
        if (role.users && role.users.length > 0) {
            throw new Error('Cannot delete role with assigned users');
        }
        await this.roleRepository.remove(role);
    }
    async assignPermissions(roleId, permissionIds) {
        const role = await this.findOne(roleId);
        const permissions = await this.permissionRepository.findByIds(permissionIds);
        role.permissions = permissions;
        await this.roleRepository.save(role);
        return this.findOne(roleId);
    }
    async removePermissions(roleId, permissionIds) {
        const role = await this.findOne(roleId);
        role.permissions = role.permissions.filter(permission => !permissionIds.includes(permission.id));
        await this.roleRepository.save(role);
        return this.findOne(roleId);
    }
    async createDefaultRoles() {
        const defaultRoles = [
            {
                name: 'Super Administrator',
                description: 'Full system access with all permissions',
                type: role_entity_1.RoleType.SUPER_ADMIN,
                isSystemRole: true,
                departmentAccess: [
                    'analytics', 'customers', 'collections', 'finance', 'hr',
                    'inventory', 'it_support', 'pos', 'procurement', 'projects',
                    'sales', 'settings', 'system_guide', 'user_management'
                ],
            },
            {
                name: 'Administrator',
                description: 'Administrative access with most permissions',
                type: role_entity_1.RoleType.ADMIN,
                isSystemRole: true,
                departmentAccess: [
                    'analytics', 'customers', 'collections', 'finance', 'hr',
                    'inventory', 'it_support', 'pos', 'procurement', 'projects',
                    'sales', 'settings'
                ],
            },
            {
                name: 'Manager',
                description: 'Management level access with departmental permissions',
                type: role_entity_1.RoleType.MANAGER,
                isSystemRole: true,
                departmentAccess: [],
            },
            {
                name: 'Supervisor',
                description: 'Supervisory access with limited administrative permissions',
                type: role_entity_1.RoleType.SUPERVISOR,
                isSystemRole: true,
                departmentAccess: [],
            },
            {
                name: 'Employee',
                description: 'Standard employee access',
                type: role_entity_1.RoleType.EMPLOYEE,
                isSystemRole: true,
                departmentAccess: [],
            },
            {
                name: 'Viewer',
                description: 'Read-only access to assigned departments',
                type: role_entity_1.RoleType.VIEWER,
                isSystemRole: true,
                departmentAccess: [],
            },
            {
                name: 'Sales Manager',
                description: 'Full access to sales department',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['sales', 'customers', 'analytics'],
            },
            {
                name: 'Sales Representative',
                description: 'Sales operations access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['sales', 'customers'],
            },
            {
                name: 'Finance Manager',
                description: 'Full access to finance department',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['finance', 'collections', 'analytics'],
            },
            {
                name: 'Accountant',
                description: 'Finance operations access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['finance'],
            },
            {
                name: 'Inventory Manager',
                description: 'Full access to inventory management',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['inventory', 'procurement', 'analytics'],
            },
            {
                name: 'Warehouse Staff',
                description: 'Warehouse operations access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['inventory'],
            },
            {
                name: 'HR Manager',
                description: 'Full access to human resources',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['hr', 'analytics'],
            },
            {
                name: 'HR Assistant',
                description: 'HR operations access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['hr'],
            },
            {
                name: 'IT Support Manager',
                description: 'Full access to IT support',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['it_support', 'settings', 'analytics'],
            },
            {
                name: 'IT Support Technician',
                description: 'IT support operations access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['it_support'],
            },
            {
                name: 'Project Manager',
                description: 'Full access to project management',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['projects', 'analytics'],
            },
            {
                name: 'Team Lead',
                description: 'Project team leadership access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['projects'],
            },
            {
                name: 'Cashier',
                description: 'Point of sale operations',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['pos'],
            },
            {
                name: 'Store Manager',
                description: 'Store operations management',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['pos', 'inventory', 'analytics'],
            },
            {
                name: 'Procurement Manager',
                description: 'Full access to procurement',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['procurement', 'inventory', 'analytics'],
            },
            {
                name: 'Procurement Officer',
                description: 'Procurement operations access',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['procurement'],
            },
            {
                name: 'Customer Service Manager',
                description: 'Customer service management',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['customers', 'collections', 'analytics'],
            },
            {
                name: 'Customer Service Representative',
                description: 'Customer service operations',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['customers'],
            },
            {
                name: 'Collections Manager',
                description: 'Collections department management',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['collections', 'customers', 'finance', 'analytics'],
            },
            {
                name: 'Collections Agent',
                description: 'Collections operations',
                type: role_entity_1.RoleType.CUSTOM,
                isSystemRole: false,
                departmentAccess: ['collections', 'customers'],
            },
        ];
        const createdRoles = [];
        for (const roleData of defaultRoles) {
            const existing = await this.roleRepository.findOne({
                where: { name: roleData.name },
            });
            if (!existing) {
                const role = await this.create(roleData);
                createdRoles.push(role);
            }
        }
        return createdRoles;
    }
    async assignDefaultPermissions() {
        const superAdminRole = await this.roleRepository.findOne({
            where: { type: role_entity_1.RoleType.SUPER_ADMIN },
        });
        if (superAdminRole) {
            const allPermissions = await this.permissionRepository.find();
            await this.assignPermissions(superAdminRole.id, allPermissions.map(p => p.id));
        }
        const adminRole = await this.roleRepository.findOne({
            where: { type: role_entity_1.RoleType.ADMIN },
        });
        if (adminRole) {
            const adminPermissions = await this.permissionRepository
                .createQueryBuilder('permission')
                .where('permission.action != :backup', { backup: 'backup' })
                .andWhere('permission.action != :restore', { restore: 'restore' })
                .getMany();
            await this.assignPermissions(adminRole.id, adminPermissions.map(p => p.id));
        }
        await this.assignDepartmentPermissions();
    }
    async assignDepartmentPermissions() {
        const departmentRoles = [
            { roleName: 'Sales Manager', modules: [permission_entity_1.PermissionModule.SALES, permission_entity_1.PermissionModule.CUSTOMERS] },
            { roleName: 'Finance Manager', modules: [permission_entity_1.PermissionModule.FINANCE, permission_entity_1.PermissionModule.COLLECTIONS] },
            { roleName: 'Inventory Manager', modules: [permission_entity_1.PermissionModule.INVENTORY, permission_entity_1.PermissionModule.PROCUREMENT] },
            { roleName: 'HR Manager', modules: [permission_entity_1.PermissionModule.HR] },
            { roleName: 'IT Support Manager', modules: [permission_entity_1.PermissionModule.IT_SUPPORT] },
            { roleName: 'Project Manager', modules: [permission_entity_1.PermissionModule.PROJECTS] },
        ];
        for (const { roleName, modules } of departmentRoles) {
            const role = await this.roleRepository.findOne({
                where: { name: roleName },
            });
            if (role) {
                const permissions = await this.permissionRepository
                    .createQueryBuilder('permission')
                    .where('permission.module IN (:...modules)', { modules })
                    .getMany();
                await this.assignPermissions(role.id, permissions.map(p => p.id));
            }
        }
    }
    async getRolesByDepartment(department) {
        return this.roleRepository
            .createQueryBuilder('role')
            .where('role.departmentAccess @> :department', { department: [department] })
            .getMany();
    }
    async cloneRole(sourceRoleId, newRoleName, description) {
        const sourceRole = await this.findOne(sourceRoleId);
        const newRole = await this.create({
            name: newRoleName,
            description: description || `Cloned from ${sourceRole.name}`,
            type: role_entity_1.RoleType.CUSTOM,
            departmentAccess: sourceRole.departmentAccess,
            isSystemRole: false,
        });
        if (sourceRole.permissions && sourceRole.permissions.length > 0) {
            await this.assignPermissions(newRole.id, sourceRole.permissions.map(p => p.id));
        }
        return this.findOne(newRole.id);
    }
};
exports.RoleService = RoleService;
exports.RoleService = RoleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(1, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], RoleService);
//# sourceMappingURL=role.service.js.map