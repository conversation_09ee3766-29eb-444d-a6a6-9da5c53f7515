import { Repository } from 'typeorm';
import { CollectionActivity, ActivityType } from '../entities/collection-activity.entity';
export declare class CollectionActivityService {
    private collectionActivityRepository;
    constructor(collectionActivityRepository: Repository<CollectionActivity>);
    create(activityData: Partial<CollectionActivity>): Promise<CollectionActivity>;
    findAll(): Promise<CollectionActivity[]>;
    findOne(id: string): Promise<CollectionActivity>;
    findByCase(caseId: string): Promise<CollectionActivity[]>;
    findByAgent(agentId: string): Promise<CollectionActivity[]>;
    update(id: string, updateData: Partial<CollectionActivity>): Promise<CollectionActivity>;
    remove(id: string): Promise<void>;
    logActivity(caseId: string, type: ActivityType, description: string, performedBy?: string, outcome?: string): Promise<CollectionActivity>;
    logCall(caseId: string, duration: number, outcome: string, notes: string, performedBy?: string): Promise<CollectionActivity>;
    logEmail(caseId: string, subject: string, outcome: string, performedBy?: string): Promise<CollectionActivity>;
    logLetter(caseId: string, letterType: string, performedBy?: string): Promise<CollectionActivity>;
    logPayment(caseId: string, amount: number, paymentMethod: string, performedBy?: string): Promise<CollectionActivity>;
    getActivitySummary(caseId: string): Promise<any>;
    getAgentActivityReport(agentId: string, startDate: Date, endDate: Date): Promise<any>;
    getRecentActivities(limit?: number): Promise<CollectionActivity[]>;
    scheduleFollowUp(caseId: string, followUpDate: Date, notes: string, performedBy?: string): Promise<CollectionActivity>;
    getScheduledFollowUps(): Promise<CollectionActivity[]>;
    markFollowUpCompleted(id: string, outcome?: string): Promise<CollectionActivity>;
}
