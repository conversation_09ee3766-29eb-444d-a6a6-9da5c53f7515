import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { CollectionCase } from './collection-case.entity';
import { PaymentPlanInstallment } from './payment-plan-installment.entity';

export enum PaymentPlanStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  DEFAULTED = 'defaulted',
  CANCELLED = 'cancelled',
  MODIFIED = 'modified',
}

export enum PaymentFrequency {
  WEEKLY = 'weekly',
  BIWEEKLY = 'biweekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  CUSTOM = 'custom',
}

@Entity('payment_plans')
export class PaymentPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  caseId: string;

  @ManyToOne(() => CollectionCase, collectionCase => collectionCase.paymentPlans)
  @JoinColumn({ name: 'caseId' })
  case: CollectionCase;

  @Column({ length: 50, unique: true })
  planNumber: string;

  @Column({
    type: 'enum',
    enum: PaymentPlanStatus,
    default: PaymentPlanStatus.DRAFT,
  })
  status: PaymentPlanStatus;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  totalAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  downPayment: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  remainingAmount: number;

  @Column({ type: 'int' })
  numberOfInstallments: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  installmentAmount: number;

  @Column({
    type: 'enum',
    enum: PaymentFrequency,
    default: PaymentFrequency.MONTHLY,
  })
  frequency: PaymentFrequency;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'date', nullable: true })
  firstPaymentDate: Date;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  interestRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  setupFee: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  lateFee: number;

  @Column({ type: 'int', default: 5 })
  gracePeriodDays: number;

  @Column({ type: 'text', nullable: true })
  terms: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column()
  createdBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ nullable: true })
  customerSignature: string;

  @Column({ type: 'timestamp', nullable: true })
  customerSignedAt: Date;

  @Column({ type: 'int', default: 0 })
  missedPayments: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalPaid: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalOutstanding: number;

  @OneToMany(() => PaymentPlanInstallment, installment => installment.paymentPlan, { cascade: true })
  installments: PaymentPlanInstallment[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
