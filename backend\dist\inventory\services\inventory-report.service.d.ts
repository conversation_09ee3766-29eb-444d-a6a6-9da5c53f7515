import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { Stock } from '../entities/stock.entity';
import { StockMovement } from '../entities/stock-movement.entity';
import { PurchaseOrder } from '../entities/purchase-order.entity';
export declare class InventoryReportService {
    private productRepository;
    private stockRepository;
    private stockMovementRepository;
    private purchaseOrderRepository;
    constructor(productRepository: Repository<Product>, stockRepository: Repository<Stock>, stockMovementRepository: Repository<StockMovement>, purchaseOrderRepository: Repository<PurchaseOrder>);
    generateInventoryValuationReport(): Promise<any>;
    generateStockLevelReport(): Promise<any>;
    generateMovementReport(startDate: Date, endDate: Date): Promise<any>;
    generatePurchaseOrderReport(startDate: Date, endDate: Date): Promise<any>;
    generateABCAnalysisReport(): Promise<any>;
}
