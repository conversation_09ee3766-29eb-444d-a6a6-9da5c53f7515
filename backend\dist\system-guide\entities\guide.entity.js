"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Guide = exports.GuideStatus = exports.GuideType = void 0;
const typeorm_1 = require("typeorm");
const guide_section_entity_1 = require("./guide-section.entity");
var GuideType;
(function (GuideType) {
    GuideType["USER_MANUAL"] = "user_manual";
    GuideType["ADMIN_GUIDE"] = "admin_guide";
    GuideType["QUICK_START"] = "quick_start";
    GuideType["FEATURE_GUIDE"] = "feature_guide";
    GuideType["TROUBLESHOOTING"] = "troubleshooting";
    GuideType["API_DOCUMENTATION"] = "api_documentation";
    GuideType["TUTORIAL"] = "tutorial";
})(GuideType || (exports.GuideType = GuideType = {}));
var GuideStatus;
(function (GuideStatus) {
    GuideStatus["DRAFT"] = "draft";
    GuideStatus["PUBLISHED"] = "published";
    GuideStatus["ARCHIVED"] = "archived";
    GuideStatus["UNDER_REVIEW"] = "under_review";
})(GuideStatus || (exports.GuideStatus = GuideStatus = {}));
let Guide = class Guide {
    id;
    title;
    description;
    type;
    status;
    version;
    coverImage;
    tags;
    targetAudience;
    sortOrder;
    isFeatured;
    isPublic;
    authorId;
    reviewedBy;
    reviewedAt;
    publishedAt;
    viewCount;
    rating;
    ratingCount;
    sections;
    metadata;
    createdAt;
    updatedAt;
};
exports.Guide = Guide;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Guide.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Guide.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Guide.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: GuideType,
        default: GuideType.USER_MANUAL,
    }),
    __metadata("design:type", String)
], Guide.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: GuideStatus,
        default: GuideStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Guide.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Guide.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], Guide.prototype, "coverImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Guide.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Guide.prototype, "targetAudience", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Guide.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Guide.prototype, "isFeatured", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Guide.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Guide.prototype, "authorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Guide.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Guide.prototype, "reviewedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Guide.prototype, "publishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Guide.prototype, "viewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Guide.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Guide.prototype, "ratingCount", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => guide_section_entity_1.GuideSection, section => section.guide, { cascade: true }),
    __metadata("design:type", Array)
], Guide.prototype, "sections", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Guide.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Guide.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Guide.prototype, "updatedAt", void 0);
exports.Guide = Guide = __decorate([
    (0, typeorm_1.Entity)('guides')
], Guide);
//# sourceMappingURL=guide.entity.js.map