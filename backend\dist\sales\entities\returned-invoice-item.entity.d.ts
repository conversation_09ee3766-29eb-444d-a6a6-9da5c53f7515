import { ReturnedInvoice } from './returned-invoice.entity';
export declare class ReturnedInvoiceItem {
    id: string;
    returnedInvoiceId: string;
    returnedInvoice: ReturnedInvoice;
    originalItemId: string;
    lineNumber: number;
    description: string;
    productCode: string;
    originalQuantity: number;
    returnQuantity: number;
    unitPrice: number;
    discount: number;
    taxType: string;
    taxAmount: number;
    lineTotal: number;
    unit: string;
    returnReason: string;
    itemCondition: 'good' | 'damaged' | 'defective' | 'expired';
    notes: string;
    tenantId: string;
}
