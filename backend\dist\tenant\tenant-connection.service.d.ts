import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
export declare class TenantConnectionService {
    private configService;
    private readonly logger;
    private connections;
    constructor(configService: ConfigService);
    getTenantConnection(tenantId: string): Promise<DataSource>;
    createTenantDatabase(tenantId: string): Promise<void>;
    closeTenantConnection(tenantId: string): Promise<void>;
    closeAllConnections(): Promise<void>;
}
