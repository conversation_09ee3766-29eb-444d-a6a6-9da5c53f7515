"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerSegmentController = void 0;
const common_1 = require("@nestjs/common");
const customer_segment_service_1 = require("../services/customer-segment.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerSegmentController = class CustomerSegmentController {
    customerSegmentService;
    constructor(customerSegmentService) {
        this.customerSegmentService = customerSegmentService;
    }
    async create(createSegmentDto) {
        return this.customerSegmentService.create(createSegmentDto);
    }
    async findAll() {
        return this.customerSegmentService.findAll();
    }
    async findOne(id) {
        return this.customerSegmentService.findOne(id);
    }
    async getSegmentCustomers(id, page, limit) {
        const pageNum = page ? parseInt(page) : 1;
        const limitNum = limit ? parseInt(limit) : 20;
        return this.customerSegmentService.getSegmentCustomers(id, pageNum, limitNum);
    }
    async getSegmentAnalytics(id) {
        return this.customerSegmentService.getSegmentAnalytics(id);
    }
    async createHighValueSegment(data) {
        return this.customerSegmentService.createHighValueSegment(data.minSpent);
    }
    async createLoyaltySegment(data) {
        return this.customerSegmentService.createLoyaltySegment(data.minPoints);
    }
    async createInactiveSegment(data) {
        return this.customerSegmentService.createInactiveSegment(data.daysSinceLastPurchase);
    }
    async createNewCustomerSegment(data) {
        return this.customerSegmentService.createNewCustomerSegment(data.daysSinceRegistration);
    }
    async update(id, updateSegmentDto) {
        return this.customerSegmentService.update(id, updateSegmentDto);
    }
    async refreshSegment(id) {
        const customerCount = await this.customerSegmentService.refreshSegmentCustomers(id);
        return {
            segmentId: id,
            customerCount,
            message: 'Segment refreshed successfully',
        };
    }
    async activate(id) {
        return this.customerSegmentService.activateSegment(id);
    }
    async deactivate(id) {
        return this.customerSegmentService.deactivateSegment(id);
    }
    async refreshAllSegments() {
        return this.customerSegmentService.refreshAllSegments();
    }
    async remove(id) {
        return this.customerSegmentService.remove(id);
    }
};
exports.CustomerSegmentController = CustomerSegmentController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/customers'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "getSegmentCustomers", null);
__decorate([
    (0, common_1.Get)(':id/analytics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "getSegmentAnalytics", null);
__decorate([
    (0, common_1.Post)('high-value'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "createHighValueSegment", null);
__decorate([
    (0, common_1.Post)('loyalty'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "createLoyaltySegment", null);
__decorate([
    (0, common_1.Post)('inactive'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "createInactiveSegment", null);
__decorate([
    (0, common_1.Post)('new-customers'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "createNewCustomerSegment", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/refresh'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "refreshSegment", null);
__decorate([
    (0, common_1.Patch)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "activate", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "deactivate", null);
__decorate([
    (0, common_1.Post)('refresh-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "refreshAllSegments", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerSegmentController.prototype, "remove", null);
exports.CustomerSegmentController = CustomerSegmentController = __decorate([
    (0, common_1.Controller)('customer-segments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_segment_service_1.CustomerSegmentService])
], CustomerSegmentController);
//# sourceMappingURL=customer-segment.controller.js.map