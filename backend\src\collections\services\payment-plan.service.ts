import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentPlan, PaymentPlanStatus } from '../entities/payment-plan.entity';
import { PaymentPlanInstallment, InstallmentStatus } from '../entities/payment-plan-installment.entity';

@Injectable()
export class PaymentPlanService {
  constructor(
    @InjectRepository(PaymentPlan)
    private paymentPlanRepository: Repository<PaymentPlan>,
    @InjectRepository(PaymentPlanInstallment)
    private installmentRepository: Repository<PaymentPlanInstallment>,
  ) {}

  async create(planData: Partial<PaymentPlan>): Promise<PaymentPlan> {
    const plan = this.paymentPlanRepository.create(planData);
    return this.paymentPlanRepository.save(plan);
  }

  async findAll(): Promise<PaymentPlan[]> {
    return this.paymentPlanRepository.find({
      relations: ['customer', 'installments'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<PaymentPlan> {
    const plan = await this.paymentPlanRepository.findOne({
      where: { id },
      relations: ['customer', 'installments'],
    });

    if (!plan) {
      throw new NotFoundException(`Payment plan with ID ${id} not found`);
    }

    return plan;
  }

  async update(id: string, updateData: Partial<PaymentPlan>): Promise<PaymentPlan> {
    await this.paymentPlanRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const plan = await this.findOne(id);
    await this.paymentPlanRepository.remove(plan);
  }

  async findByCustomer(customerId: string): Promise<PaymentPlan[]> {
    return this.paymentPlanRepository.find({
      where: { customerId },
      relations: ['installments'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByStatus(status: PaymentPlanStatus): Promise<PaymentPlan[]> {
    return this.paymentPlanRepository.find({
      where: { status },
      relations: ['customer', 'installments'],
    });
  }

  async createInstallments(planId: string, numberOfInstallments: number): Promise<PaymentPlanInstallment[]> {
    const plan = await this.findOne(planId);
    if (!plan) {
      throw new Error('Payment plan not found');
    }

    const installmentAmount = plan.totalAmount / numberOfInstallments;
    const installments: PaymentPlanInstallment[] = [];

    for (let i = 0; i < numberOfInstallments; i++) {
      const dueDate = new Date(plan.startDate);
      dueDate.setMonth(dueDate.getMonth() + i + 1);

      const installment = this.installmentRepository.create({
        paymentPlan: plan,
        installmentNumber: i + 1,
        amount: installmentAmount,
        dueDate,
        status: InstallmentStatus.PENDING,
      });

      const savedInstallment = await this.installmentRepository.save(installment);
      installments.push(savedInstallment);
    }

    return installments;
  }

  async recordPayment(installmentId: string, amount: number, paymentDate: Date): Promise<PaymentPlanInstallment> {
    const installment = await this.installmentRepository.findOne({
      where: { id: installmentId },
      relations: ['paymentPlan'],
    });

    if (!installment) {
      throw new NotFoundException('Installment not found');
    }

    installment.paidAmount = (installment.paidAmount || 0) + amount;
    installment.lastPaymentDate = paymentDate;

    if (installment.paidAmount >= installment.amount) {
      installment.status = InstallmentStatus.PAID;
    } else {
      installment.status = InstallmentStatus.PARTIAL;
    }

    const updatedInstallment = await this.installmentRepository.save(installment);

    // Update payment plan totals
    await this.updatePaymentPlanTotals(installment.paymentPlan.id);

    return updatedInstallment;
  }

  private async updatePaymentPlanTotals(planId: string): Promise<void> {
    const plan = await this.findOne(planId);
    const totalPaid = plan.installments.reduce((sum, inst) => sum + (inst.paidAmount || 0), 0);
    const remainingBalance = plan.totalAmount - totalPaid;

    let status = plan.status;
    if (remainingBalance <= 0) {
      status = PaymentPlanStatus.COMPLETED;
    } else if (totalPaid > 0) {
      status = PaymentPlanStatus.ACTIVE;
    }

    await this.paymentPlanRepository.update(planId, {
      paidAmount: totalPaid,
      remainingBalance,
      status,
    });
  }

  async getPaymentPlanSummary(planId: string): Promise<any> {
    const plan = await this.findOne(planId);
    if (!plan) {
      return null;
    }

    const totalPaid = plan.installments.reduce((sum, inst) => sum + (inst.paidAmount || 0), 0);
    const remainingBalance = plan.totalAmount - totalPaid;
    const paidInstallments = plan.installments.filter(inst => inst.status === InstallmentStatus.PAID).length;
    const overdueInstallments = plan.installments.filter(
      inst => inst.status === InstallmentStatus.PENDING && new Date(inst.dueDate) < new Date()
    ).length;

    return {
      planId: plan.id,
      customerId: plan.customerId,
      totalAmount: plan.totalAmount,
      totalPaid,
      remainingBalance,
      totalInstallments: plan.installments.length,
      paidInstallments,
      overdueInstallments,
      completionPercentage: (totalPaid / plan.totalAmount) * 100,
      status: plan.status,
      nextDueDate: this.getNextDueDate(plan.installments),
      nextDueAmount: this.getNextDueAmount(plan.installments),
    };
  }

  private getNextDueDate(installments: PaymentPlanInstallment[]): Date | null {
    const pendingInstallments = installments
      .filter(inst => inst.status === InstallmentStatus.PENDING)
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
    
    return pendingInstallments.length > 0 ? pendingInstallments[0].dueDate : null;
  }

  private getNextDueAmount(installments: PaymentPlanInstallment[]): number {
    const pendingInstallments = installments
      .filter(inst => inst.status === InstallmentStatus.PENDING)
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
    
    return pendingInstallments.length > 0 ? pendingInstallments[0].amount : 0;
  }

  async getOverdueInstallments(): Promise<PaymentPlanInstallment[]> {
    const today = new Date();
    return this.installmentRepository
      .createQueryBuilder('installment')
      .leftJoinAndSelect('installment.paymentPlan', 'plan')
      .leftJoinAndSelect('plan.customer', 'customer')
      .where('installment.dueDate < :today', { today })
      .andWhere('installment.status = :status', { status: InstallmentStatus.PENDING })
      .orderBy('installment.dueDate', 'ASC')
      .getMany();
  }

  async getUpcomingInstallments(days: number = 7): Promise<PaymentPlanInstallment[]> {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + days);

    return this.installmentRepository
      .createQueryBuilder('installment')
      .leftJoinAndSelect('installment.paymentPlan', 'plan')
      .leftJoinAndSelect('plan.customer', 'customer')
      .where('installment.dueDate BETWEEN :today AND :futureDate', { today, futureDate })
      .andWhere('installment.status = :status', { status: InstallmentStatus.PENDING })
      .orderBy('installment.dueDate', 'ASC')
      .getMany();
  }

  async markPlanAsDefaulted(planId: string, reason: string): Promise<PaymentPlan> {
    await this.paymentPlanRepository.update(planId, {
      status: PaymentPlanStatus.DEFAULTED,
      notes: reason,
    });
    return this.findOne(planId);
  }

  async getPaymentPlanStatistics(): Promise<any> {
    const totalPlans = await this.paymentPlanRepository.count();
    const activePlans = await this.paymentPlanRepository.count({ 
      where: { status: PaymentPlanStatus.ACTIVE } 
    });
    const completedPlans = await this.paymentPlanRepository.count({ 
      where: { status: PaymentPlanStatus.COMPLETED } 
    });
    const defaultedPlans = await this.paymentPlanRepository.count({ 
      where: { status: PaymentPlanStatus.DEFAULTED } 
    });

    const financialData = await this.paymentPlanRepository
      .createQueryBuilder('plan')
      .select([
        'SUM(plan.totalAmount) as totalAmount',
        'SUM(plan.paidAmount) as paidAmount',
      ])
      .getRawOne();

    const overdueInstallments = await this.getOverdueInstallments();
    const upcomingInstallments = await this.getUpcomingInstallments();

    return {
      totalPlans,
      activePlans,
      completedPlans,
      defaultedPlans,
      totalAmount: parseFloat(financialData.totalAmount) || 0,
      paidAmount: parseFloat(financialData.paidAmount) || 0,
      completionRate: totalPlans > 0 ? (completedPlans / totalPlans) * 100 : 0,
      overdueCount: overdueInstallments.length,
      upcomingCount: upcomingInstallments.length,
    };
  }
}
