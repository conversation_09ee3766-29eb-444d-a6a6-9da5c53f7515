import { Project } from './project.entity';
import { Task } from './task.entity';
export declare enum TimeEntryStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    APPROVED = "approved",
    REJECTED = "rejected",
    BILLED = "billed"
}
export declare class TimeEntry {
    id: string;
    projectId: string;
    project: Project;
    taskId: string;
    task: Task;
    userId: string;
    date: Date;
    startTime: string;
    endTime: string;
    hours: number;
    description: string;
    status: TimeEntryStatus;
    isBillable: boolean;
    hourlyRate: number;
    amount: number;
    currency: string;
    approvedBy: string;
    approvedAt: Date;
    approvalNotes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
