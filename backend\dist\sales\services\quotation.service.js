"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuotationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const quotation_entity_1 = require("../entities/quotation.entity");
const quotation_item_entity_1 = require("../entities/quotation-item.entity");
let QuotationService = class QuotationService {
    quotationRepository;
    quotationItemRepository;
    constructor(quotationRepository, quotationItemRepository) {
        this.quotationRepository = quotationRepository;
        this.quotationItemRepository = quotationItemRepository;
    }
    async create(createQuotationDto, tenantId) {
        const quotationNumber = await this.generateQuotationNumber();
        const totals = this.calculateQuotationTotals(createQuotationDto);
        const quotation = this.quotationRepository.create({
            ...createQuotationDto,
            quotationNumber,
            tenantId,
            ...totals,
        });
        const savedQuotation = await this.quotationRepository.save(quotation);
        for (const itemDto of createQuotationDto.items) {
            const item = this.quotationItemRepository.create({
                ...itemDto,
                quotationId: savedQuotation.id,
                tenantId,
                taxAmount: this.calculateItemTax(itemDto),
                lineTotal: (itemDto.unitPrice * itemDto.quantity) - (itemDto.discount || 0),
            });
            await this.quotationItemRepository.save(item);
        }
        return this.findOne(savedQuotation.id, tenantId);
    }
    async findAll(tenantId) {
        return this.quotationRepository.find({
            where: { tenantId },
            relations: ['customer', 'items'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id, tenantId) {
        const quotation = await this.quotationRepository.findOne({
            where: { id, tenantId },
            relations: ['customer', 'items'],
        });
        if (!quotation) {
            throw new common_1.NotFoundException('Quotation not found');
        }
        return quotation;
    }
    async update(id, updateQuotationDto, tenantId) {
        const quotation = await this.findOne(id, tenantId);
        if (updateQuotationDto.items) {
            await this.quotationItemRepository.delete({ quotationId: id });
            for (const itemDto of updateQuotationDto.items) {
                const item = this.quotationItemRepository.create({
                    ...itemDto,
                    quotationId: id,
                    tenantId,
                    taxAmount: this.calculateItemTax(itemDto),
                    lineTotal: (itemDto.unitPrice * itemDto.quantity) - (itemDto.discount || 0),
                });
                await this.quotationItemRepository.save(item);
            }
            const totals = this.calculateQuotationTotals(updateQuotationDto);
            Object.assign(quotation, totals);
        }
        Object.assign(quotation, updateQuotationDto);
        return this.quotationRepository.save(quotation);
    }
    async remove(id, tenantId) {
        const quotation = await this.findOne(id, tenantId);
        await this.quotationRepository.remove(quotation);
    }
    async updateStatus(id, status, tenantId) {
        const quotation = await this.findOne(id, tenantId);
        quotation.status = status;
        return this.quotationRepository.save(quotation);
    }
    async convertToInvoice(id, tenantId) {
        const quotation = await this.findOne(id, tenantId);
        if (quotation.status !== 'accepted') {
            throw new Error('Only accepted quotations can be converted to invoices');
        }
        quotation.status = 'accepted';
        quotation.convertedToInvoice = 'pending';
        await this.quotationRepository.save(quotation);
        return { success: true, invoiceId: 'pending' };
    }
    async getQuotationStats(tenantId) {
        const totalQuotations = await this.quotationRepository.count({ where: { tenantId } });
        const acceptedQuotations = await this.quotationRepository.count({
            where: { tenantId, status: 'accepted' }
        });
        const pendingQuotations = await this.quotationRepository.count({
            where: { tenantId, status: 'sent' }
        });
        const result = await this.quotationRepository
            .createQueryBuilder('quotation')
            .select('SUM(quotation.totalAmount)', 'totalValue')
            .where('quotation.tenantId = :tenantId', { tenantId })
            .getRawOne();
        return {
            totalQuotations,
            acceptedQuotations,
            pendingQuotations,
            totalValue: parseFloat(result.totalValue) || 0,
            conversionRate: totalQuotations > 0 ? (acceptedQuotations / totalQuotations) * 100 : 0,
        };
    }
    calculateQuotationTotals(quotationDto) {
        const subtotal = quotationDto.items.reduce((sum, item) => sum + (item.unitPrice * item.quantity) - (item.discount || 0), 0);
        const discountAmount = quotationDto.discountType === 'percentage'
            ? (subtotal * (quotationDto.discountValue || 0) / 100)
            : (quotationDto.discountValue || 0);
        const taxAmount = quotationDto.items.reduce((sum, item) => sum + this.calculateItemTax(item), 0);
        const totalAmount = subtotal - discountAmount + taxAmount;
        return {
            subtotal,
            discountAmount,
            taxAmount,
            totalAmount: Math.max(0, totalAmount),
        };
    }
    calculateItemTax(item) {
        const lineTotal = (item.unitPrice * item.quantity) - (item.discount || 0);
        if (item.taxType === '15%') {
            return lineTotal * 0.15;
        }
        return 0;
    }
    async generateQuotationNumber() {
        const count = await this.quotationRepository.count();
        const year = new Date().getFullYear();
        return `QUO-${year}-${(count + 1).toString().padStart(3, '0')}`;
    }
};
exports.QuotationService = QuotationService;
exports.QuotationService = QuotationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(quotation_entity_1.Quotation)),
    __param(1, (0, typeorm_1.InjectRepository)(quotation_item_entity_1.QuotationItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], QuotationService);
//# sourceMappingURL=quotation.service.js.map