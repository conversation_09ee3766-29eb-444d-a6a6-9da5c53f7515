import { Repository } from 'typeorm';
import { Ticket, TicketStatus, TicketPriority } from '../entities/ticket.entity';
import { TicketComment } from '../entities/ticket-comment.entity';
export declare class TicketService {
    private ticketRepository;
    private ticketCommentRepository;
    constructor(ticketRepository: Repository<Ticket>, ticketCommentRepository: Repository<TicketComment>);
    create(ticketData: Partial<Ticket>): Promise<Ticket>;
    findAll(): Promise<Ticket[]>;
    findOne(id: string): Promise<Ticket>;
    update(id: string, updateData: Partial<Ticket>): Promise<Ticket>;
    remove(id: string): Promise<void>;
    findByStatus(status: TicketStatus): Promise<Ticket[]>;
    findByPriority(priority: TicketPriority): Promise<Ticket[]>;
    findByAssignee(assigneeId: string): Promise<Ticket[]>;
    findByCreator(creatorId: string): Promise<Ticket[]>;
    assignTicket(ticketId: string, assigneeId: string): Promise<Ticket>;
    updateStatus(ticketId: string, status: TicketStatus, userId?: string): Promise<Ticket>;
    updatePriority(ticketId: string, priority: TicketPriority, userId?: string): Promise<Ticket>;
    addComment(ticketId: string, content: string, authorId: string): Promise<TicketComment>;
    getTicketComments(ticketId: string): Promise<TicketComment[]>;
    searchTickets(searchTerm: string): Promise<Ticket[]>;
    getTicketStatistics(): Promise<any>;
    getOverdueTickets(): Promise<Ticket[]>;
    getTicketsByCategory(category: string): Promise<Ticket[]>;
    escalateTicket(ticketId: string, reason: string, userId?: string): Promise<Ticket>;
    closeTicket(ticketId: string, resolution: string, userId?: string): Promise<Ticket>;
    reopenTicket(ticketId: string, reason: string, userId?: string): Promise<Ticket>;
    private generateTicketNumber;
    getDashboardMetrics(): Promise<any>;
}
