import { AssetMaintenance } from './asset-maintenance.entity';
export declare enum AssetType {
    COMPUTER = "computer",
    LAPTOP = "laptop",
    SERVER = "server",
    PRINTER = "printer",
    PHONE = "phone",
    TABLET = "tablet",
    MONITOR = "monitor",
    NETWORK_DEVICE = "network_device",
    SOFTWARE = "software",
    FURNITURE = "furniture",
    VEHICLE = "vehicle",
    OTHER = "other"
}
export declare enum AssetStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    IN_REPAIR = "in_repair",
    RETIRED = "retired",
    DISPOSED = "disposed",
    LOST = "lost",
    STOLEN = "stolen"
}
export declare class Asset {
    id: string;
    assetTag: string;
    name: string;
    description: string;
    type: AssetType;
    status: AssetStatus;
    manufacturer: string;
    model: string;
    serialNumber: string;
    purchaseDate: Date;
    purchasePrice: number;
    vendor: string;
    warrantyExpiry: Date;
    assignedTo: string;
    location: string;
    department: string;
    specifications: string;
    operatingSystem: string;
    ipAddress: string;
    macAddress: string;
    lastMaintenanceDate: Date;
    nextMaintenanceDate: Date;
    customFields: any;
    attachments: string[];
    maintenanceHistory: AssetMaintenance[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
