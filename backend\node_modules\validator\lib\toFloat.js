"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = toFloat;
var _isFloat = _interopRequireDefault(require("./isFloat"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function toFloat(str) {
  if (!(0, _isFloat.default)(str)) return NaN;
  return parseFloat(str);
}
module.exports = exports.default;
module.exports.default = exports.default;