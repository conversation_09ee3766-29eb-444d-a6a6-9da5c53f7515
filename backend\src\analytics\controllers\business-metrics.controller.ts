import { Controller, Get, Query, Request, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { BusinessMetricsService } from '../services/business-metrics.service';

@ApiTags('Analytics - Business Metrics')
@Controller('analytics/business-metrics')
@UseGuards(JwtAuthGuard)
export class BusinessMetricsController {
  constructor(private readonly businessMetricsService: BusinessMetricsService) {}

  @Get()
  @ApiOperation({ summary: 'Get business metrics for specified period' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period (e.g., 30d, 90d, 1y)', example: '30d' })
  @ApiResponse({ status: 200, description: 'Business metrics retrieved successfully' })
  async getBusinessMetrics(@Query('period') period: string = '30d', @Request() req) {
    try {
      const companyId = req.user.companyId;
      const metrics = await this.businessMetricsService.getBusinessMetrics(companyId, period);
      
      return {
        success: true,
        data: metrics,
        message: 'Business metrics retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve business metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('revenue')
  @ApiOperation({ summary: 'Get revenue metrics' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period', example: '30d' })
  @ApiResponse({ status: 200, description: 'Revenue metrics retrieved successfully' })
  async getRevenueMetrics(@Query('period') period: string = '30d', @Request() req) {
    try {
      const companyId = req.user.companyId;
      const metrics = await this.businessMetricsService.getRevenueMetrics(companyId, period);
      
      return {
        success: true,
        data: metrics,
        message: 'Revenue metrics retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve revenue metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('departments')
  @ApiOperation({ summary: 'Get department-wise metrics' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period', example: '30d' })
  @ApiResponse({ status: 200, description: 'Department metrics retrieved successfully' })
  async getDepartmentMetrics(@Query('period') period: string = '30d', @Request() req) {
    try {
      const companyId = req.user.companyId;
      const metrics = await this.businessMetricsService.getDepartmentMetrics(companyId, period);
      
      return {
        success: true,
        data: metrics,
        message: 'Department metrics retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve department metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('growth')
  @ApiOperation({ summary: 'Get growth metrics and trends' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period', example: '30d' })
  @ApiResponse({ status: 200, description: 'Growth metrics retrieved successfully' })
  async getGrowthMetrics(@Query('period') period: string = '30d', @Request() req) {
    try {
      const companyId = req.user.companyId;
      const metrics = await this.businessMetricsService.getGrowthMetrics(companyId, period);
      
      return {
        success: true,
        data: metrics,
        message: 'Growth metrics retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve growth metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
