import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Category } from './category.entity';
import { Supplier } from './supplier.entity';
import { Stock } from './stock.entity';
import { StockMovement } from './stock-movement.entity';
import { PurchaseOrderItem } from './purchase-order-item.entity';

export enum ProductType {
  PHYSICAL = 'physical',
  DIGITAL = 'digital',
  SERVICE = 'service',
  BUNDLE = 'bundle',
}

export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DISCONTINUED = 'discontinued',
  OUT_OF_STOCK = 'out_of_stock',
}

export enum UnitOfMeasure {
  PIECE = 'piece',
  KILOGRAM = 'kilogram',
  GRAM = 'gram',
  LITER = 'liter',
  MILLILITER = 'milliliter',
  METER = 'meter',
  CENTIMETER = 'centimeter',
  SQUARE_METER = 'square_meter',
  CUBIC_METER = 'cubic_meter',
  BOX = 'box',
  PACK = 'pack',
  DOZEN = 'dozen',
  PAIR = 'pair',
  SET = 'set',
}

@Entity('inventory_products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  sku: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  shortDescription: string;

  @Column({
    type: 'enum',
    enum: ProductType,
    default: ProductType.PHYSICAL,
  })
  type: ProductType;

  @Column({
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.ACTIVE,
  })
  status: ProductStatus;

  @Column({ nullable: true })
  categoryId: string;

  @ManyToOne(() => Category, category => category.products)
  @JoinColumn({ name: 'categoryId' })
  category: Category;

  @Column({ nullable: true })
  supplierId: string;

  @ManyToOne(() => Supplier, supplier => supplier.products)
  @JoinColumn({ name: 'supplierId' })
  supplier: Supplier;

  @Column({ length: 50, nullable: true })
  barcode: string;

  @Column({ length: 50, nullable: true })
  qrCode: string;

  @Column({
    type: 'enum',
    enum: UnitOfMeasure,
    default: UnitOfMeasure.PIECE,
  })
  unitOfMeasure: UnitOfMeasure;

  @Column({ type: 'decimal', precision: 10, scale: 3, nullable: true })
  weight: number;

  @Column({ type: 'decimal', precision: 10, scale: 3, nullable: true })
  length: number;

  @Column({ type: 'decimal', precision: 10, scale: 3, nullable: true })
  width: number;

  @Column({ type: 'decimal', precision: 10, scale: 3, nullable: true })
  height: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  costPrice: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  sellingPrice: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  msrp: number; // Manufacturer's Suggested Retail Price

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'int', default: 0 })
  minStockLevel: number;

  @Column({ type: 'int', default: 0 })
  maxStockLevel: number;

  @Column({ type: 'int', default: 0 })
  reorderPoint: number;

  @Column({ type: 'int', default: 0 })
  reorderQuantity: number;

  @Column({ type: 'int', default: 0 })
  leadTimeDays: number;

  @Column({ default: true })
  trackInventory: boolean;

  @Column({ default: false })
  allowBackorder: boolean;

  @Column({ default: false })
  isPerishable: boolean;

  @Column({ type: 'int', nullable: true })
  shelfLifeDays: number;

  @Column({ type: 'json', nullable: true })
  images: string[];

  @Column({ type: 'json', nullable: true })
  attributes: any; // Custom product attributes

  @Column({ type: 'json', nullable: true })
  variants: any[]; // Product variants (size, color, etc.)

  @Column({ type: 'text', nullable: true })
  notes: string;

  @OneToMany(() => Stock, stock => stock.product)
  stocks: Stock[];

  @OneToMany(() => StockMovement, movement => movement.product)
  stockMovements: StockMovement[];

  @OneToMany(() => PurchaseOrderItem, item => item.product)
  purchaseOrderItems: PurchaseOrderItem[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
