import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from '../entities/department.entity';

@Injectable()
export class DepartmentService {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  async create(createDepartmentDto: any): Promise<Department> {
    const department = this.departmentRepository.create(createDepartmentDto);
    return this.departmentRepository.save(department);
  }

  async findAll(): Promise<Department[]> {
    return this.departmentRepository.find({
      relations: ['parentDepartment', 'childDepartments', 'manager', 'employees'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Department> {
    const department = await this.departmentRepository.findOne({
      where: { id },
      relations: ['parentDepartment', 'childDepartments', 'manager', 'employees'],
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return department;
  }

  async update(id: string, updateDepartmentDto: any): Promise<Department> {
    const department = await this.findOne(id);
    Object.assign(department, updateDepartmentDto);
    return this.departmentRepository.save(department);
  }

  async remove(id: string): Promise<void> {
    const department = await this.findOne(id);
    
    if (department.employees?.length > 0) {
      throw new BadRequestException('Cannot delete department with employees');
    }

    if (department.childDepartments?.length > 0) {
      throw new BadRequestException('Cannot delete department with child departments');
    }

    await this.departmentRepository.remove(department);
  }

  async getDepartmentHierarchy(): Promise<Department[]> {
    const departments = await this.departmentRepository.find({
      where: { parentDepartmentId: null },
      relations: ['childDepartments', 'manager'],
      order: { name: 'ASC' },
    });

    return this.buildDepartmentTree(departments);
  }

  private async buildDepartmentTree(departments: Department[]): Promise<Department[]> {
    for (const department of departments) {
      if (department.childDepartments?.length > 0) {
        department.childDepartments = await this.buildDepartmentTree(department.childDepartments);
      }
    }
    return departments;
  }
}
