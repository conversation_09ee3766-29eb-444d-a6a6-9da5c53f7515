"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BudgetService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const budget_entity_1 = require("../entities/budget.entity");
const budget_item_entity_1 = require("../entities/budget-item.entity");
let BudgetService = class BudgetService {
    budgetRepository;
    budgetItemRepository;
    constructor(budgetRepository, budgetItemRepository) {
        this.budgetRepository = budgetRepository;
        this.budgetItemRepository = budgetItemRepository;
    }
    async create(createBudgetDto) {
        const budget = this.budgetRepository.create(createBudgetDto);
        return this.budgetRepository.save(budget);
    }
    async findAll(filters) {
        const queryBuilder = this.budgetRepository.createQueryBuilder('budget')
            .leftJoinAndSelect('budget.budgetItems', 'budgetItems')
            .leftJoinAndSelect('budgetItems.account', 'account');
        if (filters?.type) {
            queryBuilder.andWhere('budget.type = :type', { type: filters.type });
        }
        if (filters?.status) {
            queryBuilder.andWhere('budget.status = :status', { status: filters.status });
        }
        if (filters?.departmentId) {
            queryBuilder.andWhere('budget.departmentId = :departmentId', { departmentId: filters.departmentId });
        }
        return queryBuilder
            .orderBy('budget.startDate', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const budget = await this.budgetRepository.findOne({
            where: { id },
            relations: ['budgetItems', 'budgetItems.account'],
        });
        if (!budget) {
            throw new common_1.NotFoundException(`Budget with ID ${id} not found`);
        }
        return budget;
    }
    async update(id, updateBudgetDto) {
        const budget = await this.findOne(id);
        Object.assign(budget, updateBudgetDto);
        return this.budgetRepository.save(budget);
    }
    async remove(id) {
        const budget = await this.findOne(id);
        if (budget.status === budget_entity_1.BudgetStatus.ACTIVE) {
            throw new common_1.BadRequestException('Cannot delete active budget');
        }
        await this.budgetRepository.remove(budget);
    }
    async addBudgetItem(budgetId, createBudgetItemDto) {
        const budget = await this.findOne(budgetId);
        const budgetItem = this.budgetItemRepository.create({
            ...createBudgetItemDto,
            budgetId,
        });
        const savedItem = await this.budgetItemRepository.save(budgetItem);
        await this.updateBudgetTotals(budgetId);
        return savedItem;
    }
    async updateBudgetItem(itemId, updateBudgetItemDto) {
        const budgetItem = await this.budgetItemRepository.findOne({
            where: { id: itemId },
            relations: ['budget'],
        });
        if (!budgetItem) {
            throw new common_1.NotFoundException(`Budget item with ID ${itemId} not found`);
        }
        Object.assign(budgetItem, updateBudgetItemDto);
        const savedItem = await this.budgetItemRepository.save(budgetItem);
        await this.updateBudgetTotals(budgetItem.budgetId);
        return savedItem;
    }
    async removeBudgetItem(itemId) {
        const budgetItem = await this.budgetItemRepository.findOne({
            where: { id: itemId },
        });
        if (!budgetItem) {
            throw new common_1.NotFoundException(`Budget item with ID ${itemId} not found`);
        }
        const budgetId = budgetItem.budgetId;
        await this.budgetItemRepository.remove(budgetItem);
        await this.updateBudgetTotals(budgetId);
    }
    async approveBudget(id, approvedBy) {
        const budget = await this.findOne(id);
        budget.status = budget_entity_1.BudgetStatus.APPROVED;
        budget.approvedBy = approvedBy;
        budget.approvedAt = new Date();
        return this.budgetRepository.save(budget);
    }
    async activateBudget(id) {
        const budget = await this.findOne(id);
        if (budget.status !== budget_entity_1.BudgetStatus.APPROVED) {
            throw new common_1.BadRequestException('Budget must be approved before activation');
        }
        budget.status = budget_entity_1.BudgetStatus.ACTIVE;
        return this.budgetRepository.save(budget);
    }
    async getBudgetVarianceReport(id) {
        const budget = await this.findOne(id);
        const report = {
            budget: {
                id: budget.id,
                name: budget.name,
                type: budget.type,
                period: {
                    startDate: budget.startDate,
                    endDate: budget.endDate,
                },
            },
            summary: {
                totalBudgeted: budget.totalBudgetAmount,
                totalActual: budget.totalActualAmount,
                totalVariance: budget.totalVariance,
                variancePercentage: budget.variancePercentage,
            },
            items: budget.budgetItems.map(item => ({
                id: item.id,
                itemName: item.itemName,
                accountName: item.account?.name,
                budgeted: item.budgetedAmount,
                actual: item.actualAmount,
                variance: item.variance,
                variancePercentage: item.variancePercentage,
                available: item.availableAmount,
            })),
        };
        return report;
    }
    async updateBudgetTotals(budgetId) {
        const budget = await this.findOne(budgetId);
        const totals = budget.budgetItems.reduce((acc, item) => ({
            budgeted: acc.budgeted + item.budgetedAmount,
            actual: acc.actual + item.actualAmount,
        }), { budgeted: 0, actual: 0 });
        budget.totalBudgetAmount = totals.budgeted;
        budget.totalActualAmount = totals.actual;
        budget.totalVariance = totals.actual - totals.budgeted;
        budget.variancePercentage = totals.budgeted > 0
            ? (budget.totalVariance / totals.budgeted) * 100
            : 0;
        await this.budgetRepository.save(budget);
    }
};
exports.BudgetService = BudgetService;
exports.BudgetService = BudgetService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(budget_entity_1.Budget)),
    __param(1, (0, typeorm_1.InjectRepository)(budget_item_entity_1.BudgetItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BudgetService);
//# sourceMappingURL=budget.service.js.map