import { Transaction } from './transaction.entity';
export declare enum AccountType {
    ASSET = "asset",
    LIABILITY = "liability",
    EQUITY = "equity",
    REVENUE = "revenue",
    EXPENSE = "expense"
}
export declare enum AccountSubType {
    CURRENT_ASSET = "current_asset",
    FIXED_ASSET = "fixed_asset",
    CASH = "cash",
    ACCOUNTS_RECEIVABLE = "accounts_receivable",
    INVENTORY = "inventory",
    CURRENT_LIABILITY = "current_liability",
    LONG_TERM_LIABILITY = "long_term_liability",
    ACCOUNTS_PAYABLE = "accounts_payable",
    OWNERS_EQUITY = "owners_equity",
    RETAINED_EARNINGS = "retained_earnings",
    SALES_REVENUE = "sales_revenue",
    SERVICE_REVENUE = "service_revenue",
    OTHER_REVENUE = "other_revenue",
    OPERATING_EXPENSE = "operating_expense",
    COST_OF_GOODS_SOLD = "cost_of_goods_sold",
    ADMINISTRATIVE_EXPENSE = "administrative_expense"
}
export declare class Account {
    id: string;
    accountNumber: string;
    name: string;
    description: string;
    type: AccountType;
    subType: AccountSubType;
    balance: number;
    debitBalance: number;
    creditBalance: number;
    isActive: boolean;
    isSystemAccount: boolean;
    parentAccountId: string;
    parentAccount: Account;
    childAccounts: Account[];
    debitTransactions: Transaction[];
    creditTransactions: Transaction[];
    currency: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
