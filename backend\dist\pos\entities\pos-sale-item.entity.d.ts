import { PosSale } from './pos-sale.entity';
export declare class PosSaleItem {
    id: string;
    saleId: string;
    sale: PosSale;
    productId: string;
    sku: string;
    productName: string;
    productDescription: string;
    quantity: number;
    unitPrice: number;
    originalPrice: number;
    discountAmount: number;
    discountPercentage: number;
    taxAmount: number;
    taxPercentage: number;
    lineTotal: number;
    costPrice: number;
    barcode: string;
    category: string;
    isReturned: boolean;
    returnedQuantity: number;
    notes: string;
    modifiers: any[];
    serialNumbers: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
