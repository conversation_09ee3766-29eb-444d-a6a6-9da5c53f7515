"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DemoAuthController = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcryptjs");
const companies = [];
const users = [];
let DemoAuthController = class DemoAuthController {
    jwtService;
    constructor(jwtService) {
        this.jwtService = jwtService;
    }
    async registerCompany(registerDto) {
        try {
            const existingCompany = companies.find(c => c.name === registerDto.companyName || c.slug === registerDto.companySlug);
            if (existingCompany) {
                throw new Error('Company name or slug already exists');
            }
            const existingUser = users.find(u => u.email === registerDto.adminEmail);
            if (existingUser) {
                throw new Error('User with this email already exists');
            }
            const company = {
                id: `company_${Date.now()}`,
                name: registerDto.companyName,
                slug: registerDto.companySlug,
                description: registerDto.companyDescription,
                website: registerDto.companyWebsite,
                phone: registerDto.companyPhone,
                email: registerDto.companyEmail,
                address: registerDto.companyAddress,
                city: registerDto.companyCity,
                country: registerDto.companyCountry,
                timezone: registerDto.companyTimezone,
                tenantId: `tenant_${Date.now()}`,
                isActive: true,
                createdAt: new Date(),
            };
            companies.push(company);
            const hashedPassword = await bcrypt.hash(registerDto.adminPassword, 12);
            const adminUser = {
                id: `user_${Date.now()}`,
                email: registerDto.adminEmail,
                password: hashedPassword,
                firstName: registerDto.adminFirstName,
                lastName: registerDto.adminLastName,
                phone: registerDto.adminPhone,
                role: 'admin',
                companyId: company.id,
                isActive: true,
                emailVerified: true,
                createdAt: new Date(),
            };
            users.push(adminUser);
            const payload = {
                sub: adminUser.id,
                email: adminUser.email,
                role: adminUser.role,
                companyId: company.id,
                tenantId: company.tenantId,
            };
            const token = this.jwtService.sign(payload);
            return {
                user: {
                    id: adminUser.id,
                    email: adminUser.email,
                    firstName: adminUser.firstName,
                    lastName: adminUser.lastName,
                    role: adminUser.role,
                    company: {
                        id: company.id,
                        name: company.name,
                        slug: company.slug,
                        tenantId: company.tenantId,
                    },
                },
                token,
            };
        }
        catch (error) {
            throw new Error(error.message || 'Registration failed');
        }
    }
    async login(loginDto) {
        try {
            const user = users.find(u => u.email === loginDto.email && u.isActive);
            if (!user) {
                throw new Error('Invalid credentials');
            }
            const isPasswordValid = await bcrypt.compare(loginDto.password, user.password);
            if (!isPasswordValid) {
                throw new Error('Invalid credentials');
            }
            const company = companies.find(c => c.id === user.companyId);
            const payload = {
                sub: user.id,
                email: user.email,
                role: user.role,
                companyId: user.companyId,
                tenantId: company.tenantId,
            };
            const token = this.jwtService.sign(payload);
            return {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    company: {
                        id: company.id,
                        name: company.name,
                        slug: company.slug,
                        tenantId: company.tenantId,
                    },
                },
                token,
            };
        }
        catch (error) {
            throw new Error(error.message || 'Login failed');
        }
    }
    async getProfile(req) {
        return {
            user: {
                userId: 'demo_user',
                email: '<EMAIL>',
                role: 'admin',
                companyId: 'demo_company',
                tenantId: 'demo_tenant',
            },
        };
    }
    async logout() {
        return {
            message: 'Logged out successfully',
        };
    }
};
exports.DemoAuthController = DemoAuthController;
__decorate([
    (0, common_1.Post)('register-company'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DemoAuthController.prototype, "registerCompany", null);
__decorate([
    (0, common_1.Post)('login'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DemoAuthController.prototype, "login", null);
__decorate([
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DemoAuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('logout'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DemoAuthController.prototype, "logout", null);
exports.DemoAuthController = DemoAuthController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], DemoAuthController);
//# sourceMappingURL=demo-auth.controller.js.map