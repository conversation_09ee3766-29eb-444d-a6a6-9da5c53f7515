"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionLetter = exports.LetterStatus = exports.LetterType = void 0;
const typeorm_1 = require("typeorm");
var LetterType;
(function (LetterType) {
    LetterType["INITIAL_NOTICE"] = "initial_notice";
    LetterType["REMINDER"] = "reminder";
    LetterType["FINAL_NOTICE"] = "final_notice";
    LetterType["DEMAND_LETTER"] = "demand_letter";
    LetterType["SETTLEMENT_OFFER"] = "settlement_offer";
    LetterType["PAYMENT_PLAN_OFFER"] = "payment_plan_offer";
    LetterType["LEGAL_NOTICE"] = "legal_notice";
    LetterType["CEASE_AND_DESIST"] = "cease_and_desist";
    LetterType["VALIDATION_NOTICE"] = "validation_notice";
    LetterType["CUSTOM"] = "custom";
})(LetterType || (exports.LetterType = LetterType = {}));
var LetterStatus;
(function (LetterStatus) {
    LetterStatus["DRAFT"] = "draft";
    LetterStatus["PENDING"] = "pending";
    LetterStatus["SENT"] = "sent";
    LetterStatus["DELIVERED"] = "delivered";
    LetterStatus["RETURNED"] = "returned";
    LetterStatus["FAILED"] = "failed";
})(LetterStatus || (exports.LetterStatus = LetterStatus = {}));
let CollectionLetter = class CollectionLetter {
    id;
    name;
    type;
    template;
    description;
    isActive;
    requiresApproval;
    variables;
    conditions;
    createdBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionLetter = CollectionLetter;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionLetter.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CollectionLetter.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: LetterType,
    }),
    __metadata("design:type", String)
], CollectionLetter.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CollectionLetter.prototype, "template", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionLetter.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CollectionLetter.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionLetter.prototype, "requiresApproval", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CollectionLetter.prototype, "variables", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionLetter.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionLetter.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionLetter.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionLetter.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionLetter.prototype, "updatedAt", void 0);
exports.CollectionLetter = CollectionLetter = __decorate([
    (0, typeorm_1.Entity)('collection_letters')
], CollectionLetter);
//# sourceMappingURL=collection-letter.entity.js.map