"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tooltip = exports.TooltipPosition = exports.TooltipType = void 0;
const typeorm_1 = require("typeorm");
var TooltipType;
(function (TooltipType) {
    TooltipType["HELP"] = "help";
    TooltipType["FEATURE_HIGHLIGHT"] = "feature_highlight";
    TooltipType["WARNING"] = "warning";
    TooltipType["TIP"] = "tip";
    TooltipType["ONBOARDING"] = "onboarding";
    TooltipType["ANNOUNCEMENT"] = "announcement";
})(TooltipType || (exports.TooltipType = TooltipType = {}));
var TooltipPosition;
(function (TooltipPosition) {
    TooltipPosition["TOP"] = "top";
    TooltipPosition["BOTTOM"] = "bottom";
    TooltipPosition["LEFT"] = "left";
    TooltipPosition["RIGHT"] = "right";
    TooltipPosition["AUTO"] = "auto";
})(TooltipPosition || (exports.TooltipPosition = TooltipPosition = {}));
let Tooltip = class Tooltip {
    id;
    title;
    content;
    type;
    targetElement;
    targetPage;
    position;
    isActive;
    isDismissible;
    autoHideDelay;
    targetRoles;
    conditions;
    priority;
    startDate;
    endDate;
    createdBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.Tooltip = Tooltip;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Tooltip.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Tooltip.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Tooltip.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TooltipType,
        default: TooltipType.HELP,
    }),
    __metadata("design:type", String)
], Tooltip.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Tooltip.prototype, "targetElement", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Tooltip.prototype, "targetPage", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TooltipPosition,
        default: TooltipPosition.AUTO,
    }),
    __metadata("design:type", String)
], Tooltip.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Tooltip.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Tooltip.prototype, "isDismissible", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], Tooltip.prototype, "autoHideDelay", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Tooltip.prototype, "targetRoles", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Tooltip.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Tooltip.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Tooltip.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Tooltip.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tooltip.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Tooltip.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Tooltip.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Tooltip.prototype, "updatedAt", void 0);
exports.Tooltip = Tooltip = __decorate([
    (0, typeorm_1.Entity)('tooltips')
], Tooltip);
//# sourceMappingURL=tooltip.entity.js.map