"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TutorialStep = exports.TutorialStepType = void 0;
const typeorm_1 = require("typeorm");
const tutorial_entity_1 = require("./tutorial.entity");
var TutorialStepType;
(function (TutorialStepType) {
    TutorialStepType["INSTRUCTION"] = "instruction";
    TutorialStepType["ACTION"] = "action";
    TutorialStepType["VERIFICATION"] = "verification";
    TutorialStepType["INFORMATION"] = "information";
    TutorialStepType["QUIZ"] = "quiz";
    TutorialStepType["EXERCISE"] = "exercise";
})(TutorialStepType || (exports.TutorialStepType = TutorialStepType = {}));
let TutorialStep = class TutorialStep {
    id;
    tutorialId;
    tutorial;
    title;
    content;
    type;
    sortOrder;
    media;
    interactive;
    validation;
    hint;
    isOptional;
    metadata;
    createdAt;
    updatedAt;
};
exports.TutorialStep = TutorialStep;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], TutorialStep.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], TutorialStep.prototype, "tutorialId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => tutorial_entity_1.Tutorial, tutorial => tutorial.steps, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'tutorialId' }),
    __metadata("design:type", tutorial_entity_1.Tutorial)
], TutorialStep.prototype, "tutorial", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], TutorialStep.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], TutorialStep.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TutorialStepType,
        default: TutorialStepType.INSTRUCTION,
    }),
    __metadata("design:type", String)
], TutorialStep.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], TutorialStep.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TutorialStep.prototype, "media", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TutorialStep.prototype, "interactive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TutorialStep.prototype, "validation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], TutorialStep.prototype, "hint", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], TutorialStep.prototype, "isOptional", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TutorialStep.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], TutorialStep.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], TutorialStep.prototype, "updatedAt", void 0);
exports.TutorialStep = TutorialStep = __decorate([
    (0, typeorm_1.Entity)('tutorial_steps')
], TutorialStep);
//# sourceMappingURL=tutorial-step.entity.js.map