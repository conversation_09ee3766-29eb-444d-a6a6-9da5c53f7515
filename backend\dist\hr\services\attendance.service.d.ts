import { Repository } from 'typeorm';
import { Attendance } from '../entities/attendance.entity';
export declare class AttendanceService {
    private attendanceRepository;
    constructor(attendanceRepository: Repository<Attendance>);
    create(createAttendanceDto: any): Promise<Attendance>;
    findAll(filters?: any): Promise<Attendance[]>;
    findOne(id: string): Promise<Attendance>;
    update(id: string, updateAttendanceDto: any): Promise<Attendance>;
    remove(id: string): Promise<void>;
    checkIn(employeeId: string, checkInData: any): Promise<Attendance>;
    checkOut(employeeId: string, checkOutData: any): Promise<Attendance>;
    getAttendanceReport(employeeId: string, startDate: Date, endDate: Date): Promise<any>;
    private calculateHoursWorked;
}
