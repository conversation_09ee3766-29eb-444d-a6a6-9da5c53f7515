export declare class CreateReturnedInvoiceItemDto {
    originalItemId: string;
    lineNumber: number;
    description: string;
    productCode?: string;
    originalQuantity: number;
    returnQuantity: number;
    unitPrice: number;
    discount?: number;
    taxType?: string;
    taxAmount?: number;
    lineTotal: number;
    unit?: string;
    returnReason?: string;
    itemCondition?: 'good' | 'damaged' | 'defective' | 'expired';
    notes?: string;
}
export declare class CreateReturnedInvoiceDto {
    customerId: string;
    originalInvoiceId: string;
    returnDate: string;
    reason: string;
    returnType: 'full' | 'partial';
    originalAmount: number;
    returnAmount: number;
    returnTaxAmount?: number;
    totalReturnAmount: number;
    status?: 'pending' | 'processing' | 'completed' | 'cancelled';
    refundMethod?: 'refund' | 'credit_note' | 'exchange';
    notes?: string;
    internalNotes?: string;
    processedBy?: string;
    processedDate?: string;
    refundReference?: string;
    items: CreateReturnedInvoiceItemDto[];
}
