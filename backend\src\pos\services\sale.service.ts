import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Sale, SaleStatus } from '../entities/sale.entity';
import { SaleItem } from '../entities/sale-item.entity';
import { Payment } from '../entities/payment.entity';

@Injectable()
export class SaleService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(SaleItem)
    private saleItemRepository: Repository<SaleItem>,
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
  ) {}

  async create(saleData: Partial<Sale>): Promise<Sale> {
    const saleNumber = await this.generateSaleNumber();
    const sale = this.saleRepository.create({
      ...saleData,
      saleNumber,
      status: SaleStatus.PENDING,
      saleDate: new Date(),
    });
    return this.saleRepository.save(sale);
  }

  async findAll(): Promise<Sale[]> {
    return this.saleRepository.find({
      relations: ['customer', 'cashier', 'items', 'items.product', 'payments'],
      order: { saleDate: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Sale> {
    const sale = await this.saleRepository.findOne({
      where: { id },
      relations: ['customer', 'cashier', 'items', 'items.product', 'payments'],
    });

    if (!sale) {
      throw new NotFoundException(`Sale with ID ${id} not found`);
    }

    return sale;
  }

  async update(id: string, updateData: Partial<Sale>): Promise<Sale> {
    await this.saleRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const sale = await this.findOne(id);
    
    if (sale.status === SaleStatus.COMPLETED) {
      throw new Error('Cannot delete completed sale');
    }

    await this.saleRepository.remove(sale);
  }

  async addItem(saleId: string, itemData: Partial<SaleItem>): Promise<SaleItem> {
    const sale = await this.findOne(saleId);
    
    if (sale.status === SaleStatus.COMPLETED) {
      throw new Error('Cannot add items to completed sale');
    }

    const item = this.saleItemRepository.create({
      ...itemData,
      saleId: sale.id,
    });

    const savedItem = await this.saleItemRepository.save(item);
    
    // Recalculate sale totals
    await this.recalculateSaleTotals(saleId);
    
    return savedItem;
  }

  async updateItem(itemId: string, updateData: Partial<SaleItem>): Promise<SaleItem> {
    const item = await this.saleItemRepository.findOne({
      where: { id: itemId },
      relations: ['sale'],
    });

    if (!item) {
      throw new NotFoundException(`Sale item with ID ${itemId} not found`);
    }

    if (item.sale.status === SaleStatus.COMPLETED) {
      throw new Error('Cannot update items in completed sale');
    }

    await this.saleItemRepository.update(itemId, updateData);
    
    // Recalculate sale totals
    await this.recalculateSaleTotals(item.sale.id);
    
    return this.saleItemRepository.findOne({
      where: { id: itemId },
      relations: ['product'],
    });
  }

  async removeItem(itemId: string): Promise<void> {
    const item = await this.saleItemRepository.findOne({
      where: { id: itemId },
      relations: ['sale'],
    });

    if (!item) {
      throw new NotFoundException(`Sale item with ID ${itemId} not found`);
    }

    if (item.sale.status === SaleStatus.COMPLETED) {
      throw new Error('Cannot remove items from completed sale');
    }

    const saleId = item.sale.id;
    await this.saleItemRepository.remove(item);
    
    // Recalculate sale totals
    await this.recalculateSaleTotals(saleId);
  }

  private async recalculateSaleTotals(saleId: string): Promise<void> {
    const sale = await this.findOne(saleId);
    
    const subtotal = sale.items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);

    const discountAmount = subtotal * (sale.discountPercentage || 0) / 100;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * (sale.taxRate || 0) / 100;
    const total = taxableAmount + taxAmount;

    await this.saleRepository.update(saleId, {
      subtotal,
      discountAmount,
      taxAmount,
      total,
    });
  }

  async addPayment(saleId: string, paymentData: Partial<Payment>): Promise<Payment> {
    const sale = await this.findOne(saleId);
    
    const payment = this.paymentRepository.create({
      ...paymentData,
      saleId: sale.id,
      paymentDate: new Date(),
    });

    const savedPayment = await this.paymentRepository.save(payment);
    
    // Check if sale is fully paid
    await this.checkSalePaymentStatus(saleId);
    
    return savedPayment;
  }

  private async checkSalePaymentStatus(saleId: string): Promise<void> {
    const sale = await this.findOne(saleId);
    
    const totalPaid = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const amountDue = sale.total - totalPaid;

    let status = sale.status;
    if (amountDue <= 0) {
      status = SaleStatus.COMPLETED;
    } else if (totalPaid > 0) {
      status = SaleStatus.PARTIALLY_PAID;
    }

    await this.saleRepository.update(saleId, {
      amountPaid: totalPaid,
      amountDue,
      status,
    });
  }

  async completeSale(saleId: string): Promise<Sale> {
    const sale = await this.findOne(saleId);
    
    if (sale.amountDue > 0) {
      throw new Error('Cannot complete sale with outstanding balance');
    }

    await this.saleRepository.update(saleId, {
      status: SaleStatus.COMPLETED,
      completedAt: new Date(),
    });

    return this.findOne(saleId);
  }

  async voidSale(saleId: string, reason: string): Promise<Sale> {
    const sale = await this.findOne(saleId);
    
    if (sale.status === SaleStatus.COMPLETED) {
      throw new Error('Cannot void completed sale. Use refund instead.');
    }

    await this.saleRepository.update(saleId, {
      status: SaleStatus.VOIDED,
      voidReason: reason,
      voidedAt: new Date(),
    });

    return this.findOne(saleId);
  }

  async refundSale(saleId: string, refundAmount: number, reason: string): Promise<Sale> {
    const sale = await this.findOne(saleId);
    
    if (sale.status !== SaleStatus.COMPLETED) {
      throw new Error('Can only refund completed sales');
    }

    if (refundAmount > sale.total) {
      throw new Error('Refund amount cannot exceed sale total');
    }

    await this.saleRepository.update(saleId, {
      status: SaleStatus.REFUNDED,
      refundAmount,
      refundReason: reason,
      refundedAt: new Date(),
    });

    return this.findOne(saleId);
  }

  async findByCustomer(customerId: string): Promise<Sale[]> {
    return this.saleRepository.find({
      where: { customerId },
      relations: ['items', 'payments'],
      order: { saleDate: 'DESC' },
    });
  }

  async findByCashier(cashierId: string): Promise<Sale[]> {
    return this.saleRepository.find({
      where: { cashierId },
      relations: ['customer', 'items'],
      order: { saleDate: 'DESC' },
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Sale[]> {
    return this.saleRepository
      .createQueryBuilder('sale')
      .leftJoinAndSelect('sale.customer', 'customer')
      .leftJoinAndSelect('sale.cashier', 'cashier')
      .leftJoinAndSelect('sale.items', 'items')
      .leftJoinAndSelect('sale.payments', 'payments')
      .where('sale.saleDate BETWEEN :startDate AND :endDate', { startDate, endDate })
      .orderBy('sale.saleDate', 'DESC')
      .getMany();
  }

  async getSalesStatistics(startDate?: Date, endDate?: Date): Promise<any> {
    const query = this.saleRepository.createQueryBuilder('sale');

    if (startDate && endDate) {
      query.where('sale.saleDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const totalSales = await query.getCount();
    const completedSales = await query
      .clone()
      .andWhere('sale.status = :status', { status: SaleStatus.COMPLETED })
      .getCount();

    const salesData = await query
      .clone()
      .andWhere('sale.status = :status', { status: SaleStatus.COMPLETED })
      .select([
        'SUM(sale.total) as totalRevenue',
        'AVG(sale.total) as averageSaleValue',
        'COUNT(DISTINCT sale.customerId) as uniqueCustomers',
      ])
      .getRawOne();

    return {
      totalSales,
      completedSales,
      totalRevenue: parseFloat(salesData.totalRevenue) || 0,
      averageSaleValue: parseFloat(salesData.averageSaleValue) || 0,
      uniqueCustomers: parseInt(salesData.uniqueCustomers) || 0,
    };
  }

  async getTopSellingProducts(limit: number = 10): Promise<any[]> {
    const result = await this.saleItemRepository
      .createQueryBuilder('item')
      .leftJoin('item.product', 'product')
      .leftJoin('item.sale', 'sale')
      .where('sale.status = :status', { status: SaleStatus.COMPLETED })
      .select([
        'product.id as productId',
        'product.name as productName',
        'SUM(item.quantity) as totalQuantity',
        'SUM(item.quantity * item.unitPrice) as totalRevenue',
      ])
      .groupBy('product.id, product.name')
      .orderBy('totalQuantity', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map(row => ({
      productId: row.productId,
      productName: row.productName,
      totalQuantity: parseInt(row.totalQuantity),
      totalRevenue: parseFloat(row.totalRevenue),
    }));
  }

  private async generateSaleNumber(): Promise<string> {
    const count = await this.saleRepository.count();
    const sequence = (count + 1).toString().padStart(8, '0');
    const year = new Date().getFullYear();
    return `SAL-${year}-${sequence}`;
  }

  async getDashboardMetrics(): Promise<any> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    const todayStats = await this.getSalesStatistics(startOfDay, endOfDay);
    const overallStats = await this.getSalesStatistics();

    return {
      today: todayStats,
      overall: overallStats,
    };
  }
}
