import { PosTerminal } from './pos-terminal.entity';
import { PosSaleItem } from './pos-sale-item.entity';
import { PosPayment } from './pos-payment.entity';
import { PosDiscount } from './pos-discount.entity';
import { PosTax } from './pos-tax.entity';
import { PosCustomer } from './pos-customer.entity';
export declare enum SaleStatus {
    PENDING = "pending",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    REFUNDED = "refunded",
    PARTIALLY_REFUNDED = "partially_refunded",
    ON_HOLD = "on_hold"
}
export declare enum SaleType {
    REGULAR = "regular",
    RETURN = "return",
    EXCHANGE = "exchange",
    LAYAWAY = "layaway",
    SPECIAL_ORDER = "special_order"
}
export declare class PosSale {
    id: string;
    saleNumber: string;
    terminalId: string;
    terminal: PosTerminal;
    customerId: string;
    customer: PosCustomer;
    type: SaleType;
    status: SaleStatus;
    saleDateTime: Date;
    totalItems: number;
    subtotal: number;
    totalDiscount: number;
    totalTax: number;
    totalAmount: number;
    amountPaid: number;
    changeAmount: number;
    amountDue: number;
    currency: string;
    cashierId: string;
    shiftId: string;
    notes: string;
    customerInfo: any;
    isVoided: boolean;
    voidedBy: string;
    voidedAt: Date;
    voidReason: string;
    items: PosSaleItem[];
    payments: PosPayment[];
    discounts: PosDiscount[];
    taxes: PosTax[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
