"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemSetting = exports.SettingCategory = exports.SettingType = void 0;
const typeorm_1 = require("typeorm");
var SettingType;
(function (SettingType) {
    SettingType["STRING"] = "string";
    SettingType["NUMBER"] = "number";
    SettingType["BOOLEAN"] = "boolean";
    SettingType["JSON"] = "json";
    SettingType["ARRAY"] = "array";
    SettingType["DATE"] = "date";
    SettingType["EMAIL"] = "email";
    SettingType["URL"] = "url";
    SettingType["PASSWORD"] = "password";
})(SettingType || (exports.SettingType = SettingType = {}));
var SettingCategory;
(function (SettingCategory) {
    SettingCategory["GENERAL"] = "general";
    SettingCategory["SECURITY"] = "security";
    SettingCategory["EMAIL"] = "email";
    SettingCategory["NOTIFICATION"] = "notification";
    SettingCategory["INTEGRATION"] = "integration";
    SettingCategory["APPEARANCE"] = "appearance";
    SettingCategory["LOCALIZATION"] = "localization";
    SettingCategory["BACKUP"] = "backup";
    SettingCategory["PERFORMANCE"] = "performance";
    SettingCategory["CUSTOM"] = "custom";
})(SettingCategory || (exports.SettingCategory = SettingCategory = {}));
let SystemSetting = class SystemSetting {
    id;
    key;
    value;
    defaultValue;
    type;
    category;
    name;
    description;
    isRequired;
    isEncrypted;
    isEditable;
    isSystem;
    validationRules;
    options;
    sortOrder;
    lastModifiedBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.SystemSetting = SystemSetting;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], SystemSetting.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, unique: true }),
    __metadata("design:type", String)
], SystemSetting.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], SystemSetting.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], SystemSetting.prototype, "defaultValue", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SettingType,
        default: SettingType.STRING,
    }),
    __metadata("design:type", String)
], SystemSetting.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SettingCategory,
        default: SettingCategory.GENERAL,
    }),
    __metadata("design:type", String)
], SystemSetting.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], SystemSetting.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], SystemSetting.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], SystemSetting.prototype, "isRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], SystemSetting.prototype, "isEncrypted", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], SystemSetting.prototype, "isEditable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], SystemSetting.prototype, "isSystem", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], SystemSetting.prototype, "validationRules", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], SystemSetting.prototype, "options", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], SystemSetting.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SystemSetting.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], SystemSetting.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SystemSetting.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], SystemSetting.prototype, "updatedAt", void 0);
exports.SystemSetting = SystemSetting = __decorate([
    (0, typeorm_1.Entity)('system_settings')
], SystemSetting);
//# sourceMappingURL=system-setting.entity.js.map