import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PosSale } from './pos-sale.entity';

export enum DiscountType {
  PERCENTAGE = 'percentage',
  FIXED_AMOUNT = 'fixed_amount',
  BUY_X_GET_Y = 'buy_x_get_y',
  BULK_DISCOUNT = 'bulk_discount',
}

export enum DiscountScope {
  ITEM = 'item',
  TRANSACTION = 'transaction',
  CATEGORY = 'category',
}

@Entity('pos_discounts')
export class PosDiscount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  saleId: string;

  @ManyToOne(() => PosSale, sale => sale.discounts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'saleId' })
  sale: PosSale;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: DiscountType,
  })
  type: DiscountType;

  @Column({
    type: 'enum',
    enum: DiscountScope,
  })
  scope: DiscountScope;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  percentage: number;

  @Column({ nullable: true })
  appliedBy: string;

  @Column({ type: 'text', nullable: true })
  reason: string;

  @Column({ nullable: true })
  promotionId: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
