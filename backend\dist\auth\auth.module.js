"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const auth_service_1 = require("./auth.service");
const auth_controller_1 = require("./auth.controller");
const jwt_strategy_1 = require("./strategies/jwt.strategy");
const user_module_1 = require("../user/user.module");
const company_module_1 = require("../company/company.module");
const permission_entity_1 = require("./entities/permission.entity");
const role_entity_1 = require("./entities/role.entity");
const user_entity_1 = require("../user/entities/user.entity");
const permission_service_1 = require("./services/permission.service");
const role_service_1 = require("./services/role.service");
const user_management_service_1 = require("./services/user-management.service");
const permission_controller_1 = require("./controllers/permission.controller");
const role_controller_1 = require("./controllers/role.controller");
const user_management_controller_1 = require("./controllers/user-management.controller");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([permission_entity_1.Permission, role_entity_1.Role, user_entity_1.User]),
            passport_1.PassportModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            user_module_1.UserModule,
            company_module_1.CompanyModule,
        ],
        controllers: [
            auth_controller_1.AuthController,
            permission_controller_1.PermissionController,
            role_controller_1.RoleController,
            user_management_controller_1.UserManagementController,
        ],
        providers: [
            auth_service_1.AuthService,
            jwt_strategy_1.JwtStrategy,
            permission_service_1.PermissionService,
            role_service_1.RoleService,
            user_management_service_1.UserManagementService,
        ],
        exports: [
            auth_service_1.AuthService,
            permission_service_1.PermissionService,
            role_service_1.RoleService,
            user_management_service_1.UserManagementService,
        ],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map