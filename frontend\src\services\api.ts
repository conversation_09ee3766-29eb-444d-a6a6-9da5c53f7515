import axios from 'axios';
import { LoginRequest, RegisterCompanyRequest, AuthResponse } from '../types/auth';
import { DashboardData, StatsData } from '../types/dashboard';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/login', data);
      return response.data;
    } catch (error: any) {
      // Mock response for demo purposes when backend is not available
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        // Simulate successful login for demo
        return {
          user: {
            id: '1',
            email: data.email,
            firstName: 'Demo',
            lastName: 'User',
            role: 'admin',
            tenantId: 'demo-tenant',
          },
          token: 'demo-token-' + Date.now(),
        };
      }
      throw error;
    }
  },

  registerCompany: async (data: RegisterCompanyRequest): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/register-company', data);
      return response.data;
    } catch (error: any) {
      // Mock response for demo purposes when backend is not available
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        // Simulate successful registration for demo
        return {
          user: {
            id: '1',
            email: data.adminEmail,
            firstName: data.adminFirstName,
            lastName: data.adminLastName,
            role: 'admin',
            tenantId: data.companySlug,
          },
          token: 'demo-token-' + Date.now(),
        };
      }
      throw error;
    }
  },

  getProfile: async () => {
    try {
      const response = await api.get('/auth/profile');
      return response.data;
    } catch (error: any) {
      // Mock response for demo purposes
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
      }
      throw error;
    }
  },

  logout: async () => {
    try {
      const response = await api.post('/auth/logout');
      return response.data;
    } catch (error: any) {
      // Always allow logout even if backend is not available
      return { success: true };
    }
  },
};

// Dashboard API
export const dashboardAPI = {
  getDashboard: async (): Promise<DashboardData> => {
    try {
      const response = await api.get('/dashboard');
      return response.data;
    } catch (error: any) {
      // Mock response for demo purposes when backend is not available
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          totalRevenue: 125000,
          totalCustomers: 45,
          totalOrders: 128,
          pendingTasks: 12,
          stats: {
            totalUsers: 45,
            totalProjects: 12,
            totalSales: 128,
            totalRevenue: 125000,
          },
          recentActivities: [
            {
              id: '1',
              type: 'sale',
              description: 'New sale completed',
              timestamp: new Date().toISOString(),
              amount: 2500,
            },
            {
              id: '2',
              type: 'customer',
              description: 'New customer registered',
              timestamp: new Date().toISOString(),
            },
          ],
        };
      }
      throw error;
    }
  },

  getStats: async (): Promise<StatsData> => {
    try {
      const response = await api.get('/dashboard/stats');
      return response.data;
    } catch (error: any) {
      // Mock response for demo purposes
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          salesGrowth: 12.5,
          customerGrowth: 8.3,
          revenueGrowth: 15.2,
          orderGrowth: 6.7,
          departments: {
            sales: {
              active: 25,
              total: 30,
            },
            projects: {
              active: 12,
              total: 18,
            },
            hr: {
              employees: 45,
              total: 50,
            },
            finance: {
              income: 125000,
              expenses: 85000,
            },
          },
        };
      }
      throw error;
    }
  },
};

// Sales API
export const salesAPI = {
  // Customers
  getCustomers: async (params?: { page?: number; limit?: number; search?: string }) => {
    try {
      const response = await api.get('/sales/customers', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: [
            {
              id: '1',
              name: 'ABC Corporation',
              email: '<EMAIL>',
              phone: '******-0123',
              type: 'Commercial',
              status: 'Active',
              totalOrders: 15,
              totalValue: 45000,
              createdAt: '2024-01-15',
            },
            {
              id: '2',
              name: 'John Smith',
              email: '<EMAIL>',
              phone: '******-0124',
              type: 'Individual',
              status: 'Active',
              totalOrders: 8,
              totalValue: 12000,
              createdAt: '2024-02-10',
            },
          ],
          total: 2,
          page: 1,
          limit: 10,
        };
      }
      throw error;
    }
  },

  createCustomer: async (data: any) => {
    try {
      const response = await api.post('/sales/customers', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          data: { id: Date.now().toString(), ...data },
          message: 'Customer created successfully',
        };
      }
      throw error;
    }
  },

  // Invoices
  getInvoices: async (params?: { page?: number; limit?: number; status?: string }) => {
    try {
      const response = await api.get('/sales/invoices', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: [
            {
              id: 'INV-2024-001',
              customerId: '1',
              customerName: 'ABC Corporation',
              amount: 15000,
              status: 'Paid',
              dueDate: '2024-06-15',
              createdAt: '2024-05-15',
              items: [
                { description: 'Web Development', quantity: 1, rate: 15000, amount: 15000 },
              ],
            },
            {
              id: 'INV-2024-002',
              customerId: '2',
              customerName: 'John Smith',
              amount: 3500,
              status: 'Pending',
              dueDate: '2024-06-30',
              createdAt: '2024-05-20',
              items: [
                { description: 'Consulting Services', quantity: 10, rate: 350, amount: 3500 },
              ],
            },
          ],
          total: 2,
          page: 1,
          limit: 10,
        };
      }
      throw error;
    }
  },

  createInvoice: async (data: any) => {
    try {
      const response = await api.post('/sales/invoices', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          data: { id: `INV-2024-${Date.now()}`, ...data },
          message: 'Invoice created successfully',
        };
      }
      throw error;
    }
  },
};

// Projects API
export const projectsAPI = {
  getProjects: async (params?: { page?: number; limit?: number; status?: string }) => {
    try {
      const response = await api.get('/projects', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: [
            {
              id: '1',
              name: 'Website Redesign',
              description: 'Complete website redesign for ABC Corporation',
              status: 'In Progress',
              priority: 'High',
              startDate: '2024-05-01',
              endDate: '2024-07-01',
              progress: 65,
              budget: 50000,
              spent: 32500,
              teamMembers: ['John Doe', 'Jane Smith', 'Mike Johnson'],
              tasks: 24,
              completedTasks: 16,
            },
            {
              id: '2',
              name: 'Mobile App Development',
              description: 'iOS and Android app development',
              status: 'Planning',
              priority: 'Medium',
              startDate: '2024-06-01',
              endDate: '2024-09-01',
              progress: 15,
              budget: 75000,
              spent: 11250,
              teamMembers: ['Sarah Wilson', 'Tom Brown'],
              tasks: 18,
              completedTasks: 3,
            },
          ],
          total: 2,
          page: 1,
          limit: 10,
        };
      }
      throw error;
    }
  },

  createProject: async (data: any) => {
    try {
      const response = await api.post('/projects', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          data: { id: Date.now().toString(), ...data, progress: 0 },
          message: 'Project created successfully',
        };
      }
      throw error;
    }
  },
};

// HR API
export const hrAPI = {
  getEmployees: async (params?: { page?: number; limit?: number; department?: string }) => {
    try {
      const response = await api.get('/hr/employees', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: [
            {
              id: '1',
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              phone: '******-0101',
              department: 'Engineering',
              position: 'Senior Developer',
              salary: 85000,
              hireDate: '2023-01-15',
              status: 'Active',
              manager: 'Jane Smith',
            },
            {
              id: '2',
              firstName: 'Jane',
              lastName: 'Smith',
              email: '<EMAIL>',
              phone: '******-0102',
              department: 'Engineering',
              position: 'Engineering Manager',
              salary: 120000,
              hireDate: '2022-03-10',
              status: 'Active',
              manager: 'CEO',
            },
          ],
          total: 2,
          page: 1,
          limit: 10,
        };
      }
      throw error;
    }
  },

  createEmployee: async (data: any) => {
    try {
      const response = await api.post('/hr/employees', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          data: { id: Date.now().toString(), ...data },
          message: 'Employee created successfully',
        };
      }
      throw error;
    }
  },
};

// Finance API
export const financeAPI = {
  getOverview: async () => {
    try {
      const response = await api.get('/finance/overview');
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          totalRevenue: 2450000,
          totalExpenses: 1680000,
          netProfit: 770000,
          cashFlow: 125000,
          accountsReceivable: 85000,
          accountsPayable: 45000,
          monthlyRevenue: [
            { month: 'Jan', revenue: 180000 },
            { month: 'Feb', revenue: 195000 },
            { month: 'Mar', revenue: 210000 },
            { month: 'Apr', revenue: 225000 },
            { month: 'May', revenue: 240000 },
          ],
        };
      }
      throw error;
    }
  },

  getTransactions: async (params?: { page?: number; limit?: number; type?: string }) => {
    try {
      const response = await api.get('/finance/transactions', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: [
            {
              id: '1',
              type: 'Income',
              description: 'Payment from ABC Corporation',
              amount: 15000,
              date: '2024-05-20',
              category: 'Sales',
              status: 'Completed',
            },
            {
              id: '2',
              type: 'Expense',
              description: 'Office Rent',
              amount: 5000,
              date: '2024-05-01',
              category: 'Operations',
              status: 'Completed',
            },
          ],
          total: 2,
          page: 1,
          limit: 10,
        };
      }
      throw error;
    }
  },
};

// Inventory API
export const inventoryAPI = {
  getItems: async (params?: { page?: number; limit?: number; category?: string }) => {
    try {
      const response = await api.get('/inventory/items', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: [
            {
              id: '1',
              name: 'Wireless Mouse',
              sku: 'WM-001',
              category: 'Electronics',
              currentStock: 45,
              minStock: 10,
              maxStock: 100,
              unitPrice: 25.99,
              totalValue: 1169.55,
              status: 'In Stock',
              location: 'A1-B2',
            },
            {
              id: '2',
              name: 'Office Chair',
              sku: 'OC-002',
              category: 'Furniture',
              currentStock: 5,
              minStock: 8,
              maxStock: 50,
              unitPrice: 199.99,
              totalValue: 999.95,
              status: 'Low Stock',
              location: 'B2-C3',
            },
          ],
          total: 2,
          page: 1,
          limit: 10,
        };
      }
      throw error;
    }
  },

  createItem: async (data: any) => {
    try {
      const response = await api.post('/inventory/items', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          data: { id: Date.now().toString(), ...data },
          message: 'Inventory item created successfully',
        };
      }
      throw error;
    }
  },
};

// Analytics API
export const analyticsAPI = {
  getBusinessMetrics: async (period: string = '30d') => {
    try {
      const response = await api.get(`/analytics/business-metrics?period=${period}`);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          totalRevenue: 2450000,
          totalExpenses: 1680000,
          netProfit: 770000,
          totalCustomers: 1247,
          totalProjects: 89,
          totalEmployees: 156,
          revenueGrowth: 12.5,
          customerGrowth: 8.3,
          profitGrowth: 18.2,
          departmentMetrics: [
            {
              department: 'Sales',
              revenue: 850000,
              expenses: 320000,
              profit: 530000,
              efficiency: 95,
              trend: 'up',
            },
            {
              department: 'Projects',
              revenue: 650000,
              expenses: 280000,
              profit: 370000,
              efficiency: 88,
              trend: 'up',
            },
          ],
        };
      }
      throw error;
    }
  },

  generateReport: async (config: any) => {
    try {
      const response = await api.post('/analytics/generate-report', config);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          reportId: Date.now().toString(),
          message: 'Report generated successfully',
          downloadUrl: '#',
        };
      }
      throw error;
    }
  },

  // Audit Reports
  getAuditReports: async (params: any) => {
    try {
      const response = await api.get('/analytics/audit-reports', { params });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: {
            reports: [
              {
                id: '1',
                year: 2024,
                quarter: 2,
                reportType: 'BSA_AML',
                status: 'Completed',
                createdDate: '2024-05-01',
                completedDate: '2024-05-15',
                auditor: 'John Smith, CPA',
                department: 'Compliance',
                scope: 'Institution_Wide',
                auditCategory: 'Financial_Institution',
                totalFindings: 3,
                criticalFindings: 0,
                complianceScore: 95,
                recommendations: 5,
                riskLevel: 'Low',
                regulatoryFramework: ['BSA', 'USA PATRIOT Act', 'FinCEN'],
                financialData: {
                  totalIncome: 1500000,
                  totalExpenses: 850000,
                  netResult: 650000,
                  auditedTransactions: 15000,
                  discrepancies: 2,
                  materialMisstatements: 0,
                },
                specializedData: {
                  bsaAmlData: {
                    suspiciousActivityReports: 12,
                    currencyTransactionReports: 145,
                    customerDueDiligenceReviews: 89,
                    sanctionsScreeningResults: 100,
                    complianceViolations: 0,
                  },
                },
              },
              {
                id: '2',
                year: 2024,
                quarter: 1,
                reportType: 'IT_Security',
                status: 'In Review',
                createdDate: '2024-03-01',
                auditor: 'Sarah Wilson, CISA',
                department: 'IT',
                scope: 'Institution_Wide',
                auditCategory: 'Technology',
                totalFindings: 8,
                criticalFindings: 2,
                complianceScore: 82,
                recommendations: 12,
                riskLevel: 'Medium',
                regulatoryFramework: ['FFIEC Cybersecurity'],
                specializedData: {
                  itSecurityData: {
                    vulnerabilitiesFound: 15,
                    securityIncidents: 2,
                    complianceGaps: 5,
                    penetrationTestResults: 'Moderate Risk',
                    dataBreachRisk: 'Low',
                  },
                },
              },
            ],
            findings: [
              {
                id: '1',
                category: 'BSA/AML Compliance',
                severity: 'Medium',
                title: 'Customer Due Diligence Documentation',
                description: 'Some customer files missing updated beneficial ownership information',
                recommendation: 'Update all customer files with current beneficial ownership documentation',
                status: 'In Progress',
                assignedTo: 'Compliance Team',
                dueDate: '2024-06-30',
                department: 'Compliance',
              },
              {
                id: '2',
                category: 'IT Security',
                severity: 'High',
                title: 'Network Vulnerability',
                description: 'Critical security patch missing on core banking system',
                recommendation: 'Apply security patch immediately and implement patch management process',
                status: 'Open',
                assignedTo: 'IT Security Team',
                dueDate: '2024-06-15',
                department: 'IT',
              },
            ],
            total: 2,
            page: 1,
            limit: 10,
            metrics: {
              totalReports: 2,
              completedReports: 1,
              pendingReports: 1,
              averageComplianceScore: 88,
              totalFindings: 11,
              resolvedFindings: 6,
              criticalFindings: 2,
              improvementTrend: 5,
              financialMetrics: {
                totalAuditedIncome: 1500000,
                totalAuditedExpenses: 850000,
                totalDiscrepancies: 2,
                materialMisstatements: 0,
                financialAccuracy: 98,
                departmentCoverage: 2,
              },
              financialInstitutionMetrics: {
                bsaAmlCompliance: 95,
                creditRiskScore: 88,
                operationalRiskLevel: 'Low',
                itSecurityScore: 82,
                regulatoryViolations: 0,
                totalSuspiciousActivities: 12,
                networkVulnerabilities: 15,
                trustOperationsCompliance: 92,
                soxComplianceScore: 96,
                enterpriseRiskRating: 'Low',
              },
            },
          },
        };
      }
      throw error;
    }
  },

  getAuditMetrics: async (year?: number) => {
    try {
      const response = await api.get('/analytics/audit-reports/metrics', { params: { year } });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: {
            totalReports: 2,
            completedReports: 1,
            pendingReports: 1,
            averageComplianceScore: 88,
            totalFindings: 11,
            resolvedFindings: 6,
            criticalFindings: 2,
            improvementTrend: 5,
            financialMetrics: {
              totalAuditedIncome: 1500000,
              totalAuditedExpenses: 850000,
              totalDiscrepancies: 2,
              materialMisstatements: 0,
              financialAccuracy: 98,
              departmentCoverage: 2,
            },
            financialInstitutionMetrics: {
              bsaAmlCompliance: 95,
              creditRiskScore: 88,
              operationalRiskLevel: 'Low',
              itSecurityScore: 82,
              regulatoryViolations: 0,
              totalSuspiciousActivities: 12,
              networkVulnerabilities: 15,
              trustOperationsCompliance: 92,
              soxComplianceScore: 96,
              enterpriseRiskRating: 'Low',
            },
          },
        };
      }
      throw error;
    }
  },

  createAuditReport: async (data: any) => {
    try {
      const response = await api.post('/analytics/audit-reports', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: {
            id: Date.now().toString(),
            ...data,
            status: 'Draft',
            createdDate: new Date().toISOString(),
            totalFindings: 0,
            criticalFindings: 0,
            complianceScore: 0,
            recommendations: 0,
          },
        };
      }
      throw error;
    }
  },

  updateAuditReport: async (id: string, data: any) => {
    try {
      const response = await api.put(`/analytics/audit-reports/${id}`, data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: {
            id,
            ...data,
            updatedDate: new Date().toISOString(),
          },
        };
      }
      throw error;
    }
  },

  deleteAuditReport: async (id: string) => {
    try {
      const response = await api.delete(`/analytics/audit-reports/${id}`);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return { data: { success: true } };
      }
      throw error;
    }
  },

  exportAuditReport: async (id: string, format: 'pdf' | 'excel') => {
    try {
      const response = await api.post(`/analytics/audit-reports/${id}/export`, { format });
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          data: {
            downloadUrl: '#',
            filename: `audit-report-${id}.${format}`,
          },
        };
      }
      throw error;
    }
  },
};

// Settings API
export const settingsAPI = {
  getCompanySettings: async () => {
    try {
      const response = await api.get('/settings/company');
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          name: 'ZaidanOne Corporation',
          email: '<EMAIL>',
          phone: '+****************',
          address: '123 Business Street, City, State 12345',
          website: 'https://www.zaidanone.com',
          taxId: 'TAX123456789',
          currency: 'USD',
          timezone: 'America/New_York',
          language: 'English',
        };
      }
      throw error;
    }
  },

  updateCompanySettings: async (data: any) => {
    try {
      const response = await api.put('/settings/company', data);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return {
          success: true,
          message: 'Company settings updated successfully',
        };
      }
      throw error;
    }
  },

  getUsers: async () => {
    try {
      const response = await api.get('/settings/users');
      return response.data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        return [
          {
            id: '1',
            name: 'John Smith',
            email: '<EMAIL>',
            role: 'Administrator',
            department: 'IT',
            status: 'active',
            lastLogin: '2024-05-20 14:30',
          },
          {
            id: '2',
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            role: 'Manager',
            department: 'Sales',
            status: 'active',
            lastLogin: '2024-05-20 09:15',
          },
        ];
      }
      throw error;
    }
  },
};

export default api;
