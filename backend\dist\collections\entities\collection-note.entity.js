"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionNote = exports.NoteType = void 0;
const typeorm_1 = require("typeorm");
const collection_case_entity_1 = require("./collection-case.entity");
var NoteType;
(function (NoteType) {
    NoteType["GENERAL"] = "general";
    NoteType["CONTACT"] = "contact";
    NoteType["PAYMENT"] = "payment";
    NoteType["DISPUTE"] = "dispute";
    NoteType["LEGAL"] = "legal";
    NoteType["INTERNAL"] = "internal";
    NoteType["CUSTOMER"] = "customer";
    NoteType["SYSTEM"] = "system";
})(NoteType || (exports.NoteType = NoteType = {}));
let CollectionNote = class CollectionNote {
    id;
    caseId;
    case;
    type;
    title;
    content;
    createdBy;
    isPrivate;
    isPinned;
    tags;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionNote = CollectionNote;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionNote.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionNote.prototype, "caseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_case_entity_1.CollectionCase, collectionCase => collectionCase.notes),
    (0, typeorm_1.JoinColumn)({ name: 'caseId' }),
    __metadata("design:type", collection_case_entity_1.CollectionCase)
], CollectionNote.prototype, "case", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: NoteType,
        default: NoteType.GENERAL,
    }),
    __metadata("design:type", String)
], CollectionNote.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], CollectionNote.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CollectionNote.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionNote.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionNote.prototype, "isPrivate", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionNote.prototype, "isPinned", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CollectionNote.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionNote.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionNote.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionNote.prototype, "updatedAt", void 0);
exports.CollectionNote = CollectionNote = __decorate([
    (0, typeorm_1.Entity)('collection_notes')
], CollectionNote);
//# sourceMappingURL=collection-note.entity.js.map