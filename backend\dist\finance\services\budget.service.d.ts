import { Repository } from 'typeorm';
import { Budget } from '../entities/budget.entity';
import { BudgetItem } from '../entities/budget-item.entity';
export declare class BudgetService {
    private budgetRepository;
    private budgetItemRepository;
    constructor(budgetRepository: Repository<Budget>, budgetItemRepository: Repository<BudgetItem>);
    create(createBudgetDto: any): Promise<Budget>;
    findAll(filters?: any): Promise<Budget[]>;
    findOne(id: string): Promise<Budget>;
    update(id: string, updateBudgetDto: any): Promise<Budget>;
    remove(id: string): Promise<void>;
    addBudgetItem(budgetId: string, createBudgetItemDto: any): Promise<BudgetItem>;
    updateBudgetItem(itemId: string, updateBudgetItemDto: any): Promise<BudgetItem>;
    removeBudgetItem(itemId: string): Promise<void>;
    approveBudget(id: string, approvedBy: string): Promise<Budget>;
    activateBudget(id: string): Promise<Budget>;
    getBudgetVarianceReport(id: string): Promise<any>;
    private updateBudgetTotals;
}
