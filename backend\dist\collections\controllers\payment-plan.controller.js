"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentPlanController = void 0;
const common_1 = require("@nestjs/common");
const payment_plan_service_1 = require("../services/payment-plan.service");
const payment_plan_entity_1 = require("../entities/payment-plan.entity");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let PaymentPlanController = class PaymentPlanController {
    paymentPlanService;
    constructor(paymentPlanService) {
        this.paymentPlanService = paymentPlanService;
    }
    async create(createPaymentPlanDto) {
        return this.paymentPlanService.create(createPaymentPlanDto);
    }
    async findAll(status) {
        if (status) {
            return this.paymentPlanService.findByStatus(status);
        }
        return this.paymentPlanService.findAll();
    }
    async getStatistics() {
        return this.paymentPlanService.getPaymentPlanStatistics();
    }
    async getOverdueInstallments() {
        return this.paymentPlanService.getOverdueInstallments();
    }
    async getUpcomingInstallments(days) {
        const daysAhead = days ? parseInt(days) : 7;
        return this.paymentPlanService.getUpcomingInstallments(daysAhead);
    }
    async findByCustomer(customerId) {
        return this.paymentPlanService.findByCustomer(customerId);
    }
    async findOne(id) {
        return this.paymentPlanService.findOne(id);
    }
    async getPaymentPlanSummary(id) {
        return this.paymentPlanService.getPaymentPlanSummary(id);
    }
    async createInstallments(id, installmentData) {
        return this.paymentPlanService.createInstallments(id, installmentData.numberOfInstallments);
    }
    async recordPayment(installmentId, paymentData) {
        return this.paymentPlanService.recordPayment(installmentId, paymentData.amount, new Date(paymentData.paymentDate));
    }
    async update(id, updatePaymentPlanDto) {
        return this.paymentPlanService.update(id, updatePaymentPlanDto);
    }
    async markAsDefaulted(id, defaultData) {
        return this.paymentPlanService.markPlanAsDefaulted(id, defaultData.reason);
    }
    async remove(id) {
        return this.paymentPlanService.remove(id);
    }
};
exports.PaymentPlanController = PaymentPlanController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('overdue-installments'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "getOverdueInstallments", null);
__decorate([
    (0, common_1.Get)('upcoming-installments'),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "getUpcomingInstallments", null);
__decorate([
    (0, common_1.Get)('customer/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "findByCustomer", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/summary'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "getPaymentPlanSummary", null);
__decorate([
    (0, common_1.Post)(':id/installments'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "createInstallments", null);
__decorate([
    (0, common_1.Post)('installments/:installmentId/payment'),
    __param(0, (0, common_1.Param)('installmentId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "recordPayment", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/default'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "markAsDefaulted", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentPlanController.prototype, "remove", null);
exports.PaymentPlanController = PaymentPlanController = __decorate([
    (0, common_1.Controller)('payment-plans'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [payment_plan_service_1.PaymentPlanService])
], PaymentPlanController);
//# sourceMappingURL=payment-plan.controller.js.map