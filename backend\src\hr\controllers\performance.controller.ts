import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PerformanceService } from '../services/performance.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/performance')
@UseGuards(JwtAuthGuard)
export class PerformanceController {
  constructor(private readonly performanceService: PerformanceService) {}

  @Post()
  create(@Body() createPerformanceDto: any) {
    return this.performanceService.create(createPerformanceDto);
  }

  @Get()
  findAll(
    @Query('employeeId') employeeId?: string,
    @Query('type') type?: string,
    @Query('status') status?: string
  ) {
    const filters: any = {};
    if (employeeId) filters.employeeId = employeeId;
    if (type) filters.type = type;
    if (status) filters.status = status;

    return this.performanceService.findAll(filters);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.performanceService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updatePerformanceDto: any) {
    return this.performanceService.update(id, updatePerformanceDto);
  }

  @Post(':id/submit')
  submitReview(@Param('id') id: string) {
    return this.performanceService.submitReview(id);
  }

  @Post(':id/approve')
  approveReview(@Param('id') id: string, @Body() approveDto: { approvedBy: string }) {
    return this.performanceService.approveReview(id, approveDto.approvedBy);
  }
}
