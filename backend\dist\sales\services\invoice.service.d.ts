import { Repository } from 'typeorm';
import { Invoice } from '../entities/invoice.entity';
import { InvoiceItem } from '../entities/invoice-item.entity';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { CustomerService } from './customer.service';
export declare class InvoiceService {
    private invoiceRepository;
    private invoiceItemRepository;
    private customerService;
    constructor(invoiceRepository: Repository<Invoice>, invoiceItemRepository: Repository<InvoiceItem>, customerService: CustomerService);
    create(createInvoiceDto: CreateInvoiceDto, tenantId: string): Promise<Invoice>;
    findAll(tenantId: string): Promise<Invoice[]>;
    findOne(id: string, tenantId: string): Promise<Invoice>;
    update(id: string, updateInvoiceDto: Partial<CreateInvoiceDto>, tenantId: string): Promise<Invoice>;
    remove(id: string, tenantId: string): Promise<void>;
    updateStatus(id: string, status: string, tenantId: string): Promise<Invoice>;
    getInvoiceStats(tenantId: string): Promise<{
        totalInvoices: number;
        paidInvoices: number;
        overdueInvoices: number;
        totalRevenue: number;
        pendingAmount: number;
    }>;
    private calculateInvoiceTotals;
    private calculateItemTax;
    private generateInvoiceNumber;
}
