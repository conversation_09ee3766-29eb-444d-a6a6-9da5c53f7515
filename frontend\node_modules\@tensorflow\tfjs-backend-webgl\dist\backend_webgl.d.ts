/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-backend-webgl/dist/backend_webgl" />
import './flags_webgl';
import { BackendValues, DataId, DataStorage, DataToGPUWebGLOption, DataType, GPUData, KernelBackend, MemoryInfo, Rank, RecursiveArray, Tensor, Tensor2D, TensorBuffer, TensorInfo, TimingInfo, WebGLData } from '@tensorflow/tfjs-core';
import { GPGPUContext } from './gpgpu_context';
import * as gpgpu_math from './gpgpu_math';
import { GPGPUProgram } from './gpgpu_math';
import { TextureData } from './tex_util';
import { TextureManager } from './texture_manager';
export declare const EPSILON_FLOAT32 = 1e-7;
export declare const EPSILON_FLOAT16 = 0.0001;
type KernelInfo = {
    name: string;
    query: Promise<number>;
};
export type TimerNode = RecursiveArray<KernelInfo> | KernelInfo;
export interface CPUTimerQuery {
    startMs: number;
    endMs?: number;
}
export interface WebGLMemoryInfo extends MemoryInfo {
    numBytesInGPU: number;
    numBytesInGPUAllocated: number;
    numBytesInGPUFree: number;
    unreliable: boolean;
}
export interface WebGLTimingInfo extends TimingInfo {
    uploadWaitMs: number;
    downloadWaitMs: number;
}
export declare function getBinaryCache(webGLVersion: number): {
    [key: string]: gpgpu_math.GPGPUBinary;
};
export declare class MathBackendWebGL extends KernelBackend {
    texData: DataStorage<TextureData>;
    gpgpu: GPGPUContext;
    private static nextDataId;
    private nextDataId;
    private pendingRead;
    private pendingDisposal;
    dataRefCount: WeakMap<object, number>;
    private numBytesInGPU;
    private canvas;
    private programTimersStack;
    private activeTimers;
    private uploadWaitMs;
    private downloadWaitMs;
    private lastGlFlushTime;
    private floatPrecisionValue;
    private textureManager;
    private binaryCache;
    private gpgpuCreatedLocally;
    private numMBBeforeWarning;
    private warnedAboutMemory;
    constructor(gpuResource?: GPGPUContext | HTMLCanvasElement | OffscreenCanvas);
    numDataIds(): number;
    writeTexture(texture: WebGLTexture, shape: number[], dtype: DataType, texHeight: number, texWidth: number, channels: string): DataId;
    write(values: BackendValues, shape: number[], dtype: DataType): DataId;
    /** Return refCount of a `TensorData`. */
    refCount(dataId: DataId): number;
    /** Increase refCount of a `TextureData`. */
    incRef(dataId: DataId): void;
    /** Decrease refCount of a `TextureData`. */
    decRef(dataId: DataId): void;
    move(dataId: DataId, values: BackendValues, shape: number[], dtype: DataType, refCount: number): void;
    disposeIntermediateTensorInfo(tensorInfo: TensorInfo): void;
    readSync(dataId: DataId): BackendValues;
    read(dataId: DataId): Promise<BackendValues>;
    /**
     * Read tensor to a new texture that is densely packed for ease of use.
     * @param dataId The source tensor.
     * @param options
     *     customTexShape: Optional. If set, will use the user defined texture
     *     shape to create the texture.
     */
    readToGPU(dataId: DataId, options?: DataToGPUWebGLOption): GPUData;
    bufferSync<R extends Rank, D extends DataType>(t: TensorInfo): TensorBuffer<R, D>;
    private checkNumericalProblems;
    private getValuesFromTexture;
    timerAvailable(): boolean;
    time(f: () => void): Promise<WebGLTimingInfo>;
    memory(): WebGLMemoryInfo;
    private startTimer;
    private endTimer;
    private getQueryTime;
    private pendingDeletes;
    /**
     * Decrease the RefCount on the dataId and dispose the memory if the dataId
     * has 0 refCount. If there are pending read on the data, the disposal would
     * added to the pending delete queue. Return true if the dataId is removed
     * from backend or the backend does not contain the dataId, false if the
     * dataId is not removed. Memory may or may not be released even when dataId
     * is removed, which also depends on dataRefCount, see `releaseGPU`.
     * @param dataId
     * @oaram force Optional, remove the data regardless of refCount
     */
    disposeData(dataId: DataId, force?: boolean): boolean;
    private releaseGPUData;
    getTexture(dataId: DataId): WebGLTexture;
    /**
     * Returns internal information for the specific data bucket. Used in unit
     * tests.
     */
    getDataInfo(dataId: DataId): TextureData;
    shouldExecuteOnCPU(inputs: TensorInfo[], sizeThreshold?: number): boolean;
    getGPGPUContext(): GPGPUContext;
    where(condition: Tensor): Tensor2D;
    private packedUnaryOp;
    abs<T extends Tensor>(x: T): T;
    makeTensorInfo(shape: number[], dtype: DataType, values?: BackendValues | string[]): TensorInfo;
    private makeOutput;
    unpackTensor(input: TensorInfo): TensorInfo;
    packTensor(input: TensorInfo): TensorInfo;
    private packedReshape;
    private decode;
    runWebGLProgram(program: GPGPUProgram, inputs: TensorInfo[], outputDtype: DataType, customUniformValues?: number[][], preventEagerUnpackingOfOutput?: boolean, customTexShape?: [number, number]): TensorInfo;
    compileAndRun(program: GPGPUProgram, inputs: TensorInfo[], outputDtype?: DataType, customUniformValues?: number[][], preventEagerUnpackingOfOutput?: boolean): TensorInfo;
    private getAndSaveBinary;
    getTextureManager(): TextureManager;
    private disposed;
    dispose(): void;
    floatPrecision(): 16 | 32;
    /** Returns the smallest representable number.  */
    epsilon(): number;
    uploadToGPU(dataId: DataId): void;
    private convertAndCacheOnCPU;
    private acquireTexture;
    private computeBytes;
    checkCompileCompletion(): void;
    checkCompileCompletionAsync(): Promise<boolean[]>;
    private checkCompletionAsync_;
    private checkCompletion_;
    getUniformLocations(): void;
    /**
     * Create a TF.js tensor out of an existing WebGL texture. A new texture will
     * be created.
     */
    createTensorFromGPUData(values: WebGLData, shape: number[], dtype: DataType): Tensor;
}
export {};
