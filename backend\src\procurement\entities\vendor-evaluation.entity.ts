import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Vendor } from './vendor.entity';

export enum EvaluationStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity('vendor_evaluations')
export class VendorEvaluation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  vendorId: string;

  @ManyToOne(() => Vendor, vendor => vendor.evaluations)
  @JoinColumn({ name: 'vendorId' })
  vendor: Vendor;

  @Column({ type: 'date' })
  evaluationDate: Date;

  @Column({ type: 'date' })
  periodStart: Date;

  @Column({ type: 'date' })
  periodEnd: Date;

  @Column({
    type: 'enum',
    enum: EvaluationStatus,
    default: EvaluationStatus.DRAFT,
  })
  status: EvaluationStatus;

  @Column({ type: 'decimal', precision: 3, scale: 1 })
  qualityScore: number; // 1-10

  @Column({ type: 'decimal', precision: 3, scale: 1 })
  deliveryScore: number; // 1-10

  @Column({ type: 'decimal', precision: 3, scale: 1 })
  serviceScore: number; // 1-10

  @Column({ type: 'decimal', precision: 3, scale: 1 })
  priceScore: number; // 1-10

  @Column({ type: 'decimal', precision: 3, scale: 1 })
  overallScore: number; // 1-10

  @Column({ type: 'text', nullable: true })
  strengths: string;

  @Column({ type: 'text', nullable: true })
  weaknesses: string;

  @Column({ type: 'text', nullable: true })
  recommendations: string;

  @Column()
  evaluatedBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true })
  criteria: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
