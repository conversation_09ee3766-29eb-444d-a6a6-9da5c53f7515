import { HelpCategory } from './help-category.entity';
export declare enum ArticleType {
    HOW_TO = "how_to",
    TROUBLESHOOTING = "troubleshooting",
    FAQ = "faq",
    FEATURE_EXPLANATION = "feature_explanation",
    BEST_PRACTICES = "best_practices",
    KNOWN_ISSUES = "known_issues",
    RELEASE_NOTES = "release_notes"
}
export declare enum ArticleStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived",
    UNDER_REVIEW = "under_review"
}
export declare class HelpArticle {
    id: string;
    title: string;
    content: string;
    summary: string;
    type: ArticleType;
    status: ArticleStatus;
    categoryId: string;
    category: HelpCategory;
    tags: string[];
    keywords: string[];
    authorId: string;
    reviewedBy: string;
    reviewedAt: Date;
    publishedAt: Date;
    viewCount: number;
    helpfulCount: number;
    notHelpfulCount: number;
    rating: number;
    attachments: string[];
    relatedArticles: string[];
    isFeatured: boolean;
    isPinned: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
