"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PurchaseOrderService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const purchase_order_entity_1 = require("../entities/purchase-order.entity");
const purchase_order_item_entity_1 = require("../entities/purchase-order-item.entity");
const stock_service_1 = require("./stock.service");
let PurchaseOrderService = class PurchaseOrderService {
    purchaseOrderRepository;
    purchaseOrderItemRepository;
    stockService;
    constructor(purchaseOrderRepository, purchaseOrderItemRepository, stockService) {
        this.purchaseOrderRepository = purchaseOrderRepository;
        this.purchaseOrderItemRepository = purchaseOrderItemRepository;
        this.stockService = stockService;
    }
    async create(orderData) {
        const order = this.purchaseOrderRepository.create(orderData);
        return this.purchaseOrderRepository.save(order);
    }
    async findAll() {
        return this.purchaseOrderRepository.find({
            relations: ['supplier', 'items', 'items.product'],
            order: { orderDate: 'DESC' },
        });
    }
    async findOne(id) {
        const order = await this.purchaseOrderRepository.findOne({
            where: { id },
            relations: ['supplier', 'items', 'items.product'],
        });
        if (!order) {
            throw new common_1.NotFoundException(`Purchase order with ID ${id} not found`);
        }
        return order;
    }
    async update(id, updateData) {
        await this.purchaseOrderRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const order = await this.findOne(id);
        await this.purchaseOrderRepository.remove(order);
    }
    async addItem(orderId, itemData) {
        const order = await this.findOne(orderId);
        const item = this.purchaseOrderItemRepository.create({
            ...itemData,
            purchaseOrderId: order.id,
        });
        const savedItem = await this.purchaseOrderItemRepository.save(item);
        await this.recalculateOrderTotals(orderId);
        return savedItem;
    }
    async updateItem(itemId, updateData) {
        const item = await this.purchaseOrderItemRepository.findOne({
            where: { id: itemId },
            relations: ['purchaseOrder'],
        });
        if (!item) {
            throw new common_1.NotFoundException(`Purchase order item with ID ${itemId} not found`);
        }
        await this.purchaseOrderItemRepository.update(itemId, updateData);
        await this.recalculateOrderTotals(item.purchaseOrder.id);
        return this.purchaseOrderItemRepository.findOne({
            where: { id: itemId },
            relations: ['product'],
        });
    }
    async removeItem(itemId) {
        const item = await this.purchaseOrderItemRepository.findOne({
            where: { id: itemId },
            relations: ['purchaseOrder'],
        });
        if (!item) {
            throw new common_1.NotFoundException(`Purchase order item with ID ${itemId} not found`);
        }
        const orderId = item.purchaseOrder.id;
        await this.purchaseOrderItemRepository.remove(item);
        await this.recalculateOrderTotals(orderId);
    }
    async recalculateOrderTotals(orderId) {
        const order = await this.findOne(orderId);
        const subtotal = order.items.reduce((sum, item) => {
            return sum + (item.quantity * item.unitPrice);
        }, 0);
        const taxAmount = subtotal * (order.taxRate || 0) / 100;
        const total = subtotal + taxAmount + (order.shippingCost || 0);
        await this.purchaseOrderRepository.update(orderId, {
            subtotal,
            taxAmount,
            total,
        });
    }
    async receiveOrder(orderId, warehouseId) {
        const order = await this.findOne(orderId);
        if (order.status !== 'PENDING') {
            throw new Error('Only pending orders can be received');
        }
        for (const item of order.items) {
            await this.stockService.adjustStock(item.productId, warehouseId, item.quantity, `Received from PO ${order.orderNumber}`);
        }
        await this.purchaseOrderRepository.update(orderId, {
            status: 'RECEIVED',
            receivedDate: new Date(),
        });
        return this.findOne(orderId);
    }
    async partialReceive(orderId, warehouseId, receivedItems) {
        const order = await this.findOne(orderId);
        for (const receivedItem of receivedItems) {
            const orderItem = order.items.find(item => item.id === receivedItem.itemId);
            if (orderItem && receivedItem.receivedQuantity > 0) {
                await this.stockService.adjustStock(orderItem.productId, warehouseId, receivedItem.receivedQuantity, `Partial receipt from PO ${order.orderNumber}`);
                await this.purchaseOrderItemRepository.update(receivedItem.itemId, {
                    receivedQuantity: (orderItem.receivedQuantity || 0) + receivedItem.receivedQuantity,
                });
            }
        }
        const updatedOrder = await this.findOne(orderId);
        const isFullyReceived = updatedOrder.items.every(item => (item.receivedQuantity || 0) >= item.quantity);
        if (isFullyReceived) {
            await this.purchaseOrderRepository.update(orderId, {
                status: 'RECEIVED',
                receivedDate: new Date(),
            });
        }
        else {
            await this.purchaseOrderRepository.update(orderId, {
                status: 'PARTIALLY_RECEIVED',
            });
        }
        return this.findOne(orderId);
    }
    async cancelOrder(orderId, reason) {
        await this.purchaseOrderRepository.update(orderId, {
            status: 'CANCELLED',
            notes: reason,
        });
        return this.findOne(orderId);
    }
    async findBySupplier(supplierId) {
        return this.purchaseOrderRepository.find({
            where: { supplierId },
            relations: ['items', 'items.product'],
            order: { orderDate: 'DESC' },
        });
    }
    async findByStatus(status) {
        return this.purchaseOrderRepository.find({
            where: { status },
            relations: ['supplier', 'items'],
            order: { orderDate: 'DESC' },
        });
    }
    async getPurchaseOrderStatistics() {
        const totalOrders = await this.purchaseOrderRepository.count();
        const pendingOrders = await this.purchaseOrderRepository.count({ where: { status: 'PENDING' } });
        const receivedOrders = await this.purchaseOrderRepository.count({ where: { status: 'RECEIVED' } });
        const cancelledOrders = await this.purchaseOrderRepository.count({ where: { status: 'CANCELLED' } });
        const totalValue = await this.purchaseOrderRepository
            .createQueryBuilder('po')
            .select('SUM(po.total)', 'totalValue')
            .getRawOne();
        return {
            totalOrders,
            pendingOrders,
            receivedOrders,
            cancelledOrders,
            totalValue: parseFloat(totalValue.totalValue) || 0,
        };
    }
    async generateOrderNumber() {
        const count = await this.purchaseOrderRepository.count();
        const sequence = (count + 1).toString().padStart(6, '0');
        const year = new Date().getFullYear();
        return `PO-${year}-${sequence}`;
    }
};
exports.PurchaseOrderService = PurchaseOrderService;
exports.PurchaseOrderService = PurchaseOrderService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(purchase_order_entity_1.PurchaseOrder)),
    __param(1, (0, typeorm_1.InjectRepository)(purchase_order_item_entity_1.PurchaseOrderItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        stock_service_1.StockService])
], PurchaseOrderService);
//# sourceMappingURL=purchase-order.service.js.map