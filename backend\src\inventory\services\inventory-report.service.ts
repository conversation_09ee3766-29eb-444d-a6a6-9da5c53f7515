import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { Stock } from '../entities/stock.entity';
import { StockMovement } from '../entities/stock-movement.entity';
import { PurchaseOrder } from '../entities/purchase-order.entity';

@Injectable()
export class InventoryReportService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Stock)
    private stockRepository: Repository<Stock>,
    @InjectRepository(StockMovement)
    private stockMovementRepository: Repository<StockMovement>,
    @InjectRepository(PurchaseOrder)
    private purchaseOrderRepository: Repository<PurchaseOrder>,
  ) {}

  async generateInventoryValuationReport(): Promise<any> {
    const stockData = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .leftJoinAndSelect('product.category', 'category')
      .getMany();

    const report = {
      generatedAt: new Date(),
      totalItems: stockData.length,
      totalQuantity: 0,
      totalValue: 0,
      warehouses: {},
      categories: {},
      items: [],
    };

    for (const stock of stockData) {
      const itemValue = stock.quantity * stock.product.costPrice;
      report.totalQuantity += stock.quantity;
      report.totalValue += itemValue;

      // Group by warehouse
      if (!report.warehouses[stock.warehouse.name]) {
        report.warehouses[stock.warehouse.name] = {
          quantity: 0,
          value: 0,
          items: 0,
        };
      }
      report.warehouses[stock.warehouse.name].quantity += stock.quantity;
      report.warehouses[stock.warehouse.name].value += itemValue;
      report.warehouses[stock.warehouse.name].items += 1;

      // Group by category
      const categoryName = stock.product.category?.name || 'Uncategorized';
      if (!report.categories[categoryName]) {
        report.categories[categoryName] = {
          quantity: 0,
          value: 0,
          items: 0,
        };
      }
      report.categories[categoryName].quantity += stock.quantity;
      report.categories[categoryName].value += itemValue;
      report.categories[categoryName].items += 1;

      // Add item details
      report.items.push({
        productId: stock.product.id,
        productName: stock.product.name,
        sku: stock.product.sku,
        warehouse: stock.warehouse.name,
        category: categoryName,
        quantity: stock.quantity,
        costPrice: stock.product.costPrice,
        totalValue: itemValue,
      });
    }

    return report;
  }

  async generateStockLevelReport(): Promise<any> {
    const lowStockItems = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .where('stock.availableQuantity <= product.reorderLevel')
      .andWhere('stock.availableQuantity > 0')
      .getMany();

    const outOfStockItems = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .where('stock.availableQuantity = 0')
      .getMany();

    const overstockItems = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .where('stock.quantity > product.maxStockLevel')
      .getMany();

    return {
      generatedAt: new Date(),
      summary: {
        lowStockCount: lowStockItems.length,
        outOfStockCount: outOfStockItems.length,
        overstockCount: overstockItems.length,
      },
      lowStockItems: lowStockItems.map(stock => ({
        productName: stock.product.name,
        sku: stock.product.sku,
        warehouse: stock.warehouse.name,
        currentStock: stock.availableQuantity,
        reorderLevel: stock.product.reorderLevel,
        deficit: stock.product.reorderLevel - stock.availableQuantity,
      })),
      outOfStockItems: outOfStockItems.map(stock => ({
        productName: stock.product.name,
        sku: stock.product.sku,
        warehouse: stock.warehouse.name,
        lastStockDate: stock.updatedAt,
      })),
      overstockItems: overstockItems.map(stock => ({
        productName: stock.product.name,
        sku: stock.product.sku,
        warehouse: stock.warehouse.name,
        currentStock: stock.quantity,
        maxLevel: stock.product.maxStockLevel,
        excess: stock.quantity - stock.product.maxStockLevel,
      })),
    };
  }

  async generateMovementReport(startDate: Date, endDate: Date): Promise<any> {
    const movements = await this.stockMovementRepository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.product', 'product')
      .leftJoinAndSelect('movement.warehouse', 'warehouse')
      .where('movement.movementDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .orderBy('movement.movementDate', 'DESC')
      .getMany();

    const summary = {
      totalMovements: movements.length,
      inMovements: movements.filter(m => m.type === 'IN').length,
      outMovements: movements.filter(m => m.type === 'OUT').length,
      totalInQuantity: movements
        .filter(m => m.type === 'IN')
        .reduce((sum, m) => sum + m.quantity, 0),
      totalOutQuantity: movements
        .filter(m => m.type === 'OUT')
        .reduce((sum, m) => sum + m.quantity, 0),
    };

    const movementsByType = {};
    movements.forEach(movement => {
      if (!movementsByType[movement.movementType]) {
        movementsByType[movement.movementType] = {
          count: 0,
          totalQuantity: 0,
        };
      }
      movementsByType[movement.movementType].count += 1;
      movementsByType[movement.movementType].totalQuantity += movement.quantity;
    });

    return {
      generatedAt: new Date(),
      period: { startDate, endDate },
      summary,
      movementsByType,
      movements: movements.map(movement => ({
        date: movement.movementDate,
        product: movement.product.name,
        sku: movement.product.sku,
        warehouse: movement.warehouse.name,
        type: movement.type,
        movementType: movement.movementType,
        quantity: movement.quantity,
        reference: movement.reference,
      })),
    };
  }

  async generatePurchaseOrderReport(startDate: Date, endDate: Date): Promise<any> {
    const orders = await this.purchaseOrderRepository
      .createQueryBuilder('po')
      .leftJoinAndSelect('po.supplier', 'supplier')
      .leftJoinAndSelect('po.items', 'items')
      .leftJoinAndSelect('items.product', 'product')
      .where('po.orderDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .orderBy('po.orderDate', 'DESC')
      .getMany();

    const summary = {
      totalOrders: orders.length,
      totalValue: orders.reduce((sum, order) => sum + order.total, 0),
      pendingOrders: orders.filter(o => o.status === 'PENDING').length,
      receivedOrders: orders.filter(o => o.status === 'RECEIVED').length,
      cancelledOrders: orders.filter(o => o.status === 'CANCELLED').length,
    };

    const supplierSummary = {};
    orders.forEach(order => {
      const supplierName = order.supplier.name;
      if (!supplierSummary[supplierName]) {
        supplierSummary[supplierName] = {
          orderCount: 0,
          totalValue: 0,
        };
      }
      supplierSummary[supplierName].orderCount += 1;
      supplierSummary[supplierName].totalValue += order.total;
    });

    return {
      generatedAt: new Date(),
      period: { startDate, endDate },
      summary,
      supplierSummary,
      orders: orders.map(order => ({
        orderNumber: order.orderNumber,
        orderDate: order.orderDate,
        supplier: order.supplier.name,
        status: order.status,
        total: order.total,
        itemCount: order.items.length,
        receivedDate: order.receivedDate,
      })),
    };
  }

  async generateABCAnalysisReport(): Promise<any> {
    // This would require sales data to properly calculate ABC analysis
    // For now, we'll use stock value as a proxy
    const stockData = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .getMany();

    const productValues = stockData.map(stock => ({
      productId: stock.product.id,
      productName: stock.product.name,
      sku: stock.product.sku,
      value: stock.quantity * stock.product.costPrice,
    }));

    // Sort by value descending
    productValues.sort((a, b) => b.value - a.value);

    const totalValue = productValues.reduce((sum, item) => sum + item.value, 0);
    let cumulativeValue = 0;

    const classifiedProducts = productValues.map(product => {
      cumulativeValue += product.value;
      const cumulativePercentage = (cumulativeValue / totalValue) * 100;
      
      let classification = 'C';
      if (cumulativePercentage <= 80) {
        classification = 'A';
      } else if (cumulativePercentage <= 95) {
        classification = 'B';
      }

      return {
        ...product,
        classification,
        cumulativePercentage: Math.round(cumulativePercentage * 100) / 100,
      };
    });

    const summary = {
      classA: classifiedProducts.filter(p => p.classification === 'A').length,
      classB: classifiedProducts.filter(p => p.classification === 'B').length,
      classC: classifiedProducts.filter(p => p.classification === 'C').length,
    };

    return {
      generatedAt: new Date(),
      summary,
      products: classifiedProducts,
    };
  }
}
