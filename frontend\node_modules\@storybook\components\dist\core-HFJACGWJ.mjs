import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_core=__commonJS({"../../node_modules/highlight.js/lib/core.js"(exports,module){function deepFreeze(obj){return obj instanceof Map?obj.clear=obj.delete=obj.set=function(){throw new Error("map is read-only")}:obj instanceof Set&&(obj.add=obj.clear=obj.delete=function(){throw new Error("set is read-only")}),Object.freeze(obj),Object.getOwnPropertyNames(obj).forEach(function(name){var prop=obj[name];typeof prop=="object"&&!Object.isFrozen(prop)&&deepFreeze(prop);}),obj}var deepFreezeEs6=deepFreeze,_default=deepFreeze;deepFreezeEs6.default=_default;var Response=class{constructor(mode){mode.data===void 0&&(mode.data={}),this.data=mode.data,this.isMatchIgnored=!1;}ignoreMatch(){this.isMatchIgnored=!0;}};function escapeHTML(value){return value.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function inherit(original,...objects){let result=Object.create(null);for(let key in original)result[key]=original[key];return objects.forEach(function(obj){for(let key in obj)result[key]=obj[key];}),result}var SPAN_CLOSE="</span>",emitsWrappingTags=node=>!!node.kind,HTMLRenderer=class{constructor(parseTree,options){this.buffer="",this.classPrefix=options.classPrefix,parseTree.walk(this);}addText(text){this.buffer+=escapeHTML(text);}openNode(node){if(!emitsWrappingTags(node))return;let className=node.kind;node.sublanguage||(className=`${this.classPrefix}${className}`),this.span(className);}closeNode(node){emitsWrappingTags(node)&&(this.buffer+=SPAN_CLOSE);}value(){return this.buffer}span(className){this.buffer+=`<span class="${className}">`;}},TokenTree=class _TokenTree{constructor(){this.rootNode={children:[]},this.stack=[this.rootNode];}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(node){this.top.children.push(node);}openNode(kind){let node={kind,children:[]};this.add(node),this.stack.push(node);}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(builder){return this.constructor._walk(builder,this.rootNode)}static _walk(builder,node){return typeof node=="string"?builder.addText(node):node.children&&(builder.openNode(node),node.children.forEach(child=>this._walk(builder,child)),builder.closeNode(node)),builder}static _collapse(node){typeof node!="string"&&node.children&&(node.children.every(el=>typeof el=="string")?node.children=[node.children.join("")]:node.children.forEach(child=>{_TokenTree._collapse(child);}));}},TokenTreeEmitter=class extends TokenTree{constructor(options){super(),this.options=options;}addKeyword(text,kind){text!==""&&(this.openNode(kind),this.addText(text),this.closeNode());}addText(text){text!==""&&this.add(text);}addSublanguage(emitter,name){let node=emitter.root;node.kind=name,node.sublanguage=!0,this.add(node);}toHTML(){return new HTMLRenderer(this,this.options).value()}finalize(){return !0}};function escape(value){return new RegExp(value.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")}function source(re){return re?typeof re=="string"?re:re.source:null}function concat(...args){return args.map(x=>source(x)).join("")}function either(...args){return "("+args.map(x=>source(x)).join("|")+")"}function countMatchGroups(re){return new RegExp(re.toString()+"|").exec("").length-1}function startsWith(re,lexeme){let match=re&&re.exec(lexeme);return match&&match.index===0}var BACKREF_RE=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function join(regexps,separator="|"){let numCaptures=0;return regexps.map(regex=>{numCaptures+=1;let offset=numCaptures,re=source(regex),out="";for(;re.length>0;){let match=BACKREF_RE.exec(re);if(!match){out+=re;break}out+=re.substring(0,match.index),re=re.substring(match.index+match[0].length),match[0][0]==="\\"&&match[1]?out+="\\"+String(Number(match[1])+offset):(out+=match[0],match[0]==="("&&numCaptures++);}return out}).map(re=>`(${re})`).join(separator)}var MATCH_NOTHING_RE=/\b\B/,IDENT_RE="[a-zA-Z]\\w*",UNDERSCORE_IDENT_RE="[a-zA-Z_]\\w*",NUMBER_RE="\\b\\d+(\\.\\d+)?",C_NUMBER_RE="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",BINARY_NUMBER_RE="\\b(0b[01]+)",RE_STARTERS_RE="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG=(opts={})=>{let beginShebang=/^#![ ]*\//;return opts.binary&&(opts.begin=concat(beginShebang,/.*\b/,opts.binary,/\b.*/)),inherit({className:"meta",begin:beginShebang,end:/$/,relevance:0,"on:begin":(m,resp)=>{m.index!==0&&resp.ignoreMatch();}},opts)},BACKSLASH_ESCAPE={begin:"\\\\[\\s\\S]",relevance:0},APOS_STRING_MODE={className:"string",begin:"'",end:"'",illegal:"\\n",contains:[BACKSLASH_ESCAPE]},QUOTE_STRING_MODE={className:"string",begin:'"',end:'"',illegal:"\\n",contains:[BACKSLASH_ESCAPE]},PHRASAL_WORDS_MODE={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},COMMENT=function(begin,end,modeOptions={}){let mode=inherit({className:"comment",begin,end,contains:[]},modeOptions);return mode.contains.push(PHRASAL_WORDS_MODE),mode.contains.push({className:"doctag",begin:"(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):",relevance:0}),mode},C_LINE_COMMENT_MODE=COMMENT("//","$"),C_BLOCK_COMMENT_MODE=COMMENT("/\\*","\\*/"),HASH_COMMENT_MODE=COMMENT("#","$"),NUMBER_MODE={className:"number",begin:NUMBER_RE,relevance:0},C_NUMBER_MODE={className:"number",begin:C_NUMBER_RE,relevance:0},BINARY_NUMBER_MODE={className:"number",begin:BINARY_NUMBER_RE,relevance:0},CSS_NUMBER_MODE={className:"number",begin:NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},REGEXP_MODE={begin:/(?=\/[^/\n]*\/)/,contains:[{className:"regexp",begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[BACKSLASH_ESCAPE,{begin:/\[/,end:/\]/,relevance:0,contains:[BACKSLASH_ESCAPE]}]}]},TITLE_MODE={className:"title",begin:IDENT_RE,relevance:0},UNDERSCORE_TITLE_MODE={className:"title",begin:UNDERSCORE_IDENT_RE,relevance:0},METHOD_GUARD={begin:"\\.\\s*"+UNDERSCORE_IDENT_RE,relevance:0},END_SAME_AS_BEGIN=function(mode){return Object.assign(mode,{"on:begin":(m,resp)=>{resp.data._beginMatch=m[1];},"on:end":(m,resp)=>{resp.data._beginMatch!==m[1]&&resp.ignoreMatch();}})},MODES=Object.freeze({__proto__:null,MATCH_NOTHING_RE,IDENT_RE,UNDERSCORE_IDENT_RE,NUMBER_RE,C_NUMBER_RE,BINARY_NUMBER_RE,RE_STARTERS_RE,SHEBANG,BACKSLASH_ESCAPE,APOS_STRING_MODE,QUOTE_STRING_MODE,PHRASAL_WORDS_MODE,COMMENT,C_LINE_COMMENT_MODE,C_BLOCK_COMMENT_MODE,HASH_COMMENT_MODE,NUMBER_MODE,C_NUMBER_MODE,BINARY_NUMBER_MODE,CSS_NUMBER_MODE,REGEXP_MODE,TITLE_MODE,UNDERSCORE_TITLE_MODE,METHOD_GUARD,END_SAME_AS_BEGIN});function skipIfhasPrecedingDot(match,response){match.input[match.index-1]==="."&&response.ignoreMatch();}function beginKeywords(mode,parent){parent&&mode.beginKeywords&&(mode.begin="\\b("+mode.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",mode.__beforeBegin=skipIfhasPrecedingDot,mode.keywords=mode.keywords||mode.beginKeywords,delete mode.beginKeywords,mode.relevance===void 0&&(mode.relevance=0));}function compileIllegal(mode,_parent){Array.isArray(mode.illegal)&&(mode.illegal=either(...mode.illegal));}function compileMatch(mode,_parent){if(mode.match){if(mode.begin||mode.end)throw new Error("begin & end are not supported with match");mode.begin=mode.match,delete mode.match;}}function compileRelevance(mode,_parent){mode.relevance===void 0&&(mode.relevance=1);}var COMMON_KEYWORDS=["of","and","for","in","not","or","if","then","parent","list","value"],DEFAULT_KEYWORD_CLASSNAME="keyword";function compileKeywords(rawKeywords,caseInsensitive,className=DEFAULT_KEYWORD_CLASSNAME){let compiledKeywords={};return typeof rawKeywords=="string"?compileList(className,rawKeywords.split(" ")):Array.isArray(rawKeywords)?compileList(className,rawKeywords):Object.keys(rawKeywords).forEach(function(className2){Object.assign(compiledKeywords,compileKeywords(rawKeywords[className2],caseInsensitive,className2));}),compiledKeywords;function compileList(className2,keywordList){caseInsensitive&&(keywordList=keywordList.map(x=>x.toLowerCase())),keywordList.forEach(function(keyword){let pair=keyword.split("|");compiledKeywords[pair[0]]=[className2,scoreForKeyword(pair[0],pair[1])];});}}function scoreForKeyword(keyword,providedScore){return providedScore?Number(providedScore):commonKeyword(keyword)?0:1}function commonKeyword(keyword){return COMMON_KEYWORDS.includes(keyword.toLowerCase())}function compileLanguage(language,{plugins}){function langRe(value,global){return new RegExp(source(value),"m"+(language.case_insensitive?"i":"")+(global?"g":""))}class MultiRegex{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0;}addRule(re,opts){opts.position=this.position++,this.matchIndexes[this.matchAt]=opts,this.regexes.push([opts,re]),this.matchAt+=countMatchGroups(re)+1;}compile(){this.regexes.length===0&&(this.exec=()=>null);let terminators=this.regexes.map(el=>el[1]);this.matcherRe=langRe(join(terminators),!0),this.lastIndex=0;}exec(s){this.matcherRe.lastIndex=this.lastIndex;let match=this.matcherRe.exec(s);if(!match)return null;let i=match.findIndex((el,i2)=>i2>0&&el!==void 0),matchData=this.matchIndexes[i];return match.splice(0,i),Object.assign(match,matchData)}}class ResumableMultiRegex{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0;}getMatcher(index){if(this.multiRegexes[index])return this.multiRegexes[index];let matcher=new MultiRegex;return this.rules.slice(index).forEach(([re,opts])=>matcher.addRule(re,opts)),matcher.compile(),this.multiRegexes[index]=matcher,matcher}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0;}addRule(re,opts){this.rules.push([re,opts]),opts.type==="begin"&&this.count++;}exec(s){let m=this.getMatcher(this.regexIndex);m.lastIndex=this.lastIndex;let result=m.exec(s);if(this.resumingScanAtSamePosition()&&!(result&&result.index===this.lastIndex)){let m2=this.getMatcher(0);m2.lastIndex=this.lastIndex+1,result=m2.exec(s);}return result&&(this.regexIndex+=result.position+1,this.regexIndex===this.count&&this.considerAll()),result}}function buildModeRegex(mode){let mm=new ResumableMultiRegex;return mode.contains.forEach(term=>mm.addRule(term.begin,{rule:term,type:"begin"})),mode.terminatorEnd&&mm.addRule(mode.terminatorEnd,{type:"end"}),mode.illegal&&mm.addRule(mode.illegal,{type:"illegal"}),mm}function compileMode(mode,parent){let cmode=mode;if(mode.isCompiled)return cmode;[compileMatch].forEach(ext=>ext(mode,parent)),language.compilerExtensions.forEach(ext=>ext(mode,parent)),mode.__beforeBegin=null,[beginKeywords,compileIllegal,compileRelevance].forEach(ext=>ext(mode,parent)),mode.isCompiled=!0;let keywordPattern=null;if(typeof mode.keywords=="object"&&(keywordPattern=mode.keywords.$pattern,delete mode.keywords.$pattern),mode.keywords&&(mode.keywords=compileKeywords(mode.keywords,language.case_insensitive)),mode.lexemes&&keywordPattern)throw new Error("ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) ");return keywordPattern=keywordPattern||mode.lexemes||/\w+/,cmode.keywordPatternRe=langRe(keywordPattern,!0),parent&&(mode.begin||(mode.begin=/\B|\b/),cmode.beginRe=langRe(mode.begin),mode.endSameAsBegin&&(mode.end=mode.begin),!mode.end&&!mode.endsWithParent&&(mode.end=/\B|\b/),mode.end&&(cmode.endRe=langRe(mode.end)),cmode.terminatorEnd=source(mode.end)||"",mode.endsWithParent&&parent.terminatorEnd&&(cmode.terminatorEnd+=(mode.end?"|":"")+parent.terminatorEnd)),mode.illegal&&(cmode.illegalRe=langRe(mode.illegal)),mode.contains||(mode.contains=[]),mode.contains=[].concat(...mode.contains.map(function(c){return expandOrCloneMode(c==="self"?mode:c)})),mode.contains.forEach(function(c){compileMode(c,cmode);}),mode.starts&&compileMode(mode.starts,parent),cmode.matcher=buildModeRegex(cmode),cmode}if(language.compilerExtensions||(language.compilerExtensions=[]),language.contains&&language.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return language.classNameAliases=inherit(language.classNameAliases||{}),compileMode(language)}function dependencyOnParent(mode){return mode?mode.endsWithParent||dependencyOnParent(mode.starts):!1}function expandOrCloneMode(mode){return mode.variants&&!mode.cachedVariants&&(mode.cachedVariants=mode.variants.map(function(variant){return inherit(mode,{variants:null},variant)})),mode.cachedVariants?mode.cachedVariants:dependencyOnParent(mode)?inherit(mode,{starts:mode.starts?inherit(mode.starts):null}):Object.isFrozen(mode)?inherit(mode):mode}var version="10.7.3";function hasValueOrEmptyAttribute(value){return !!(value||value==="")}function BuildVuePlugin(hljs){let Component={props:["language","code","autodetect"],data:function(){return {detectedLanguage:"",unknownLanguage:!1}},computed:{className(){return this.unknownLanguage?"":"hljs "+this.detectedLanguage},highlighted(){if(!this.autoDetect&&!hljs.getLanguage(this.language))return console.warn(`The language "${this.language}" you specified could not be found.`),this.unknownLanguage=!0,escapeHTML(this.code);let result={};return this.autoDetect?(result=hljs.highlightAuto(this.code),this.detectedLanguage=result.language):(result=hljs.highlight(this.language,this.code,this.ignoreIllegals),this.detectedLanguage=this.language),result.value},autoDetect(){return !this.language||hasValueOrEmptyAttribute(this.autodetect)},ignoreIllegals(){return !0}},render(createElement){return createElement("pre",{},[createElement("code",{class:this.className,domProps:{innerHTML:this.highlighted}})])}};return {Component,VuePlugin:{install(Vue){Vue.component("highlightjs",Component);}}}}var mergeHTMLPlugin={"after:highlightElement":({el,result,text})=>{let originalStream=nodeStream(el);if(!originalStream.length)return;let resultNode=document.createElement("div");resultNode.innerHTML=result.value,result.value=mergeStreams(originalStream,nodeStream(resultNode),text);}};function tag(node){return node.nodeName.toLowerCase()}function nodeStream(node){let result=[];return function _nodeStream(node2,offset){for(let child=node2.firstChild;child;child=child.nextSibling)child.nodeType===3?offset+=child.nodeValue.length:child.nodeType===1&&(result.push({event:"start",offset,node:child}),offset=_nodeStream(child,offset),tag(child).match(/br|hr|img|input/)||result.push({event:"stop",offset,node:child}));return offset}(node,0),result}function mergeStreams(original,highlighted,value){let processed=0,result="",nodeStack=[];function selectStream(){return !original.length||!highlighted.length?original.length?original:highlighted:original[0].offset!==highlighted[0].offset?original[0].offset<highlighted[0].offset?original:highlighted:highlighted[0].event==="start"?original:highlighted}function open(node){function attributeString(attr){return " "+attr.nodeName+'="'+escapeHTML(attr.value)+'"'}result+="<"+tag(node)+[].map.call(node.attributes,attributeString).join("")+">";}function close(node){result+="</"+tag(node)+">";}function render(event){(event.event==="start"?open:close)(event.node);}for(;original.length||highlighted.length;){let stream=selectStream();if(result+=escapeHTML(value.substring(processed,stream[0].offset)),processed=stream[0].offset,stream===original){nodeStack.reverse().forEach(close);do render(stream.splice(0,1)[0]),stream=selectStream();while(stream===original&&stream.length&&stream[0].offset===processed);nodeStack.reverse().forEach(open);}else stream[0].event==="start"?nodeStack.push(stream[0].node):nodeStack.pop(),render(stream.splice(0,1)[0]);}return result+escapeHTML(value.substr(processed))}var seenDeprecations={},error=message=>{console.error(message);},warn=(message,...args)=>{console.log(`WARN: ${message}`,...args);},deprecated=(version2,message)=>{seenDeprecations[`${version2}/${message}`]||(console.log(`Deprecated as of ${version2}. ${message}`),seenDeprecations[`${version2}/${message}`]=!0);},escape$1=escapeHTML,inherit$1=inherit,NO_MATCH=Symbol("nomatch"),HLJS=function(hljs){let languages=Object.create(null),aliases=Object.create(null),plugins=[],SAFE_MODE=!0,fixMarkupRe=/(^(<[^>]+>|\t|)+|\n)/gm,LANGUAGE_NOT_FOUND="Could not find the language '{}', did you forget to load/include a language module?",PLAINTEXT_LANGUAGE={disableAutodetect:!0,name:"Plain text",contains:[]},options={noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:null,__emitter:TokenTreeEmitter};function shouldNotHighlight(languageName){return options.noHighlightRe.test(languageName)}function blockLanguage(block){let classes=block.className+" ";classes+=block.parentNode?block.parentNode.className:"";let match=options.languageDetectRe.exec(classes);if(match){let language=getLanguage(match[1]);return language||(warn(LANGUAGE_NOT_FOUND.replace("{}",match[1])),warn("Falling back to no-highlight mode for this block.",block)),language?match[1]:"no-highlight"}return classes.split(/\s+/).find(_class=>shouldNotHighlight(_class)||getLanguage(_class))}function highlight2(codeOrlanguageName,optionsOrCode,ignoreIllegals,continuation){let code="",languageName="";typeof optionsOrCode=="object"?(code=codeOrlanguageName,ignoreIllegals=optionsOrCode.ignoreIllegals,languageName=optionsOrCode.language,continuation=void 0):(deprecated("10.7.0","highlight(lang, code, ...args) has been deprecated."),deprecated("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),languageName=codeOrlanguageName,code=optionsOrCode);let context={code,language:languageName};fire("before:highlight",context);let result=context.result?context.result:_highlight(context.language,context.code,ignoreIllegals,continuation);return result.code=context.code,fire("after:highlight",result),result}function _highlight(languageName,codeToHighlight,ignoreIllegals,continuation){function keywordData(mode,match){let matchText=language.case_insensitive?match[0].toLowerCase():match[0];return Object.prototype.hasOwnProperty.call(mode.keywords,matchText)&&mode.keywords[matchText]}function processKeywords(){if(!top.keywords){emitter.addText(modeBuffer);return}let lastIndex=0;top.keywordPatternRe.lastIndex=0;let match=top.keywordPatternRe.exec(modeBuffer),buf="";for(;match;){buf+=modeBuffer.substring(lastIndex,match.index);let data=keywordData(top,match);if(data){let[kind,keywordRelevance]=data;if(emitter.addText(buf),buf="",relevance+=keywordRelevance,kind.startsWith("_"))buf+=match[0];else {let cssClass=language.classNameAliases[kind]||kind;emitter.addKeyword(match[0],cssClass);}}else buf+=match[0];lastIndex=top.keywordPatternRe.lastIndex,match=top.keywordPatternRe.exec(modeBuffer);}buf+=modeBuffer.substr(lastIndex),emitter.addText(buf);}function processSubLanguage(){if(modeBuffer==="")return;let result2=null;if(typeof top.subLanguage=="string"){if(!languages[top.subLanguage]){emitter.addText(modeBuffer);return}result2=_highlight(top.subLanguage,modeBuffer,!0,continuations[top.subLanguage]),continuations[top.subLanguage]=result2.top;}else result2=highlightAuto(modeBuffer,top.subLanguage.length?top.subLanguage:null);top.relevance>0&&(relevance+=result2.relevance),emitter.addSublanguage(result2.emitter,result2.language);}function processBuffer(){top.subLanguage!=null?processSubLanguage():processKeywords(),modeBuffer="";}function startNewMode(mode){return mode.className&&emitter.openNode(language.classNameAliases[mode.className]||mode.className),top=Object.create(mode,{parent:{value:top}}),top}function endOfMode(mode,match,matchPlusRemainder){let matched=startsWith(mode.endRe,matchPlusRemainder);if(matched){if(mode["on:end"]){let resp=new Response(mode);mode["on:end"](match,resp),resp.isMatchIgnored&&(matched=!1);}if(matched){for(;mode.endsParent&&mode.parent;)mode=mode.parent;return mode}}if(mode.endsWithParent)return endOfMode(mode.parent,match,matchPlusRemainder)}function doIgnore(lexeme){return top.matcher.regexIndex===0?(modeBuffer+=lexeme[0],1):(resumeScanAtSamePosition=!0,0)}function doBeginMatch(match){let lexeme=match[0],newMode=match.rule,resp=new Response(newMode),beforeCallbacks=[newMode.__beforeBegin,newMode["on:begin"]];for(let cb of beforeCallbacks)if(cb&&(cb(match,resp),resp.isMatchIgnored))return doIgnore(lexeme);return newMode&&newMode.endSameAsBegin&&(newMode.endRe=escape(lexeme)),newMode.skip?modeBuffer+=lexeme:(newMode.excludeBegin&&(modeBuffer+=lexeme),processBuffer(),!newMode.returnBegin&&!newMode.excludeBegin&&(modeBuffer=lexeme)),startNewMode(newMode),newMode.returnBegin?0:lexeme.length}function doEndMatch(match){let lexeme=match[0],matchPlusRemainder=codeToHighlight.substr(match.index),endMode=endOfMode(top,match,matchPlusRemainder);if(!endMode)return NO_MATCH;let origin=top;origin.skip?modeBuffer+=lexeme:(origin.returnEnd||origin.excludeEnd||(modeBuffer+=lexeme),processBuffer(),origin.excludeEnd&&(modeBuffer=lexeme));do top.className&&emitter.closeNode(),!top.skip&&!top.subLanguage&&(relevance+=top.relevance),top=top.parent;while(top!==endMode.parent);return endMode.starts&&(endMode.endSameAsBegin&&(endMode.starts.endRe=endMode.endRe),startNewMode(endMode.starts)),origin.returnEnd?0:lexeme.length}function processContinuations(){let list=[];for(let current=top;current!==language;current=current.parent)current.className&&list.unshift(current.className);list.forEach(item=>emitter.openNode(item));}let lastMatch={};function processLexeme(textBeforeMatch,match){let lexeme=match&&match[0];if(modeBuffer+=textBeforeMatch,lexeme==null)return processBuffer(),0;if(lastMatch.type==="begin"&&match.type==="end"&&lastMatch.index===match.index&&lexeme===""){if(modeBuffer+=codeToHighlight.slice(match.index,match.index+1),!SAFE_MODE){let err=new Error("0 width match regex");throw err.languageName=languageName,err.badRule=lastMatch.rule,err}return 1}if(lastMatch=match,match.type==="begin")return doBeginMatch(match);if(match.type==="illegal"&&!ignoreIllegals){let err=new Error('Illegal lexeme "'+lexeme+'" for mode "'+(top.className||"<unnamed>")+'"');throw err.mode=top,err}else if(match.type==="end"){let processed=doEndMatch(match);if(processed!==NO_MATCH)return processed}if(match.type==="illegal"&&lexeme==="")return 1;if(iterations>1e5&&iterations>match.index*3)throw new Error("potential infinite loop, way more iterations than matches");return modeBuffer+=lexeme,lexeme.length}let language=getLanguage(languageName);if(!language)throw error(LANGUAGE_NOT_FOUND.replace("{}",languageName)),new Error('Unknown language: "'+languageName+'"');let md=compileLanguage(language,{plugins}),result="",top=continuation||md,continuations={},emitter=new options.__emitter(options);processContinuations();let modeBuffer="",relevance=0,index=0,iterations=0,resumeScanAtSamePosition=!1;try{for(top.matcher.considerAll();;){iterations++,resumeScanAtSamePosition?resumeScanAtSamePosition=!1:top.matcher.considerAll(),top.matcher.lastIndex=index;let match=top.matcher.exec(codeToHighlight);if(!match)break;let beforeMatch=codeToHighlight.substring(index,match.index),processedCount=processLexeme(beforeMatch,match);index=match.index+processedCount;}return processLexeme(codeToHighlight.substr(index)),emitter.closeAllNodes(),emitter.finalize(),result=emitter.toHTML(),{relevance:Math.floor(relevance),value:result,language:languageName,illegal:!1,emitter,top}}catch(err){if(err.message&&err.message.includes("Illegal"))return {illegal:!0,illegalBy:{msg:err.message,context:codeToHighlight.slice(index-100,index+100),mode:err.mode},sofar:result,relevance:0,value:escape$1(codeToHighlight),emitter};if(SAFE_MODE)return {illegal:!1,relevance:0,value:escape$1(codeToHighlight),emitter,language:languageName,top,errorRaised:err};throw err}}function justTextHighlightResult(code){let result={relevance:0,emitter:new options.__emitter(options),value:escape$1(code),illegal:!1,top:PLAINTEXT_LANGUAGE};return result.emitter.addText(code),result}function highlightAuto(code,languageSubset){languageSubset=languageSubset||options.languages||Object.keys(languages);let plaintext=justTextHighlightResult(code),results=languageSubset.filter(getLanguage).filter(autoDetection).map(name=>_highlight(name,code,!1));results.unshift(plaintext);let sorted=results.sort((a,b)=>{if(a.relevance!==b.relevance)return b.relevance-a.relevance;if(a.language&&b.language){if(getLanguage(a.language).supersetOf===b.language)return 1;if(getLanguage(b.language).supersetOf===a.language)return -1}return 0}),[best,secondBest]=sorted,result=best;return result.second_best=secondBest,result}function fixMarkup(html){return options.tabReplace||options.useBR?html.replace(fixMarkupRe,match=>match===`
`?options.useBR?"<br>":match:options.tabReplace?match.replace(/\t/g,options.tabReplace):match):html}function updateClassName(element,currentLang,resultLang){let language=currentLang?aliases[currentLang]:resultLang;element.classList.add("hljs"),language&&element.classList.add(language);}let brPlugin={"before:highlightElement":({el})=>{options.useBR&&(el.innerHTML=el.innerHTML.replace(/\n/g,"").replace(/<br[ /]*>/g,`
`));},"after:highlightElement":({result})=>{options.useBR&&(result.value=result.value.replace(/\n/g,"<br>"));}},TAB_REPLACE_RE=/^(<[^>]+>|\t)+/gm,tabReplacePlugin={"after:highlightElement":({result})=>{options.tabReplace&&(result.value=result.value.replace(TAB_REPLACE_RE,m=>m.replace(/\t/g,options.tabReplace)));}};function highlightElement(element){let node=null,language=blockLanguage(element);if(shouldNotHighlight(language))return;fire("before:highlightElement",{el:element,language}),node=element;let text=node.textContent,result=language?highlight2(text,{language,ignoreIllegals:!0}):highlightAuto(text);fire("after:highlightElement",{el:element,result,text}),element.innerHTML=result.value,updateClassName(element,language,result.language),element.result={language:result.language,re:result.relevance,relavance:result.relevance},result.second_best&&(element.second_best={language:result.second_best.language,re:result.second_best.relevance,relavance:result.second_best.relevance});}function configure(userOptions){userOptions.useBR&&(deprecated("10.3.0","'useBR' will be removed entirely in v11.0"),deprecated("10.3.0","Please see https://github.com/highlightjs/highlight.js/issues/2559")),options=inherit$1(options,userOptions);}let initHighlighting=()=>{if(initHighlighting.called)return;initHighlighting.called=!0,deprecated("10.6.0","initHighlighting() is deprecated.  Use highlightAll() instead."),document.querySelectorAll("pre code").forEach(highlightElement);};function initHighlightingOnLoad(){deprecated("10.6.0","initHighlightingOnLoad() is deprecated.  Use highlightAll() instead."),wantsHighlight=!0;}let wantsHighlight=!1;function highlightAll(){if(document.readyState==="loading"){wantsHighlight=!0;return}document.querySelectorAll("pre code").forEach(highlightElement);}function boot(){wantsHighlight&&highlightAll();}typeof window<"u"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",boot,!1);function registerLanguage(languageName,languageDefinition){let lang=null;try{lang=languageDefinition(hljs);}catch(error$1){if(error("Language definition for '{}' could not be registered.".replace("{}",languageName)),SAFE_MODE)error(error$1);else throw error$1;lang=PLAINTEXT_LANGUAGE;}lang.name||(lang.name=languageName),languages[languageName]=lang,lang.rawDefinition=languageDefinition.bind(null,hljs),lang.aliases&&registerAliases(lang.aliases,{languageName});}function unregisterLanguage(languageName){delete languages[languageName];for(let alias of Object.keys(aliases))aliases[alias]===languageName&&delete aliases[alias];}function listLanguages(){return Object.keys(languages)}function requireLanguage(name){deprecated("10.4.0","requireLanguage will be removed entirely in v11."),deprecated("10.4.0","Please see https://github.com/highlightjs/highlight.js/pull/2844");let lang=getLanguage(name);if(lang)return lang;throw new Error("The '{}' language is required, but not loaded.".replace("{}",name))}function getLanguage(name){return name=(name||"").toLowerCase(),languages[name]||languages[aliases[name]]}function registerAliases(aliasList,{languageName}){typeof aliasList=="string"&&(aliasList=[aliasList]),aliasList.forEach(alias=>{aliases[alias.toLowerCase()]=languageName;});}function autoDetection(name){let lang=getLanguage(name);return lang&&!lang.disableAutodetect}function upgradePluginAPI(plugin){plugin["before:highlightBlock"]&&!plugin["before:highlightElement"]&&(plugin["before:highlightElement"]=data=>{plugin["before:highlightBlock"](Object.assign({block:data.el},data));}),plugin["after:highlightBlock"]&&!plugin["after:highlightElement"]&&(plugin["after:highlightElement"]=data=>{plugin["after:highlightBlock"](Object.assign({block:data.el},data));});}function addPlugin(plugin){upgradePluginAPI(plugin),plugins.push(plugin);}function fire(event,args){let cb=event;plugins.forEach(function(plugin){plugin[cb]&&plugin[cb](args);});}function deprecateFixMarkup(arg){return deprecated("10.2.0","fixMarkup will be removed entirely in v11.0"),deprecated("10.2.0","Please see https://github.com/highlightjs/highlight.js/issues/2534"),fixMarkup(arg)}function deprecateHighlightBlock(el){return deprecated("10.7.0","highlightBlock will be removed entirely in v12.0"),deprecated("10.7.0","Please use highlightElement now."),highlightElement(el)}Object.assign(hljs,{highlight:highlight2,highlightAuto,highlightAll,fixMarkup:deprecateFixMarkup,highlightElement,highlightBlock:deprecateHighlightBlock,configure,initHighlighting,initHighlightingOnLoad,registerLanguage,unregisterLanguage,listLanguages,getLanguage,registerAliases,requireLanguage,autoDetection,inherit:inherit$1,addPlugin,vuePlugin:BuildVuePlugin(hljs).VuePlugin}),hljs.debugMode=function(){SAFE_MODE=!1;},hljs.safeMode=function(){SAFE_MODE=!0;},hljs.versionString=version;for(let key in MODES)typeof MODES[key]=="object"&&deepFreezeEs6(MODES[key]);return Object.assign(hljs,MODES),hljs.addPlugin(brPlugin),hljs.addPlugin(mergeHTMLPlugin),hljs.addPlugin(tabReplacePlugin),hljs},highlight=HLJS({});module.exports=highlight;}});var require_format=__commonJS({"../../node_modules/format/format.js"(exports,module){(function(){var namespace;typeof module<"u"?namespace=module.exports=format:namespace=function(){return this||(0, eval)("this")}(),namespace.format=format,namespace.vsprintf=vsprintf,typeof console<"u"&&typeof console.log=="function"&&(namespace.printf=printf);function printf(){console.log(format.apply(null,arguments));}function vsprintf(fmt,replacements){return format.apply(null,[fmt].concat(replacements))}function format(fmt){for(var argIndex=1,args=[].slice.call(arguments),i=0,n=fmt.length,result="",c,escaped=!1,arg,tmp,leadingZero=!1,precision,nextArg=function(){return args[argIndex++]},slurpNumber=function(){for(var digits="";/\d/.test(fmt[i]);)digits+=fmt[i++],c=fmt[i];return digits.length>0?parseInt(digits):null};i<n;++i)if(c=fmt[i],escaped)switch(escaped=!1,c=="."?(leadingZero=!1,c=fmt[++i]):c=="0"&&fmt[i+1]=="."?(leadingZero=!0,i+=2,c=fmt[i]):leadingZero=!0,precision=slurpNumber(),c){case"b":result+=parseInt(nextArg(),10).toString(2);break;case"c":arg=nextArg(),typeof arg=="string"||arg instanceof String?result+=arg:result+=String.fromCharCode(parseInt(arg,10));break;case"d":result+=parseInt(nextArg(),10);break;case"f":tmp=String(parseFloat(nextArg()).toFixed(precision||6)),result+=leadingZero?tmp:tmp.replace(/^0/,"");break;case"j":result+=JSON.stringify(nextArg());break;case"o":result+="0"+parseInt(nextArg(),10).toString(8);break;case"s":result+=nextArg();break;case"x":result+="0x"+parseInt(nextArg(),10).toString(16);break;case"X":result+="0x"+parseInt(nextArg(),10).toString(16).toUpperCase();break;default:result+=c;break}else c==="%"?escaped=!0:result+=c;return result}})();}});var require_fault=__commonJS({"../../node_modules/fault/index.js"(exports,module){var formatter=require_format(),fault=create(Error);module.exports=fault;fault.eval=create(EvalError);fault.range=create(RangeError);fault.reference=create(ReferenceError);fault.syntax=create(SyntaxError);fault.type=create(TypeError);fault.uri=create(URIError);fault.create=create;function create(EConstructor){return FormattedError.displayName=EConstructor.displayName||EConstructor.name,FormattedError;function FormattedError(format){return format&&(format=formatter.apply(null,arguments)),new EConstructor(format)}}}});var require_core2=__commonJS({"../../node_modules/lowlight/lib/core.js"(exports){var high=require_core(),fault=require_fault();exports.highlight=highlight;exports.highlightAuto=highlightAuto;exports.registerLanguage=registerLanguage;exports.listLanguages=listLanguages;exports.registerAlias=registerAlias;Emitter.prototype.addText=text;Emitter.prototype.addKeyword=addKeyword;Emitter.prototype.addSublanguage=addSublanguage;Emitter.prototype.openNode=open;Emitter.prototype.closeNode=close;Emitter.prototype.closeAllNodes=noop;Emitter.prototype.finalize=noop;Emitter.prototype.toHTML=toHtmlNoop;var defaultPrefix="hljs-";function highlight(name,value,options){var before=high.configure({}),settings=options||{},prefix=settings.prefix,result;if(typeof name!="string")throw fault("Expected `string` for name, got `%s`",name);if(!high.getLanguage(name))throw fault("Unknown language: `%s` is not registered",name);if(typeof value!="string")throw fault("Expected `string` for value, got `%s`",value);if(prefix==null&&(prefix=defaultPrefix),high.configure({__emitter:Emitter,classPrefix:prefix}),result=high.highlight(value,{language:name,ignoreIllegals:!0}),high.configure(before||{}),result.errorRaised)throw result.errorRaised;return {relevance:result.relevance,language:result.language,value:result.emitter.rootNode.children}}function highlightAuto(value,options){var settings=options||{},subset=settings.subset||high.listLanguages();settings.prefix;var length=subset.length,index=-1,result,secondBest,current,name;if(typeof value!="string")throw fault("Expected `string` for value, got `%s`",value);for(secondBest={relevance:0,language:null,value:[]},result={relevance:0,language:null,value:[]};++index<length;)name=subset[index],high.getLanguage(name)&&(current=highlight(name,value,options),current.language=name,current.relevance>secondBest.relevance&&(secondBest=current),current.relevance>result.relevance&&(secondBest=result,result=current));return secondBest.language&&(result.secondBest=secondBest),result}function registerLanguage(name,syntax){high.registerLanguage(name,syntax);}function listLanguages(){return high.listLanguages()}function registerAlias(name,alias){var map=name,key;alias&&(map={},map[name]=alias);for(key in map)high.registerAliases(map[key],{languageName:key});}function Emitter(options){this.options=options,this.rootNode={children:[]},this.stack=[this.rootNode];}function addKeyword(value,name){this.openNode(name),this.addText(value),this.closeNode();}function addSublanguage(other,name){var stack=this.stack,current=stack[stack.length-1],results=other.rootNode.children,node=name?{type:"element",tagName:"span",properties:{className:[name]},children:results}:results;current.children=current.children.concat(node);}function text(value){var stack=this.stack,current,tail;value!==""&&(current=stack[stack.length-1],tail=current.children[current.children.length-1],tail&&tail.type==="text"?tail.value+=value:current.children.push({type:"text",value}));}function open(name){var stack=this.stack,className=this.options.classPrefix+name,current=stack[stack.length-1],child={type:"element",tagName:"span",properties:{className:[className]},children:[]};current.children.push(child),stack.push(child);}function close(){this.stack.pop();}function toHtmlNoop(){return ""}function noop(){}}});var coreHFJACGWJ = require_core2();

export { coreHFJACGWJ as default };
