"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankAccountService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bank_account_entity_1 = require("../entities/bank-account.entity");
const bank_transaction_entity_1 = require("../entities/bank-transaction.entity");
let BankAccountService = class BankAccountService {
    bankAccountRepository;
    bankTransactionRepository;
    constructor(bankAccountRepository, bankTransactionRepository) {
        this.bankAccountRepository = bankAccountRepository;
        this.bankTransactionRepository = bankTransactionRepository;
    }
    async create(createBankAccountDto) {
        const bankAccount = this.bankAccountRepository.create(createBankAccountDto);
        return this.bankAccountRepository.save(bankAccount);
    }
    async findAll() {
        return this.bankAccountRepository.find({
            where: { status: bank_account_entity_1.BankAccountStatus.ACTIVE },
            order: { accountName: 'ASC' },
        });
    }
    async findOne(id) {
        const bankAccount = await this.bankAccountRepository.findOne({
            where: { id },
            relations: ['transactions'],
        });
        if (!bankAccount) {
            throw new common_1.NotFoundException(`Bank account with ID ${id} not found`);
        }
        return bankAccount;
    }
    async update(id, updateBankAccountDto) {
        const bankAccount = await this.findOne(id);
        Object.assign(bankAccount, updateBankAccountDto);
        return this.bankAccountRepository.save(bankAccount);
    }
    async remove(id) {
        const bankAccount = await this.findOne(id);
        if (bankAccount.transactions?.length > 0) {
            throw new common_1.BadRequestException('Cannot delete bank account with transactions');
        }
        await this.bankAccountRepository.remove(bankAccount);
    }
    async addTransaction(bankAccountId, transactionData) {
        const bankAccount = await this.findOne(bankAccountId);
        const transaction = this.bankTransactionRepository.create({
            ...transactionData,
            bankAccountId,
        });
        const savedTransaction = await this.bankTransactionRepository.save(transaction);
        await this.updateAccountBalance(bankAccountId, savedTransaction);
        return savedTransaction;
    }
    async importTransactions(bankAccountId, transactions) {
        const bankAccount = await this.findOne(bankAccountId);
        const savedTransactions = [];
        for (const transactionData of transactions) {
            const existingTransaction = await this.bankTransactionRepository.findOne({
                where: {
                    bankAccountId,
                    transactionId: transactionData.transactionId,
                },
            });
            if (!existingTransaction) {
                const transaction = this.bankTransactionRepository.create({
                    ...transactionData,
                    bankAccountId,
                    status: bank_transaction_entity_1.BankTransactionStatus.CLEARED,
                });
                const savedTransaction = await this.bankTransactionRepository.save(transaction);
                savedTransactions.push(savedTransaction);
                await this.updateAccountBalance(bankAccountId, savedTransaction);
            }
        }
        return savedTransactions;
    }
    async reconcileAccount(bankAccountId, reconciledBalance, reconciledBy) {
        const bankAccount = await this.findOne(bankAccountId);
        bankAccount.lastReconciledDate = new Date();
        bankAccount.lastReconciledBalance = reconciledBalance;
        await this.bankTransactionRepository.update({
            bankAccountId,
            status: bank_transaction_entity_1.BankTransactionStatus.CLEARED,
            isReconciled: false,
        }, {
            isReconciled: true,
            reconciledDate: new Date(),
            reconciledBy,
        });
        return this.bankAccountRepository.save(bankAccount);
    }
    async getAccountStatement(bankAccountId, startDate, endDate) {
        const bankAccount = await this.findOne(bankAccountId);
        const queryBuilder = this.bankTransactionRepository.createQueryBuilder('transaction')
            .where('transaction.bankAccountId = :bankAccountId', { bankAccountId });
        if (startDate && endDate) {
            queryBuilder.andWhere('transaction.transactionDate BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        const transactions = await queryBuilder
            .orderBy('transaction.transactionDate', 'ASC')
            .addOrderBy('transaction.createdAt', 'ASC')
            .getMany();
        let runningBalance = 0;
        const statementEntries = transactions.map(transaction => {
            runningBalance += transaction.amount;
            return {
                date: transaction.transactionDate,
                description: transaction.description,
                reference: transaction.reference,
                amount: transaction.amount,
                balance: runningBalance,
                status: transaction.status,
                isReconciled: transaction.isReconciled,
            };
        });
        return {
            account: {
                id: bankAccount.id,
                accountName: bankAccount.accountName,
                accountNumber: bankAccount.accountNumber,
                bankName: bankAccount.bankName,
                currentBalance: bankAccount.currentBalance,
            },
            period: { startDate, endDate },
            entries: statementEntries,
            summary: {
                openingBalance: 0,
                totalDebits: transactions.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0),
                totalCredits: transactions.filter(t => t.amount < 0).reduce((sum, t) => Math.abs(t.amount), 0),
                closingBalance: runningBalance,
            },
        };
    }
    async getUnreconciledTransactions(bankAccountId) {
        return this.bankTransactionRepository.find({
            where: {
                bankAccountId,
                isReconciled: false,
                status: bank_transaction_entity_1.BankTransactionStatus.CLEARED,
            },
            order: { transactionDate: 'ASC' },
        });
    }
    async setDefaultAccount(id) {
        await this.bankAccountRepository.update({}, { isDefault: false });
        const bankAccount = await this.findOne(id);
        bankAccount.isDefault = true;
        return this.bankAccountRepository.save(bankAccount);
    }
    async updateAccountBalance(bankAccountId, transaction) {
        const bankAccount = await this.findOne(bankAccountId);
        bankAccount.currentBalance += transaction.amount;
        if (transaction.status === bank_transaction_entity_1.BankTransactionStatus.CLEARED) {
            bankAccount.availableBalance += transaction.amount;
        }
        transaction.runningBalance = bankAccount.currentBalance;
        await this.bankTransactionRepository.save(transaction);
        await this.bankAccountRepository.save(bankAccount);
    }
};
exports.BankAccountService = BankAccountService;
exports.BankAccountService = BankAccountService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(bank_account_entity_1.BankAccount)),
    __param(1, (0, typeorm_1.InjectRepository)(bank_transaction_entity_1.BankTransaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BankAccountService);
//# sourceMappingURL=bank-account.service.js.map