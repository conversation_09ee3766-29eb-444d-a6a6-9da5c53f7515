"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerCredit = exports.CreditStatus = exports.CreditTransactionType = void 0;
const typeorm_1 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
var CreditTransactionType;
(function (CreditTransactionType) {
    CreditTransactionType["CREDIT"] = "credit";
    CreditTransactionType["DEBIT"] = "debit";
    CreditTransactionType["ADJUSTMENT"] = "adjustment";
    CreditTransactionType["REFUND"] = "refund";
    CreditTransactionType["PAYMENT"] = "payment";
    CreditTransactionType["TRANSFER"] = "transfer";
})(CreditTransactionType || (exports.CreditTransactionType = CreditTransactionType = {}));
var CreditStatus;
(function (CreditStatus) {
    CreditStatus["ACTIVE"] = "active";
    CreditStatus["EXPIRED"] = "expired";
    CreditStatus["USED"] = "used";
    CreditStatus["CANCELLED"] = "cancelled";
})(CreditStatus || (exports.CreditStatus = CreditStatus = {}));
let CustomerCredit = class CustomerCredit {
    id;
    customerId;
    customer;
    type;
    amount;
    balanceBefore;
    balanceAfter;
    status;
    description;
    reference;
    relatedEntityType;
    relatedEntityId;
    expiryDate;
    processedBy;
    approvedBy;
    approvedAt;
    metadata;
    createdAt;
    updatedAt;
};
exports.CustomerCredit = CustomerCredit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CustomerCredit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CustomerCredit.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => customer_entity_1.Customer, customer => customer.creditHistory),
    (0, typeorm_1.JoinColumn)({ name: 'customerId' }),
    __metadata("design:type", customer_entity_1.Customer)
], CustomerCredit.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CreditTransactionType,
    }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], CustomerCredit.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], CustomerCredit.prototype, "balanceBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], CustomerCredit.prototype, "balanceAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CreditStatus,
        default: CreditStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "reference", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "relatedEntityType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "relatedEntityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CustomerCredit.prototype, "expiryDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "processedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CustomerCredit.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CustomerCredit.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomerCredit.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomerCredit.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomerCredit.prototype, "updatedAt", void 0);
exports.CustomerCredit = CustomerCredit = __decorate([
    (0, typeorm_1.Entity)('customer_credit')
], CustomerCredit);
//# sourceMappingURL=customer-credit.entity.js.map