"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Vendor = exports.VendorType = exports.VendorStatus = void 0;
const typeorm_1 = require("typeorm");
const vendor_contact_entity_1 = require("./vendor-contact.entity");
const vendor_evaluation_entity_1 = require("./vendor-evaluation.entity");
const contract_entity_1 = require("./contract.entity");
var VendorStatus;
(function (VendorStatus) {
    VendorStatus["ACTIVE"] = "active";
    VendorStatus["INACTIVE"] = "inactive";
    VendorStatus["SUSPENDED"] = "suspended";
    VendorStatus["BLACKLISTED"] = "blacklisted";
    VendorStatus["PENDING_APPROVAL"] = "pending_approval";
})(VendorStatus || (exports.VendorStatus = VendorStatus = {}));
var VendorType;
(function (VendorType) {
    VendorType["SUPPLIER"] = "supplier";
    VendorType["SERVICE_PROVIDER"] = "service_provider";
    VendorType["CONTRACTOR"] = "contractor";
    VendorType["CONSULTANT"] = "consultant";
    VendorType["DISTRIBUTOR"] = "distributor";
    VendorType["MANUFACTURER"] = "manufacturer";
})(VendorType || (exports.VendorType = VendorType = {}));
let Vendor = class Vendor {
    id;
    vendorNumber;
    name;
    legalName;
    type;
    status;
    taxId;
    registrationNumber;
    address;
    city;
    state;
    zipCode;
    country;
    phone;
    fax;
    email;
    website;
    categories;
    certifications;
    paymentTerms;
    currency;
    creditLimit;
    currentBalance;
    leadTimeDays;
    minimumOrderAmount;
    discountPercentage;
    rating;
    bankDetails;
    insuranceDetails;
    qualityMetrics;
    notes;
    contacts;
    evaluations;
    contracts;
    metadata;
    createdAt;
    updatedAt;
};
exports.Vendor = Vendor;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Vendor.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], Vendor.prototype, "vendorNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Vendor.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "legalName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: VendorType,
        default: VendorType.SUPPLIER,
    }),
    __metadata("design:type", String)
], Vendor.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: VendorStatus,
        default: VendorStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], Vendor.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "taxId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "registrationNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "state", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "zipCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "fax", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "website", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Vendor.prototype, "categories", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Vendor.prototype, "certifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 30 }),
    __metadata("design:type", Number)
], Vendor.prototype, "paymentTerms", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], Vendor.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Vendor.prototype, "creditLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Vendor.prototype, "currentBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 7 }),
    __metadata("design:type", Number)
], Vendor.prototype, "leadTimeDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Vendor.prototype, "minimumOrderAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Vendor.prototype, "discountPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 2, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], Vendor.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Vendor.prototype, "bankDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Vendor.prototype, "insuranceDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Vendor.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Vendor.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => vendor_contact_entity_1.VendorContact, contact => contact.vendor, { cascade: true }),
    __metadata("design:type", Array)
], Vendor.prototype, "contacts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => vendor_evaluation_entity_1.VendorEvaluation, evaluation => evaluation.vendor),
    __metadata("design:type", Array)
], Vendor.prototype, "evaluations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => contract_entity_1.Contract, contract => contract.vendor),
    __metadata("design:type", Array)
], Vendor.prototype, "contracts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Vendor.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Vendor.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Vendor.prototype, "updatedAt", void 0);
exports.Vendor = Vendor = __decorate([
    (0, typeorm_1.Entity)('vendors')
], Vendor);
//# sourceMappingURL=vendor.entity.js.map