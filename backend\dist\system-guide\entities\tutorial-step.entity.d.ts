import { Tutorial } from './tutorial.entity';
export declare enum TutorialStepType {
    INSTRUCTION = "instruction",
    ACTION = "action",
    VERIFICATION = "verification",
    INFORMATION = "information",
    QUIZ = "quiz",
    EXERCISE = "exercise"
}
export declare class TutorialStep {
    id: string;
    tutorialId: string;
    tutorial: Tutorial;
    title: string;
    content: string;
    type: TutorialStepType;
    sortOrder: number;
    media: any;
    interactive: any;
    validation: any;
    hint: string;
    isOptional: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
