"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxRecord = exports.TaxStatus = exports.TaxType = void 0;
const typeorm_1 = require("typeorm");
var TaxType;
(function (TaxType) {
    TaxType["INCOME_TAX"] = "income_tax";
    TaxType["SALES_TAX"] = "sales_tax";
    TaxType["VAT"] = "vat";
    TaxType["PAYROLL_TAX"] = "payroll_tax";
    TaxType["PROPERTY_TAX"] = "property_tax";
    TaxType["EXCISE_TAX"] = "excise_tax";
    TaxType["CUSTOMS_DUTY"] = "customs_duty";
    TaxType["OTHER"] = "other";
})(TaxType || (exports.TaxType = TaxType = {}));
var TaxStatus;
(function (TaxStatus) {
    TaxStatus["CALCULATED"] = "calculated";
    TaxStatus["FILED"] = "filed";
    TaxStatus["PAID"] = "paid";
    TaxStatus["OVERDUE"] = "overdue";
    TaxStatus["AMENDED"] = "amended";
})(TaxStatus || (exports.TaxStatus = TaxStatus = {}));
let TaxRecord = class TaxRecord {
    id;
    taxRecordNumber;
    taxType;
    status;
    taxPeriodStart;
    taxPeriodEnd;
    dueDate;
    taxableAmount;
    taxRate;
    taxAmount;
    penaltyAmount;
    interestAmount;
    totalAmount;
    paidAmount;
    outstandingAmount;
    currency;
    taxAuthority;
    referenceNumber;
    attachments;
    filedDate;
    paidDate;
    filedBy;
    notes;
    calculations;
    metadata;
    createdAt;
    updatedAt;
};
exports.TaxRecord = TaxRecord;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], TaxRecord.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], TaxRecord.prototype, "taxRecordNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TaxType,
    }),
    __metadata("design:type", String)
], TaxRecord.prototype, "taxType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TaxStatus,
        default: TaxStatus.CALCULATED,
    }),
    __metadata("design:type", String)
], TaxRecord.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], TaxRecord.prototype, "taxPeriodStart", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], TaxRecord.prototype, "taxPeriodEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], TaxRecord.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "taxableAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "taxRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "penaltyAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "interestAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "paidAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], TaxRecord.prototype, "outstandingAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], TaxRecord.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], TaxRecord.prototype, "taxAuthority", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], TaxRecord.prototype, "referenceNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], TaxRecord.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], TaxRecord.prototype, "filedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], TaxRecord.prototype, "paidDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], TaxRecord.prototype, "filedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], TaxRecord.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TaxRecord.prototype, "calculations", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TaxRecord.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], TaxRecord.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], TaxRecord.prototype, "updatedAt", void 0);
exports.TaxRecord = TaxRecord = __decorate([
    (0, typeorm_1.Entity)('finance_tax_records')
], TaxRecord);
//# sourceMappingURL=tax-record.entity.js.map