"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancialReportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const financial_report_entity_1 = require("../entities/financial-report.entity");
const account_service_1 = require("./account.service");
const transaction_service_1 = require("./transaction.service");
let FinancialReportService = class FinancialReportService {
    reportRepository;
    accountService;
    transactionService;
    constructor(reportRepository, accountService, transactionService) {
        this.reportRepository = reportRepository;
        this.accountService = accountService;
        this.transactionService = transactionService;
    }
    async create(createReportDto) {
        const report = this.reportRepository.create({
            ...createReportDto,
            status: financial_report_entity_1.ReportStatus.GENERATING,
        });
        const savedReport = await this.reportRepository.save(report);
        this.generateReportData(savedReport.id);
        return savedReport;
    }
    async findAll(filters) {
        const queryBuilder = this.reportRepository.createQueryBuilder('report');
        if (filters?.type) {
            queryBuilder.andWhere('report.type = :type', { type: filters.type });
        }
        if (filters?.status) {
            queryBuilder.andWhere('report.status = :status', { status: filters.status });
        }
        return queryBuilder
            .orderBy('report.createdAt', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const report = await this.reportRepository.findOne({ where: { id } });
        if (!report) {
            throw new common_1.NotFoundException(`Financial report with ID ${id} not found`);
        }
        return report;
    }
    async generateBalanceSheet(startDate, endDate) {
        const accounts = await this.accountService.findAll();
        const assets = accounts.filter(acc => acc.type === 'asset');
        const liabilities = accounts.filter(acc => acc.type === 'liability');
        const equity = accounts.filter(acc => acc.type === 'equity');
        const balanceSheet = {
            asOfDate: endDate,
            assets: {
                currentAssets: assets.filter(acc => acc.subType === 'current_asset'),
                fixedAssets: assets.filter(acc => acc.subType === 'fixed_asset'),
                totalAssets: assets.reduce((sum, acc) => sum + acc.balance, 0),
            },
            liabilities: {
                currentLiabilities: liabilities.filter(acc => acc.subType === 'current_liability'),
                longTermLiabilities: liabilities.filter(acc => acc.subType === 'long_term_liability'),
                totalLiabilities: liabilities.reduce((sum, acc) => sum + acc.balance, 0),
            },
            equity: {
                accounts: equity,
                totalEquity: equity.reduce((sum, acc) => sum + acc.balance, 0),
            },
        };
        return balanceSheet;
    }
    async generateIncomeStatement(startDate, endDate) {
        const accounts = await this.accountService.findAll();
        const revenue = accounts.filter(acc => acc.type === 'revenue');
        const expenses = accounts.filter(acc => acc.type === 'expense');
        const totalRevenue = revenue.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const incomeStatement = {
            period: { startDate, endDate },
            revenue: {
                accounts: revenue,
                total: totalRevenue,
            },
            expenses: {
                operatingExpenses: expenses.filter(acc => acc.subType === 'operating_expense'),
                costOfGoodsSold: expenses.filter(acc => acc.subType === 'cost_of_goods_sold'),
                administrativeExpenses: expenses.filter(acc => acc.subType === 'administrative_expense'),
                total: totalExpenses,
            },
            netIncome: totalRevenue - totalExpenses,
            grossProfit: totalRevenue - expenses.filter(acc => acc.subType === 'cost_of_goods_sold')
                .reduce((sum, acc) => sum + acc.balance, 0),
        };
        return incomeStatement;
    }
    async generateCashFlowStatement(startDate, endDate) {
        const transactions = await this.transactionService.findAll({
            startDate,
            endDate,
            status: 'posted',
        });
        const operatingActivities = transactions.filter(t => ['payment', 'receipt'].includes(t.type));
        const investingActivities = transactions.filter(t => t.type === 'transfer' && t.description.toLowerCase().includes('investment'));
        const financingActivities = transactions.filter(t => t.type === 'transfer' && t.description.toLowerCase().includes('loan'));
        const cashFlow = {
            period: { startDate, endDate },
            operatingActivities: {
                transactions: operatingActivities,
                netCash: operatingActivities.reduce((sum, t) => sum + t.amount, 0),
            },
            investingActivities: {
                transactions: investingActivities,
                netCash: investingActivities.reduce((sum, t) => sum + t.amount, 0),
            },
            financingActivities: {
                transactions: financingActivities,
                netCash: financingActivities.reduce((sum, t) => sum + t.amount, 0),
            },
        };
        return cashFlow;
    }
    async scheduleReport(reportConfig) {
        const report = this.reportRepository.create({
            ...reportConfig,
            isScheduled: true,
            status: financial_report_entity_1.ReportStatus.SCHEDULED,
        });
        return this.reportRepository.save(report);
    }
    async generateReportData(reportId) {
        try {
            const report = await this.findOne(reportId);
            let reportData;
            switch (report.type) {
                case financial_report_entity_1.ReportType.BALANCE_SHEET:
                    reportData = await this.generateBalanceSheet(report.startDate, report.endDate);
                    break;
                case financial_report_entity_1.ReportType.INCOME_STATEMENT:
                    reportData = await this.generateIncomeStatement(report.startDate, report.endDate);
                    break;
                case financial_report_entity_1.ReportType.CASH_FLOW:
                    reportData = await this.generateCashFlowStatement(report.startDate, report.endDate);
                    break;
                case financial_report_entity_1.ReportType.TRIAL_BALANCE:
                    reportData = await this.accountService.getTrialBalance(report.endDate);
                    break;
                default:
                    throw new Error(`Unsupported report type: ${report.type}`);
            }
            report.reportData = JSON.stringify(reportData);
            report.status = financial_report_entity_1.ReportStatus.COMPLETED;
            report.generatedAt = new Date();
            await this.reportRepository.save(report);
        }
        catch (error) {
            const report = await this.findOne(reportId);
            report.status = financial_report_entity_1.ReportStatus.FAILED;
            report.errorMessage = error.message;
            await this.reportRepository.save(report);
        }
    }
};
exports.FinancialReportService = FinancialReportService;
exports.FinancialReportService = FinancialReportService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(financial_report_entity_1.FinancialReport)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        account_service_1.AccountService,
        transaction_service_1.TransactionService])
], FinancialReportService);
//# sourceMappingURL=financial-report.service.js.map