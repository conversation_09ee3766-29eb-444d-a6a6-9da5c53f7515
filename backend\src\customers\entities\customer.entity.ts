import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CustomerContact } from './customer-contact.entity';
import { CustomerAddress } from './customer-address.entity';
import { CustomerGroup } from './customer-group.entity';
import { CustomerNote } from './customer-note.entity';
import { CustomerDocument } from './customer-document.entity';
import { CustomerInteraction } from './customer-interaction.entity';
import { CustomerLoyalty } from './customer-loyalty.entity';
import { CustomerCredit } from './customer-credit.entity';
import { CustomerPreference } from './customer-preference.entity';

export enum CustomerType {
  INDIVIDUAL = 'individual',
  BUSINESS = 'business',
  GOVERNMENT = 'government',
  NON_PROFIT = 'non_profit',
}

export enum CustomerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLACKLISTED = 'blacklisted',
  PROSPECT = 'prospect',
  LEAD = 'lead',
}

export enum CustomerTier {
  BRONZE = 'bronze',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum',
  DIAMOND = 'diamond',
}

@Entity('customers')
export class Customer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  customerNumber: string;

  @Column({
    type: 'enum',
    enum: CustomerType,
    default: CustomerType.INDIVIDUAL,
  })
  type: CustomerType;

  @Column({
    type: 'enum',
    enum: CustomerStatus,
    default: CustomerStatus.PROSPECT,
  })
  status: CustomerStatus;

  @Column({
    type: 'enum',
    enum: CustomerTier,
    default: CustomerTier.BRONZE,
  })
  tier: CustomerTier;

  // Individual Customer Fields
  @Column({ length: 100, nullable: true })
  firstName: string;

  @Column({ length: 100, nullable: true })
  lastName: string;

  @Column({ length: 100, nullable: true })
  middleName: string;

  @Column({ length: 20, nullable: true })
  title: string; // Mr, Mrs, Dr, etc.

  @Column({ type: 'date', nullable: true })
  dateOfBirth: Date;

  @Column({ length: 10, nullable: true })
  gender: string;

  // Business Customer Fields
  @Column({ length: 255, nullable: true })
  companyName: string;

  @Column({ length: 255, nullable: true })
  legalName: string;

  @Column({ length: 50, nullable: true })
  taxId: string;

  @Column({ length: 50, nullable: true })
  registrationNumber: string;

  @Column({ length: 100, nullable: true })
  industry: string;

  @Column({ type: 'int', nullable: true })
  employeeCount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  annualRevenue: number;

  // Contact Information
  @Column({ length: 200, nullable: true })
  primaryEmail: string;

  @Column({ length: 20, nullable: true })
  primaryPhone: string;

  @Column({ length: 255, nullable: true })
  website: string;

  // Financial Information
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  creditLimit: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  currentBalance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalSpent: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  averageOrderValue: number;

  @Column({ type: 'int', default: 30 })
  paymentTerms: number; // days

  @Column({ length: 10, default: 'USD' })
  currency: string;

  // Relationship Information
  @Column({ nullable: true })
  groupId: string;

  @ManyToOne(() => CustomerGroup, { nullable: true })
  @JoinColumn({ name: 'groupId' })
  group: CustomerGroup;

  @Column({ nullable: true })
  assignedTo: string; // Sales rep or account manager

  @Column({ nullable: true })
  referredBy: string;

  @Column({ type: 'date', nullable: true })
  firstPurchaseDate: Date;

  @Column({ type: 'date', nullable: true })
  lastPurchaseDate: Date;

  @Column({ type: 'date', nullable: true })
  lastContactDate: Date;

  // Marketing & Preferences
  @Column({ default: true })
  allowMarketing: boolean;

  @Column({ default: true })
  allowEmail: boolean;

  @Column({ default: true })
  allowSms: boolean;

  @Column({ default: true })
  allowPhone: boolean;

  @Column({ type: 'json', nullable: true })
  communicationPreferences: any;

  @Column({ type: 'json', nullable: true })
  interests: string[];

  @Column({ type: 'json', nullable: true })
  tags: string[];

  // Loyalty & Rewards
  @Column({ type: 'int', default: 0 })
  loyaltyPoints: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  storeCredit: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  discountPercentage: number;

  // Social Media
  @Column({ length: 255, nullable: true })
  facebookProfile: string;

  @Column({ length: 255, nullable: true })
  twitterProfile: string;

  @Column({ length: 255, nullable: true })
  linkedinProfile: string;

  @Column({ length: 255, nullable: true })
  instagramProfile: string;

  // Additional Information
  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 500, nullable: true })
  profilePicture: string;

  @Column({ type: 'json', nullable: true })
  customFields: any;

  // Relationships
  @OneToMany(() => CustomerContact, contact => contact.customer, { cascade: true })
  contacts: CustomerContact[];

  @OneToMany(() => CustomerAddress, address => address.customer, { cascade: true })
  addresses: CustomerAddress[];

  @OneToMany(() => CustomerNote, note => note.customer)
  notes: CustomerNote[];

  @OneToMany(() => CustomerDocument, document => document.customer)
  documents: CustomerDocument[];

  @OneToMany(() => CustomerInteraction, interaction => interaction.customer)
  interactions: CustomerInteraction[];

  @OneToMany(() => CustomerLoyalty, loyalty => loyalty.customer)
  loyaltyHistory: CustomerLoyalty[];

  @OneToMany(() => CustomerCredit, credit => credit.customer)
  creditHistory: CustomerCredit[];

  @OneToMany(() => CustomerPreference, preference => preference.customer)
  preferences: CustomerPreference[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
