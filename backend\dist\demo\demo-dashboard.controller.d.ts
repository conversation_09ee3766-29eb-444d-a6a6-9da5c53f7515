export declare class DemoDashboardController {
    getDashboard(): Promise<{
        message: string;
        user: {
            userId: string;
            email: string;
            role: string;
        };
        stats: {
            totalUsers: number;
            totalProjects: number;
            totalSales: number;
            totalRevenue: number;
        };
        recentActivities: never[];
    }>;
    getStats(): Promise<{
        overview: {
            totalUsers: number;
            totalProjects: number;
            totalSales: number;
            totalRevenue: number;
        };
        departments: {
            sales: {
                active: number;
                pending: number;
            };
            projects: {
                active: number;
                completed: number;
            };
            hr: {
                employees: number;
                departments: number;
            };
            finance: {
                income: number;
                expenses: number;
            };
        };
    }>;
}
