"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const employee_entity_1 = require("../entities/employee.entity");
let EmployeeService = class EmployeeService {
    employeeRepository;
    constructor(employeeRepository) {
        this.employeeRepository = employeeRepository;
    }
    async create(createEmployeeDto) {
        const employeeNumber = await this.generateEmployeeNumber();
        const employee = this.employeeRepository.create({
            ...createEmployeeDto,
            employeeNumber,
        });
        return this.employeeRepository.save(employee);
    }
    async findAll(filters) {
        const queryBuilder = this.employeeRepository.createQueryBuilder('employee')
            .leftJoinAndSelect('employee.department', 'department')
            .leftJoinAndSelect('employee.position', 'position')
            .leftJoinAndSelect('employee.manager', 'manager');
        if (filters?.status) {
            queryBuilder.andWhere('employee.status = :status', { status: filters.status });
        }
        if (filters?.departmentId) {
            queryBuilder.andWhere('employee.departmentId = :departmentId', { departmentId: filters.departmentId });
        }
        if (filters?.positionId) {
            queryBuilder.andWhere('employee.positionId = :positionId', { positionId: filters.positionId });
        }
        if (filters?.managerId) {
            queryBuilder.andWhere('employee.managerId = :managerId', { managerId: filters.managerId });
        }
        if (filters?.search) {
            queryBuilder.andWhere('(employee.firstName LIKE :search OR employee.lastName LIKE :search OR employee.email LIKE :search OR employee.employeeNumber LIKE :search)', { search: `%${filters.search}%` });
        }
        return queryBuilder
            .orderBy('employee.firstName', 'ASC')
            .addOrderBy('employee.lastName', 'ASC')
            .getMany();
    }
    async findOne(id) {
        const employee = await this.employeeRepository.findOne({
            where: { id },
            relations: [
                'department',
                'position',
                'manager',
                'directReports',
                'attendances',
                'leaves',
                'payrolls',
                'performances',
                'trainings',
                'benefits',
            ],
        });
        if (!employee) {
            throw new common_1.NotFoundException(`Employee with ID ${id} not found`);
        }
        return employee;
    }
    async findByEmployeeNumber(employeeNumber) {
        const employee = await this.employeeRepository.findOne({
            where: { employeeNumber },
            relations: ['department', 'position', 'manager'],
        });
        if (!employee) {
            throw new common_1.NotFoundException(`Employee with number ${employeeNumber} not found`);
        }
        return employee;
    }
    async update(id, updateEmployeeDto) {
        const employee = await this.findOne(id);
        Object.assign(employee, updateEmployeeDto);
        return this.employeeRepository.save(employee);
    }
    async remove(id) {
        const employee = await this.findOne(id);
        if (employee.directReports?.length > 0) {
            throw new common_1.BadRequestException('Cannot delete employee with direct reports');
        }
        await this.employeeRepository.remove(employee);
    }
    async terminate(id, terminationDate, reason) {
        const employee = await this.findOne(id);
        employee.status = employee_entity_1.EmployeeStatus.TERMINATED;
        employee.terminationDate = terminationDate;
        if (reason) {
            employee.notes = (employee.notes || '') + `\nTermination Reason: ${reason}`;
        }
        return this.employeeRepository.save(employee);
    }
    async reactivate(id) {
        const employee = await this.findOne(id);
        employee.status = employee_entity_1.EmployeeStatus.ACTIVE;
        employee.terminationDate = null;
        return this.employeeRepository.save(employee);
    }
    async getEmployeeHierarchy(managerId) {
        const queryBuilder = this.employeeRepository.createQueryBuilder('employee')
            .leftJoinAndSelect('employee.department', 'department')
            .leftJoinAndSelect('employee.position', 'position')
            .leftJoinAndSelect('employee.directReports', 'directReports')
            .where('employee.status = :status', { status: employee_entity_1.EmployeeStatus.ACTIVE });
        if (managerId) {
            queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
        }
        else {
            queryBuilder.andWhere('employee.managerId IS NULL');
        }
        return queryBuilder
            .orderBy('employee.firstName', 'ASC')
            .getMany();
    }
    async getEmployeeStats() {
        const totalEmployees = await this.employeeRepository.count();
        const activeEmployees = await this.employeeRepository.count({
            where: { status: employee_entity_1.EmployeeStatus.ACTIVE },
        });
        const inactiveEmployees = await this.employeeRepository.count({
            where: { status: employee_entity_1.EmployeeStatus.INACTIVE },
        });
        const terminatedEmployees = await this.employeeRepository.count({
            where: { status: employee_entity_1.EmployeeStatus.TERMINATED },
        });
        const employeesByDepartment = await this.employeeRepository
            .createQueryBuilder('employee')
            .leftJoin('employee.department', 'department')
            .select('department.name', 'departmentName')
            .addSelect('COUNT(employee.id)', 'count')
            .where('employee.status = :status', { status: employee_entity_1.EmployeeStatus.ACTIVE })
            .groupBy('department.id')
            .getRawMany();
        const employeesByPosition = await this.employeeRepository
            .createQueryBuilder('employee')
            .leftJoin('employee.position', 'position')
            .select('position.title', 'positionTitle')
            .addSelect('COUNT(employee.id)', 'count')
            .where('employee.status = :status', { status: employee_entity_1.EmployeeStatus.ACTIVE })
            .groupBy('position.id')
            .getRawMany();
        return {
            total: totalEmployees,
            active: activeEmployees,
            inactive: inactiveEmployees,
            terminated: terminatedEmployees,
            byDepartment: employeesByDepartment,
            byPosition: employeesByPosition,
        };
    }
    async searchEmployees(searchTerm) {
        return this.employeeRepository
            .createQueryBuilder('employee')
            .leftJoinAndSelect('employee.department', 'department')
            .leftJoinAndSelect('employee.position', 'position')
            .where('(employee.firstName LIKE :search OR employee.lastName LIKE :search OR employee.email LIKE :search OR employee.employeeNumber LIKE :search)', { search: `%${searchTerm}%` })
            .andWhere('employee.status = :status', { status: employee_entity_1.EmployeeStatus.ACTIVE })
            .orderBy('employee.firstName', 'ASC')
            .limit(20)
            .getMany();
    }
    async generateEmployeeNumber() {
        const year = new Date().getFullYear();
        const prefix = `EMP-${year}-`;
        const lastEmployee = await this.employeeRepository.findOne({
            where: { employeeNumber: (0, typeorm_2.Like)(`${prefix}%`) },
            order: { employeeNumber: 'DESC' },
        });
        let nextNumber = 1;
        if (lastEmployee) {
            const lastNumber = parseInt(lastEmployee.employeeNumber.split('-')[2]);
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
    }
};
exports.EmployeeService = EmployeeService;
exports.EmployeeService = EmployeeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(employee_entity_1.Employee)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EmployeeService);
//# sourceMappingURL=employee.service.js.map