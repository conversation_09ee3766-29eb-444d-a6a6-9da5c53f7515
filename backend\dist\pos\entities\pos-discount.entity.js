"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosDiscount = exports.DiscountScope = exports.DiscountType = void 0;
const typeorm_1 = require("typeorm");
const pos_sale_entity_1 = require("./pos-sale.entity");
var DiscountType;
(function (DiscountType) {
    DiscountType["PERCENTAGE"] = "percentage";
    DiscountType["FIXED_AMOUNT"] = "fixed_amount";
    DiscountType["BUY_X_GET_Y"] = "buy_x_get_y";
    DiscountType["BULK_DISCOUNT"] = "bulk_discount";
})(DiscountType || (exports.DiscountType = DiscountType = {}));
var DiscountScope;
(function (DiscountScope) {
    DiscountScope["ITEM"] = "item";
    DiscountScope["TRANSACTION"] = "transaction";
    DiscountScope["CATEGORY"] = "category";
})(DiscountScope || (exports.DiscountScope = DiscountScope = {}));
let PosDiscount = class PosDiscount {
    id;
    saleId;
    sale;
    name;
    type;
    scope;
    amount;
    percentage;
    appliedBy;
    reason;
    promotionId;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosDiscount = PosDiscount;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosDiscount.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosDiscount.prototype, "saleId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_sale_entity_1.PosSale, sale => sale.discounts, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'saleId' }),
    __metadata("design:type", pos_sale_entity_1.PosSale)
], PosDiscount.prototype, "sale", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PosDiscount.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DiscountType,
    }),
    __metadata("design:type", String)
], PosDiscount.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DiscountScope,
    }),
    __metadata("design:type", String)
], PosDiscount.prototype, "scope", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PosDiscount.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosDiscount.prototype, "percentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosDiscount.prototype, "appliedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosDiscount.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosDiscount.prototype, "promotionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosDiscount.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosDiscount.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosDiscount.prototype, "updatedAt", void 0);
exports.PosDiscount = PosDiscount = __decorate([
    (0, typeorm_1.Entity)('pos_discounts')
], PosDiscount);
//# sourceMappingURL=pos-discount.entity.js.map