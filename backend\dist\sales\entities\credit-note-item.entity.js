"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditNoteItem = void 0;
const typeorm_1 = require("typeorm");
const credit_note_entity_1 = require("./credit-note.entity");
let CreditNoteItem = class CreditNoteItem {
    id;
    creditNoteId;
    creditNote;
    lineNumber;
    description;
    productCode;
    unitPrice;
    quantity;
    taxType;
    taxAmount;
    lineTotal;
    unit;
    notes;
    tenantId;
};
exports.CreditNoteItem = CreditNoteItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "creditNoteId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => credit_note_entity_1.CreditNote, creditNote => creditNote.items, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'creditNoteId' }),
    __metadata("design:type", credit_note_entity_1.CreditNote)
], CreditNoteItem.prototype, "creditNote", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CreditNoteItem.prototype, "lineNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "productCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CreditNoteItem.prototype, "unitPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CreditNoteItem.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "taxType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], CreditNoteItem.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CreditNoteItem.prototype, "lineTotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CreditNoteItem.prototype, "tenantId", void 0);
exports.CreditNoteItem = CreditNoteItem = __decorate([
    (0, typeorm_1.Entity)('credit_note_items')
], CreditNoteItem);
//# sourceMappingURL=credit-note-item.entity.js.map