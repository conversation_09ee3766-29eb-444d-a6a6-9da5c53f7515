import { RoleService } from '../services/role.service';
import { Role } from '../entities/role.entity';
export declare class RoleController {
    private readonly roleService;
    constructor(roleService: RoleService);
    create(createRoleDto: Partial<Role>): Promise<Role>;
    findAll(department?: string): Promise<Role[]>;
    findOne(id: string): Promise<Role>;
    createDefaultRoles(): Promise<Role[]>;
    assignDefaultPermissions(): Promise<{
        message: string;
    }>;
    cloneRole(id: string, cloneData: {
        name: string;
        description?: string;
    }): Promise<Role>;
    update(id: string, updateRoleDto: Partial<Role>): Promise<Role>;
    assignPermissions(id: string, permissionData: {
        permissionIds: string[];
    }): Promise<Role>;
    removePermissions(id: string, permissionData: {
        permissionIds: string[];
    }): Promise<Role>;
    remove(id: string): Promise<void>;
}
