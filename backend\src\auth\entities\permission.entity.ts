import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
} from 'typeorm';
import { Role } from './role.entity';

export enum PermissionModule {
  ANALYTICS = 'analytics',
  CUSTOMERS = 'customers',
  COLLECTIONS = 'collections',
  FINANCE = 'finance',
  HR = 'hr',
  INVENTORY = 'inventory',
  IT_SUPPORT = 'it_support',
  POS = 'pos',
  PROCUREMENT = 'procurement',
  PROJECTS = 'projects',
  SALES = 'sales',
  SETTINGS = 'settings',
  SYSTEM_GUIDE = 'system_guide',
  USER_MANAGEMENT = 'user_management',
}

export enum PermissionAction {
  // Basic CRUD operations
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  
  // Advanced operations
  APPROVE = 'approve',
  REJECT = 'reject',
  ASSIGN = 'assign',
  UNASSIGN = 'unassign',
  ESCALATE = 'escalate',
  CLOSE = 'close',
  REOPEN = 'reopen',
  EXPORT = 'export',
  IMPORT = 'import',
  PRINT = 'print',
  
  // Financial operations
  PROCESS_PAYMENT = 'process_payment',
  REFUND = 'refund',
  VOID = 'void',
  ADJUST = 'adjust',
  
  // Reporting and analytics
  VIEW_REPORTS = 'view_reports',
  GENERATE_REPORTS = 'generate_reports',
  VIEW_ANALYTICS = 'view_analytics',
  
  // Administrative operations
  MANAGE_SETTINGS = 'manage_settings',
  MANAGE_USERS = 'manage_users',
  MANAGE_ROLES = 'manage_roles',
  MANAGE_PERMISSIONS = 'manage_permissions',
  
  // System operations
  BACKUP = 'backup',
  RESTORE = 'restore',
  AUDIT = 'audit',
  CONFIGURE = 'configure',
}

@Entity('permissions')
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: PermissionModule,
  })
  module: PermissionModule;

  @Column({
    type: 'enum',
    enum: PermissionAction,
  })
  action: PermissionAction;

  @Column({ length: 100 })
  resource: string; // e.g., 'customer', 'product', 'ticket', etc.

  @Column({ length: 255 })
  name: string; // Human-readable name

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  conditions: any; // Additional conditions for permission

  @ManyToMany(() => Role, role => role.permissions)
  roles: Role[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
