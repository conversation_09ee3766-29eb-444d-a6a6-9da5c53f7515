import { Warehouse } from './warehouse.entity';
import { Stock } from './stock.entity';
export declare enum LocationType {
    SHELF = "shelf",
    BIN = "bin",
    RACK = "rack",
    FLOOR = "floor",
    PALLET = "pallet",
    COOLER = "cooler",
    FREEZER = "freezer",
    QUARANTINE = "quarantine",
    STAGING = "staging",
    RECEIVING = "receiving",
    SHIPPING = "shipping"
}
export declare class Location {
    id: string;
    warehouseId: string;
    warehouse: Warehouse;
    name: string;
    code: string;
    description: string;
    type: LocationType;
    aisle: string;
    bay: string;
    level: string;
    position: string;
    maxWeight: number;
    maxVolume: number;
    maxItems: number;
    isActive: boolean;
    isPickable: boolean;
    isReceivable: boolean;
    latitude: number;
    longitude: number;
    environmentalConditions: any;
    stocks: Stock[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
