import { Repository } from 'typeorm';
import { RecurringInvoice } from '../entities/recurring-invoice.entity';
import { RecurringInvoiceItem } from '../entities/recurring-invoice-item.entity';
export declare class RecurringInvoiceService {
    private recurringInvoiceRepository;
    private recurringInvoiceItemRepository;
    constructor(recurringInvoiceRepository: Repository<RecurringInvoice>, recurringInvoiceItemRepository: Repository<RecurringInvoiceItem>);
    create(createRecurringInvoiceDto: any, tenantId: string): Promise<RecurringInvoice>;
    findAll(tenantId: string): Promise<RecurringInvoice[]>;
    findOne(id: string, tenantId: string): Promise<RecurringInvoice>;
    update(id: string, updateRecurringInvoiceDto: any, tenantId: string): Promise<RecurringInvoice>;
    remove(id: string, tenantId: string): Promise<void>;
    updateStatus(id: string, status: string, tenantId: string): Promise<RecurringInvoice>;
    generateInvoice(id: string, tenantId: string): Promise<{
        success: boolean;
        invoiceId?: string;
    }>;
    getRecurringInvoiceStats(tenantId: string): Promise<{
        totalRecurringInvoices: number;
        activeRecurringInvoices: number;
        pausedRecurringInvoices: number;
        totalValue: number;
        totalGenerated: number;
        frequencyStats: any[];
    }>;
    private calculateRecurringInvoiceTotals;
    private calculateItemTax;
}
