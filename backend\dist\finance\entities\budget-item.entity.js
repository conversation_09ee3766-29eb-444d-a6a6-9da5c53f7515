"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BudgetItem = exports.BudgetPeriod = exports.BudgetItemType = void 0;
const typeorm_1 = require("typeorm");
const budget_entity_1 = require("./budget.entity");
const account_entity_1 = require("./account.entity");
var BudgetItemType;
(function (BudgetItemType) {
    BudgetItemType["REVENUE"] = "revenue";
    BudgetItemType["EXPENSE"] = "expense";
    BudgetItemType["CAPITAL"] = "capital";
})(BudgetItemType || (exports.BudgetItemType = BudgetItemType = {}));
var BudgetPeriod;
(function (BudgetPeriod) {
    BudgetPeriod["MONTHLY"] = "monthly";
    BudgetPeriod["QUARTERLY"] = "quarterly";
    BudgetPeriod["ANNUALLY"] = "annually";
})(BudgetPeriod || (exports.BudgetPeriod = BudgetPeriod = {}));
let BudgetItem = class BudgetItem {
    id;
    budgetId;
    budget;
    accountId;
    account;
    itemName;
    description;
    type;
    period;
    budgetedAmount;
    actualAmount;
    variance;
    variancePercentage;
    encumberedAmount;
    availableAmount;
    monthlyBreakdown;
    notes;
    isActive;
    createdAt;
    updatedAt;
};
exports.BudgetItem = BudgetItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], BudgetItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BudgetItem.prototype, "budgetId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => budget_entity_1.Budget, budget => budget.budgetItems, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'budgetId' }),
    __metadata("design:type", budget_entity_1.Budget)
], BudgetItem.prototype, "budget", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BudgetItem.prototype, "accountId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => account_entity_1.Account),
    (0, typeorm_1.JoinColumn)({ name: 'accountId' }),
    __metadata("design:type", account_entity_1.Account)
], BudgetItem.prototype, "account", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], BudgetItem.prototype, "itemName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], BudgetItem.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BudgetItemType,
    }),
    __metadata("design:type", String)
], BudgetItem.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BudgetPeriod,
        default: BudgetPeriod.MONTHLY,
    }),
    __metadata("design:type", String)
], BudgetItem.prototype, "period", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], BudgetItem.prototype, "budgetedAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BudgetItem.prototype, "actualAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BudgetItem.prototype, "variance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BudgetItem.prototype, "variancePercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BudgetItem.prototype, "encumberedAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BudgetItem.prototype, "availableAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], BudgetItem.prototype, "monthlyBreakdown", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], BudgetItem.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], BudgetItem.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], BudgetItem.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], BudgetItem.prototype, "updatedAt", void 0);
exports.BudgetItem = BudgetItem = __decorate([
    (0, typeorm_1.Entity)('finance_budget_items')
], BudgetItem);
//# sourceMappingURL=budget-item.entity.js.map