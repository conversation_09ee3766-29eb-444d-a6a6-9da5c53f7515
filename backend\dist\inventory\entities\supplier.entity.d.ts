import { Product } from './product.entity';
import { PurchaseOrder } from './purchase-order.entity';
export declare enum SupplierStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    BLACKLISTED = "blacklisted"
}
export declare enum SupplierType {
    MANUFACTURER = "manufacturer",
    DISTRIBUTOR = "distributor",
    WHOLESALER = "wholesaler",
    RETAILER = "retailer",
    SERVICE_PROVIDER = "service_provider"
}
export declare class Supplier {
    id: string;
    code: string;
    name: string;
    legalName: string;
    type: SupplierType;
    status: SupplierStatus;
    taxId: string;
    registrationNumber: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone: string;
    fax: string;
    email: string;
    website: string;
    contactPerson: string;
    contactTitle: string;
    contactPhone: string;
    contactEmail: string;
    paymentTermsDays: number;
    currency: string;
    creditLimit: number;
    currentBalance: number;
    leadTimeDays: number;
    minimumOrderAmount: number;
    discountPercentage: number;
    rating: number;
    bankDetails: any;
    certifications: string[];
    documents: string[];
    notes: string;
    products: Product[];
    purchaseOrders: PurchaseOrder[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
