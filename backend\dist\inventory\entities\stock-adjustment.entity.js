"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockAdjustment = exports.AdjustmentReason = exports.AdjustmentType = void 0;
const typeorm_1 = require("typeorm");
const product_entity_1 = require("./product.entity");
const warehouse_entity_1 = require("./warehouse.entity");
var AdjustmentType;
(function (AdjustmentType) {
    AdjustmentType["INCREASE"] = "increase";
    AdjustmentType["DECREASE"] = "decrease";
})(AdjustmentType || (exports.AdjustmentType = AdjustmentType = {}));
var AdjustmentReason;
(function (AdjustmentReason) {
    AdjustmentReason["PHYSICAL_COUNT"] = "physical_count";
    AdjustmentReason["DAMAGE"] = "damage";
    AdjustmentReason["EXPIRY"] = "expiry";
    AdjustmentReason["THEFT"] = "theft";
    AdjustmentReason["LOSS"] = "loss";
    AdjustmentReason["FOUND"] = "found";
    AdjustmentReason["CORRECTION"] = "correction";
    AdjustmentReason["WRITE_OFF"] = "write_off";
    AdjustmentReason["OTHER"] = "other";
})(AdjustmentReason || (exports.AdjustmentReason = AdjustmentReason = {}));
let StockAdjustment = class StockAdjustment {
    id;
    adjustmentNumber;
    productId;
    product;
    warehouseId;
    warehouse;
    type;
    reason;
    quantityBefore;
    quantityAdjusted;
    quantityAfter;
    unitCost;
    totalCost;
    adjustmentDate;
    notes;
    performedBy;
    approvedBy;
    approvedAt;
    metadata;
    createdAt;
    updatedAt;
};
exports.StockAdjustment = StockAdjustment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], StockAdjustment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], StockAdjustment.prototype, "adjustmentNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], StockAdjustment.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.Product),
    (0, typeorm_1.JoinColumn)({ name: 'productId' }),
    __metadata("design:type", product_entity_1.Product)
], StockAdjustment.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], StockAdjustment.prototype, "warehouseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => warehouse_entity_1.Warehouse),
    (0, typeorm_1.JoinColumn)({ name: 'warehouseId' }),
    __metadata("design:type", warehouse_entity_1.Warehouse)
], StockAdjustment.prototype, "warehouse", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AdjustmentType,
    }),
    __metadata("design:type", String)
], StockAdjustment.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AdjustmentReason,
    }),
    __metadata("design:type", String)
], StockAdjustment.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], StockAdjustment.prototype, "quantityBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], StockAdjustment.prototype, "quantityAdjusted", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], StockAdjustment.prototype, "quantityAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], StockAdjustment.prototype, "unitCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], StockAdjustment.prototype, "totalCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], StockAdjustment.prototype, "adjustmentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], StockAdjustment.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockAdjustment.prototype, "performedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], StockAdjustment.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], StockAdjustment.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StockAdjustment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], StockAdjustment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], StockAdjustment.prototype, "updatedAt", void 0);
exports.StockAdjustment = StockAdjustment = __decorate([
    (0, typeorm_1.Entity)('inventory_stock_adjustments')
], StockAdjustment);
//# sourceMappingURL=stock-adjustment.entity.js.map