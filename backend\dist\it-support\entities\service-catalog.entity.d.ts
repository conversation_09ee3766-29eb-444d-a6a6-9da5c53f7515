export declare enum ServiceStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DEPRECATED = "deprecated",
    UNDER_DEVELOPMENT = "under_development"
}
export declare enum ServiceType {
    BUSINESS_SERVICE = "business_service",
    TECHNICAL_SERVICE = "technical_service",
    INFRASTRUCTURE_SERVICE = "infrastructure_service",
    APPLICATION_SERVICE = "application_service"
}
export declare class ServiceCatalog {
    id: string;
    name: string;
    description: string;
    type: ServiceType;
    status: ServiceStatus;
    category: string;
    serviceOwner: string;
    businessOwner: string;
    cost: number;
    currency: string;
    sla: string;
    dependencies: string[];
    supportedBy: string[];
    requestProcess: string;
    deliverables: string[];
    estimatedDeliveryDays: number;
    isRequestable: boolean;
    requiresApproval: boolean;
    approvers: string[];
    requestForm: any;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
