import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { SystemSetting } from './entities/system-setting.entity';
import { UserSetting } from './entities/user-setting.entity';
import { CompanySetting } from './entities/company-setting.entity';
import { EmailTemplate } from './entities/email-template.entity';
import { NotificationTemplate } from './entities/notification-template.entity';
import { WorkflowTemplate } from './entities/workflow-template.entity';
import { CustomField } from './entities/custom-field.entity';
import { CustomFieldValue } from './entities/custom-field-value.entity';
import { Integration } from './entities/integration.entity';
import { APIKey } from './entities/api-key.entity';
import { AuditLog } from './entities/audit-log.entity';
import { SystemBackup } from './entities/system-backup.entity';

// Services
import { SystemSettingService } from './services/system-setting.service';
import { UserSettingService } from './services/user-setting.service';
import { CompanySettingService } from './services/company-setting.service';
import { EmailTemplateService } from './services/email-template.service';
import { NotificationService } from './services/notification.service';
import { WorkflowService } from './services/workflow.service';
import { CustomFieldService } from './services/custom-field.service';
import { IntegrationService } from './services/integration.service';
import { APIKeyService } from './services/api-key.service';
import { AuditLogService } from './services/audit-log.service';
import { BackupService } from './services/backup.service';

// Controllers
import { SystemSettingController } from './controllers/system-setting.controller';
import { UserSettingController } from './controllers/user-setting.controller';
import { CompanySettingController } from './controllers/company-setting.controller';
import { EmailTemplateController } from './controllers/email-template.controller';
import { NotificationController } from './controllers/notification.controller';
import { WorkflowController } from './controllers/workflow.controller';
import { CustomFieldController } from './controllers/custom-field.controller';
import { IntegrationController } from './controllers/integration.controller';
import { APIKeyController } from './controllers/api-key.controller';
import { AuditLogController } from './controllers/audit-log.controller';
import { BackupController } from './controllers/backup.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SystemSetting,
      UserSetting,
      CompanySetting,
      EmailTemplate,
      NotificationTemplate,
      WorkflowTemplate,
      CustomField,
      CustomFieldValue,
      Integration,
      APIKey,
      AuditLog,
      SystemBackup,
    ]),
  ],
  controllers: [
    SystemSettingController,
    UserSettingController,
    CompanySettingController,
    EmailTemplateController,
    NotificationController,
    WorkflowController,
    CustomFieldController,
    IntegrationController,
    APIKeyController,
    AuditLogController,
    BackupController,
  ],
  providers: [
    SystemSettingService,
    UserSettingService,
    CompanySettingService,
    EmailTemplateService,
    NotificationService,
    WorkflowService,
    CustomFieldService,
    IntegrationService,
    APIKeyService,
    AuditLogService,
    BackupService,
  ],
  exports: [
    SystemSettingService,
    UserSettingService,
    CompanySettingService,
    EmailTemplateService,
    NotificationService,
    WorkflowService,
    CustomFieldService,
    IntegrationService,
    APIKeyService,
    AuditLogService,
    BackupService,
  ],
})
export class SettingsModule {}
