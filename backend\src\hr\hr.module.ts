import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Employee } from './entities/employee.entity';
import { Department } from './entities/department.entity';
import { Position } from './entities/position.entity';
import { Attendance } from './entities/attendance.entity';
import { Leave } from './entities/leave.entity';
import { LeaveType } from './entities/leave-type.entity';
import { Payroll } from './entities/payroll.entity';
import { PayrollItem } from './entities/payroll-item.entity';
import { Performance } from './entities/performance.entity';
import { Training } from './entities/training.entity';
import { Benefit } from './entities/benefit.entity';
import { EmployeeBenefit } from './entities/employee-benefit.entity';

// Services
import { EmployeeService } from './services/employee.service';
import { DepartmentService } from './services/department.service';
import { AttendanceService } from './services/attendance.service';
import { LeaveService } from './services/leave.service';
import { PayrollService } from './services/payroll.service';
import { PerformanceService } from './services/performance.service';
import { TrainingService } from './services/training.service';
import { BenefitService } from './services/benefit.service';

// Controllers
import { EmployeeController } from './controllers/employee.controller';
import { DepartmentController } from './controllers/department.controller';
import { AttendanceController } from './controllers/attendance.controller';
import { LeaveController } from './controllers/leave.controller';
import { PayrollController } from './controllers/payroll.controller';
import { PerformanceController } from './controllers/performance.controller';
import { TrainingController } from './controllers/training.controller';
import { BenefitController } from './controllers/benefit.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Employee,
      Department,
      Position,
      Attendance,
      Leave,
      LeaveType,
      Payroll,
      PayrollItem,
      Performance,
      Training,
      Benefit,
      EmployeeBenefit,
    ]),
  ],
  controllers: [
    EmployeeController,
    DepartmentController,
    AttendanceController,
    LeaveController,
    PayrollController,
    PerformanceController,
    TrainingController,
    BenefitController,
  ],
  providers: [
    EmployeeService,
    DepartmentService,
    AttendanceService,
    LeaveService,
    PayrollService,
    PerformanceService,
    TrainingService,
    BenefitService,
  ],
  exports: [
    EmployeeService,
    DepartmentService,
    AttendanceService,
    LeaveService,
    PayrollService,
    PerformanceService,
    TrainingService,
    BenefitService,
  ],
})
export class HrModule {}
