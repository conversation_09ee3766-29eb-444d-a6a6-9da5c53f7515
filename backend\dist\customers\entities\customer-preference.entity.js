"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerPreference = exports.PreferenceType = void 0;
const typeorm_1 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
var PreferenceType;
(function (PreferenceType) {
    PreferenceType["COMMUNICATION"] = "communication";
    PreferenceType["PRODUCT"] = "product";
    PreferenceType["SERVICE"] = "service";
    PreferenceType["DELIVERY"] = "delivery";
    PreferenceType["PAYMENT"] = "payment";
    PreferenceType["MARKETING"] = "marketing";
    PreferenceType["PRIVACY"] = "privacy";
    PreferenceType["NOTIFICATION"] = "notification";
    PreferenceType["CUSTOM"] = "custom";
})(PreferenceType || (exports.PreferenceType = PreferenceType = {}));
let CustomerPreference = class CustomerPreference {
    id;
    customerId;
    customer;
    type;
    key;
    value;
    description;
    isActive;
    metadata;
    createdAt;
    updatedAt;
};
exports.CustomerPreference = CustomerPreference;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CustomerPreference.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CustomerPreference.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => customer_entity_1.Customer, customer => customer.preferences),
    (0, typeorm_1.JoinColumn)({ name: 'customerId' }),
    __metadata("design:type", customer_entity_1.Customer)
], CustomerPreference.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PreferenceType,
    }),
    __metadata("design:type", String)
], CustomerPreference.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CustomerPreference.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CustomerPreference.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomerPreference.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CustomerPreference.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomerPreference.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomerPreference.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomerPreference.prototype, "updatedAt", void 0);
exports.CustomerPreference = CustomerPreference = __decorate([
    (0, typeorm_1.Entity)('customer_preferences')
], CustomerPreference);
//# sourceMappingURL=customer-preference.entity.js.map