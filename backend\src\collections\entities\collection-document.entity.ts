import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CollectionCase } from './collection-case.entity';

export enum DocumentType {
  DEMAND_LETTER = 'demand_letter',
  PAYMENT_AGREEMENT = 'payment_agreement',
  SETTLEMENT_AGREEMENT = 'settlement_agreement',
  LEGAL_NOTICE = 'legal_notice',
  COURT_DOCUMENT = 'court_document',
  PAYMENT_RECEIPT = 'payment_receipt',
  CORRESPONDENCE = 'correspondence',
  DISPUTE_DOCUMENT = 'dispute_document',
  IDENTIFICATION = 'identification',
  FINANCIAL_STATEMENT = 'financial_statement',
  OTHER = 'other',
}

@Entity('collection_documents')
export class CollectionDocument {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  caseId: string;

  @ManyToOne(() => CollectionCase, collectionCase => collectionCase.documents)
  @JoinColumn({ name: 'caseId' })
  case: CollectionCase;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.OTHER,
  })
  type: DocumentType;

  @Column({ length: 255 })
  fileName: string;

  @Column({ length: 255 })
  originalName: string;

  @Column({ length: 500 })
  filePath: string;

  @Column({ length: 100 })
  mimeType: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column()
  uploadedBy: string;

  @Column({ type: 'date', nullable: true })
  documentDate: Date;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isConfidential: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
