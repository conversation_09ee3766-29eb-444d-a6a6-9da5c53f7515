import { AuditReportsService } from '../services/audit-reports.service';
import { CreateAuditReportDto } from '../dto/create-audit-report.dto';
import { UpdateAuditReportDto } from '../dto/update-audit-report.dto';
import { AuditReportsQueryDto } from '../dto/audit-reports-query.dto';
export declare class AuditReportsController {
    private readonly auditReportsService;
    constructor(auditReportsService: AuditReportsService);
    getAuditReports(query: AuditReportsQueryDto, req: any): Promise<{
        success: boolean;
        data: {
            reports: {
                totalFindings: number;
                criticalFindings: number;
                findings: import("../entities/audit-finding.entity").AuditFinding[];
                id: string;
                companyId: string;
                year: number;
                quarter: number;
                reportType: string;
                status: string;
                department: string;
                scope: string;
                auditCategory: string;
                regulatoryFramework: string[];
                riskLevel: string;
                auditor: string;
                auditScope: string;
                objectives: string;
                methodology: string;
                executiveSummary: string;
                complianceScore: number;
                highFindings: number;
                mediumFindings: number;
                lowFindings: number;
                recommendations: number;
                plannedStartDate: Date;
                plannedEndDate: Date;
                actualStartDate: Date;
                actualEndDate: Date;
                completedDate: Date;
                auditTeam: {
                    leadAuditor: string;
                    members: string[];
                    externalAuditors?: string[];
                };
                auditCriteria: {
                    standards: string[];
                    regulations: string[];
                    policies: string[];
                };
                riskAssessment: {
                    inherentRisk: string;
                    controlRisk: string;
                    detectionRisk: string;
                    overallRisk: string;
                };
                samplingMethod: {
                    type: string;
                    size: number;
                    criteria: string;
                };
                limitations: string;
                conclusion: string;
                managementResponse: string;
                attachments: {
                    fileName: string;
                    fileUrl: string;
                    fileType: string;
                    uploadDate: Date;
                }[];
                distributionList: {
                    name: string;
                    email: string;
                    role: string;
                }[];
                nextAuditDate: Date;
                followUpActions: {
                    action: string;
                    responsible: string;
                    dueDate: Date;
                    status: string;
                }[];
                financialData: {
                    totalIncome: number;
                    totalExpenses: number;
                    netResult: number;
                    auditedTransactions: number;
                    discrepancies: number;
                    materialMisstatements: number;
                    departmentBreakdown: {
                        department: string;
                        income: number;
                        expenses: number;
                        accuracy: number;
                    }[];
                    incomeVerification: {
                        revenueRecognitionCompliant: boolean;
                        invoiceMatchingVerified: boolean;
                        cashReceiptsReconciled: boolean;
                        timingDifferences: number;
                    };
                    expenseVerification: {
                        purchaseOrderMatching: boolean;
                        payrollCalculationsVerified: boolean;
                        accrualProceduresCompliant: boolean;
                        unauthorizedExpenses: number;
                    };
                };
                specializedData: {
                    bsaAmlData?: {
                        suspiciousActivityReports: number;
                        currencyTransactionReports: number;
                        customerDueDiligenceReviews: number;
                        sanctionsScreeningResults: number;
                        complianceViolations: number;
                        sarFilingTimeliness: number;
                        ctrAccuracy: number;
                        kycCompleteness: number;
                    };
                    creditData?: {
                        loansReviewed: number;
                        creditRiskRating: string;
                        portfolioQuality: number;
                        allowanceAdequacy: number;
                        underwritingCompliance: number;
                        concentrationRisk: number;
                        creditPolicyCompliance: number;
                        loanDocumentationScore: number;
                    };
                    itSecurityData?: {
                        vulnerabilitiesFound: number;
                        securityIncidents: number;
                        complianceGaps: number;
                        penetrationTestResults: string;
                        dataBreachRisk: string;
                        accessControlsScore: number;
                        encryptionCompliance: number;
                        backupRecoveryScore: number;
                    };
                    operationsData?: {
                        processEfficiency: number;
                        controlDeficiencies: number;
                        procedureCompliance: number;
                        staffingAdequacy: number;
                        branchOperationsScore: number;
                        cashManagementScore: number;
                        customerServiceScore: number;
                    };
                    trustData?: {
                        fiduciaryCompliance: number;
                        investmentPolicyAdherence: number;
                        clientReportingTimeliness: number;
                        regulatoryFilingCompleteness: number;
                        assetSafeguarding: number;
                        conflictOfInterestManagement: number;
                    };
                    soxData?: {
                        internalControlsEffectiveness: number;
                        financialReportingAccuracy: number;
                        managementAssessmentScore: number;
                        auditCommitteeOversight: number;
                        disclosureControlsScore: number;
                        fdiciaCertificationStatus: string;
                    };
                    almData?: {
                        interestRateRiskScore: number;
                        liquidityRiskScore: number;
                        capitalAdequacyRatio: number;
                        assetQualityScore: number;
                        earningsStability: number;
                        managementQuality: number;
                    };
                };
                createdBy: string;
                updatedBy: string;
                createdDate: Date;
                updatedDate: Date;
                company: import("../../company/entities/company.entity").Company;
                creator: import("../../user/entities/user.entity").User;
                updater: import("../../user/entities/user.entity").User;
            }[];
            findings: import("../entities/audit-finding.entity").AuditFinding[];
            total: number;
            page: number;
            limit: number;
            totalPages: number;
            metrics: {
                totalReports: number;
                completedReports: number;
                pendingReports: number;
                averageComplianceScore: number;
                totalFindings: number;
                resolvedFindings: number;
                criticalFindings: number;
                improvementTrend: number;
                financialMetrics: {
                    totalAuditedIncome: number;
                    totalAuditedExpenses: number;
                    totalDiscrepancies: number;
                    materialMisstatements: number;
                    financialAccuracy: number;
                    departmentCoverage: number;
                };
                financialInstitutionMetrics: {
                    bsaAmlCompliance: number;
                    creditRiskScore: number;
                    operationalRiskLevel: string;
                    itSecurityScore: number;
                    regulatoryViolations: number;
                    totalSuspiciousActivities: number;
                    networkVulnerabilities: number;
                    trustOperationsCompliance: number;
                    soxComplianceScore: number;
                    enterpriseRiskRating: string;
                };
            };
        };
        message: string;
    }>;
    getAuditMetrics(year: number, req: any): Promise<{
        success: boolean;
        data: {
            totalReports: number;
            completedReports: number;
            pendingReports: number;
            averageComplianceScore: number;
            totalFindings: number;
            resolvedFindings: number;
            criticalFindings: number;
            improvementTrend: number;
            financialMetrics: {
                totalAuditedIncome: number;
                totalAuditedExpenses: number;
                totalDiscrepancies: number;
                materialMisstatements: number;
                financialAccuracy: number;
                departmentCoverage: number;
            };
            financialInstitutionMetrics: {
                bsaAmlCompliance: number;
                creditRiskScore: number;
                operationalRiskLevel: string;
                itSecurityScore: number;
                regulatoryViolations: number;
                totalSuspiciousActivities: number;
                networkVulnerabilities: number;
                trustOperationsCompliance: number;
                soxComplianceScore: number;
                enterpriseRiskRating: string;
            };
        };
        message: string;
    }>;
    getAuditReportById(id: string, req: any): Promise<{
        success: boolean;
        data: {
            totalFindings: number;
            criticalFindings: number;
            findings: import("../entities/audit-finding.entity").AuditFinding[];
            id: string;
            companyId: string;
            year: number;
            quarter: number;
            reportType: string;
            status: string;
            department: string;
            scope: string;
            auditCategory: string;
            regulatoryFramework: string[];
            riskLevel: string;
            auditor: string;
            auditScope: string;
            objectives: string;
            methodology: string;
            executiveSummary: string;
            complianceScore: number;
            highFindings: number;
            mediumFindings: number;
            lowFindings: number;
            recommendations: number;
            plannedStartDate: Date;
            plannedEndDate: Date;
            actualStartDate: Date;
            actualEndDate: Date;
            completedDate: Date;
            auditTeam: {
                leadAuditor: string;
                members: string[];
                externalAuditors?: string[];
            };
            auditCriteria: {
                standards: string[];
                regulations: string[];
                policies: string[];
            };
            riskAssessment: {
                inherentRisk: string;
                controlRisk: string;
                detectionRisk: string;
                overallRisk: string;
            };
            samplingMethod: {
                type: string;
                size: number;
                criteria: string;
            };
            limitations: string;
            conclusion: string;
            managementResponse: string;
            attachments: {
                fileName: string;
                fileUrl: string;
                fileType: string;
                uploadDate: Date;
            }[];
            distributionList: {
                name: string;
                email: string;
                role: string;
            }[];
            nextAuditDate: Date;
            followUpActions: {
                action: string;
                responsible: string;
                dueDate: Date;
                status: string;
            }[];
            financialData: {
                totalIncome: number;
                totalExpenses: number;
                netResult: number;
                auditedTransactions: number;
                discrepancies: number;
                materialMisstatements: number;
                departmentBreakdown: {
                    department: string;
                    income: number;
                    expenses: number;
                    accuracy: number;
                }[];
                incomeVerification: {
                    revenueRecognitionCompliant: boolean;
                    invoiceMatchingVerified: boolean;
                    cashReceiptsReconciled: boolean;
                    timingDifferences: number;
                };
                expenseVerification: {
                    purchaseOrderMatching: boolean;
                    payrollCalculationsVerified: boolean;
                    accrualProceduresCompliant: boolean;
                    unauthorizedExpenses: number;
                };
            };
            specializedData: {
                bsaAmlData?: {
                    suspiciousActivityReports: number;
                    currencyTransactionReports: number;
                    customerDueDiligenceReviews: number;
                    sanctionsScreeningResults: number;
                    complianceViolations: number;
                    sarFilingTimeliness: number;
                    ctrAccuracy: number;
                    kycCompleteness: number;
                };
                creditData?: {
                    loansReviewed: number;
                    creditRiskRating: string;
                    portfolioQuality: number;
                    allowanceAdequacy: number;
                    underwritingCompliance: number;
                    concentrationRisk: number;
                    creditPolicyCompliance: number;
                    loanDocumentationScore: number;
                };
                itSecurityData?: {
                    vulnerabilitiesFound: number;
                    securityIncidents: number;
                    complianceGaps: number;
                    penetrationTestResults: string;
                    dataBreachRisk: string;
                    accessControlsScore: number;
                    encryptionCompliance: number;
                    backupRecoveryScore: number;
                };
                operationsData?: {
                    processEfficiency: number;
                    controlDeficiencies: number;
                    procedureCompliance: number;
                    staffingAdequacy: number;
                    branchOperationsScore: number;
                    cashManagementScore: number;
                    customerServiceScore: number;
                };
                trustData?: {
                    fiduciaryCompliance: number;
                    investmentPolicyAdherence: number;
                    clientReportingTimeliness: number;
                    regulatoryFilingCompleteness: number;
                    assetSafeguarding: number;
                    conflictOfInterestManagement: number;
                };
                soxData?: {
                    internalControlsEffectiveness: number;
                    financialReportingAccuracy: number;
                    managementAssessmentScore: number;
                    auditCommitteeOversight: number;
                    disclosureControlsScore: number;
                    fdiciaCertificationStatus: string;
                };
                almData?: {
                    interestRateRiskScore: number;
                    liquidityRiskScore: number;
                    capitalAdequacyRatio: number;
                    assetQualityScore: number;
                    earningsStability: number;
                    managementQuality: number;
                };
            };
            createdBy: string;
            updatedBy: string;
            createdDate: Date;
            updatedDate: Date;
            company: import("../../company/entities/company.entity").Company;
            creator: import("../../user/entities/user.entity").User;
            updater: import("../../user/entities/user.entity").User;
        };
        message: string;
    }>;
    createAuditReport(createAuditReportDto: CreateAuditReportDto, req: any): Promise<{
        success: boolean;
        data: import("../entities/audit-report.entity").AuditReport;
        message: string;
    }>;
    updateAuditReport(id: string, updateAuditReportDto: UpdateAuditReportDto, req: any): Promise<{
        success: boolean;
        data: import("../entities/audit-report.entity").AuditReport;
        message: string;
    }>;
    deleteAuditReport(id: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    exportAuditReport(id: string, format: 'pdf' | 'excel', req: any): Promise<{
        success: boolean;
        data: {
            fileName: string;
            downloadUrl: string;
            format: "pdf" | "excel";
            size: string;
            generatedAt: Date;
        };
        message: string;
    }>;
    getAuditFindings(id: string, req: any): Promise<{
        success: boolean;
        data: import("../entities/audit-finding.entity").AuditFinding[];
        message: string;
    }>;
    addAuditFinding(id: string, findingData: any, req: any): Promise<{
        success: boolean;
        data: import("../entities/audit-finding.entity").AuditFinding[];
        message: string;
    }>;
    updateAuditReportStatus(id: string, status: string, req: any): Promise<{
        success: boolean;
        data: import("../entities/audit-report.entity").AuditReport;
        message: string;
    }>;
    getComplianceTrends(years: number | undefined, req: any): Promise<{
        success: boolean;
        data: {
            year: number;
            averageComplianceScore: number;
            totalReports: number;
            completedReports: number;
        }[];
        message: string;
    }>;
}
