import { Repository } from 'typeorm';
import { Payroll } from '../entities/payroll.entity';
import { PayrollItem } from '../entities/payroll-item.entity';
export declare class PayrollService {
    private payrollRepository;
    private payrollItemRepository;
    constructor(payrollRepository: Repository<Payroll>, payrollItemRepository: Repository<PayrollItem>);
    create(createPayrollDto: any): Promise<Payroll>;
    findAll(filters?: any): Promise<Payroll[]>;
    findOne(id: string): Promise<Payroll>;
    calculatePayroll(id: string): Promise<Payroll>;
    approvePayroll(id: string, approvedBy: string): Promise<Payroll>;
    processPayment(id: string, paidBy: string): Promise<Payroll>;
    addPayrollItem(payrollId: string, createPayrollItemDto: any): Promise<PayrollItem>;
    private generatePayrollNumber;
}
