// Customer creation logic verification
console.log('🧪 Testing Customer Creation Logic...\n');

// Simulate the customer controller logic
class CustomerController {
  constructor() {
    this.customers = [];
    this.customerCounter = 1;
  }

  getCustomers() {
    console.log('📋 GET /api/sales/customers - Fetching customers');
    console.log(`📊 Current customers count: ${this.customers.length}`);
    
    const response = {
      data: this.customers,
      total: this.customers.length,
      page: 1,
      limit: 20,
    };
    
    console.log('✅ Returning customers:', response);
    return response;
  }

  createCustomer(customerData) {
    console.log('🆕 POST /api/sales/customers - Creating new customer');
    console.log('📝 Received customer data:', customerData);
    
    const newCustomer = {
      id: `customer-${this.customerCounter++}`,
      customerNumber: `CUST-${String(this.customerCounter).padStart(6, '0')}`,
      ...customerData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    this.customers.push(newCustomer);
    
    console.log('✅ Customer created successfully:', newCustomer);
    console.log(`📊 Total customers now: ${this.customers.length}`);
    
    const response = {
      success: true,
      data: newCustomer,
      message: 'Customer created successfully',
    };
    
    console.log('📤 Sending response:', response);
    return response;
  }

  getCustomerStats() {
    const stats = {
      total: this.customers.length,
      active: this.customers.filter(c => c.status === 'active').length,
      inactive: this.customers.filter(c => c.status === 'inactive').length,
      individual: this.customers.filter(c => c.type === 'individual').length,
      commercial: this.customers.filter(c => c.type === 'commercial').length,
    };
    
    console.log('📊 Customer stats:', stats);
    return stats;
  }
}

// Test the customer controller
const controller = new CustomerController();

console.log('='.repeat(50));
console.log('TEST 1: Get empty customer list');
console.log('='.repeat(50));
controller.getCustomers();

console.log('\n' + '='.repeat(50));
console.log('TEST 2: Create first customer (Individual)');
console.log('='.repeat(50));
const customer1 = controller.createCustomer({
  name: 'John Smith',
  type: 'individual',
  email: '<EMAIL>',
  phone: '******-0123',
  address: '123 Main St',
  city: 'New York',
  status: 'active'
});

console.log('\n' + '='.repeat(50));
console.log('TEST 3: Create second customer (Commercial)');
console.log('='.repeat(50));
const customer2 = controller.createCustomer({
  name: 'ABC Corporation',
  type: 'commercial',
  email: '<EMAIL>',
  phone: '******-0456',
  address: '456 Business Ave',
  city: 'Los Angeles',
  status: 'active'
});

console.log('\n' + '='.repeat(50));
console.log('TEST 4: Get customer list with data');
console.log('='.repeat(50));
controller.getCustomers();

console.log('\n' + '='.repeat(50));
console.log('TEST 5: Get customer statistics');
console.log('='.repeat(50));
controller.getCustomerStats();

console.log('\n' + '🎉 All tests completed successfully!');
console.log('\n📋 Summary:');
console.log(`   - Created ${controller.customers.length} customers`);
console.log(`   - Customer 1: ${customer1.data.name} (${customer1.data.customerNumber})`);
console.log(`   - Customer 2: ${customer2.data.name} (${customer2.data.customerNumber})`);
console.log('\n✅ Customer creation logic is working correctly!');
