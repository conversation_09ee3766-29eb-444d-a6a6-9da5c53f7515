"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancialReportController = void 0;
const common_1 = require("@nestjs/common");
const financial_report_service_1 = require("../services/financial-report.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let FinancialReportController = class FinancialReportController {
    reportService;
    constructor(reportService) {
        this.reportService = reportService;
    }
    create(createReportDto) {
        return this.reportService.create(createReportDto);
    }
    findAll(type, status) {
        const filters = {};
        if (type)
            filters.type = type;
        if (status)
            filters.status = status;
        return this.reportService.findAll(filters);
    }
    generateBalanceSheet(startDate, endDate) {
        return this.reportService.generateBalanceSheet(new Date(startDate), new Date(endDate));
    }
    generateIncomeStatement(startDate, endDate) {
        return this.reportService.generateIncomeStatement(new Date(startDate), new Date(endDate));
    }
    generateCashFlowStatement(startDate, endDate) {
        return this.reportService.generateCashFlowStatement(new Date(startDate), new Date(endDate));
    }
    scheduleReport(scheduleReportDto) {
        return this.reportService.scheduleReport(scheduleReportDto);
    }
    findOne(id) {
        return this.reportService.findOne(id);
    }
};
exports.FinancialReportController = FinancialReportController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('type')),
    __param(1, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('balance-sheet'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "generateBalanceSheet", null);
__decorate([
    (0, common_1.Get)('income-statement'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "generateIncomeStatement", null);
__decorate([
    (0, common_1.Get)('cash-flow'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "generateCashFlowStatement", null);
__decorate([
    (0, common_1.Post)('schedule'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "scheduleReport", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FinancialReportController.prototype, "findOne", null);
exports.FinancialReportController = FinancialReportController = __decorate([
    (0, common_1.Controller)('finance/reports'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [financial_report_service_1.FinancialReportService])
], FinancialReportController);
//# sourceMappingURL=financial-report.controller.js.map