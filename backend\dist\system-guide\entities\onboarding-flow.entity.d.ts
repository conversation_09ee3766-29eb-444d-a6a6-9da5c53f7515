export declare enum OnboardingType {
    USER_ONBOARDING = "user_onboarding",
    FEATURE_ONBOARDING = "feature_onboarding",
    ROLE_ONBOARDING = "role_onboarding",
    MODULE_ONBOARDING = "module_onboarding",
    CUSTOM = "custom"
}
export declare enum OnboardingStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    TESTING = "testing",
    ARCHIVED = "archived"
}
export declare class OnboardingFlow {
    id: string;
    name: string;
    description: string;
    type: OnboardingType;
    status: OnboardingStatus;
    steps: any[];
    triggers: any;
    targetRoles: string[];
    targetPages: string[];
    conditions: any;
    isSkippable: boolean;
    isRepeatable: boolean;
    priority: number;
    estimatedDuration: number;
    createdBy: string;
    lastModifiedBy: string;
    completionCount: number;
    skipCount: number;
    completionRate: number;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
