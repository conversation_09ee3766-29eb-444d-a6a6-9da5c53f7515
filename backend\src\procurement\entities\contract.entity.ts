import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Vendor } from './vendor.entity';
import { ContractTerm } from './contract-term.entity';

export enum ContractStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TERMINATED = 'terminated',
  CANCELLED = 'cancelled',
  RENEWED = 'renewed',
}

export enum ContractType {
  PURCHASE_AGREEMENT = 'purchase_agreement',
  SERVICE_AGREEMENT = 'service_agreement',
  MASTER_AGREEMENT = 'master_agreement',
  FRAMEWORK_AGREEMENT = 'framework_agreement',
  NDA = 'nda',
  SLA = 'sla',
  OTHER = 'other',
}

@Entity('contracts')
export class Contract {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  contractNumber: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ContractType,
    default: ContractType.PURCHASE_AGREEMENT,
  })
  type: ContractType;

  @Column({
    type: 'enum',
    enum: ContractStatus,
    default: ContractStatus.DRAFT,
  })
  status: ContractStatus;

  @Column()
  vendorId: string;

  @ManyToOne(() => Vendor, vendor => vendor.contracts)
  @JoinColumn({ name: 'vendorId' })
  vendor: Vendor;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalValue: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'int', nullable: true })
  renewalPeriodMonths: number;

  @Column({ default: false })
  autoRenewal: boolean;

  @Column({ type: 'int', nullable: true })
  noticePeriodDays: number;

  @Column({ type: 'text', nullable: true })
  paymentTerms: string;

  @Column({ type: 'text', nullable: true })
  deliveryTerms: string;

  @Column({ type: 'text', nullable: true })
  penaltyClause: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column()
  createdBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ nullable: true })
  signedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  signedAt: Date;

  @OneToMany(() => ContractTerm, term => term.contract, { cascade: true })
  terms: ContractTerm[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
