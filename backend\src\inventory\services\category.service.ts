import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from '../entities/category.entity';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
  ) {}

  async create(categoryData: Partial<Category>): Promise<Category> {
    const category = this.categoryRepository.create(categoryData);
    return this.categoryRepository.save(category);
  }

  async findAll(): Promise<Category[]> {
    return this.categoryRepository.find({
      relations: ['parent', 'children', 'products'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'products'],
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    return category;
  }

  async update(id: string, updateData: Partial<Category>): Promise<Category> {
    await this.categoryRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const category = await this.findOne(id);
    
    // Check if category has products
    if (category.products && category.products.length > 0) {
      throw new Error('Cannot delete category with existing products');
    }

    // Check if category has children
    if (category.children && category.children.length > 0) {
      throw new Error('Cannot delete category with subcategories');
    }

    await this.categoryRepository.remove(category);
  }

  async findRootCategories(): Promise<Category[]> {
    return this.categoryRepository.find({
      where: { parentId: null },
      relations: ['children'],
      order: { name: 'ASC' },
    });
  }

  async findByParent(parentId: string): Promise<Category[]> {
    return this.categoryRepository.find({
      where: { parentId },
      relations: ['children', 'products'],
      order: { name: 'ASC' },
    });
  }

  async getCategoryTree(): Promise<Category[]> {
    const rootCategories = await this.findRootCategories();
    
    for (const category of rootCategories) {
      await this.loadCategoryTree(category);
    }

    return rootCategories;
  }

  private async loadCategoryTree(category: Category): Promise<void> {
    const children = await this.findByParent(category.id);
    category.children = children;

    for (const child of children) {
      await this.loadCategoryTree(child);
    }
  }

  async getCategoryPath(categoryId: string): Promise<string[]> {
    const category = await this.findOne(categoryId);
    const path = [category.name];

    let current = category;
    while (current.parent) {
      current = await this.findOne(current.parent.id);
      path.unshift(current.name);
    }

    return path;
  }

  async getCategoryStatistics(): Promise<any> {
    const totalCategories = await this.categoryRepository.count();
    const rootCategories = await this.categoryRepository.count({ where: { parentId: null } });
    
    const categoriesWithProducts = await this.categoryRepository
      .createQueryBuilder('category')
      .leftJoin('category.products', 'products')
      .where('products.id IS NOT NULL')
      .getCount();

    return {
      totalCategories,
      rootCategories,
      subcategories: totalCategories - rootCategories,
      categoriesWithProducts,
      emptyCategoriesCount: totalCategories - categoriesWithProducts,
    };
  }

  async searchCategories(searchTerm: string): Promise<Category[]> {
    return this.categoryRepository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.children', 'children')
      .where('category.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('category.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('category.name', 'ASC')
      .getMany();
  }

  async moveCategory(categoryId: string, newParentId: string | null): Promise<Category> {
    const category = await this.findOne(categoryId);
    
    // Prevent circular references
    if (newParentId) {
      const newParent = await this.findOne(newParentId);
      if (await this.isDescendant(newParent, category)) {
        throw new Error('Cannot move category to its own descendant');
      }
    }

    category.parentId = newParentId;
    await this.categoryRepository.save(category);
    
    return this.findOne(categoryId);
  }

  private async isDescendant(potentialDescendant: Category, ancestor: Category): Promise<boolean> {
    if (potentialDescendant.id === ancestor.id) {
      return true;
    }

    if (!potentialDescendant.parent) {
      return false;
    }

    const parent = await this.findOne(potentialDescendant.parent.id);
    return this.isDescendant(parent, ancestor);
  }

  async generateCategoryCode(name: string): Promise<string> {
    const baseCode = name.substring(0, 3).toUpperCase();
    const count = await this.categoryRepository.count();
    const sequence = (count + 1).toString().padStart(3, '0');
    return `${baseCode}${sequence}`;
  }

  async getProductCountByCategory(): Promise<Array<{ categoryId: string; categoryName: string; productCount: number }>> {
    const result = await this.categoryRepository
      .createQueryBuilder('category')
      .leftJoin('category.products', 'products')
      .select([
        'category.id as categoryId',
        'category.name as categoryName',
        'COUNT(products.id) as productCount',
      ])
      .groupBy('category.id, category.name')
      .orderBy('productCount', 'DESC')
      .getRawMany();

    return result.map(row => ({
      categoryId: row.categoryId,
      categoryName: row.categoryName,
      productCount: parseInt(row.productCount),
    }));
  }
}
