import { Repository } from 'typeorm';
import { BankAccount } from '../entities/bank-account.entity';
import { BankTransaction } from '../entities/bank-transaction.entity';
export declare class BankAccountService {
    private bankAccountRepository;
    private bankTransactionRepository;
    constructor(bankAccountRepository: Repository<BankAccount>, bankTransactionRepository: Repository<BankTransaction>);
    create(createBankAccountDto: any): Promise<BankAccount>;
    findAll(): Promise<BankAccount[]>;
    findOne(id: string): Promise<BankAccount>;
    update(id: string, updateBankAccountDto: any): Promise<BankAccount>;
    remove(id: string): Promise<void>;
    addTransaction(bankAccountId: string, transactionData: any): Promise<BankTransaction>;
    importTransactions(bankAccountId: string, transactions: any[]): Promise<BankTransaction[]>;
    reconcileAccount(bankAccountId: string, reconciledBalance: number, reconciledBy: string): Promise<BankAccount>;
    getAccountStatement(bankAccountId: string, startDate?: Date, endDate?: Date): Promise<any>;
    getUnreconciledTransactions(bankAccountId: string): Promise<BankTransaction[]>;
    setDefaultAccount(id: string): Promise<BankAccount>;
    private updateAccountBalance;
}
