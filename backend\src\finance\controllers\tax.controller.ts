import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { TaxService } from '../services/tax.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/tax')
@UseGuards(JwtAuthGuard)
export class TaxController {
  constructor(private readonly taxService: TaxService) {}

  @Post()
  create(@Body() createTaxRecordDto: any) {
    return this.taxService.create(createTaxRecordDto);
  }

  @Get()
  findAll(
    @Query('taxType') taxType?: string,
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const filters: any = {};
    if (taxType) filters.taxType = taxType;
    if (status) filters.status = status;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    return this.taxService.findAll(filters);
  }

  @Get('summary/:year')
  getTaxSummary(@Param('year') year: string) {
    return this.taxService.getTaxSummary(parseInt(year));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.taxService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTaxRecordDto: any) {
    return this.taxService.update(id, updateTaxRecordDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.taxService.remove(id);
  }

  @Post(':id/file')
  fileReturn(
    @Param('id') id: string,
    @Body() fileDto: { filedBy: string; referenceNumber?: string }
  ) {
    return this.taxService.fileReturn(id, fileDto.filedBy, fileDto.referenceNumber);
  }

  @Post(':id/payment')
  recordPayment(@Param('id') id: string, @Body() paymentDto: { amount: number }) {
    return this.taxService.recordPayment(id, paymentDto.amount);
  }

  @Post(':id/calculate-penalties')
  calculatePenaltiesAndInterest(@Param('id') id: string) {
    return this.taxService.calculatePenaltiesAndInterest(id);
  }
}
