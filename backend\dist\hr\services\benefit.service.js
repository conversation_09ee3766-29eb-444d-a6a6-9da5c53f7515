"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BenefitService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const benefit_entity_1 = require("../entities/benefit.entity");
const employee_benefit_entity_1 = require("../entities/employee-benefit.entity");
let BenefitService = class BenefitService {
    benefitRepository;
    employeeBenefitRepository;
    constructor(benefitRepository, employeeBenefitRepository) {
        this.benefitRepository = benefitRepository;
        this.employeeBenefitRepository = employeeBenefitRepository;
    }
    async create(createBenefitDto) {
        const benefit = this.benefitRepository.create(createBenefitDto);
        return this.benefitRepository.save(benefit);
    }
    async findAll() {
        return this.benefitRepository.find({
            where: { isActive: true },
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const benefit = await this.benefitRepository.findOne({
            where: { id },
            relations: ['employeeBenefits'],
        });
        if (!benefit) {
            throw new common_1.NotFoundException(`Benefit with ID ${id} not found`);
        }
        return benefit;
    }
    async enrollEmployee(employeeId, benefitId, enrollmentData) {
        const employeeBenefit = this.employeeBenefitRepository.create({
            employeeId,
            benefitId,
            ...enrollmentData,
            status: employee_benefit_entity_1.EnrollmentStatus.ENROLLED,
            enrollmentDate: new Date(),
        });
        return this.employeeBenefitRepository.save(employeeBenefit);
    }
    async getEmployeeBenefits(employeeId) {
        return this.employeeBenefitRepository.find({
            where: { employeeId },
            relations: ['benefit'],
            order: { enrollmentDate: 'DESC' },
        });
    }
};
exports.BenefitService = BenefitService;
exports.BenefitService = BenefitService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(benefit_entity_1.Benefit)),
    __param(1, (0, typeorm_1.InjectRepository)(employee_benefit_entity_1.EmployeeBenefit)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BenefitService);
//# sourceMappingURL=benefit.service.js.map