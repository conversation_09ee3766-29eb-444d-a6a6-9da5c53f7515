@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: ZaidanOne Ultimate ERP System - Stop Services Script
:: =============================================================================
:: This script safely stops all ERP system services
:: =============================================================================

title ZaidanOne ERP - Stop Services

color 0C

echo.
echo ===============================================================================
echo                    ZAIDANONE ERP SYSTEM - STOP SERVICES
echo ===============================================================================
echo.

echo [INFO] Stopping ZaidanOne Ultimate ERP System services...
echo.

:: Check current running processes
echo [INFO] Checking for running ERP services...

:: Check if frontend is running
netstat -an | findstr ":5173" >nul
if not errorlevel 1 (
    echo [FOUND] Frontend service running on port 5173
    set frontend_running=1
) else (
    echo [INFO] Frontend service not running
    set frontend_running=0
)

:: Check if backend is running
netstat -an | findstr ":3001" >nul
if not errorlevel 1 (
    echo [FOUND] Backend service running on port 3001
    set backend_running=1
) else (
    echo [INFO] Backend service not running
    set backend_running=0
)

echo.

if %frontend_running%==0 if %backend_running%==0 (
    echo [INFO] No ERP services are currently running.
    echo.
    pause
    exit /b 0
)

echo [WARNING] This will stop all Node.js processes related to the ERP system.
echo [WARNING] Make sure to save any unsaved work before proceeding.
echo.
set /p confirm="Do you want to continue? (y/N): "

if /i not "%confirm%"=="y" (
    echo [INFO] Operation cancelled.
    pause
    exit /b 0
)

echo.
echo [INFO] Stopping services...

:: Stop Node.js processes
taskkill /f /im node.exe >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] All Node.js processes have been stopped.
) else (
    echo [INFO] No Node.js processes were found running.
)

:: Additional cleanup for specific ports
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5173"') do (
    taskkill /f /pid %%a >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3001"') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo [SUCCESS] All ERP system services have been stopped.
echo.

:: Verify services are stopped
echo [INFO] Verifying services are stopped...
timeout /t 2 >nul

netstat -an | findstr ":5173" >nul
if errorlevel 1 (
    echo [VERIFIED] Frontend service (port 5173) stopped
) else (
    echo [WARNING] Frontend service may still be running
)

netstat -an | findstr ":3001" >nul
if errorlevel 1 (
    echo [VERIFIED] Backend service (port 3001) stopped
) else (
    echo [WARNING] Backend service may still be running
)

echo.
echo [INFO] ERP system shutdown complete.
echo [INFO] You can now safely close this window or restart the system.
echo.
pause
