import { Repository } from 'typeorm';
import { Stock } from '../entities/stock.entity';
import { StockMovement } from '../entities/stock-movement.entity';
import { StockAdjustment } from '../entities/stock-adjustment.entity';
export declare class StockService {
    private stockRepository;
    private stockMovementRepository;
    private stockAdjustmentRepository;
    constructor(stockRepository: Repository<Stock>, stockMovementRepository: Repository<StockMovement>, stockAdjustmentRepository: Repository<StockAdjustment>);
    findAll(): Promise<Stock[]>;
    findOne(id: string): Promise<Stock>;
    findByProduct(productId: string): Promise<Stock[]>;
    findByWarehouse(warehouseId: string): Promise<Stock[]>;
    findByProductAndWarehouse(productId: string, warehouseId: string): Promise<Stock | null>;
    adjustStock(productId: string, warehouseId: string, adjustment: number, reason: string, adjustedBy?: string): Promise<Stock>;
    recordStockMovement(productId: string, warehouseId: string, type: 'IN' | 'OUT', quantity: number, movementType: string, reference?: string): Promise<StockMovement>;
    getStockMovements(productId?: string, warehouseId?: string, startDate?: Date, endDate?: Date): Promise<StockMovement[]>;
    getLowStockItems(threshold?: number): Promise<Stock[]>;
    getOutOfStockItems(): Promise<Stock[]>;
    getOverstockItems(threshold?: number): Promise<Stock[]>;
    reserveStock(productId: string, warehouseId: string, quantity: number): Promise<boolean>;
    releaseReservation(productId: string, warehouseId: string, quantity: number): Promise<void>;
    fulfillReservation(productId: string, warehouseId: string, quantity: number): Promise<boolean>;
    getStockStatistics(): Promise<any>;
    getStockAdjustments(productId?: string, warehouseId?: string, startDate?: Date, endDate?: Date): Promise<StockAdjustment[]>;
    bulkStockUpdate(updates: Array<{
        productId: string;
        warehouseId: string;
        quantity: number;
        reason: string;
    }>): Promise<void>;
}
