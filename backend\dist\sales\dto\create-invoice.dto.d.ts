export declare class CreateInvoiceItemDto {
    lineNumber: number;
    description: string;
    productCode?: string;
    unitPrice: number;
    quantity: number;
    discount?: number;
    taxType?: string;
    unit?: string;
    notes?: string;
}
export declare class CreateInvoiceDto {
    customerId: string;
    invoiceDate: string;
    issueDate: string;
    dueDate?: string;
    paymentTerms?: string;
    salesOfficer?: string;
    governorAccount?: string;
    discountType?: 'percentage' | 'amount';
    discountValue?: number;
    settlementAmount?: number;
    advancePayment?: number;
    deliveryMethod?: 'email' | 'print';
    internalNotes?: string;
    invoiceTerms?: string;
    additionalDetails?: string;
    items: CreateInvoiceItemDto[];
}
