import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Employee } from './employee.entity';

export enum PerformanceType {
  ANNUAL_REVIEW = 'annual_review',
  QUARTERLY_REVIEW = 'quarterly_review',
  MONTHLY_REVIEW = 'monthly_review',
  PROJECT_REVIEW = 'project_review',
  PROBATION_REVIEW = 'probation_review',
  GOAL_SETTING = 'goal_setting',
}

export enum PerformanceStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  CANCELLED = 'cancelled',
}

export enum PerformanceRating {
  OUTSTANDING = 'outstanding',
  EXCEEDS_EXPECTATIONS = 'exceeds_expectations',
  MEETS_EXPECTATIONS = 'meets_expectations',
  BELOW_EXPECTATIONS = 'below_expectations',
  UNSATISFACTORY = 'unsatisfactory',
}

@Entity('hr_performance')
export class Performance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  employeeId: string;

  @ManyToOne(() => Employee, employee => employee.performances)
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column({
    type: 'enum',
    enum: PerformanceType,
  })
  type: PerformanceType;

  @Column({
    type: 'enum',
    enum: PerformanceStatus,
    default: PerformanceStatus.DRAFT,
  })
  status: PerformanceStatus;

  @Column({ type: 'date' })
  reviewPeriodStart: Date;

  @Column({ type: 'date' })
  reviewPeriodEnd: Date;

  @Column({ type: 'date', nullable: true })
  dueDate: Date;

  @Column({
    type: 'enum',
    enum: PerformanceRating,
    nullable: true,
  })
  overallRating: PerformanceRating;

  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })
  overallScore: number;

  @Column({ type: 'json', nullable: true })
  goals: any[];

  @Column({ type: 'json', nullable: true })
  achievements: any[];

  @Column({ type: 'json', nullable: true })
  competencies: any[];

  @Column({ type: 'json', nullable: true })
  ratings: any;

  @Column({ type: 'text', nullable: true })
  employeeSelfReview: string;

  @Column({ type: 'text', nullable: true })
  managerReview: string;

  @Column({ type: 'text', nullable: true })
  strengths: string;

  @Column({ type: 'text', nullable: true })
  areasForImprovement: string;

  @Column({ type: 'text', nullable: true })
  developmentPlan: string;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ nullable: true })
  reviewedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
