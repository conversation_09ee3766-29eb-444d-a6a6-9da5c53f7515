"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryReportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const stock_entity_1 = require("../entities/stock.entity");
const stock_movement_entity_1 = require("../entities/stock-movement.entity");
const purchase_order_entity_1 = require("../entities/purchase-order.entity");
let InventoryReportService = class InventoryReportService {
    productRepository;
    stockRepository;
    stockMovementRepository;
    purchaseOrderRepository;
    constructor(productRepository, stockRepository, stockMovementRepository, purchaseOrderRepository) {
        this.productRepository = productRepository;
        this.stockRepository = stockRepository;
        this.stockMovementRepository = stockMovementRepository;
        this.purchaseOrderRepository = purchaseOrderRepository;
    }
    async generateInventoryValuationReport() {
        const stockData = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .leftJoinAndSelect('product.category', 'category')
            .getMany();
        const report = {
            generatedAt: new Date(),
            totalItems: stockData.length,
            totalQuantity: 0,
            totalValue: 0,
            warehouses: {},
            categories: {},
            items: [],
        };
        for (const stock of stockData) {
            const itemValue = stock.quantity * stock.product.costPrice;
            report.totalQuantity += stock.quantity;
            report.totalValue += itemValue;
            if (!report.warehouses[stock.warehouse.name]) {
                report.warehouses[stock.warehouse.name] = {
                    quantity: 0,
                    value: 0,
                    items: 0,
                };
            }
            report.warehouses[stock.warehouse.name].quantity += stock.quantity;
            report.warehouses[stock.warehouse.name].value += itemValue;
            report.warehouses[stock.warehouse.name].items += 1;
            const categoryName = stock.product.category?.name || 'Uncategorized';
            if (!report.categories[categoryName]) {
                report.categories[categoryName] = {
                    quantity: 0,
                    value: 0,
                    items: 0,
                };
            }
            report.categories[categoryName].quantity += stock.quantity;
            report.categories[categoryName].value += itemValue;
            report.categories[categoryName].items += 1;
            report.items.push({
                productId: stock.product.id,
                productName: stock.product.name,
                sku: stock.product.sku,
                warehouse: stock.warehouse.name,
                category: categoryName,
                quantity: stock.quantity,
                costPrice: stock.product.costPrice,
                totalValue: itemValue,
            });
        }
        return report;
    }
    async generateStockLevelReport() {
        const lowStockItems = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .where('stock.availableQuantity <= product.reorderLevel')
            .andWhere('stock.availableQuantity > 0')
            .getMany();
        const outOfStockItems = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .where('stock.availableQuantity = 0')
            .getMany();
        const overstockItems = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .where('stock.quantity > product.maxStockLevel')
            .getMany();
        return {
            generatedAt: new Date(),
            summary: {
                lowStockCount: lowStockItems.length,
                outOfStockCount: outOfStockItems.length,
                overstockCount: overstockItems.length,
            },
            lowStockItems: lowStockItems.map(stock => ({
                productName: stock.product.name,
                sku: stock.product.sku,
                warehouse: stock.warehouse.name,
                currentStock: stock.availableQuantity,
                reorderLevel: stock.product.reorderLevel,
                deficit: stock.product.reorderLevel - stock.availableQuantity,
            })),
            outOfStockItems: outOfStockItems.map(stock => ({
                productName: stock.product.name,
                sku: stock.product.sku,
                warehouse: stock.warehouse.name,
                lastStockDate: stock.updatedAt,
            })),
            overstockItems: overstockItems.map(stock => ({
                productName: stock.product.name,
                sku: stock.product.sku,
                warehouse: stock.warehouse.name,
                currentStock: stock.quantity,
                maxLevel: stock.product.maxStockLevel,
                excess: stock.quantity - stock.product.maxStockLevel,
            })),
        };
    }
    async generateMovementReport(startDate, endDate) {
        const movements = await this.stockMovementRepository
            .createQueryBuilder('movement')
            .leftJoinAndSelect('movement.product', 'product')
            .leftJoinAndSelect('movement.warehouse', 'warehouse')
            .where('movement.movementDate BETWEEN :startDate AND :endDate', {
            startDate,
            endDate,
        })
            .orderBy('movement.movementDate', 'DESC')
            .getMany();
        const summary = {
            totalMovements: movements.length,
            inMovements: movements.filter(m => m.type === 'IN').length,
            outMovements: movements.filter(m => m.type === 'OUT').length,
            totalInQuantity: movements
                .filter(m => m.type === 'IN')
                .reduce((sum, m) => sum + m.quantity, 0),
            totalOutQuantity: movements
                .filter(m => m.type === 'OUT')
                .reduce((sum, m) => sum + m.quantity, 0),
        };
        const movementsByType = {};
        movements.forEach(movement => {
            if (!movementsByType[movement.movementType]) {
                movementsByType[movement.movementType] = {
                    count: 0,
                    totalQuantity: 0,
                };
            }
            movementsByType[movement.movementType].count += 1;
            movementsByType[movement.movementType].totalQuantity += movement.quantity;
        });
        return {
            generatedAt: new Date(),
            period: { startDate, endDate },
            summary,
            movementsByType,
            movements: movements.map(movement => ({
                date: movement.movementDate,
                product: movement.product.name,
                sku: movement.product.sku,
                warehouse: movement.warehouse.name,
                type: movement.type,
                movementType: movement.movementType,
                quantity: movement.quantity,
                reference: movement.reference,
            })),
        };
    }
    async generatePurchaseOrderReport(startDate, endDate) {
        const orders = await this.purchaseOrderRepository
            .createQueryBuilder('po')
            .leftJoinAndSelect('po.supplier', 'supplier')
            .leftJoinAndSelect('po.items', 'items')
            .leftJoinAndSelect('items.product', 'product')
            .where('po.orderDate BETWEEN :startDate AND :endDate', {
            startDate,
            endDate,
        })
            .orderBy('po.orderDate', 'DESC')
            .getMany();
        const summary = {
            totalOrders: orders.length,
            totalValue: orders.reduce((sum, order) => sum + order.total, 0),
            pendingOrders: orders.filter(o => o.status === 'PENDING').length,
            receivedOrders: orders.filter(o => o.status === 'RECEIVED').length,
            cancelledOrders: orders.filter(o => o.status === 'CANCELLED').length,
        };
        const supplierSummary = {};
        orders.forEach(order => {
            const supplierName = order.supplier.name;
            if (!supplierSummary[supplierName]) {
                supplierSummary[supplierName] = {
                    orderCount: 0,
                    totalValue: 0,
                };
            }
            supplierSummary[supplierName].orderCount += 1;
            supplierSummary[supplierName].totalValue += order.total;
        });
        return {
            generatedAt: new Date(),
            period: { startDate, endDate },
            summary,
            supplierSummary,
            orders: orders.map(order => ({
                orderNumber: order.orderNumber,
                orderDate: order.orderDate,
                supplier: order.supplier.name,
                status: order.status,
                total: order.total,
                itemCount: order.items.length,
                receivedDate: order.receivedDate,
            })),
        };
    }
    async generateABCAnalysisReport() {
        const stockData = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .getMany();
        const productValues = stockData.map(stock => ({
            productId: stock.product.id,
            productName: stock.product.name,
            sku: stock.product.sku,
            value: stock.quantity * stock.product.costPrice,
        }));
        productValues.sort((a, b) => b.value - a.value);
        const totalValue = productValues.reduce((sum, item) => sum + item.value, 0);
        let cumulativeValue = 0;
        const classifiedProducts = productValues.map(product => {
            cumulativeValue += product.value;
            const cumulativePercentage = (cumulativeValue / totalValue) * 100;
            let classification = 'C';
            if (cumulativePercentage <= 80) {
                classification = 'A';
            }
            else if (cumulativePercentage <= 95) {
                classification = 'B';
            }
            return {
                ...product,
                classification,
                cumulativePercentage: Math.round(cumulativePercentage * 100) / 100,
            };
        });
        const summary = {
            classA: classifiedProducts.filter(p => p.classification === 'A').length,
            classB: classifiedProducts.filter(p => p.classification === 'B').length,
            classC: classifiedProducts.filter(p => p.classification === 'C').length,
        };
        return {
            generatedAt: new Date(),
            summary,
            products: classifiedProducts,
        };
    }
};
exports.InventoryReportService = InventoryReportService;
exports.InventoryReportService = InventoryReportService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(stock_entity_1.Stock)),
    __param(2, (0, typeorm_1.InjectRepository)(stock_movement_entity_1.StockMovement)),
    __param(3, (0, typeorm_1.InjectRepository)(purchase_order_entity_1.PurchaseOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], InventoryReportService);
//# sourceMappingURL=inventory-report.service.js.map