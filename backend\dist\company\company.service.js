"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const company_entity_1 = require("./entities/company.entity");
const tenant_connection_service_1 = require("../tenant/tenant-connection.service");
const uuid_1 = require("uuid");
let CompanyService = class CompanyService {
    companyRepository;
    tenantConnectionService;
    constructor(companyRepository, tenantConnectionService) {
        this.companyRepository = companyRepository;
        this.tenantConnectionService = tenantConnectionService;
    }
    async create(companyData) {
        const existingCompany = await this.companyRepository.findOne({
            where: [
                { name: companyData.name },
                { slug: companyData.slug },
            ],
        });
        if (existingCompany) {
            throw new common_1.ConflictException('Company name or slug already exists');
        }
        const tenantId = (0, uuid_1.v4)();
        const company = this.companyRepository.create({
            ...companyData,
            tenantId,
        });
        const savedCompany = await this.companyRepository.save(company);
        try {
            await this.tenantConnectionService.createTenantDatabase(tenantId);
        }
        catch (error) {
            await this.companyRepository.remove(savedCompany);
            throw error;
        }
        return savedCompany;
    }
    async findAll() {
        return this.companyRepository.find({
            where: { isActive: true },
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const company = await this.companyRepository.findOne({
            where: { id, isActive: true },
        });
        if (!company) {
            throw new common_1.NotFoundException('Company not found');
        }
        return company;
    }
    async findBySlug(slug) {
        const company = await this.companyRepository.findOne({
            where: { slug, isActive: true },
        });
        if (!company) {
            throw new common_1.NotFoundException('Company not found');
        }
        return company;
    }
    async findByTenantId(tenantId) {
        const company = await this.companyRepository.findOne({
            where: { tenantId, isActive: true },
        });
        if (!company) {
            throw new common_1.NotFoundException('Company not found');
        }
        return company;
    }
    async update(id, updateData) {
        const company = await this.findOne(id);
        if (updateData.name || updateData.slug) {
            const existingCompany = await this.companyRepository.findOne({
                where: [
                    { name: updateData.name },
                    { slug: updateData.slug },
                ],
            });
            if (existingCompany && existingCompany.id !== id) {
                throw new common_1.ConflictException('Company name or slug already exists');
            }
        }
        Object.assign(company, updateData);
        return this.companyRepository.save(company);
    }
    async remove(id) {
        const company = await this.findOne(id);
        company.isActive = false;
        await this.companyRepository.save(company);
    }
};
exports.CompanyService = CompanyService;
exports.CompanyService = CompanyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        tenant_connection_service_1.TenantConnectionService])
], CompanyService);
//# sourceMappingURL=company.service.js.map