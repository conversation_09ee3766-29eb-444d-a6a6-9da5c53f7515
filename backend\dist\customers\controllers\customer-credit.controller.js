"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerCreditController = void 0;
const common_1 = require("@nestjs/common");
const customer_credit_service_1 = require("../services/customer-credit.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerCreditController = class CustomerCreditController {
    customerCreditService;
    constructor(customerCreditService) {
        this.customerCreditService = customerCreditService;
    }
    async findAll() {
        return this.customerCreditService.findAll();
    }
    async getCreditStatistics() {
        return this.customerCreditService.getCreditStatistics();
    }
    async getCustomersRequiringReview() {
        return this.customerCreditService.getCustomersRequiringReview();
    }
    async findByCustomer(customerId) {
        return this.customerCreditService.findByCustomer(customerId);
    }
    async getLatestCreditAssessment(customerId) {
        return this.customerCreditService.getLatestCreditAssessment(customerId);
    }
    async calculateCreditScore(customerId) {
        const score = await this.customerCreditService.calculateCreditScore(customerId);
        return { customerId, creditScore: score };
    }
    async findOne(id) {
        return this.customerCreditService.findOne(id);
    }
    async createCreditAssessment(assessmentData) {
        return this.customerCreditService.createCreditAssessment(assessmentData.customerId, {
            creditScore: assessmentData.creditScore,
            creditLimit: assessmentData.creditLimit,
            paymentTerms: assessmentData.paymentTerms,
            riskLevel: assessmentData.riskLevel,
            notes: assessmentData.notes,
            assessedBy: assessmentData.assessedBy,
        });
    }
    async updateCreditLimit(updateData) {
        return this.customerCreditService.updateCreditLimit(updateData.customerId, updateData.newLimit, updateData.reason, updateData.updatedBy);
    }
    async scheduleCreditReview(reviewData) {
        return this.customerCreditService.scheduleCreditReview(reviewData.customerId, new Date(reviewData.reviewDate), reviewData.reason);
    }
};
exports.CustomerCreditController = CustomerCreditController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "getCreditStatistics", null);
__decorate([
    (0, common_1.Get)('review-required'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "getCustomersRequiringReview", null);
__decorate([
    (0, common_1.Get)('customer/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "findByCustomer", null);
__decorate([
    (0, common_1.Get)('customer/:customerId/latest'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "getLatestCreditAssessment", null);
__decorate([
    (0, common_1.Get)('customer/:customerId/score'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "calculateCreditScore", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('assessment'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "createCreditAssessment", null);
__decorate([
    (0, common_1.Patch)('credit-limit'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "updateCreditLimit", null);
__decorate([
    (0, common_1.Post)('schedule-review'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerCreditController.prototype, "scheduleCreditReview", null);
exports.CustomerCreditController = CustomerCreditController = __decorate([
    (0, common_1.Controller)('customer-credit'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_credit_service_1.CustomerCreditService])
], CustomerCreditController);
//# sourceMappingURL=customer-credit.controller.js.map