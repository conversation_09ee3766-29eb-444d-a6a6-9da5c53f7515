import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_lisp=__commonJS({"../../node_modules/highlight.js/lib/languages/lisp.js"(exports,module){function lisp(hljs){var LISP_IDENT_RE="[a-zA-Z_\\-+\\*\\/<=>&#][a-zA-Z0-9_\\-+*\\/<=>&#!]*",MEC_RE="\\|[^]*?\\|",LISP_SIMPLE_NUMBER_RE="(-|\\+)?\\d+(\\.\\d+|\\/\\d+)?((d|e|f|l|s|D|E|F|L|S)(\\+|-)?\\d+)?",LITERAL={className:"literal",begin:"\\b(t{1}|nil)\\b"},NUMBER={className:"number",variants:[{begin:LISP_SIMPLE_NUMBER_RE,relevance:0},{begin:"#(b|B)[0-1]+(/[0-1]+)?"},{begin:"#(o|O)[0-7]+(/[0-7]+)?"},{begin:"#(x|X)[0-9a-fA-F]+(/[0-9a-fA-F]+)?"},{begin:"#(c|C)\\("+LISP_SIMPLE_NUMBER_RE+" +"+LISP_SIMPLE_NUMBER_RE,end:"\\)"}]},STRING=hljs.inherit(hljs.QUOTE_STRING_MODE,{illegal:null}),COMMENT=hljs.COMMENT(";","$",{relevance:0}),VARIABLE={begin:"\\*",end:"\\*"},KEYWORD={className:"symbol",begin:"[:&]"+LISP_IDENT_RE},IDENT={begin:LISP_IDENT_RE,relevance:0},MEC={begin:MEC_RE},QUOTED_LIST={begin:"\\(",end:"\\)",contains:["self",LITERAL,STRING,NUMBER,IDENT]},QUOTED={contains:[NUMBER,STRING,VARIABLE,KEYWORD,QUOTED_LIST,IDENT],variants:[{begin:"['`]\\(",end:"\\)"},{begin:"\\(quote ",end:"\\)",keywords:{name:"quote"}},{begin:"'"+MEC_RE}]},QUOTED_ATOM={variants:[{begin:"'"+LISP_IDENT_RE},{begin:"#'"+LISP_IDENT_RE+"(::"+LISP_IDENT_RE+")*"}]},LIST={begin:"\\(\\s*",end:"\\)"},BODY={endsWithParent:!0,relevance:0};return LIST.contains=[{className:"name",variants:[{begin:LISP_IDENT_RE,relevance:0},{begin:MEC_RE}]},BODY],BODY.contains=[QUOTED,QUOTED_ATOM,LIST,LITERAL,NUMBER,STRING,COMMENT,VARIABLE,KEYWORD,MEC,IDENT],{name:"Lisp",illegal:/\S/,contains:[NUMBER,hljs.SHEBANG(),LITERAL,STRING,COMMENT,QUOTED,QUOTED_ATOM,LIST,IDENT]}}module.exports=lisp;}});var lispS4EETFWH = require_lisp();

export { lispS4EETFWH as default };
