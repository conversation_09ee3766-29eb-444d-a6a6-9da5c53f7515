"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerGroup = exports.GroupType = void 0;
const typeorm_1 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
var GroupType;
(function (GroupType) {
    GroupType["PRICING"] = "pricing";
    GroupType["GEOGRAPHIC"] = "geographic";
    GroupType["INDUSTRY"] = "industry";
    GroupType["SIZE"] = "size";
    GroupType["BEHAVIOR"] = "behavior";
    GroupType["CUSTOM"] = "custom";
})(GroupType || (exports.GroupType = GroupType = {}));
let CustomerGroup = class CustomerGroup {
    id;
    name;
    code;
    description;
    type;
    discountPercentage;
    creditLimit;
    paymentTerms;
    pricingRules;
    benefits;
    restrictions;
    isActive;
    sortOrder;
    customers;
    metadata;
    createdAt;
    updatedAt;
};
exports.CustomerGroup = CustomerGroup;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CustomerGroup.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CustomerGroup.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], CustomerGroup.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomerGroup.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: GroupType,
        default: GroupType.CUSTOM,
    }),
    __metadata("design:type", String)
], CustomerGroup.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CustomerGroup.prototype, "discountPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CustomerGroup.prototype, "creditLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], CustomerGroup.prototype, "paymentTerms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomerGroup.prototype, "pricingRules", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CustomerGroup.prototype, "benefits", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CustomerGroup.prototype, "restrictions", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CustomerGroup.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CustomerGroup.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => customer_entity_1.Customer, customer => customer.group),
    __metadata("design:type", Array)
], CustomerGroup.prototype, "customers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomerGroup.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomerGroup.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomerGroup.prototype, "updatedAt", void 0);
exports.CustomerGroup = CustomerGroup = __decorate([
    (0, typeorm_1.Entity)('customer_groups')
], CustomerGroup);
//# sourceMappingURL=customer-group.entity.js.map