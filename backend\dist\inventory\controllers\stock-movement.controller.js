"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockMovementController = void 0;
const common_1 = require("@nestjs/common");
const stock_movement_service_1 = require("../services/stock-movement.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let StockMovementController = class StockMovementController {
    stockMovementService;
    constructor(stockMovementService) {
        this.stockMovementService = stockMovementService;
    }
    async findAll() {
        return this.stockMovementService.findAll();
    }
    async getStatistics(startDate, endDate) {
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        return this.stockMovementService.getMovementStatistics(start, end);
    }
    async getRecentMovements(limit) {
        const limitNum = limit ? parseInt(limit) : 50;
        return this.stockMovementService.getRecentMovements(limitNum);
    }
    async getMovementsByType(movementType) {
        return this.stockMovementService.getMovementsByType(movementType);
    }
    async findByProduct(productId) {
        return this.stockMovementService.findByProduct(productId);
    }
    async findByWarehouse(warehouseId) {
        return this.stockMovementService.findByWarehouse(warehouseId);
    }
    async findByDateRange(startDate, endDate) {
        return this.stockMovementService.findByDateRange(new Date(startDate), new Date(endDate));
    }
};
exports.StockMovementController = StockMovementController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('recent'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "getRecentMovements", null);
__decorate([
    (0, common_1.Get)('type/:movementType'),
    __param(0, (0, common_1.Param)('movementType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "getMovementsByType", null);
__decorate([
    (0, common_1.Get)('product/:productId'),
    __param(0, (0, common_1.Param)('productId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "findByProduct", null);
__decorate([
    (0, common_1.Get)('warehouse/:warehouseId'),
    __param(0, (0, common_1.Param)('warehouseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "findByWarehouse", null);
__decorate([
    (0, common_1.Get)('date-range'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StockMovementController.prototype, "findByDateRange", null);
exports.StockMovementController = StockMovementController = __decorate([
    (0, common_1.Controller)('stock-movements'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [stock_movement_service_1.StockMovementService])
], StockMovementController);
//# sourceMappingURL=stock-movement.controller.js.map