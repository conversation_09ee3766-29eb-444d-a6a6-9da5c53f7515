import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { AssetMaintenance } from './asset-maintenance.entity';

export enum AssetType {
  COMPUTER = 'computer',
  LAPTOP = 'laptop',
  SERVER = 'server',
  PRINTER = 'printer',
  PHONE = 'phone',
  TABLET = 'tablet',
  MONITOR = 'monitor',
  NETWORK_DEVICE = 'network_device',
  SOFTWARE = 'software',
  FURNITURE = 'furniture',
  VEHICLE = 'vehicle',
  OTHER = 'other',
}

export enum AssetStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  IN_REPAIR = 'in_repair',
  RETIRED = 'retired',
  DISPOSED = 'disposed',
  LOST = 'lost',
  STOLEN = 'stolen',
}

@Entity('assets')
export class Asset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  assetTag: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AssetType,
  })
  type: AssetType;

  @Column({
    type: 'enum',
    enum: AssetStatus,
    default: AssetStatus.ACTIVE,
  })
  status: AssetStatus;

  @Column({ length: 255, nullable: true })
  manufacturer: string;

  @Column({ length: 255, nullable: true })
  model: string;

  @Column({ length: 255, nullable: true })
  serialNumber: string;

  @Column({ type: 'date', nullable: true })
  purchaseDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  purchasePrice: number;

  @Column({ length: 255, nullable: true })
  vendor: string;

  @Column({ type: 'date', nullable: true })
  warrantyExpiry: Date;

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ length: 255, nullable: true })
  location: string;

  @Column({ length: 255, nullable: true })
  department: string;

  @Column({ type: 'text', nullable: true })
  specifications: string;

  @Column({ length: 255, nullable: true })
  operatingSystem: string;

  @Column({ length: 100, nullable: true })
  ipAddress: string;

  @Column({ length: 100, nullable: true })
  macAddress: string;

  @Column({ type: 'date', nullable: true })
  lastMaintenanceDate: Date;

  @Column({ type: 'date', nullable: true })
  nextMaintenanceDate: Date;

  @Column({ type: 'json', nullable: true })
  customFields: any;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @OneToMany(() => AssetMaintenance, maintenance => maintenance.asset)
  maintenanceHistory: AssetMaintenance[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
