import { Repository } from 'typeorm';
import { CustomerLoyalty } from '../entities/customer-loyalty.entity';
import { Customer } from '../entities/customer.entity';
export declare class CustomerLoyaltyService {
    private loyaltyRepository;
    private customerRepository;
    constructor(loyaltyRepository: Repository<CustomerLoyalty>, customerRepository: Repository<Customer>);
    create(loyaltyData: Partial<CustomerLoyalty>): Promise<CustomerLoyalty>;
    findAll(): Promise<CustomerLoyalty[]>;
    findOne(id: string): Promise<CustomerLoyalty>;
    findByCustomer(customerId: string): Promise<CustomerLoyalty[]>;
    addPoints(customerId: string, points: number, reason: string, orderId?: string): Promise<CustomerLoyalty>;
    redeemPoints(customerId: string, points: number, reason: string, orderId?: string): Promise<CustomerLoyalty>;
    getCustomerLoyaltySummary(customerId: string): Promise<any>;
    getLoyaltyStatistics(): Promise<any>;
    private calculateLoyaltyTier;
    adjustPoints(customerId: string, adjustment: number, reason: string): Promise<CustomerLoyalty>;
    expirePoints(customerId: string, points: number): Promise<CustomerLoyalty>;
    getTopLoyaltyCustomers(limit?: number): Promise<Customer[]>;
}
