"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HelpCategory = void 0;
const typeorm_1 = require("typeorm");
let HelpCategory = class HelpCategory {
    id;
    name;
    description;
    icon;
    parentCategoryId;
    parentCategory;
    childCategories;
    sortOrder;
    isActive;
    metadata;
    createdAt;
    updatedAt;
};
exports.HelpCategory = HelpCategory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], HelpCategory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], HelpCategory.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], HelpCategory.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], HelpCategory.prototype, "icon", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], HelpCategory.prototype, "parentCategoryId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => HelpCategory, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'parentCategoryId' }),
    __metadata("design:type", HelpCategory)
], HelpCategory.prototype, "parentCategory", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => HelpCategory, category => category.parentCategory),
    __metadata("design:type", Array)
], HelpCategory.prototype, "childCategories", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], HelpCategory.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], HelpCategory.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], HelpCategory.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], HelpCategory.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], HelpCategory.prototype, "updatedAt", void 0);
exports.HelpCategory = HelpCategory = __decorate([
    (0, typeorm_1.Entity)('help_categories')
], HelpCategory);
//# sourceMappingURL=help-category.entity.js.map