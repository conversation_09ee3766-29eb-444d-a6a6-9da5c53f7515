import { CustomerService } from '../services/customer.service';
import { Customer, CustomerStatus, CustomerType, CustomerTier } from '../entities/customer.entity';
export declare class CustomerController {
    private readonly customerService;
    constructor(customerService: CustomerService);
    create(createCustomerDto: Partial<Customer>): Promise<Customer>;
    findAll(page?: string, limit?: string, search?: string, status?: CustomerStatus, type?: CustomerType, tier?: CustomerTier, groupId?: string): Promise<{
        customers: Customer[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    getStatistics(): Promise<any>;
    search(query: string): Promise<{
        customers: Customer[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<Customer>;
    findByCustomerNumber(customerNumber: string): Promise<Customer>;
    update(id: string, updateCustomerDto: Partial<Customer>): Promise<Customer>;
    updateStatus(id: string, statusUpdate: {
        status: CustomerStatus;
        reason?: string;
    }): Promise<Customer>;
    updateTier(id: string, tierUpdate: {
        tier: CustomerTier;
        reason?: string;
    }): Promise<Customer>;
    updateFinancials(id: string, financials: {
        creditLimit?: number;
        currentBalance?: number;
        totalSpent?: number;
        averageOrderValue?: number;
        paymentTerms?: number;
    }): Promise<Customer>;
    updateLoyaltyPoints(id: string, loyaltyUpdate: {
        points: number;
        operation?: 'add' | 'subtract' | 'set';
    }): Promise<Customer>;
    addContact(id: string, contactData: any): Promise<import("../entities/customer-contact.entity").CustomerContact>;
    addAddress(id: string, addressData: any): Promise<import("../entities/customer-address.entity").CustomerAddress>;
    addNote(id: string, noteData: {
        content: string;
        type?: string;
        createdBy?: string;
    }): Promise<import("../entities/customer-note.entity").CustomerNote>;
    remove(id: string): Promise<void>;
}
