import { Employee } from './employee.entity';
export declare enum PerformanceType {
    ANNUAL_REVIEW = "annual_review",
    QUARTERLY_REVIEW = "quarterly_review",
    MONTHLY_REVIEW = "monthly_review",
    PROJECT_REVIEW = "project_review",
    PROBATION_REVIEW = "probation_review",
    GOAL_SETTING = "goal_setting"
}
export declare enum PerformanceStatus {
    DRAFT = "draft",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    APPROVED = "approved",
    CANCELLED = "cancelled"
}
export declare enum PerformanceRating {
    OUTSTANDING = "outstanding",
    EXCEEDS_EXPECTATIONS = "exceeds_expectations",
    MEETS_EXPECTATIONS = "meets_expectations",
    BELOW_EXPECTATIONS = "below_expectations",
    UNSATISFACTORY = "unsatisfactory"
}
export declare class Performance {
    id: string;
    employeeId: string;
    employee: Employee;
    type: PerformanceType;
    status: PerformanceStatus;
    reviewPeriodStart: Date;
    reviewPeriodEnd: Date;
    dueDate: Date;
    overallRating: PerformanceRating;
    overallScore: number;
    goals: any[];
    achievements: any[];
    competencies: any[];
    ratings: any;
    employeeSelfReview: string;
    managerReview: string;
    strengths: string;
    areasForImprovement: string;
    developmentPlan: string;
    comments: string;
    reviewedBy: string;
    reviewedAt: Date;
    approvedBy: string;
    approvedAt: Date;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
