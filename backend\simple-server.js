const express = require('express');
const cors = require('cors');

const app = express();
const port = 3001;

// In-memory storage for customers
let customers = [];
let customerCounter = 1;

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175', 'http://localhost:3000'],
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
  credentials: true,
}));

app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  console.log('Headers:', req.headers);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', req.body);
  }
  next();
});

// Root endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'ZaidanOne Management System API',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      customers: '/api/sales/customers',
      businessMetrics: '/api/analytics/business-metrics',
      health: '/api/analytics/health'
    }
  });
});

// Customer endpoints
app.get('/api/sales/customers', (req, res) => {
  console.log('📋 GET /api/sales/customers - Fetching customers');
  console.log(`📊 Current customers count: ${customers.length}`);

  const response = {
    data: customers,
    total: customers.length,
    page: 1,
    limit: 20,
  };

  console.log('✅ Returning customers:', response);
  res.json(response);
});

app.post('/api/sales/customers', (req, res) => {
  console.log('🆕 POST /api/sales/customers - Creating new customer');
  console.log('📝 Received customer data:', req.body);

  const customerData = req.body;
  const newCustomer = {
    id: `customer-${customerCounter++}`,
    customerNumber: `CUST-${String(customerCounter).padStart(6, '0')}`,
    ...customerData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  customers.push(newCustomer);

  console.log('✅ Customer created successfully:', newCustomer);
  console.log(`📊 Total customers now: ${customers.length}`);

  const response = {
    success: true,
    data: newCustomer,
    message: 'Customer created successfully',
  };

  console.log('📤 Sending response:', response);
  res.status(201).json(response);
});

app.get('/api/sales/customers/stats', (req, res) => {
  console.log('📊 GET /api/sales/customers/stats - Fetching customer stats');

  const stats = {
    total: customers.length,
    active: customers.filter(c => c.status === 'active').length,
    inactive: customers.filter(c => c.status === 'inactive').length,
    individual: customers.filter(c => c.type === 'individual').length,
    commercial: customers.filter(c => c.type === 'commercial').length,
  };

  console.log('✅ Returning stats:', stats);
  res.json(stats);
});

// Analytics endpoints
app.get('/api/analytics/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'ZaidanOne Customer API',
    version: '1.0.0'
  });
});

app.get('/api/analytics/business-metrics', (req, res) => {
  res.json({
    totalRevenue: 2500000,
    totalCustomers: customers.length,
    totalOrders: 3420,
    lastUpdated: new Date().toISOString(),
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('❌ Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: err.message
  });
});

// 404 handler
app.use((req, res) => {
  console.log(`❌ 404 - Route not found: ${req.method} ${req.path}`);
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.path} not found`
  });
});

// Start server
app.listen(port, () => {
  console.log('🚀 Simple Backend server running on: http://localhost:' + port);
  console.log('👥 Customer Endpoints: http://localhost:' + port + '/api/sales/customers');
  console.log('❤️  Health Check: http://localhost:' + port + '/api/analytics/health');
  console.log('📊 Business Metrics: http://localhost:' + port + '/api/analytics/business-metrics');
  console.log('');
  console.log('Available endpoints:');
  console.log('  GET    /api/sales/customers');
  console.log('  POST   /api/sales/customers');
  console.log('  GET    /api/sales/customers/stats');
  console.log('  GET    /api/analytics/health');
  console.log('  GET    /api/analytics/business-metrics');
  console.log('');
  console.log('Ready to accept requests! 🎉');
});
