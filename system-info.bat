@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: ZaidanOne Ultimate ERP System - System Information
:: =============================================================================

title ZaidanOne ERP - System Information

color 0F

cls
echo.
echo ===============================================================================
echo                    ZAIDANONE ULTIMATE ERP MANAGEMENT SYSTEM
echo                          System Information & Documentation
echo ===============================================================================
echo.
echo [INFO] Welcome to the World's Most Advanced ERP Platform!
echo.
echo ===============================================================================
echo                                SYSTEM OVERVIEW
echo ===============================================================================
echo.
echo Frontend Technology Stack:
echo   - React 19.1.0 with TypeScript
echo   - Vite 6.3.5 for lightning-fast development
echo   - Ant Design 5.25.2 for beautiful UI components
echo   - TensorFlow.js for AI-powered features
echo   - Tailwind CSS for modern styling
echo   - Framer Motion for smooth animations
echo   - Zustand for state management
echo.
echo Backend Technology Stack:
echo   - Node.js with NestJS framework
echo   - TypeScript for type safety
echo   - PostgreSQL database with TypeORM
echo   - JWT authentication with enterprise security
echo   - Multi-tenant architecture
echo   - RESTful APIs with OpenAPI documentation
echo.
echo ===============================================================================
echo                                PORT CONFIGURATION
echo ===============================================================================
echo.
echo   Frontend Development Server: http://localhost:5173
echo   Backend API Server:          http://localhost:3001
echo   Database:                    localhost:5432 (PostgreSQL)
echo   Storybook (UI Components):   http://localhost:6006
echo.
echo ===============================================================================
echo                                AVAILABLE SCRIPTS
echo ===============================================================================
echo.
echo   start-erp-system.bat    - Main control panel with full menu
echo   quick-start.bat         - Quick development startup
echo   stop-erp-system.bat     - Stop all ERP services
echo   dev-tools.bat           - Development utilities and tools
echo   system-info.bat         - This information screen
echo.
echo ===============================================================================
echo                                SYSTEM REQUIREMENTS
echo ===============================================================================
echo.
echo   - Node.js 18.0.0 or higher
echo   - npm 9.0.0 or higher
echo   - Windows 10/11 or Windows Server 2019+
echo   - 8GB RAM minimum (16GB recommended)
echo   - 10GB free disk space
echo   - Modern web browser (Chrome, Firefox, Edge)
echo.
echo ===============================================================================
echo                                CURRENT STATUS
echo ===============================================================================
echo.

:: Check Node.js
echo [CHECK] Node.js Installation:
node --version >nul 2>&1
if errorlevel 1 (
    echo   Status: NOT INSTALLED
    echo   Action: Please install Node.js from https://nodejs.org/
) else (
    echo   Status: INSTALLED
    node --version
)

:: Check npm
echo.
echo [CHECK] npm Installation:
npm --version >nul 2>&1
if errorlevel 1 (
    echo   Status: NOT INSTALLED
) else (
    echo   Status: INSTALLED
    npm --version
)

:: Check project directories
echo.
echo [CHECK] Project Structure:
if exist "frontend" (
    echo   Frontend Directory: EXISTS
) else (
    echo   Frontend Directory: MISSING
)

if exist "backend" (
    echo   Backend Directory: EXISTS
) else (
    echo   Backend Directory: MISSING
)

:: Check if services are running
echo.
echo [CHECK] Service Status:
netstat -an | findstr ":5173" >nul
if not errorlevel 1 (
    echo   Frontend (Port 5173): RUNNING
) else (
    echo   Frontend (Port 5173): STOPPED
)

netstat -an | findstr ":3001" >nul
if not errorlevel 1 (
    echo   Backend (Port 3001): RUNNING
) else (
    echo   Backend (Port 3001): STOPPED
)

echo.
echo ===============================================================================
echo                                QUICK ACTIONS
echo ===============================================================================
echo.
echo [1] Start Full ERP System
echo [2] Open Main Control Panel
echo [3] View Development Tools
echo [4] Stop All Services
echo [0] Exit
echo.
set /p action="Select an action (0-4): "

if "%action%"=="1" (
    echo [INFO] Starting Full ERP System...
    call quick-start.bat
)
if "%action%"=="2" (
    call start-erp-system.bat
)
if "%action%"=="3" (
    call dev-tools.bat
)
if "%action%"=="4" (
    call stop-erp-system.bat
)
if "%action%"=="0" (
    exit /b 0
)

echo.
echo [INFO] Thank you for using ZaidanOne Ultimate ERP System!
pause
