"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionStrategyController = void 0;
const common_1 = require("@nestjs/common");
const collection_strategy_service_1 = require("../services/collection-strategy.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CollectionStrategyController = class CollectionStrategyController {
    collectionStrategyService;
    constructor(collectionStrategyService) {
        this.collectionStrategyService = collectionStrategyService;
    }
    async create(createStrategyDto) {
        return this.collectionStrategyService.create(createStrategyDto);
    }
    async findAll() {
        return this.collectionStrategyService.findAll();
    }
    async getActiveStrategies() {
        return this.collectionStrategyService.getActiveStrategies();
    }
    async getStrategyEffectiveness() {
        return this.collectionStrategyService.getStrategyEffectiveness();
    }
    async getRecommendedStrategy(debtAmount, daysOverdue) {
        return this.collectionStrategyService.getRecommendedStrategy(Number(debtAmount), Number(daysOverdue));
    }
    async getStrategyRecommendations(debtAmount, daysOverdue, customerType, paymentHistory) {
        return this.collectionStrategyService.getStrategyRecommendations({
            debtAmount: Number(debtAmount),
            daysOverdue: Number(daysOverdue),
            customerType,
            previousPaymentHistory: paymentHistory,
        });
    }
    async findByDebtRange(minAmount, maxAmount) {
        return this.collectionStrategyService.findByDebtRange(Number(minAmount), Number(maxAmount));
    }
    async findByDaysOverdue(days) {
        return this.collectionStrategyService.findByDaysOverdue(Number(days));
    }
    async findOne(id) {
        return this.collectionStrategyService.findOne(id);
    }
    async createDefaultStrategies() {
        return this.collectionStrategyService.createDefaultStrategies();
    }
    async update(id, updateStrategyDto) {
        return this.collectionStrategyService.update(id, updateStrategyDto);
    }
    async activate(id) {
        return this.collectionStrategyService.activateStrategy(id);
    }
    async deactivate(id) {
        return this.collectionStrategyService.deactivateStrategy(id);
    }
    async remove(id) {
        return this.collectionStrategyService.remove(id);
    }
};
exports.CollectionStrategyController = CollectionStrategyController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('active'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "getActiveStrategies", null);
__decorate([
    (0, common_1.Get)('effectiveness'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "getStrategyEffectiveness", null);
__decorate([
    (0, common_1.Get)('recommend'),
    __param(0, (0, common_1.Query)('debtAmount')),
    __param(1, (0, common_1.Query)('daysOverdue')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "getRecommendedStrategy", null);
__decorate([
    (0, common_1.Get)('recommendations'),
    __param(0, (0, common_1.Query)('debtAmount')),
    __param(1, (0, common_1.Query)('daysOverdue')),
    __param(2, (0, common_1.Query)('customerType')),
    __param(3, (0, common_1.Query)('paymentHistory')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "getStrategyRecommendations", null);
__decorate([
    (0, common_1.Get)('debt-range'),
    __param(0, (0, common_1.Query)('minAmount')),
    __param(1, (0, common_1.Query)('maxAmount')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "findByDebtRange", null);
__decorate([
    (0, common_1.Get)('days-overdue/:days'),
    __param(0, (0, common_1.Param)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "findByDaysOverdue", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('create-defaults'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "createDefaultStrategies", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "activate", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "deactivate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CollectionStrategyController.prototype, "remove", null);
exports.CollectionStrategyController = CollectionStrategyController = __decorate([
    (0, common_1.Controller)('collection-strategies'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [collection_strategy_service_1.CollectionStrategyService])
], CollectionStrategyController);
//# sourceMappingURL=collection-strategy.controller.js.map