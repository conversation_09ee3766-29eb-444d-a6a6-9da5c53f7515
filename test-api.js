// Simple Node.js script to test the customer API
const http = require('http');

// Test GET customers endpoint
function testGetCustomers() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/sales/customers',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      console.log('✅ GET /api/sales/customers Response:');
      console.log('Status:', res.statusCode);
      console.log('Data:', JSON.parse(data));
      console.log('');
      
      // Now test POST
      testCreateCustomer();
    });
  });

  req.on('error', (e) => {
    console.error('❌ GET Request Error:', e.message);
  });

  req.end();
}

// Test POST customer creation
function testCreateCustomer() {
  const customerData = {
    name: 'Test Customer Corp',
    type: 'commercial',
    email: '<EMAIL>',
    phone: '******-TEST',
    address: '123 Test Street',
    city: 'Test City',
    status: 'active'
  };

  const postData = JSON.stringify(customerData);

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/sales/customers',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      console.log('✅ POST /api/sales/customers Response:');
      console.log('Status:', res.statusCode);
      console.log('Data:', JSON.parse(data));
      console.log('');
      
      // Test GET again to see the new customer
      testGetCustomersAgain();
    });
  });

  req.on('error', (e) => {
    console.error('❌ POST Request Error:', e.message);
  });

  req.write(postData);
  req.end();
}

// Test GET customers again to see the created customer
function testGetCustomersAgain() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/sales/customers',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      console.log('✅ GET /api/sales/customers (After Creation) Response:');
      console.log('Status:', res.statusCode);
      const responseData = JSON.parse(data);
      console.log('Total Customers:', responseData.total);
      console.log('Customers:', responseData.data);
      console.log('');
      console.log('🎉 API Test Complete!');
    });
  });

  req.on('error', (e) => {
    console.error('❌ GET Request Error:', e.message);
  });

  req.end();
}

console.log('🧪 Testing Customer API...');
console.log('Backend URL: http://localhost:3000');
console.log('');

// Start the test
testGetCustomers();
