import { Customer } from './customer.entity';
import { Invoice } from './invoice.entity';
export declare class Payment {
    id: string;
    paymentNumber: string;
    customerId: string;
    customer: Customer;
    invoiceId: string;
    invoice: Invoice;
    paymentDate: Date;
    amount: number;
    paymentMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'check' | 'other';
    referenceNumber: string;
    bankAccount: string;
    status: 'received' | 'pending' | 'failed' | 'refunded';
    notes: string;
    receiptNumber: string;
    exchangeRate: number;
    currency: string;
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
