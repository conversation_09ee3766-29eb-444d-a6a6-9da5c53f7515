"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const supplier_entity_1 = require("../entities/supplier.entity");
let SupplierService = class SupplierService {
    supplierRepository;
    constructor(supplierRepository) {
        this.supplierRepository = supplierRepository;
    }
    async create(supplierData) {
        const supplier = this.supplierRepository.create(supplierData);
        return this.supplierRepository.save(supplier);
    }
    async findAll() {
        return this.supplierRepository.find({
            relations: ['purchaseOrders'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const supplier = await this.supplierRepository.findOne({
            where: { id },
            relations: ['purchaseOrders'],
        });
        if (!supplier) {
            throw new common_1.NotFoundException(`Supplier with ID ${id} not found`);
        }
        return supplier;
    }
    async update(id, updateData) {
        await this.supplierRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const supplier = await this.findOne(id);
        await this.supplierRepository.remove(supplier);
    }
    async findByCode(code) {
        const supplier = await this.supplierRepository.findOne({
            where: { code },
        });
        if (!supplier) {
            throw new common_1.NotFoundException(`Supplier with code ${code} not found`);
        }
        return supplier;
    }
    async getActiveSuppliers() {
        return this.supplierRepository.find({
            where: { isActive: true },
            order: { name: 'ASC' },
        });
    }
    async searchSuppliers(searchTerm) {
        return this.supplierRepository
            .createQueryBuilder('supplier')
            .where('supplier.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('supplier.code ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('supplier.email ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('supplier.name', 'ASC')
            .getMany();
    }
    async getSupplierStatistics() {
        const totalSuppliers = await this.supplierRepository.count();
        const activeSuppliers = await this.supplierRepository.count({ where: { isActive: true } });
        return {
            totalSuppliers,
            activeSuppliers,
            inactiveSuppliers: totalSuppliers - activeSuppliers,
        };
    }
    async generateSupplierCode(name) {
        const baseCode = name.substring(0, 3).toUpperCase();
        const count = await this.supplierRepository.count();
        const sequence = (count + 1).toString().padStart(4, '0');
        return `SUP-${baseCode}${sequence}`;
    }
};
exports.SupplierService = SupplierService;
exports.SupplierService = SupplierService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(supplier_entity_1.Supplier)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SupplierService);
//# sourceMappingURL=supplier.service.js.map