import { Repository } from 'typeorm';
import { Supplier } from '../entities/supplier.entity';
export declare class SupplierService {
    private supplierRepository;
    constructor(supplierRepository: Repository<Supplier>);
    create(supplierData: Partial<Supplier>): Promise<Supplier>;
    findAll(): Promise<Supplier[]>;
    findOne(id: string): Promise<Supplier>;
    update(id: string, updateData: Partial<Supplier>): Promise<Supplier>;
    remove(id: string): Promise<void>;
    findByCode(code: string): Promise<Supplier>;
    getActiveSuppliers(): Promise<Supplier[]>;
    searchSuppliers(searchTerm: string): Promise<Supplier[]>;
    getSupplierStatistics(): Promise<any>;
    generateSupplierCode(name: string): Promise<string>;
}
