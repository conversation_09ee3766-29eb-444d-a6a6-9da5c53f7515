import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum ITRequestType {
  NEW_USER_SETUP = 'new_user_setup',
  USER_TERMINATION = 'user_termination',
  ACCESS_REQUEST = 'access_request',
  SOFTWARE_INSTALLATION = 'software_installation',
  HARDWARE_REQUEST = 'hardware_request',
  PASSWORD_RESET = 'password_reset',
  EMAIL_SETUP = 'email_setup',
  PHONE_SETUP = 'phone_setup',
  TRAINING = 'training',
  OTHER = 'other',
}

export enum RequestStatus {
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum RequestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('it_requests')
export class ITRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  requestNumber: string;

  @Column({
    type: 'enum',
    enum: ITRequestType,
  })
  type: ITRequestType;

  @Column({
    type: 'enum',
    enum: RequestStatus,
    default: RequestStatus.SUBMITTED,
  })
  status: RequestStatus;

  @Column({
    type: 'enum',
    enum: RequestPriority,
    default: RequestPriority.MEDIUM,
  })
  priority: RequestPriority;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column()
  requesterId: string;

  @Column({ length: 255 })
  requesterName: string;

  @Column({ length: 200 })
  requesterEmail: string;

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ type: 'date', nullable: true })
  requestedDate: Date;

  @Column({ type: 'date', nullable: true })
  dueDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ type: 'text', nullable: true })
  approvalNotes: string;

  @Column({ type: 'text', nullable: true })
  completionNotes: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
