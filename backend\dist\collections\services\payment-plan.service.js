"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentPlanService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_plan_entity_1 = require("../entities/payment-plan.entity");
const payment_plan_installment_entity_1 = require("../entities/payment-plan-installment.entity");
let PaymentPlanService = class PaymentPlanService {
    paymentPlanRepository;
    installmentRepository;
    constructor(paymentPlanRepository, installmentRepository) {
        this.paymentPlanRepository = paymentPlanRepository;
        this.installmentRepository = installmentRepository;
    }
    async create(planData) {
        const plan = this.paymentPlanRepository.create(planData);
        return this.paymentPlanRepository.save(plan);
    }
    async findAll() {
        return this.paymentPlanRepository.find({
            relations: ['customer', 'installments'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const plan = await this.paymentPlanRepository.findOne({
            where: { id },
            relations: ['customer', 'installments'],
        });
        if (!plan) {
            throw new common_1.NotFoundException(`Payment plan with ID ${id} not found`);
        }
        return plan;
    }
    async update(id, updateData) {
        await this.paymentPlanRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const plan = await this.findOne(id);
        await this.paymentPlanRepository.remove(plan);
    }
    async findByCustomer(customerId) {
        return this.paymentPlanRepository.find({
            where: { customerId },
            relations: ['installments'],
            order: { createdAt: 'DESC' },
        });
    }
    async findByStatus(status) {
        return this.paymentPlanRepository.find({
            where: { status },
            relations: ['customer', 'installments'],
        });
    }
    async createInstallments(planId, numberOfInstallments) {
        const plan = await this.findOne(planId);
        if (!plan) {
            throw new Error('Payment plan not found');
        }
        const installmentAmount = plan.totalAmount / numberOfInstallments;
        const installments = [];
        for (let i = 0; i < numberOfInstallments; i++) {
            const dueDate = new Date(plan.startDate);
            dueDate.setMonth(dueDate.getMonth() + i + 1);
            const installment = this.installmentRepository.create({
                paymentPlan: plan,
                installmentNumber: i + 1,
                amount: installmentAmount,
                dueDate,
                status: payment_plan_installment_entity_1.InstallmentStatus.PENDING,
            });
            const savedInstallment = await this.installmentRepository.save(installment);
            installments.push(savedInstallment);
        }
        return installments;
    }
    async recordPayment(installmentId, amount, paymentDate) {
        const installment = await this.installmentRepository.findOne({
            where: { id: installmentId },
            relations: ['paymentPlan'],
        });
        if (!installment) {
            throw new common_1.NotFoundException('Installment not found');
        }
        installment.paidAmount = (installment.paidAmount || 0) + amount;
        installment.lastPaymentDate = paymentDate;
        if (installment.paidAmount >= installment.amount) {
            installment.status = payment_plan_installment_entity_1.InstallmentStatus.PAID;
        }
        else {
            installment.status = payment_plan_installment_entity_1.InstallmentStatus.PARTIAL;
        }
        const updatedInstallment = await this.installmentRepository.save(installment);
        await this.updatePaymentPlanTotals(installment.paymentPlan.id);
        return updatedInstallment;
    }
    async updatePaymentPlanTotals(planId) {
        const plan = await this.findOne(planId);
        const totalPaid = plan.installments.reduce((sum, inst) => sum + (inst.paidAmount || 0), 0);
        const remainingBalance = plan.totalAmount - totalPaid;
        let status = plan.status;
        if (remainingBalance <= 0) {
            status = payment_plan_entity_1.PaymentPlanStatus.COMPLETED;
        }
        else if (totalPaid > 0) {
            status = payment_plan_entity_1.PaymentPlanStatus.ACTIVE;
        }
        await this.paymentPlanRepository.update(planId, {
            paidAmount: totalPaid,
            remainingBalance,
            status,
        });
    }
    async getPaymentPlanSummary(planId) {
        const plan = await this.findOne(planId);
        if (!plan) {
            return null;
        }
        const totalPaid = plan.installments.reduce((sum, inst) => sum + (inst.paidAmount || 0), 0);
        const remainingBalance = plan.totalAmount - totalPaid;
        const paidInstallments = plan.installments.filter(inst => inst.status === payment_plan_installment_entity_1.InstallmentStatus.PAID).length;
        const overdueInstallments = plan.installments.filter(inst => inst.status === payment_plan_installment_entity_1.InstallmentStatus.PENDING && new Date(inst.dueDate) < new Date()).length;
        return {
            planId: plan.id,
            customerId: plan.customerId,
            totalAmount: plan.totalAmount,
            totalPaid,
            remainingBalance,
            totalInstallments: plan.installments.length,
            paidInstallments,
            overdueInstallments,
            completionPercentage: (totalPaid / plan.totalAmount) * 100,
            status: plan.status,
            nextDueDate: this.getNextDueDate(plan.installments),
            nextDueAmount: this.getNextDueAmount(plan.installments),
        };
    }
    getNextDueDate(installments) {
        const pendingInstallments = installments
            .filter(inst => inst.status === payment_plan_installment_entity_1.InstallmentStatus.PENDING)
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
        return pendingInstallments.length > 0 ? pendingInstallments[0].dueDate : null;
    }
    getNextDueAmount(installments) {
        const pendingInstallments = installments
            .filter(inst => inst.status === payment_plan_installment_entity_1.InstallmentStatus.PENDING)
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
        return pendingInstallments.length > 0 ? pendingInstallments[0].amount : 0;
    }
    async getOverdueInstallments() {
        const today = new Date();
        return this.installmentRepository
            .createQueryBuilder('installment')
            .leftJoinAndSelect('installment.paymentPlan', 'plan')
            .leftJoinAndSelect('plan.customer', 'customer')
            .where('installment.dueDate < :today', { today })
            .andWhere('installment.status = :status', { status: payment_plan_installment_entity_1.InstallmentStatus.PENDING })
            .orderBy('installment.dueDate', 'ASC')
            .getMany();
    }
    async getUpcomingInstallments(days = 7) {
        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(today.getDate() + days);
        return this.installmentRepository
            .createQueryBuilder('installment')
            .leftJoinAndSelect('installment.paymentPlan', 'plan')
            .leftJoinAndSelect('plan.customer', 'customer')
            .where('installment.dueDate BETWEEN :today AND :futureDate', { today, futureDate })
            .andWhere('installment.status = :status', { status: payment_plan_installment_entity_1.InstallmentStatus.PENDING })
            .orderBy('installment.dueDate', 'ASC')
            .getMany();
    }
    async markPlanAsDefaulted(planId, reason) {
        await this.paymentPlanRepository.update(planId, {
            status: payment_plan_entity_1.PaymentPlanStatus.DEFAULTED,
            notes: reason,
        });
        return this.findOne(planId);
    }
    async getPaymentPlanStatistics() {
        const totalPlans = await this.paymentPlanRepository.count();
        const activePlans = await this.paymentPlanRepository.count({
            where: { status: payment_plan_entity_1.PaymentPlanStatus.ACTIVE }
        });
        const completedPlans = await this.paymentPlanRepository.count({
            where: { status: payment_plan_entity_1.PaymentPlanStatus.COMPLETED }
        });
        const defaultedPlans = await this.paymentPlanRepository.count({
            where: { status: payment_plan_entity_1.PaymentPlanStatus.DEFAULTED }
        });
        const financialData = await this.paymentPlanRepository
            .createQueryBuilder('plan')
            .select([
            'SUM(plan.totalAmount) as totalAmount',
            'SUM(plan.paidAmount) as paidAmount',
        ])
            .getRawOne();
        const overdueInstallments = await this.getOverdueInstallments();
        const upcomingInstallments = await this.getUpcomingInstallments();
        return {
            totalPlans,
            activePlans,
            completedPlans,
            defaultedPlans,
            totalAmount: parseFloat(financialData.totalAmount) || 0,
            paidAmount: parseFloat(financialData.paidAmount) || 0,
            completionRate: totalPlans > 0 ? (completedPlans / totalPlans) * 100 : 0,
            overdueCount: overdueInstallments.length,
            upcomingCount: upcomingInstallments.length,
        };
    }
};
exports.PaymentPlanService = PaymentPlanService;
exports.PaymentPlanService = PaymentPlanService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_plan_entity_1.PaymentPlan)),
    __param(1, (0, typeorm_1.InjectRepository)(payment_plan_installment_entity_1.PaymentPlanInstallment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PaymentPlanService);
//# sourceMappingURL=payment-plan.service.js.map