@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: ZaidanOne Ultimate ERP System - Quick Start Script
:: =============================================================================
:: This script provides a quick way to start the ERP system for development
:: =============================================================================

title ZaidanOne ERP - Quick Start

color 0B

echo.
echo ===============================================================================
echo                    ZAIDANONE ERP SYSTEM - QUICK START
echo ===============================================================================
echo.

:: Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found! Please install Node.js first.
    pause
    exit /b 1
)

echo [INFO] Starting ZaidanOne Ultimate ERP System...
echo [INFO] Frontend: http://localhost:5173
echo [INFO] Backend API: http://localhost:3001
echo.

:: Start backend
echo [INFO] Starting Backend Server...
start "ERP Backend" cmd /k "cd backend && npm run start:dev"

:: Wait for backend to initialize
timeout /t 3 >nul

:: Start frontend
echo [INFO] Starting Frontend Server...
start "ERP Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo [SUCCESS] ERP System is starting up!
echo [INFO] Please wait for both servers to initialize...
echo [INFO] The frontend will automatically open in your browser.
echo.
echo [INFO] Press any key to return to the main menu...
pause >nul

:: Launch main control panel
call start-erp-system.bat
