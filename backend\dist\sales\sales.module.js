"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const customer_entity_1 = require("./entities/customer.entity");
const invoice_entity_1 = require("./entities/invoice.entity");
const invoice_item_entity_1 = require("./entities/invoice-item.entity");
const quotation_entity_1 = require("./entities/quotation.entity");
const quotation_item_entity_1 = require("./entities/quotation-item.entity");
const credit_note_entity_1 = require("./entities/credit-note.entity");
const credit_note_item_entity_1 = require("./entities/credit-note-item.entity");
const payment_entity_1 = require("./entities/payment.entity");
const recurring_invoice_entity_1 = require("./entities/recurring-invoice.entity");
const recurring_invoice_item_entity_1 = require("./entities/recurring-invoice-item.entity");
const returned_invoice_entity_1 = require("./entities/returned-invoice.entity");
const returned_invoice_item_entity_1 = require("./entities/returned-invoice-item.entity");
const customer_service_1 = require("./services/customer.service");
const invoice_service_1 = require("./services/invoice.service");
const quotation_service_1 = require("./services/quotation.service");
const payment_service_1 = require("./services/payment.service");
const credit_note_service_1 = require("./services/credit-note.service");
const recurring_invoice_service_1 = require("./services/recurring-invoice.service");
const returned_invoice_service_1 = require("./services/returned-invoice.service");
const customer_controller_1 = require("./controllers/customer.controller");
const invoice_controller_1 = require("./controllers/invoice.controller");
const quotation_controller_1 = require("./controllers/quotation.controller");
const payment_controller_1 = require("./controllers/payment.controller");
const credit_note_controller_1 = require("./controllers/credit-note.controller");
const recurring_invoice_controller_1 = require("./controllers/recurring-invoice.controller");
const returned_invoice_controller_1 = require("./controllers/returned-invoice.controller");
let SalesModule = class SalesModule {
};
exports.SalesModule = SalesModule;
exports.SalesModule = SalesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                customer_entity_1.Customer,
                invoice_entity_1.Invoice,
                invoice_item_entity_1.InvoiceItem,
                quotation_entity_1.Quotation,
                quotation_item_entity_1.QuotationItem,
                credit_note_entity_1.CreditNote,
                credit_note_item_entity_1.CreditNoteItem,
                payment_entity_1.Payment,
                recurring_invoice_entity_1.RecurringInvoice,
                recurring_invoice_item_entity_1.RecurringInvoiceItem,
                returned_invoice_entity_1.ReturnedInvoice,
                returned_invoice_item_entity_1.ReturnedInvoiceItem,
            ]),
        ],
        controllers: [
            customer_controller_1.CustomerController,
            invoice_controller_1.InvoiceController,
            quotation_controller_1.QuotationController,
            payment_controller_1.PaymentController,
            credit_note_controller_1.CreditNoteController,
            recurring_invoice_controller_1.RecurringInvoiceController,
            returned_invoice_controller_1.ReturnedInvoiceController,
        ],
        providers: [
            customer_service_1.CustomerService,
            invoice_service_1.InvoiceService,
            quotation_service_1.QuotationService,
            payment_service_1.PaymentService,
            credit_note_service_1.CreditNoteService,
            recurring_invoice_service_1.RecurringInvoiceService,
            returned_invoice_service_1.ReturnedInvoiceService,
        ],
        exports: [
            customer_service_1.CustomerService,
            invoice_service_1.InvoiceService,
            quotation_service_1.QuotationService,
            payment_service_1.PaymentService,
            credit_note_service_1.CreditNoteService,
            recurring_invoice_service_1.RecurringInvoiceService,
            returned_invoice_service_1.ReturnedInvoiceService,
        ],
    })
], SalesModule);
//# sourceMappingURL=sales.module.js.map