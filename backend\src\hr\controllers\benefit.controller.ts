import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { BenefitService } from '../services/benefit.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/benefits')
@UseGuards(JwtAuthGuard)
export class BenefitController {
  constructor(private readonly benefitService: BenefitService) {}

  @Post()
  create(@Body() createBenefitDto: any) {
    return this.benefitService.create(createBenefitDto);
  }

  @Get()
  findAll() {
    return this.benefitService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.benefitService.findOne(id);
  }

  @Post('enroll')
  enrollEmployee(@Body() enrollDto: { employeeId: string; benefitId: string; enrollmentData: any }) {
    return this.benefitService.enrollEmployee(
      enrollDto.employeeId,
      enrollDto.benefitId,
      enrollDto.enrollmentData
    );
  }

  @Get('employee/:employeeId')
  getEmployeeBenefits(@Param('employeeId') employeeId: string) {
    return this.benefitService.getEmployeeBenefits(employeeId);
  }
}
