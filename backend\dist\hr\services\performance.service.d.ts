import { Repository } from 'typeorm';
import { Performance } from '../entities/performance.entity';
export declare class PerformanceService {
    private performanceRepository;
    constructor(performanceRepository: Repository<Performance>);
    create(createPerformanceDto: any): Promise<Performance>;
    findAll(filters?: any): Promise<Performance[]>;
    findOne(id: string): Promise<Performance>;
    update(id: string, updatePerformanceDto: any): Promise<Performance>;
    submitReview(id: string): Promise<Performance>;
    approveReview(id: string, approvedBy: string): Promise<Performance>;
}
