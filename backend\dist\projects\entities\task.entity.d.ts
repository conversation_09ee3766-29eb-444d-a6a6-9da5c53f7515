import { Project } from './project.entity';
import { TaskAssignment } from './task-assignment.entity';
import { TaskComment } from './task-comment.entity';
import { TaskAttachment } from './task-attachment.entity';
import { TimeEntry } from './time-entry.entity';
export declare enum TaskStatus {
    TODO = "todo",
    IN_PROGRESS = "in_progress",
    IN_REVIEW = "in_review",
    DONE = "done",
    CANCELLED = "cancelled",
    BLOCKED = "blocked"
}
export declare enum TaskPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export declare enum TaskType {
    TASK = "task",
    BUG = "bug",
    FEATURE = "feature",
    IMPROVEMENT = "improvement",
    RESEARCH = "research",
    DOCUMENTATION = "documentation",
    TESTING = "testing",
    DEPLOYMENT = "deployment"
}
export declare class Task {
    id: string;
    projectId: string;
    project: Project;
    taskNumber: string;
    title: string;
    description: string;
    type: TaskType;
    status: TaskStatus;
    priority: TaskPriority;
    startDate: Date;
    dueDate: Date;
    completedDate: Date;
    estimatedHours: number;
    actualHours: number;
    completionPercentage: number;
    parentTaskId: string;
    parentTask: Task;
    subTasks: Task[];
    createdBy: string;
    assignedTo: string;
    labels: string[];
    customFields: any;
    assignments: TaskAssignment[];
    comments: TaskComment[];
    attachments: TaskAttachment[];
    timeEntries: TimeEntry[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
