import { Repository } from 'typeorm';
import { Warehouse } from '../entities/warehouse.entity';
import { Location } from '../entities/location.entity';
import { Stock } from '../entities/stock.entity';
export declare class WarehouseService {
    private warehouseRepository;
    private locationRepository;
    private stockRepository;
    constructor(warehouseRepository: Repository<Warehouse>, locationRepository: Repository<Location>, stockRepository: Repository<Stock>);
    create(warehouseData: Partial<Warehouse>): Promise<Warehouse>;
    findAll(): Promise<Warehouse[]>;
    findOne(id: string): Promise<Warehouse>;
    update(id: string, updateData: Partial<Warehouse>): Promise<Warehouse>;
    remove(id: string): Promise<void>;
    findByCode(code: string): Promise<Warehouse>;
    getActiveWarehouses(): Promise<Warehouse[]>;
    createLocation(warehouseId: string, locationData: Partial<Location>): Promise<Location>;
    getWarehouseLocations(warehouseId: string): Promise<Location[]>;
    getWarehouseStock(warehouseId: string): Promise<Stock[]>;
    getWarehouseStockValue(warehouseId: string): Promise<number>;
    getWarehouseCapacityUtilization(warehouseId: string): Promise<any>;
    getWarehouseStatistics(): Promise<any>;
    searchWarehouses(searchTerm: string): Promise<Warehouse[]>;
    getWarehousesByRegion(region: string): Promise<Warehouse[]>;
    transferStock(fromWarehouseId: string, toWarehouseId: string, productId: string, quantity: number): Promise<{
        success: boolean;
        message: string;
    }>;
    generateWarehouseCode(name: string): Promise<string>;
}
