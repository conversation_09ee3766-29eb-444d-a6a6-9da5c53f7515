"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const permission_entity_1 = require("../entities/permission.entity");
const role_entity_1 = require("../entities/role.entity");
let PermissionService = class PermissionService {
    permissionRepository;
    roleRepository;
    constructor(permissionRepository, roleRepository) {
        this.permissionRepository = permissionRepository;
        this.roleRepository = roleRepository;
    }
    async createDefaultPermissions() {
        const permissions = this.getDefaultPermissions();
        const createdPermissions = [];
        for (const permissionData of permissions) {
            const existing = await this.permissionRepository.findOne({
                where: {
                    module: permissionData.module,
                    action: permissionData.action,
                    resource: permissionData.resource,
                },
            });
            if (!existing) {
                const permission = this.permissionRepository.create(permissionData);
                const saved = await this.permissionRepository.save(permission);
                createdPermissions.push(saved);
            }
        }
        return createdPermissions;
    }
    getDefaultPermissions() {
        return [
            {
                module: permission_entity_1.PermissionModule.ANALYTICS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'dashboard',
                name: 'View Analytics Dashboard',
                description: 'Access to view analytics dashboard and basic metrics',
            },
            {
                module: permission_entity_1.PermissionModule.ANALYTICS,
                action: permission_entity_1.PermissionAction.VIEW_REPORTS,
                resource: 'reports',
                name: 'View Analytics Reports',
                description: 'Access to view detailed analytics reports',
            },
            {
                module: permission_entity_1.PermissionModule.ANALYTICS,
                action: permission_entity_1.PermissionAction.GENERATE_REPORTS,
                resource: 'reports',
                name: 'Generate Analytics Reports',
                description: 'Ability to generate custom analytics reports',
            },
            {
                module: permission_entity_1.PermissionModule.ANALYTICS,
                action: permission_entity_1.PermissionAction.EXPORT,
                resource: 'data',
                name: 'Export Analytics Data',
                description: 'Export analytics data to various formats',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'customer',
                name: 'Create Customer',
                description: 'Add new customers to the system',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'customer',
                name: 'View Customer',
                description: 'View customer information and details',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'customer',
                name: 'Update Customer',
                description: 'Edit customer information and details',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.DELETE,
                resource: 'customer',
                name: 'Delete Customer',
                description: 'Remove customers from the system',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'interaction',
                name: 'Log Customer Interaction',
                description: 'Record interactions with customers',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'interaction',
                name: 'View Customer Interactions',
                description: 'View customer interaction history',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'segment',
                name: 'Create Customer Segment',
                description: 'Create customer segments for marketing',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.MANAGE_SETTINGS,
                resource: 'loyalty',
                name: 'Manage Customer Loyalty',
                description: 'Manage customer loyalty programs and points',
            },
            {
                module: permission_entity_1.PermissionModule.CUSTOMERS,
                action: permission_entity_1.PermissionAction.ADJUST,
                resource: 'credit',
                name: 'Adjust Customer Credit',
                description: 'Modify customer credit limits and terms',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'case',
                name: 'Create Collection Case',
                description: 'Create new collection cases for overdue accounts',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'case',
                name: 'View Collection Cases',
                description: 'View collection case details and status',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'case',
                name: 'Update Collection Case',
                description: 'Update collection case information and status',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.ASSIGN,
                resource: 'case',
                name: 'Assign Collection Case',
                description: 'Assign collection cases to agents',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.ESCALATE,
                resource: 'case',
                name: 'Escalate Collection Case',
                description: 'Escalate collection cases to higher priority',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.CLOSE,
                resource: 'case',
                name: 'Close Collection Case',
                description: 'Close resolved collection cases',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'activity',
                name: 'Log Collection Activity',
                description: 'Record collection activities and communications',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'payment_plan',
                name: 'Create Payment Plan',
                description: 'Set up payment plans for customers',
            },
            {
                module: permission_entity_1.PermissionModule.COLLECTIONS,
                action: permission_entity_1.PermissionAction.PROCESS_PAYMENT,
                resource: 'payment',
                name: 'Process Collection Payment',
                description: 'Process payments against collection cases',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'transaction',
                name: 'Create Financial Transaction',
                description: 'Record financial transactions',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'transaction',
                name: 'View Financial Transactions',
                description: 'View financial transaction details',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.APPROVE,
                resource: 'transaction',
                name: 'Approve Financial Transaction',
                description: 'Approve pending financial transactions',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'invoice',
                name: 'Create Invoice',
                description: 'Generate customer invoices',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'invoice',
                name: 'Update Invoice',
                description: 'Modify invoice details',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.VOID,
                resource: 'invoice',
                name: 'Void Invoice',
                description: 'Cancel or void invoices',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.PROCESS_PAYMENT,
                resource: 'payment',
                name: 'Process Payment',
                description: 'Process customer payments',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.REFUND,
                resource: 'payment',
                name: 'Process Refund',
                description: 'Process customer refunds',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.VIEW_REPORTS,
                resource: 'reports',
                name: 'View Financial Reports',
                description: 'Access financial reports and statements',
            },
            {
                module: permission_entity_1.PermissionModule.FINANCE,
                action: permission_entity_1.PermissionAction.GENERATE_REPORTS,
                resource: 'reports',
                name: 'Generate Financial Reports',
                description: 'Create custom financial reports',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'employee',
                name: 'Create Employee',
                description: 'Add new employees to the system',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'employee',
                name: 'View Employee',
                description: 'View employee information and records',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'employee',
                name: 'Update Employee',
                description: 'Edit employee information and records',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.DELETE,
                resource: 'employee',
                name: 'Delete Employee',
                description: 'Remove employees from the system',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'payroll',
                name: 'View Payroll',
                description: 'Access payroll information',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.PROCESS_PAYMENT,
                resource: 'payroll',
                name: 'Process Payroll',
                description: 'Process employee payroll',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'leave',
                name: 'Manage Leave Requests',
                description: 'Create and manage employee leave requests',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.APPROVE,
                resource: 'leave',
                name: 'Approve Leave Requests',
                description: 'Approve or reject employee leave requests',
            },
            {
                module: permission_entity_1.PermissionModule.HR,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'performance',
                name: 'Manage Performance Reviews',
                description: 'Create and manage employee performance reviews',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'product',
                name: 'Create Product',
                description: 'Add new products to inventory',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'product',
                name: 'View Product',
                description: 'View product information and details',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'product',
                name: 'Update Product',
                description: 'Edit product information and pricing',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.DELETE,
                resource: 'product',
                name: 'Delete Product',
                description: 'Remove products from inventory',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.ADJUST,
                resource: 'stock',
                name: 'Adjust Stock',
                description: 'Adjust product stock levels',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'stock',
                name: 'View Stock Levels',
                description: 'View current stock levels and availability',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'purchase_order',
                name: 'Create Purchase Order',
                description: 'Create purchase orders for suppliers',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.APPROVE,
                resource: 'purchase_order',
                name: 'Approve Purchase Order',
                description: 'Approve purchase orders',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'supplier',
                name: 'Manage Suppliers',
                description: 'Add and manage supplier information',
            },
            {
                module: permission_entity_1.PermissionModule.INVENTORY,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'warehouse',
                name: 'Manage Warehouses',
                description: 'Create and manage warehouse locations',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'ticket',
                name: 'Create Support Ticket',
                description: 'Create new IT support tickets',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'ticket',
                name: 'View Support Tickets',
                description: 'View IT support ticket details',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'ticket',
                name: 'Update Support Ticket',
                description: 'Update IT support ticket information',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.ASSIGN,
                resource: 'ticket',
                name: 'Assign Support Ticket',
                description: 'Assign tickets to IT support agents',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.ESCALATE,
                resource: 'ticket',
                name: 'Escalate Support Ticket',
                description: 'Escalate tickets to higher support levels',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.CLOSE,
                resource: 'ticket',
                name: 'Close Support Ticket',
                description: 'Close resolved support tickets',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'asset',
                name: 'Manage IT Assets',
                description: 'Create and manage IT assets',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.ASSIGN,
                resource: 'asset',
                name: 'Assign IT Assets',
                description: 'Assign IT assets to employees',
            },
            {
                module: permission_entity_1.PermissionModule.IT_SUPPORT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'knowledge_base',
                name: 'Manage Knowledge Base',
                description: 'Create and manage knowledge base articles',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'sale',
                name: 'Process Sale',
                description: 'Process customer sales transactions',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'sale',
                name: 'View Sales',
                description: 'View sales transaction details',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.VOID,
                resource: 'sale',
                name: 'Void Sale',
                description: 'Cancel or void sales transactions',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.REFUND,
                resource: 'sale',
                name: 'Process Refund',
                description: 'Process customer refunds',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.PROCESS_PAYMENT,
                resource: 'payment',
                name: 'Process Payment',
                description: 'Process various payment methods',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'cash_register',
                name: 'Access Cash Register',
                description: 'Access and operate cash register',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.MANAGE_SETTINGS,
                resource: 'cash_register',
                name: 'Manage Cash Register',
                description: 'Open/close cash register and manage cash',
            },
            {
                module: permission_entity_1.PermissionModule.POS,
                action: permission_entity_1.PermissionAction.PRINT,
                resource: 'receipt',
                name: 'Print Receipts',
                description: 'Print customer receipts',
            },
            {
                module: permission_entity_1.PermissionModule.PROCUREMENT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'purchase_request',
                name: 'Create Purchase Request',
                description: 'Create new purchase requests',
            },
            {
                module: permission_entity_1.PermissionModule.PROCUREMENT,
                action: permission_entity_1.PermissionAction.APPROVE,
                resource: 'purchase_request',
                name: 'Approve Purchase Request',
                description: 'Approve purchase requests',
            },
            {
                module: permission_entity_1.PermissionModule.PROCUREMENT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'vendor',
                name: 'Manage Vendors',
                description: 'Add and manage vendor information',
            },
            {
                module: permission_entity_1.PermissionModule.PROCUREMENT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'rfq',
                name: 'Manage RFQ',
                description: 'Create and manage Request for Quotations',
            },
            {
                module: permission_entity_1.PermissionModule.PROCUREMENT,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'contract',
                name: 'Manage Contracts',
                description: 'Create and manage procurement contracts',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'project',
                name: 'Create Project',
                description: 'Create new projects',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'project',
                name: 'View Projects',
                description: 'View project details and status',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'project',
                name: 'Update Project',
                description: 'Edit project information and settings',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.DELETE,
                resource: 'project',
                name: 'Delete Project',
                description: 'Remove projects from the system',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'task',
                name: 'Manage Tasks',
                description: 'Create and manage project tasks',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.ASSIGN,
                resource: 'task',
                name: 'Assign Tasks',
                description: 'Assign tasks to team members',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'milestone',
                name: 'Manage Milestones',
                description: 'Create and manage project milestones',
            },
            {
                module: permission_entity_1.PermissionModule.PROJECTS,
                action: permission_entity_1.PermissionAction.ASSIGN,
                resource: 'resource',
                name: 'Manage Resources',
                description: 'Assign and manage project resources',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'lead',
                name: 'Create Lead',
                description: 'Add new sales leads',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.READ,
                resource: 'lead',
                name: 'View Leads',
                description: 'View sales lead information',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.UPDATE,
                resource: 'lead',
                name: 'Update Lead',
                description: 'Edit sales lead information',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.ASSIGN,
                resource: 'lead',
                name: 'Assign Lead',
                description: 'Assign leads to sales representatives',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'opportunity',
                name: 'Manage Opportunities',
                description: 'Create and manage sales opportunities',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'quote',
                name: 'Create Quote',
                description: 'Generate customer quotes',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.APPROVE,
                resource: 'quote',
                name: 'Approve Quote',
                description: 'Approve customer quotes',
            },
            {
                module: permission_entity_1.PermissionModule.SALES,
                action: permission_entity_1.PermissionAction.CREATE,
                resource: 'order',
                name: 'Create Sales Order',
                description: 'Create customer sales orders',
            },
            {
                module: permission_entity_1.PermissionModule.SETTINGS,
                action: permission_entity_1.PermissionAction.MANAGE_SETTINGS,
                resource: 'system',
                name: 'Manage System Settings',
                description: 'Configure system-wide settings',
            },
            {
                module: permission_entity_1.PermissionModule.SETTINGS,
                action: permission_entity_1.PermissionAction.CONFIGURE,
                resource: 'company',
                name: 'Configure Company Settings',
                description: 'Configure company information and settings',
            },
            {
                module: permission_entity_1.PermissionModule.SETTINGS,
                action: permission_entity_1.PermissionAction.MANAGE_SETTINGS,
                resource: 'integration',
                name: 'Manage Integrations',
                description: 'Configure third-party integrations',
            },
            {
                module: permission_entity_1.PermissionModule.SETTINGS,
                action: permission_entity_1.PermissionAction.BACKUP,
                resource: 'data',
                name: 'Backup Data',
                description: 'Create system backups',
            },
            {
                module: permission_entity_1.PermissionModule.SETTINGS,
                action: permission_entity_1.PermissionAction.RESTORE,
                resource: 'data',
                name: 'Restore Data',
                description: 'Restore system from backups',
            },
            {
                module: permission_entity_1.PermissionModule.USER_MANAGEMENT,
                action: permission_entity_1.PermissionAction.MANAGE_USERS,
                resource: 'user',
                name: 'Manage Users',
                description: 'Create, edit, and manage user accounts',
            },
            {
                module: permission_entity_1.PermissionModule.USER_MANAGEMENT,
                action: permission_entity_1.PermissionAction.MANAGE_ROLES,
                resource: 'role',
                name: 'Manage Roles',
                description: 'Create and manage user roles',
            },
            {
                module: permission_entity_1.PermissionModule.USER_MANAGEMENT,
                action: permission_entity_1.PermissionAction.MANAGE_PERMISSIONS,
                resource: 'permission',
                name: 'Manage Permissions',
                description: 'Configure user permissions',
            },
            {
                module: permission_entity_1.PermissionModule.USER_MANAGEMENT,
                action: permission_entity_1.PermissionAction.AUDIT,
                resource: 'activity',
                name: 'View Audit Logs',
                description: 'Access user activity audit logs',
            },
        ];
    }
    async findAll() {
        return this.permissionRepository.find({
            order: { module: 'ASC', resource: 'ASC', action: 'ASC' },
        });
    }
    async findByModule(module) {
        return this.permissionRepository.find({
            where: { module },
            order: { resource: 'ASC', action: 'ASC' },
        });
    }
    async getPermissionsByRole(roleId) {
        const role = await this.roleRepository.findOne({
            where: { id: roleId },
            relations: ['permissions'],
        });
        return role?.permissions || [];
    }
    async getGroupedPermissions() {
        const permissions = await this.findAll();
        const grouped = {};
        permissions.forEach(permission => {
            if (!grouped[permission.module]) {
                grouped[permission.module] = {};
            }
            if (!grouped[permission.module][permission.resource]) {
                grouped[permission.module][permission.resource] = [];
            }
            grouped[permission.module][permission.resource].push(permission);
        });
        return grouped;
    }
};
exports.PermissionService = PermissionService;
exports.PermissionService = PermissionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PermissionService);
//# sourceMappingURL=permission.service.js.map