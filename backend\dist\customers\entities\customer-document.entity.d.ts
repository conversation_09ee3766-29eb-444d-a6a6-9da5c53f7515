import { Customer } from './customer.entity';
export declare enum DocumentType {
    CONTRACT = "contract",
    INVOICE = "invoice",
    QUOTE = "quote",
    PROPOSAL = "proposal",
    AGREEMENT = "agreement",
    CERTIFICATE = "certificate",
    LICENSE = "license",
    IDENTIFICATION = "identification",
    FINANCIAL = "financial",
    LEGAL = "legal",
    CORRESPONDENCE = "correspondence",
    OTHER = "other"
}
export declare class CustomerDocument {
    id: string;
    customerId: string;
    customer: Customer;
    name: string;
    description: string;
    type: DocumentType;
    fileName: string;
    originalName: string;
    filePath: string;
    mimeType: string;
    fileSize: number;
    version: string;
    uploadedBy: string;
    expiryDate: Date;
    tags: string[];
    isActive: boolean;
    isConfidential: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
