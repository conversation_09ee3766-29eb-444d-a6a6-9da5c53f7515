import { CollectionCase } from './collection-case.entity';
export declare enum ActivityType {
    CALL = "call",
    EMAIL = "email",
    LETTER = "letter",
    SMS = "sms",
    VISIT = "visit",
    PAYMENT = "payment",
    PROMISE_TO_PAY = "promise_to_pay",
    DISPUTE = "dispute",
    LEGAL_ACTION = "legal_action",
    SETTLEMENT = "settlement",
    WRITE_OFF = "write_off",
    NOTE = "note",
    STATUS_CHANGE = "status_change"
}
export declare enum ActivityResult {
    SUCCESSFUL = "successful",
    NO_ANSWER = "no_answer",
    BUSY = "busy",
    DISCONNECTED = "disconnected",
    WRONG_NUMBER = "wrong_number",
    LEFT_MESSAGE = "left_message",
    PROMISED_TO_PAY = "promised_to_pay",
    DISPUTED = "disputed",
    REFUSED_TO_PAY = "refused_to_pay",
    PAYMENT_RECEIVED = "payment_received",
    CALLBACK_REQUESTED = "callback_requested",
    OTHER = "other"
}
export declare class CollectionActivity {
    id: string;
    caseId: string;
    case: CollectionCase;
    type: ActivityType;
    result: ActivityResult;
    activityDate: Date;
    duration: number;
    description: string;
    outcome: string;
    performedBy: string;
    contactMethod: string;
    contactNumber: string;
    paymentAmount: number;
    promiseDate: Date;
    promiseAmount: number;
    followUpDate: Date;
    followUpAction: string;
    isAutomated: boolean;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
