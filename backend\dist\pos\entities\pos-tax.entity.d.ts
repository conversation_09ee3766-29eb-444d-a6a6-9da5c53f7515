import { PosSale } from './pos-sale.entity';
export declare enum TaxType {
    SALES_TAX = "sales_tax",
    VAT = "vat",
    GST = "gst",
    EXCISE_TAX = "excise_tax",
    LUXURY_TAX = "luxury_tax",
    ENVIRONMENTAL_TAX = "environmental_tax"
}
export declare class PosTax {
    id: string;
    saleId: string;
    sale: PosSale;
    name: string;
    type: TaxType;
    rate: number;
    taxableAmount: number;
    taxAmount: number;
    isInclusive: boolean;
    jurisdiction: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
