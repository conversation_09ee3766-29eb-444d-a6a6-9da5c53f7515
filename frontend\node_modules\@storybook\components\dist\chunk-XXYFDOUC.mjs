import { require_java } from './chunk-RRUJBSF3.mjs';
import { require_javadoclike } from './chunk-ZPDD6KTK.mjs';
import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_javadoc=__commonJS({"../../node_modules/refractor/lang/javadoc.js"(exports,module){var refractorJava=require_java(),refractorJavadoclike=require_javadoclike();module.exports=javadoc;javadoc.displayName="javadoc";javadoc.aliases=[];function javadoc(Prism){Prism.register(refractorJava),Prism.register(refractorJavadoclike),function(Prism2){var codeLinePattern=/(^(?:[\t ]*(?:\*\s*)*))[^*\s].*$/m,memberReference=/#\s*\w+(?:\s*\([^()]*\))?/.source,reference=/(?:\b[a-zA-Z]\w+\s*\.\s*)*\b[A-Z]\w*(?:\s*<mem>)?|<mem>/.source.replace(/<mem>/g,function(){return memberReference});Prism2.languages.javadoc=Prism2.languages.extend("javadoclike",{}),Prism2.languages.insertBefore("javadoc","keyword",{reference:{pattern:RegExp(/(@(?:exception|link|linkplain|see|throws|value)\s+(?:\*\s*)?)/.source+"(?:"+reference+")"),lookbehind:!0,inside:{function:{pattern:/(#\s*)\w+(?=\s*\()/,lookbehind:!0},field:{pattern:/(#\s*)\w+/,lookbehind:!0},namespace:{pattern:/\b(?:[a-z]\w*\s*\.\s*)+/,inside:{punctuation:/\./}},"class-name":/\b[A-Z]\w*/,keyword:Prism2.languages.java.keyword,punctuation:/[#()[\],.]/}},"class-name":{pattern:/(@param\s+)<[A-Z]\w*>/,lookbehind:!0,inside:{punctuation:/[.<>]/}},"code-section":[{pattern:/(\{@code\s+(?!\s))(?:[^\s{}]|\s+(?![\s}])|\{(?:[^{}]|\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})*\})*\})+(?=\s*\})/,lookbehind:!0,inside:{code:{pattern:codeLinePattern,lookbehind:!0,inside:Prism2.languages.java,alias:"language-java"}}},{pattern:/(<(code|pre|tt)>(?!<code>)\s*)\S(?:\S|\s+\S)*?(?=\s*<\/\2>)/,lookbehind:!0,inside:{line:{pattern:codeLinePattern,lookbehind:!0,inside:{tag:Prism2.languages.markup.tag,entity:Prism2.languages.markup.entity,code:{pattern:/.+/,inside:Prism2.languages.java,alias:"language-java"}}}}}],tag:Prism2.languages.markup.tag,entity:Prism2.languages.markup.entity}),Prism2.languages.javadoclike.addSupport("java",Prism2.languages.javadoc);}(Prism);}}});

export { require_javadoc };
