import { CollectionStrategyService } from '../services/collection-strategy.service';
import { CollectionStrategy } from '../entities/collection-strategy.entity';
export declare class CollectionStrategyController {
    private readonly collectionStrategyService;
    constructor(collectionStrategyService: CollectionStrategyService);
    create(createStrategyDto: Partial<CollectionStrategy>): Promise<CollectionStrategy>;
    findAll(): Promise<CollectionStrategy[]>;
    getActiveStrategies(): Promise<CollectionStrategy[]>;
    getStrategyEffectiveness(): Promise<any[]>;
    getRecommendedStrategy(debtAmount: number, daysOverdue: number): Promise<CollectionStrategy | null>;
    getStrategyRecommendations(debtAmount: number, daysOverdue: number, customerType?: string, paymentHistory?: string): Promise<{
        primaryStrategy: CollectionStrategy | null;
        alternativeStrategies: CollectionStrategy[];
        recommendations: string[];
    }>;
    findByDebtRange(minAmount: number, maxAmount: number): Promise<CollectionStrategy[]>;
    findByDaysOverdue(days: number): Promise<CollectionStrategy[]>;
    findOne(id: string): Promise<CollectionStrategy>;
    createDefaultStrategies(): Promise<CollectionStrategy[]>;
    update(id: string, updateStrategyDto: Partial<CollectionStrategy>): Promise<CollectionStrategy>;
    activate(id: string): Promise<CollectionStrategy>;
    deactivate(id: string): Promise<CollectionStrategy>;
    remove(id: string): Promise<void>;
}
