import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'antd',
      '@tensorflow/tfjs',
      'crypto-js',
      'framer-motion',
      'recharts',
      'zustand'
    ],
  },
  server: {
    port: 3000,
    host: true,
    open: true,
  },
  build: {
    target: 'esnext',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['recharts', 'chart.js'],
          ai: ['@tensorflow/tfjs', 'ml-matrix', 'simple-statistics'],
          security: ['crypto-js', 'jose'],
          animations: ['framer-motion'],
        },
      },
    },
  },
})
