{"version": 3, "file": "tax-record.entity.js", "sourceRoot": "", "sources": ["../../../src/finance/entities/tax-record.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAMiB;AAEjB,IAAY,OASX;AATD,WAAY,OAAO;IACjB,oCAAyB,CAAA;IACzB,kCAAuB,CAAA;IACvB,sBAAW,CAAA;IACX,sCAA2B,CAAA;IAC3B,wCAA6B,CAAA;IAC7B,oCAAyB,CAAA;IACzB,wCAA6B,CAAA;IAC7B,0BAAe,CAAA;AACjB,CAAC,EATW,OAAO,uBAAP,OAAO,QASlB;AAED,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,sCAAyB,CAAA;IACzB,4BAAe,CAAA;IACf,0BAAa,CAAA;IACb,gCAAmB,CAAA;IACnB,gCAAmB,CAAA;AACrB,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AAGM,IAAM,SAAS,GAAf,MAAM,SAAS;IAEpB,EAAE,CAAS;IAGX,eAAe,CAAS;IAMxB,OAAO,CAAU;IAOjB,MAAM,CAAY;IAGlB,cAAc,CAAO;IAGrB,YAAY,CAAO;IAGnB,OAAO,CAAO;IAGd,aAAa,CAAS;IAGtB,OAAO,CAAS;IAGhB,SAAS,CAAS;IAGlB,aAAa,CAAS;IAGtB,cAAc,CAAS;IAGvB,WAAW,CAAS;IAGpB,UAAU,CAAS;IAGnB,iBAAiB,CAAS;IAG1B,QAAQ,CAAS;IAGjB,YAAY,CAAS;IAGrB,eAAe,CAAS;IAGxB,WAAW,CAAW;IAGtB,SAAS,CAAO;IAGhB,QAAQ,CAAO;IAGf,OAAO,CAAS;IAGhB,KAAK,CAAS;IAGd,YAAY,CAAM;IAGlB,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AAxFY,8BAAS;AAEpB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;qCACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;kDACb;AAMxB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,OAAO;KACd,CAAC;;0CACe;AAOjB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS,CAAC,UAAU;KAC9B,CAAC;;yCACgB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACT,IAAI;iDAAC;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACX,IAAI;+CAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BAChB,IAAI;0CAAC;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;gDAC/B;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;0CACpC;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;4CACnC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDAC3C;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;iDAC1C;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;8CACjC;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CAC9C;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;oDACvC;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;2CACtB;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACnB;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAChB;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CACnB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAC9B,IAAI;4CAAC;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAC/B,IAAI;2CAAC;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACX;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC3B;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACvB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;4CAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;4CAAC;oBAvFL,SAAS;IADrB,IAAA,gBAAM,EAAC,qBAAqB,CAAC;GACjB,SAAS,CAwFrB"}