import { PosShift } from './pos-shift.entity';
export declare enum CashDrawerActivity {
    OPEN = "open",
    CLOSE = "close",
    CASH_IN = "cash_in",
    CASH_OUT = "cash_out",
    PAYOUT = "payout",
    DROP = "drop",
    COUNT = "count",
    RECONCILE = "reconcile"
}
export declare class PosCashDrawer {
    id: string;
    shiftId: string;
    shift: PosShift;
    activity: CashDrawerActivity;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    reason: string;
    notes: string;
    performedBy: string;
    timestamp: Date;
    denominationBreakdown: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
