import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Account } from './entities/account.entity';
import { Transaction } from './entities/transaction.entity';
import { Budget } from './entities/budget.entity';
import { BudgetItem } from './entities/budget-item.entity';
import { Expense } from './entities/expense.entity';
import { ExpenseCategory } from './entities/expense-category.entity';
import { FinancialReport } from './entities/financial-report.entity';
import { TaxRecord } from './entities/tax-record.entity';
import { BankAccount } from './entities/bank-account.entity';
import { BankTransaction } from './entities/bank-transaction.entity';

// Services
import { AccountService } from './services/account.service';
import { TransactionService } from './services/transaction.service';
import { BudgetService } from './services/budget.service';
import { ExpenseService } from './services/expense.service';
import { FinancialReportService } from './services/financial-report.service';
import { TaxService } from './services/tax.service';
import { BankAccountService } from './services/bank-account.service';

// Controllers
import { AccountController } from './controllers/account.controller';
import { TransactionController } from './controllers/transaction.controller';
import { BudgetController } from './controllers/budget.controller';
import { ExpenseController } from './controllers/expense.controller';
import { FinancialReportController } from './controllers/financial-report.controller';
import { TaxController } from './controllers/tax.controller';
import { BankAccountController } from './controllers/bank-account.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Account,
      Transaction,
      Budget,
      BudgetItem,
      Expense,
      ExpenseCategory,
      FinancialReport,
      TaxRecord,
      BankAccount,
      BankTransaction,
    ]),
  ],
  controllers: [
    AccountController,
    TransactionController,
    BudgetController,
    ExpenseController,
    FinancialReportController,
    TaxController,
    BankAccountController,
  ],
  providers: [
    AccountService,
    TransactionService,
    BudgetService,
    ExpenseService,
    FinancialReportService,
    TaxService,
    BankAccountService,
  ],
  exports: [
    AccountService,
    TransactionService,
    BudgetService,
    ExpenseService,
    FinancialReportService,
    TaxService,
    BankAccountService,
  ],
})
export class FinanceModule {}
