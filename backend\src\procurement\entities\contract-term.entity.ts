import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Contract } from './contract.entity';

export enum TermType {
  PAYMENT = 'payment',
  DELIVERY = 'delivery',
  QUALITY = 'quality',
  PENALTY = 'penalty',
  TERMINATION = 'termination',
  CONFIDENTIALITY = 'confidentiality',
  LIABILITY = 'liability',
  FORCE_MAJEURE = 'force_majeure',
  DISPUTE_RESOLUTION = 'dispute_resolution',
  GENERAL = 'general',
}

@Entity('contract_terms')
export class ContractTerm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  contractId: string;

  @ManyToOne(() => Contract, contract => contract.terms, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;

  @Column({
    type: 'enum',
    enum: TermType,
  })
  type: TermType;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'int', default: 0 })
  sortOrder: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
