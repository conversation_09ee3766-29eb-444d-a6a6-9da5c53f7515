{"version": 3, "file": "stock-movement.entity.js", "sourceRoot": "", "sources": ["../../../src/inventory/entities/stock-movement.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQiB;AACjB,qDAA2C;AAC3C,yDAA+C;AAC/C,uDAA6C;AAC7C,iDAAuC;AAEvC,IAAY,YAYX;AAZD,WAAY,YAAY;IACtB,mCAAmB,CAAA;IACnB,+BAAe,CAAA;IACf,qCAAqB,CAAA;IACrB,yCAAyB,CAAA;IACzB,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,qCAAqB,CAAA;IACrB,yCAAyB,CAAA;IACzB,2CAA2B,CAAA;AAC7B,CAAC,EAZW,YAAY,4BAAZ,YAAY,QAYvB;AAED,IAAY,cAaX;AAbD,WAAY,cAAc;IACxB,mDAAiC,CAAA;IACjC,6CAA2B,CAAA;IAC3B,mDAAiC,CAAA;IACjC,uDAAqC,CAAA;IACrC,mDAAiC,CAAA;IACjC,uDAAqC,CAAA;IACrC,uDAAqC,CAAA;IACrC,qDAAmC,CAAA;IACnC,qDAAmC,CAAA;IACnC,mEAAiD,CAAA;IACjD,yDAAuC,CAAA;IACvC,yDAAuC,CAAA;AACzC,CAAC,EAbW,cAAc,8BAAd,cAAc,QAazB;AAGM,IAAM,aAAa,GAAnB,MAAM,aAAa;IAExB,EAAE,CAAS;IAGX,SAAS,CAAS;IAIlB,OAAO,CAAU;IAGjB,WAAW,CAAS;IAIpB,SAAS,CAAY;IAGrB,UAAU,CAAS;IAInB,QAAQ,CAAW;IAGnB,OAAO,CAAS;IAIhB,KAAK,CAAQ;IAMb,IAAI,CAAe;IAMnB,MAAM,CAAiB;IAGvB,QAAQ,CAAS;IAGjB,cAAc,CAAS;IAGvB,aAAa,CAAS;IAGtB,QAAQ,CAAS;IAGjB,SAAS,CAAS;IAGlB,QAAQ,CAAS;IAGjB,YAAY,CAAO;IAGnB,eAAe,CAAS;IAGxB,iBAAiB,CAAS;IAG1B,eAAe,CAAS;IAGxB,WAAW,CAAS;IAGpB,YAAY,CAAS;IAGrB,UAAU,CAAO;IAGjB,KAAK,CAAS;IAGd,WAAW,CAAS;IAGpB,UAAU,CAAS;IAGnB,UAAU,CAAO;IAGjB,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AAvGY,sCAAa;AAExB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;yCACpB;AAGX;IADC,IAAA,gBAAM,GAAE;;gDACS;AAIlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,wBAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC;IAC3D,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;8BACzB,wBAAO;8CAAC;AAGjB;IADC,IAAA,gBAAM,GAAE;;kDACW;AAIpB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,4BAAS,CAAC;IAC1B,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;8BACzB,4BAAS;gDAAC;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACR;AAInB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,0BAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC7C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,0BAAQ;+CAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CACX;AAIhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;8BACzB,oBAAK;4CAAC;AAMb;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,YAAY;KACnB,CAAC;;2CACiB;AAMnB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,cAAc;KACrB,CAAC;;6CACqB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;+CACP;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;qDACD;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;oDACF;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACpD;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACnD;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;+CACtB;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACX,IAAI;mDAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAChB;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDACD;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACH;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDACpB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACnB;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAC7B,IAAI;iDAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC3B;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACR;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAClC,IAAI;iDAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;gDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;gDAAC;wBAtGL,aAAa;IADzB,IAAA,gBAAM,EAAC,2BAA2B,CAAC;GACvB,aAAa,CAuGzB"}