import { PaymentPlanService } from '../services/payment-plan.service';
import { PaymentPlan, PaymentPlanStatus } from '../entities/payment-plan.entity';
export declare class PaymentPlanController {
    private readonly paymentPlanService;
    constructor(paymentPlanService: PaymentPlanService);
    create(createPaymentPlanDto: Partial<PaymentPlan>): Promise<PaymentPlan>;
    findAll(status?: PaymentPlanStatus): Promise<PaymentPlan[]>;
    getStatistics(): Promise<any>;
    getOverdueInstallments(): Promise<import("../entities/payment-plan-installment.entity").PaymentPlanInstallment[]>;
    getUpcomingInstallments(days?: string): Promise<import("../entities/payment-plan-installment.entity").PaymentPlanInstallment[]>;
    findByCustomer(customerId: string): Promise<PaymentPlan[]>;
    findOne(id: string): Promise<PaymentPlan>;
    getPaymentPlanSummary(id: string): Promise<any>;
    createInstallments(id: string, installmentData: {
        numberOfInstallments: number;
    }): Promise<import("../entities/payment-plan-installment.entity").PaymentPlanInstallment[]>;
    recordPayment(installmentId: string, paymentData: {
        amount: number;
        paymentDate: Date;
    }): Promise<import("../entities/payment-plan-installment.entity").PaymentPlanInstallment>;
    update(id: string, updatePaymentPlanDto: Partial<PaymentPlan>): Promise<PaymentPlan>;
    markAsDefaulted(id: string, defaultData: {
        reason: string;
    }): Promise<PaymentPlan>;
    remove(id: string): Promise<void>;
}
