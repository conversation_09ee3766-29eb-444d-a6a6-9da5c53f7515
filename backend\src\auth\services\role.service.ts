import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role, RoleType } from '../entities/role.entity';
import { Permission, PermissionModule } from '../entities/permission.entity';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async create(roleData: Partial<Role>): Promise<Role> {
    const role = this.roleRepository.create(roleData);
    return this.roleRepository.save(role);
  }

  async findAll(): Promise<Role[]> {
    return this.roleRepository.find({
      relations: ['permissions', 'users'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['permissions', 'users'],
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    return role;
  }

  async update(id: string, updateData: Partial<Role>): Promise<Role> {
    await this.roleRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const role = await this.findOne(id);
    
    if (role.isSystemRole) {
      throw new Error('Cannot delete system role');
    }

    if (role.users && role.users.length > 0) {
      throw new Error('Cannot delete role with assigned users');
    }

    await this.roleRepository.remove(role);
  }

  async assignPermissions(roleId: string, permissionIds: string[]): Promise<Role> {
    const role = await this.findOne(roleId);
    const permissions = await this.permissionRepository.findByIds(permissionIds);
    
    role.permissions = permissions;
    await this.roleRepository.save(role);
    
    return this.findOne(roleId);
  }

  async removePermissions(roleId: string, permissionIds: string[]): Promise<Role> {
    const role = await this.findOne(roleId);
    
    role.permissions = role.permissions.filter(
      permission => !permissionIds.includes(permission.id)
    );
    
    await this.roleRepository.save(role);
    return this.findOne(roleId);
  }

  async createDefaultRoles(): Promise<Role[]> {
    const defaultRoles = [
      {
        name: 'Super Administrator',
        description: 'Full system access with all permissions',
        type: RoleType.SUPER_ADMIN,
        isSystemRole: true,
        departmentAccess: [
          'analytics', 'customers', 'collections', 'finance', 'hr',
          'inventory', 'it_support', 'pos', 'procurement', 'projects',
          'sales', 'settings', 'system_guide', 'user_management'
        ],
      },
      {
        name: 'Administrator',
        description: 'Administrative access with most permissions',
        type: RoleType.ADMIN,
        isSystemRole: true,
        departmentAccess: [
          'analytics', 'customers', 'collections', 'finance', 'hr',
          'inventory', 'it_support', 'pos', 'procurement', 'projects',
          'sales', 'settings'
        ],
      },
      {
        name: 'Manager',
        description: 'Management level access with departmental permissions',
        type: RoleType.MANAGER,
        isSystemRole: true,
        departmentAccess: [],
      },
      {
        name: 'Supervisor',
        description: 'Supervisory access with limited administrative permissions',
        type: RoleType.SUPERVISOR,
        isSystemRole: true,
        departmentAccess: [],
      },
      {
        name: 'Employee',
        description: 'Standard employee access',
        type: RoleType.EMPLOYEE,
        isSystemRole: true,
        departmentAccess: [],
      },
      {
        name: 'Viewer',
        description: 'Read-only access to assigned departments',
        type: RoleType.VIEWER,
        isSystemRole: true,
        departmentAccess: [],
      },
      // Department-specific roles
      {
        name: 'Sales Manager',
        description: 'Full access to sales department',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['sales', 'customers', 'analytics'],
      },
      {
        name: 'Sales Representative',
        description: 'Sales operations access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['sales', 'customers'],
      },
      {
        name: 'Finance Manager',
        description: 'Full access to finance department',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['finance', 'collections', 'analytics'],
      },
      {
        name: 'Accountant',
        description: 'Finance operations access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['finance'],
      },
      {
        name: 'Inventory Manager',
        description: 'Full access to inventory management',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['inventory', 'procurement', 'analytics'],
      },
      {
        name: 'Warehouse Staff',
        description: 'Warehouse operations access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['inventory'],
      },
      {
        name: 'HR Manager',
        description: 'Full access to human resources',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['hr', 'analytics'],
      },
      {
        name: 'HR Assistant',
        description: 'HR operations access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['hr'],
      },
      {
        name: 'IT Support Manager',
        description: 'Full access to IT support',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['it_support', 'settings', 'analytics'],
      },
      {
        name: 'IT Support Technician',
        description: 'IT support operations access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['it_support'],
      },
      {
        name: 'Project Manager',
        description: 'Full access to project management',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['projects', 'analytics'],
      },
      {
        name: 'Team Lead',
        description: 'Project team leadership access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['projects'],
      },
      {
        name: 'Cashier',
        description: 'Point of sale operations',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['pos'],
      },
      {
        name: 'Store Manager',
        description: 'Store operations management',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['pos', 'inventory', 'analytics'],
      },
      {
        name: 'Procurement Manager',
        description: 'Full access to procurement',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['procurement', 'inventory', 'analytics'],
      },
      {
        name: 'Procurement Officer',
        description: 'Procurement operations access',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['procurement'],
      },
      {
        name: 'Customer Service Manager',
        description: 'Customer service management',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['customers', 'collections', 'analytics'],
      },
      {
        name: 'Customer Service Representative',
        description: 'Customer service operations',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['customers'],
      },
      {
        name: 'Collections Manager',
        description: 'Collections department management',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['collections', 'customers', 'finance', 'analytics'],
      },
      {
        name: 'Collections Agent',
        description: 'Collections operations',
        type: RoleType.CUSTOM,
        isSystemRole: false,
        departmentAccess: ['collections', 'customers'],
      },
    ];

    const createdRoles = [];
    for (const roleData of defaultRoles) {
      const existing = await this.roleRepository.findOne({
        where: { name: roleData.name },
      });

      if (!existing) {
        const role = await this.create(roleData);
        createdRoles.push(role);
      }
    }

    return createdRoles;
  }

  async assignDefaultPermissions(): Promise<void> {
    // Super Admin gets all permissions
    const superAdminRole = await this.roleRepository.findOne({
      where: { type: RoleType.SUPER_ADMIN },
    });
    
    if (superAdminRole) {
      const allPermissions = await this.permissionRepository.find();
      await this.assignPermissions(superAdminRole.id, allPermissions.map(p => p.id));
    }

    // Admin gets most permissions (excluding some sensitive ones)
    const adminRole = await this.roleRepository.findOne({
      where: { type: RoleType.ADMIN },
    });
    
    if (adminRole) {
      const adminPermissions = await this.permissionRepository
        .createQueryBuilder('permission')
        .where('permission.action != :backup', { backup: 'backup' })
        .andWhere('permission.action != :restore', { restore: 'restore' })
        .getMany();
      
      await this.assignPermissions(adminRole.id, adminPermissions.map(p => p.id));
    }

    // Assign department-specific permissions to custom roles
    await this.assignDepartmentPermissions();
  }

  private async assignDepartmentPermissions(): Promise<void> {
    const departmentRoles = [
      { roleName: 'Sales Manager', modules: [PermissionModule.SALES, PermissionModule.CUSTOMERS] },
      { roleName: 'Finance Manager', modules: [PermissionModule.FINANCE, PermissionModule.COLLECTIONS] },
      { roleName: 'Inventory Manager', modules: [PermissionModule.INVENTORY, PermissionModule.PROCUREMENT] },
      { roleName: 'HR Manager', modules: [PermissionModule.HR] },
      { roleName: 'IT Support Manager', modules: [PermissionModule.IT_SUPPORT] },
      { roleName: 'Project Manager', modules: [PermissionModule.PROJECTS] },
    ];

    for (const { roleName, modules } of departmentRoles) {
      const role = await this.roleRepository.findOne({
        where: { name: roleName },
      });

      if (role) {
        const permissions = await this.permissionRepository
          .createQueryBuilder('permission')
          .where('permission.module IN (:...modules)', { modules })
          .getMany();

        await this.assignPermissions(role.id, permissions.map(p => p.id));
      }
    }
  }

  async getRolesByDepartment(department: string): Promise<Role[]> {
    return this.roleRepository
      .createQueryBuilder('role')
      .where('role.departmentAccess @> :department', { department: [department] })
      .getMany();
  }

  async cloneRole(sourceRoleId: string, newRoleName: string, description?: string): Promise<Role> {
    const sourceRole = await this.findOne(sourceRoleId);
    
    const newRole = await this.create({
      name: newRoleName,
      description: description || `Cloned from ${sourceRole.name}`,
      type: RoleType.CUSTOM,
      departmentAccess: sourceRole.departmentAccess,
      isSystemRole: false,
    });

    // Copy permissions
    if (sourceRole.permissions && sourceRole.permissions.length > 0) {
      await this.assignPermissions(
        newRole.id,
        sourceRole.permissions.map(p => p.id)
      );
    }

    return this.findOne(newRole.id);
  }
}
