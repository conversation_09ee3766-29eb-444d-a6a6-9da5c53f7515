import { Repository } from 'typeorm';
import { Expense } from '../entities/expense.entity';
import { ExpenseCategory } from '../entities/expense-category.entity';
export declare class ExpenseService {
    private expenseRepository;
    private categoryRepository;
    constructor(expenseRepository: Repository<Expense>, categoryRepository: Repository<ExpenseCategory>);
    create(createExpenseDto: any): Promise<Expense>;
    findAll(filters?: any): Promise<Expense[]>;
    findOne(id: string): Promise<Expense>;
    update(id: string, updateExpenseDto: any): Promise<Expense>;
    remove(id: string): Promise<void>;
    submitExpense(id: string, submittedBy: string): Promise<Expense>;
    approveExpense(id: string, approvedBy: string, notes?: string): Promise<Expense>;
    rejectExpense(id: string, rejectedBy: string, notes: string): Promise<Expense>;
    markAsPaid(id: string): Promise<Expense>;
    getExpenseReport(filters?: any): Promise<any>;
    createCategory(createCategoryDto: any): Promise<ExpenseCategory>;
    findAllCategories(): Promise<ExpenseCategory[]>;
    findCategory(id: string): Promise<ExpenseCategory>;
    private generateExpenseNumber;
    private groupByField;
    private groupByCategory;
}
