import { Permission } from './permission.entity';
import { User } from '../../user/entities/user.entity';
export declare enum RoleType {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    MANAGER = "manager",
    SUPERVISOR = "supervisor",
    EMPLOYEE = "employee",
    VIEWER = "viewer",
    CUSTOM = "custom"
}
export declare class Role {
    id: string;
    name: string;
    description: string;
    type: RoleType;
    isActive: boolean;
    isSystemRole: boolean;
    departmentAccess: string[];
    restrictions: any;
    permissions: Permission[];
    users: User[];
    createdAt: Date;
    updatedAt: Date;
}
