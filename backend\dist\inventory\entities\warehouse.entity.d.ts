import { Location } from './location.entity';
import { Stock } from './stock.entity';
export declare enum WarehouseType {
    MAIN = "main",
    DISTRIBUTION = "distribution",
    RETAIL = "retail",
    TRANSIT = "transit",
    QUARANTINE = "quarantine",
    RETURNS = "returns"
}
export declare class Warehouse {
    id: string;
    name: string;
    code: string;
    description: string;
    type: WarehouseType;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone: string;
    email: string;
    managerId: string;
    totalArea: number;
    usableArea: number;
    maxCapacity: number;
    isActive: boolean;
    isDefault: boolean;
    operatingHours: any;
    contactInfo: any;
    locations: Location[];
    stocks: Stock[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
