import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Expense, ExpenseStatus, ExpenseType } from '../entities/expense.entity';
import { ExpenseCategory } from '../entities/expense-category.entity';

@Injectable()
export class ExpenseService {
  constructor(
    @InjectRepository(Expense)
    private expenseRepository: Repository<Expense>,
    @InjectRepository(ExpenseCategory)
    private categoryRepository: Repository<ExpenseCategory>,
  ) {}

  async create(createExpenseDto: any): Promise<Expense> {
    const expenseNumber = await this.generateExpenseNumber();
    
    const expense = this.expenseRepository.create({
      ...createExpenseDto,
      expenseNumber,
      baseCurrencyAmount: createExpenseDto.amount * (createExpenseDto.exchangeRate || 1),
      totalAmount: createExpenseDto.amount + (createExpenseDto.taxAmount || 0),
    });

    return this.expenseRepository.save(expense);
  }

  async findAll(filters?: any): Promise<Expense[]> {
    const queryBuilder = this.expenseRepository.createQueryBuilder('expense')
      .leftJoinAndSelect('expense.category', 'category')
      .leftJoinAndSelect('expense.account', 'account');

    if (filters?.status) {
      queryBuilder.andWhere('expense.status = :status', { status: filters.status });
    }

    if (filters?.type) {
      queryBuilder.andWhere('expense.type = :type', { type: filters.type });
    }

    if (filters?.employeeId) {
      queryBuilder.andWhere('expense.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters?.departmentId) {
      queryBuilder.andWhere('expense.departmentId = :departmentId', { departmentId: filters.departmentId });
    }

    if (filters?.startDate && filters?.endDate) {
      queryBuilder.andWhere('expense.expenseDate BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    return queryBuilder
      .orderBy('expense.expenseDate', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Expense> {
    const expense = await this.expenseRepository.findOne({
      where: { id },
      relations: ['category', 'account'],
    });

    if (!expense) {
      throw new NotFoundException(`Expense with ID ${id} not found`);
    }

    return expense;
  }

  async update(id: string, updateExpenseDto: any): Promise<Expense> {
    const expense = await this.findOne(id);

    if (expense.status === ExpenseStatus.PAID) {
      throw new BadRequestException('Cannot update paid expense');
    }

    Object.assign(expense, updateExpenseDto);
    
    if (updateExpenseDto.amount || updateExpenseDto.taxAmount) {
      expense.totalAmount = expense.amount + (expense.taxAmount || 0);
    }

    if (updateExpenseDto.amount || updateExpenseDto.exchangeRate) {
      expense.baseCurrencyAmount = expense.amount * (expense.exchangeRate || 1);
    }

    return this.expenseRepository.save(expense);
  }

  async remove(id: string): Promise<void> {
    const expense = await this.findOne(id);

    if (expense.status === ExpenseStatus.PAID) {
      throw new BadRequestException('Cannot delete paid expense');
    }

    await this.expenseRepository.remove(expense);
  }

  async submitExpense(id: string, submittedBy: string): Promise<Expense> {
    const expense = await this.findOne(id);

    if (expense.status !== ExpenseStatus.DRAFT) {
      throw new BadRequestException('Only draft expenses can be submitted');
    }

    expense.status = ExpenseStatus.SUBMITTED;
    expense.submittedBy = submittedBy;
    expense.submittedAt = new Date();

    return this.expenseRepository.save(expense);
  }

  async approveExpense(id: string, approvedBy: string, notes?: string): Promise<Expense> {
    const expense = await this.findOne(id);

    if (expense.status !== ExpenseStatus.SUBMITTED) {
      throw new BadRequestException('Only submitted expenses can be approved');
    }

    expense.status = ExpenseStatus.APPROVED;
    expense.approvedBy = approvedBy;
    expense.approvedAt = new Date();
    expense.approvalNotes = notes;

    return this.expenseRepository.save(expense);
  }

  async rejectExpense(id: string, rejectedBy: string, notes: string): Promise<Expense> {
    const expense = await this.findOne(id);

    if (expense.status !== ExpenseStatus.SUBMITTED) {
      throw new BadRequestException('Only submitted expenses can be rejected');
    }

    expense.status = ExpenseStatus.REJECTED;
    expense.approvedBy = rejectedBy;
    expense.approvedAt = new Date();
    expense.approvalNotes = notes;

    return this.expenseRepository.save(expense);
  }

  async markAsPaid(id: string): Promise<Expense> {
    const expense = await this.findOne(id);

    if (expense.status !== ExpenseStatus.APPROVED) {
      throw new BadRequestException('Only approved expenses can be marked as paid');
    }

    expense.status = ExpenseStatus.PAID;
    return this.expenseRepository.save(expense);
  }

  async getExpenseReport(filters?: any): Promise<any> {
    const expenses = await this.findAll(filters);

    const report = {
      totalExpenses: expenses.length,
      totalAmount: expenses.reduce((sum, exp) => sum + exp.totalAmount, 0),
      byStatus: this.groupByField(expenses, 'status'),
      byType: this.groupByField(expenses, 'type'),
      byCategory: this.groupByCategory(expenses),
      byEmployee: this.groupByField(expenses, 'employeeId'),
      byDepartment: this.groupByField(expenses, 'departmentId'),
      expenses: expenses.map(exp => ({
        id: exp.id,
        expenseNumber: exp.expenseNumber,
        title: exp.title,
        type: exp.type,
        status: exp.status,
        amount: exp.amount,
        totalAmount: exp.totalAmount,
        expenseDate: exp.expenseDate,
        category: exp.category?.name,
        vendor: exp.vendor,
      })),
    };

    return report;
  }

  // Category management
  async createCategory(createCategoryDto: any): Promise<ExpenseCategory> {
    const category = this.categoryRepository.create(createCategoryDto);
    return this.categoryRepository.save(category);
  }

  async findAllCategories(): Promise<ExpenseCategory[]> {
    return this.categoryRepository.find({
      where: { isActive: true },
      relations: ['parentCategory', 'childCategories', 'defaultAccount'],
      order: { name: 'ASC' },
    });
  }

  async findCategory(id: string): Promise<ExpenseCategory> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parentCategory', 'childCategories', 'defaultAccount', 'expenses'],
    });

    if (!category) {
      throw new NotFoundException(`Expense category with ID ${id} not found`);
    }

    return category;
  }

  private async generateExpenseNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    
    const prefix = `EXP-${year}${month}-`;
    
    const lastExpense = await this.expenseRepository.findOne({
      where: { expenseNumber: Like(`${prefix}%`) },
      order: { expenseNumber: 'DESC' },
    });

    let nextNumber = 1;
    if (lastExpense) {
      const lastNumber = parseInt(lastExpense.expenseNumber.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
  }

  private groupByField(expenses: Expense[], field: string): any {
    return expenses.reduce((acc, expense) => {
      const key = expense[field] || 'Unknown';
      if (!acc[key]) {
        acc[key] = { count: 0, amount: 0 };
      }
      acc[key].count++;
      acc[key].amount += expense.totalAmount;
      return acc;
    }, {});
  }

  private groupByCategory(expenses: Expense[]): any {
    return expenses.reduce((acc, expense) => {
      const key = expense.category?.name || 'Uncategorized';
      if (!acc[key]) {
        acc[key] = { count: 0, amount: 0 };
      }
      acc[key].count++;
      acc[key].amount += expense.totalAmount;
      return acc;
    }, {});
  }
}
