"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryReportController = void 0;
const common_1 = require("@nestjs/common");
const inventory_report_service_1 = require("../services/inventory-report.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let InventoryReportController = class InventoryReportController {
    inventoryReportService;
    constructor(inventoryReportService) {
        this.inventoryReportService = inventoryReportService;
    }
    async generateInventoryValuationReport() {
        return this.inventoryReportService.generateInventoryValuationReport();
    }
    async generateStockLevelReport() {
        return this.inventoryReportService.generateStockLevelReport();
    }
    async generateMovementReport(startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();
        return this.inventoryReportService.generateMovementReport(start, end);
    }
    async generatePurchaseOrderReport(startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();
        return this.inventoryReportService.generatePurchaseOrderReport(start, end);
    }
    async generateABCAnalysisReport() {
        return this.inventoryReportService.generateABCAnalysisReport();
    }
};
exports.InventoryReportController = InventoryReportController;
__decorate([
    (0, common_1.Get)('valuation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], InventoryReportController.prototype, "generateInventoryValuationReport", null);
__decorate([
    (0, common_1.Get)('stock-levels'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], InventoryReportController.prototype, "generateStockLevelReport", null);
__decorate([
    (0, common_1.Get)('movements'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], InventoryReportController.prototype, "generateMovementReport", null);
__decorate([
    (0, common_1.Get)('purchase-orders'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], InventoryReportController.prototype, "generatePurchaseOrderReport", null);
__decorate([
    (0, common_1.Get)('abc-analysis'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], InventoryReportController.prototype, "generateABCAnalysisReport", null);
exports.InventoryReportController = InventoryReportController = __decorate([
    (0, common_1.Controller)('inventory-reports'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [inventory_report_service_1.InventoryReportService])
], InventoryReportController);
//# sourceMappingURL=inventory-report.controller.js.map