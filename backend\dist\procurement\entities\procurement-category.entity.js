"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcurementCategory = void 0;
const typeorm_1 = require("typeorm");
let ProcurementCategory = class ProcurementCategory {
    id;
    name;
    code;
    description;
    parentCategoryId;
    parentCategory;
    childCategories;
    isActive;
    sortOrder;
    budgetLimit;
    approvalWorkflow;
    metadata;
    createdAt;
    updatedAt;
};
exports.ProcurementCategory = ProcurementCategory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProcurementCategory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ProcurementCategory.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], ProcurementCategory.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementCategory.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProcurementCategory.prototype, "parentCategoryId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ProcurementCategory, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'parentCategoryId' }),
    __metadata("design:type", ProcurementCategory)
], ProcurementCategory.prototype, "parentCategory", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProcurementCategory, category => category.parentCategory),
    __metadata("design:type", Array)
], ProcurementCategory.prototype, "childCategories", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], ProcurementCategory.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], ProcurementCategory.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], ProcurementCategory.prototype, "budgetLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ProcurementCategory.prototype, "approvalWorkflow", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ProcurementCategory.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProcurementCategory.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ProcurementCategory.prototype, "updatedAt", void 0);
exports.ProcurementCategory = ProcurementCategory = __decorate([
    (0, typeorm_1.Entity)('procurement_categories')
], ProcurementCategory);
//# sourceMappingURL=procurement-category.entity.js.map