import { GuideSection } from './guide-section.entity';
export declare enum GuideType {
    USER_MANUAL = "user_manual",
    ADMIN_GUIDE = "admin_guide",
    QUICK_START = "quick_start",
    FEATURE_GUIDE = "feature_guide",
    TROUBLESHOOTING = "troubleshooting",
    API_DOCUMENTATION = "api_documentation",
    TUTORIAL = "tutorial"
}
export declare enum GuideStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived",
    UNDER_REVIEW = "under_review"
}
export declare class Guide {
    id: string;
    title: string;
    description: string;
    type: GuideType;
    status: GuideStatus;
    version: string;
    coverImage: string;
    tags: string[];
    targetAudience: string[];
    sortOrder: number;
    isFeatured: boolean;
    isPublic: boolean;
    authorId: string;
    reviewedBy: string;
    reviewedAt: Date;
    publishedAt: Date;
    viewCount: number;
    rating: number;
    ratingCount: number;
    sections: GuideSection[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
