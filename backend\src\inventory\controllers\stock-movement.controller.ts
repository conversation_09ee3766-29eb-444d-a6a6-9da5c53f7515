import {
  Controller,
  Get,
  Query,
  Param,
  UseGuards,
} from '@nestjs/common';
import { StockMovementService } from '../services/stock-movement.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('stock-movements')
@UseGuards(JwtAuthGuard)
export class StockMovementController {
  constructor(private readonly stockMovementService: StockMovementService) {}

  @Get()
  async findAll() {
    return this.stockMovementService.findAll();
  }

  @Get('statistics')
  async getStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    return this.stockMovementService.getMovementStatistics(start, end);
  }

  @Get('recent')
  async getRecentMovements(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 50;
    return this.stockMovementService.getRecentMovements(limitNum);
  }

  @Get('type/:movementType')
  async getMovementsByType(@Param('movementType') movementType: string) {
    return this.stockMovementService.getMovementsByType(movementType);
  }

  @Get('product/:productId')
  async findByProduct(@Param('productId') productId: string) {
    return this.stockMovementService.findByProduct(productId);
  }

  @Get('warehouse/:warehouseId')
  async findByWarehouse(@Param('warehouseId') warehouseId: string) {
    return this.stockMovementService.findByWarehouse(warehouseId);
  }

  @Get('date-range')
  async findByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.stockMovementService.findByDateRange(
      new Date(startDate),
      new Date(endDate),
    );
  }
}
