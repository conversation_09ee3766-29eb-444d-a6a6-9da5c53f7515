import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { KnowledgeBaseService } from '../services/knowledge-base.service';
import { KnowledgeBaseArticle } from '../entities/knowledge-base-article.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('knowledge-base')
@UseGuards(JwtAuthGuard)
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createArticleDto: Partial<KnowledgeBaseArticle>) {
    return this.knowledgeBaseService.create(createArticleDto);
  }

  @Get()
  async findAll() {
    return this.knowledgeBaseService.findAll();
  }

  @Get('published')
  async getPublishedArticles() {
    return this.knowledgeBaseService.getPublishedArticles();
  }

  @Get('drafts')
  async getDraftArticles() {
    return this.knowledgeBaseService.getDraftArticles();
  }

  @Get('popular')
  async getPopularArticles(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 10;
    return this.knowledgeBaseService.getPopularArticles(limitNum);
  }

  @Get('recent')
  async getRecentArticles(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 10;
    return this.knowledgeBaseService.getRecentArticles(limitNum);
  }

  @Get('statistics')
  async getStatistics() {
    return this.knowledgeBaseService.getArticleStatistics();
  }

  @Get('categories')
  async getCategories() {
    return this.knowledgeBaseService.getCategories();
  }

  @Get('tags')
  async getAllTags() {
    return this.knowledgeBaseService.getAllTags();
  }

  @Get('search')
  async searchArticles(@Query('q') searchTerm: string) {
    return this.knowledgeBaseService.searchArticles(searchTerm);
  }

  @Get('category/:category')
  async findByCategory(@Param('category') category: string) {
    return this.knowledgeBaseService.findByCategory(category);
  }

  @Get('author/:authorId')
  async getArticlesByAuthor(@Param('authorId') authorId: string) {
    return this.knowledgeBaseService.getArticlesByAuthor(authorId);
  }

  @Get('tags/:tags')
  async findByTags(@Param('tags') tagsParam: string) {
    const tags = tagsParam.split(',');
    return this.knowledgeBaseService.findByTags(tags);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.knowledgeBaseService.findOne(id);
  }

  @Get(':id/related')
  async getRelatedArticles(
    @Param('id') id: string,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit) : 5;
    return this.knowledgeBaseService.getRelatedArticles(id, limitNum);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateArticleDto: Partial<KnowledgeBaseArticle>,
  ) {
    return this.knowledgeBaseService.update(id, updateArticleDto);
  }

  @Post(':id/publish')
  async publishArticle(@Param('id') id: string) {
    return this.knowledgeBaseService.publishArticle(id);
  }

  @Post(':id/unpublish')
  async unpublishArticle(@Param('id') id: string) {
    return this.knowledgeBaseService.unpublishArticle(id);
  }

  @Post(':id/rate')
  async rateArticle(
    @Param('id') id: string,
    @Body() ratingData: { rating: number },
  ) {
    return this.knowledgeBaseService.rateArticle(id, ratingData.rating);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.knowledgeBaseService.remove(id);
  }
}
