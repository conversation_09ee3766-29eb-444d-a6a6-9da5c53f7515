"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcurementApproval = exports.ApprovalLevel = exports.ApprovalStatus = void 0;
const typeorm_1 = require("typeorm");
const procurement_request_entity_1 = require("./procurement-request.entity");
var ApprovalStatus;
(function (ApprovalStatus) {
    ApprovalStatus["PENDING"] = "pending";
    ApprovalStatus["APPROVED"] = "approved";
    ApprovalStatus["REJECTED"] = "rejected";
    ApprovalStatus["DELEGATED"] = "delegated";
})(ApprovalStatus || (exports.ApprovalStatus = ApprovalStatus = {}));
var ApprovalLevel;
(function (ApprovalLevel) {
    ApprovalLevel["SUPERVISOR"] = "supervisor";
    ApprovalLevel["MANAGER"] = "manager";
    ApprovalLevel["DIRECTOR"] = "director";
    ApprovalLevel["VP"] = "vp";
    ApprovalLevel["CEO"] = "ceo";
    ApprovalLevel["BOARD"] = "board";
})(ApprovalLevel || (exports.ApprovalLevel = ApprovalLevel = {}));
let ProcurementApproval = class ProcurementApproval {
    id;
    requestId;
    request;
    level;
    status;
    approverId;
    sequence;
    approvedAt;
    comments;
    delegatedTo;
    delegatedAt;
    metadata;
    createdAt;
    updatedAt;
};
exports.ProcurementApproval = ProcurementApproval;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "requestId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => procurement_request_entity_1.ProcurementRequest, request => request.approvals),
    (0, typeorm_1.JoinColumn)({ name: 'requestId' }),
    __metadata("design:type", procurement_request_entity_1.ProcurementRequest)
], ProcurementApproval.prototype, "request", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ApprovalLevel,
    }),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ApprovalStatus,
        default: ApprovalStatus.PENDING,
    }),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "approverId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], ProcurementApproval.prototype, "sequence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProcurementApproval.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "comments", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProcurementApproval.prototype, "delegatedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProcurementApproval.prototype, "delegatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ProcurementApproval.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProcurementApproval.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ProcurementApproval.prototype, "updatedAt", void 0);
exports.ProcurementApproval = ProcurementApproval = __decorate([
    (0, typeorm_1.Entity)('procurement_approvals')
], ProcurementApproval);
//# sourceMappingURL=procurement-approval.entity.js.map