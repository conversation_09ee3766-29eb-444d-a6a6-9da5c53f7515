import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { StockService } from '../services/stock.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('stock')
@UseGuards(JwtAuthGuard)
export class StockController {
  constructor(private readonly stockService: StockService) {}

  @Get()
  async findAll() {
    return this.stockService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.stockService.getStockStatistics();
  }

  @Get('low-stock')
  async getLowStockItems(@Query('threshold') threshold?: string) {
    const thresholdNum = threshold ? parseInt(threshold) : 10;
    return this.stockService.getLowStockItems(thresholdNum);
  }

  @Get('out-of-stock')
  async getOutOfStockItems() {
    return this.stockService.getOutOfStockItems();
  }

  @Get('overstock')
  async getOverstockItems(@Query('threshold') threshold?: string) {
    const thresholdNum = threshold ? parseInt(threshold) : 1000;
    return this.stockService.getOverstockItems(thresholdNum);
  }

  @Get('movements')
  async getStockMovements(
    @Query('productId') productId?: string,
    @Query('warehouseId') warehouseId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.stockService.getStockMovements(productId, warehouseId, start, end);
  }

  @Get('adjustments')
  async getStockAdjustments(
    @Query('productId') productId?: string,
    @Query('warehouseId') warehouseId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.stockService.getStockAdjustments(productId, warehouseId, start, end);
  }

  @Get('product/:productId')
  async findByProduct(@Param('productId') productId: string) {
    return this.stockService.findByProduct(productId);
  }

  @Get('warehouse/:warehouseId')
  async findByWarehouse(@Param('warehouseId') warehouseId: string) {
    return this.stockService.findByWarehouse(warehouseId);
  }

  @Get('product/:productId/warehouse/:warehouseId')
  async findByProductAndWarehouse(
    @Param('productId') productId: string,
    @Param('warehouseId') warehouseId: string,
  ) {
    return this.stockService.findByProductAndWarehouse(productId, warehouseId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.stockService.findOne(id);
  }

  @Post('adjust')
  @HttpCode(HttpStatus.CREATED)
  async adjustStock(
    @Body() adjustmentData: {
      productId: string;
      warehouseId: string;
      adjustment: number;
      reason: string;
      adjustedBy?: string;
    },
  ) {
    return this.stockService.adjustStock(
      adjustmentData.productId,
      adjustmentData.warehouseId,
      adjustmentData.adjustment,
      adjustmentData.reason,
      adjustmentData.adjustedBy,
    );
  }

  @Post('reserve')
  @HttpCode(HttpStatus.CREATED)
  async reserveStock(
    @Body() reservationData: {
      productId: string;
      warehouseId: string;
      quantity: number;
    },
  ) {
    const success = await this.stockService.reserveStock(
      reservationData.productId,
      reservationData.warehouseId,
      reservationData.quantity,
    );
    return { success, message: success ? 'Stock reserved' : 'Insufficient stock' };
  }

  @Post('release-reservation')
  @HttpCode(HttpStatus.CREATED)
  async releaseReservation(
    @Body() releaseData: {
      productId: string;
      warehouseId: string;
      quantity: number;
    },
  ) {
    await this.stockService.releaseReservation(
      releaseData.productId,
      releaseData.warehouseId,
      releaseData.quantity,
    );
    return { message: 'Reservation released successfully' };
  }

  @Post('fulfill-reservation')
  @HttpCode(HttpStatus.CREATED)
  async fulfillReservation(
    @Body() fulfillmentData: {
      productId: string;
      warehouseId: string;
      quantity: number;
    },
  ) {
    const success = await this.stockService.fulfillReservation(
      fulfillmentData.productId,
      fulfillmentData.warehouseId,
      fulfillmentData.quantity,
    );
    return { success, message: success ? 'Reservation fulfilled' : 'Insufficient reserved stock' };
  }

  @Post('bulk-update')
  @HttpCode(HttpStatus.CREATED)
  async bulkStockUpdate(
    @Body() updates: Array<{
      productId: string;
      warehouseId: string;
      quantity: number;
      reason: string;
    }>,
  ) {
    await this.stockService.bulkStockUpdate(updates);
    return { message: 'Bulk stock update completed successfully' };
  }
}
