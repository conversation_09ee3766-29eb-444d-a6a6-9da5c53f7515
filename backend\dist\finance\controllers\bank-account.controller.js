"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankAccountController = void 0;
const common_1 = require("@nestjs/common");
const bank_account_service_1 = require("../services/bank-account.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let BankAccountController = class BankAccountController {
    bankAccountService;
    constructor(bankAccountService) {
        this.bankAccountService = bankAccountService;
    }
    create(createBankAccountDto) {
        return this.bankAccountService.create(createBankAccountDto);
    }
    findAll() {
        return this.bankAccountService.findAll();
    }
    findOne(id) {
        return this.bankAccountService.findOne(id);
    }
    update(id, updateBankAccountDto) {
        return this.bankAccountService.update(id, updateBankAccountDto);
    }
    remove(id) {
        return this.bankAccountService.remove(id);
    }
    addTransaction(id, transactionData) {
        return this.bankAccountService.addTransaction(id, transactionData);
    }
    importTransactions(id, importData) {
        return this.bankAccountService.importTransactions(id, importData.transactions);
    }
    reconcileAccount(id, reconcileData) {
        return this.bankAccountService.reconcileAccount(id, reconcileData.reconciledBalance, reconcileData.reconciledBy);
    }
    getAccountStatement(id, startDate, endDate) {
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        return this.bankAccountService.getAccountStatement(id, start, end);
    }
    getUnreconciledTransactions(id) {
        return this.bankAccountService.getUnreconciledTransactions(id);
    }
    setDefaultAccount(id) {
        return this.bankAccountService.setDefaultAccount(id);
    }
};
exports.BankAccountController = BankAccountController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/transactions'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "addTransaction", null);
__decorate([
    (0, common_1.Post)(':id/import-transactions'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "importTransactions", null);
__decorate([
    (0, common_1.Post)(':id/reconcile'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "reconcileAccount", null);
__decorate([
    (0, common_1.Get)(':id/statement'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "getAccountStatement", null);
__decorate([
    (0, common_1.Get)(':id/unreconciled-transactions'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "getUnreconciledTransactions", null);
__decorate([
    (0, common_1.Post)(':id/set-default'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BankAccountController.prototype, "setDefaultAccount", null);
exports.BankAccountController = BankAccountController = __decorate([
    (0, common_1.Controller)('finance/bank-accounts'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [bank_account_service_1.BankAccountService])
], BankAccountController);
//# sourceMappingURL=bank-account.controller.js.map