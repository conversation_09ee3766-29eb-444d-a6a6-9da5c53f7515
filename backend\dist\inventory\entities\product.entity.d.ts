import { Category } from './category.entity';
import { Supplier } from './supplier.entity';
import { Stock } from './stock.entity';
import { StockMovement } from './stock-movement.entity';
import { PurchaseOrderItem } from './purchase-order-item.entity';
export declare enum ProductType {
    PHYSICAL = "physical",
    DIGITAL = "digital",
    SERVICE = "service",
    BUNDLE = "bundle"
}
export declare enum ProductStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DISCONTINUED = "discontinued",
    OUT_OF_STOCK = "out_of_stock"
}
export declare enum UnitOfMeasure {
    PIECE = "piece",
    KILOGRAM = "kilogram",
    GRAM = "gram",
    LITER = "liter",
    MILLILITER = "milliliter",
    METER = "meter",
    CENTIMETER = "centimeter",
    SQUARE_METER = "square_meter",
    CUBIC_METER = "cubic_meter",
    BOX = "box",
    PACK = "pack",
    DOZEN = "dozen",
    PAIR = "pair",
    SET = "set"
}
export declare class Product {
    id: string;
    sku: string;
    name: string;
    description: string;
    shortDescription: string;
    type: ProductType;
    status: ProductStatus;
    categoryId: string;
    category: Category;
    supplierId: string;
    supplier: Supplier;
    barcode: string;
    qrCode: string;
    unitOfMeasure: UnitOfMeasure;
    weight: number;
    length: number;
    width: number;
    height: number;
    costPrice: number;
    sellingPrice: number;
    msrp: number;
    currency: string;
    minStockLevel: number;
    maxStockLevel: number;
    reorderPoint: number;
    reorderQuantity: number;
    leadTimeDays: number;
    trackInventory: boolean;
    allowBackorder: boolean;
    isPerishable: boolean;
    shelfLifeDays: number;
    images: string[];
    attributes: any;
    variants: any[];
    notes: string;
    stocks: Stock[];
    stockMovements: StockMovement[];
    purchaseOrderItems: PurchaseOrderItem[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
