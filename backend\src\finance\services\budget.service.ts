import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Budget, BudgetType, BudgetStatus } from '../entities/budget.entity';
import { BudgetItem } from '../entities/budget-item.entity';

@Injectable()
export class BudgetService {
  constructor(
    @InjectRepository(Budget)
    private budgetRepository: Repository<Budget>,
    @InjectRepository(BudgetItem)
    private budgetItemRepository: Repository<BudgetItem>,
  ) {}

  async create(createBudgetDto: any): Promise<Budget> {
    const budget = this.budgetRepository.create(createBudgetDto);
    return this.budgetRepository.save(budget);
  }

  async findAll(filters?: any): Promise<Budget[]> {
    const queryBuilder = this.budgetRepository.createQueryBuilder('budget')
      .leftJoinAndSelect('budget.budgetItems', 'budgetItems')
      .leftJoinAndSelect('budgetItems.account', 'account');

    if (filters?.type) {
      queryBuilder.andWhere('budget.type = :type', { type: filters.type });
    }

    if (filters?.status) {
      queryBuilder.andWhere('budget.status = :status', { status: filters.status });
    }

    if (filters?.departmentId) {
      queryBuilder.andWhere('budget.departmentId = :departmentId', { departmentId: filters.departmentId });
    }

    return queryBuilder
      .orderBy('budget.startDate', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Budget> {
    const budget = await this.budgetRepository.findOne({
      where: { id },
      relations: ['budgetItems', 'budgetItems.account'],
    });

    if (!budget) {
      throw new NotFoundException(`Budget with ID ${id} not found`);
    }

    return budget;
  }

  async update(id: string, updateBudgetDto: any): Promise<Budget> {
    const budget = await this.findOne(id);
    Object.assign(budget, updateBudgetDto);
    return this.budgetRepository.save(budget);
  }

  async remove(id: string): Promise<void> {
    const budget = await this.findOne(id);
    
    if (budget.status === BudgetStatus.ACTIVE) {
      throw new BadRequestException('Cannot delete active budget');
    }

    await this.budgetRepository.remove(budget);
  }

  async addBudgetItem(budgetId: string, createBudgetItemDto: any): Promise<BudgetItem> {
    const budget = await this.findOne(budgetId);
    
    const budgetItem = this.budgetItemRepository.create({
      ...createBudgetItemDto,
      budgetId,
    });

    const savedItem = await this.budgetItemRepository.save(budgetItem);
    
    // Update budget totals
    await this.updateBudgetTotals(budgetId);
    
    return savedItem;
  }

  async updateBudgetItem(itemId: string, updateBudgetItemDto: any): Promise<BudgetItem> {
    const budgetItem = await this.budgetItemRepository.findOne({
      where: { id: itemId },
      relations: ['budget'],
    });

    if (!budgetItem) {
      throw new NotFoundException(`Budget item with ID ${itemId} not found`);
    }

    Object.assign(budgetItem, updateBudgetItemDto);
    const savedItem = await this.budgetItemRepository.save(budgetItem);
    
    // Update budget totals
    await this.updateBudgetTotals(budgetItem.budgetId);
    
    return savedItem;
  }

  async removeBudgetItem(itemId: string): Promise<void> {
    const budgetItem = await this.budgetItemRepository.findOne({
      where: { id: itemId },
    });

    if (!budgetItem) {
      throw new NotFoundException(`Budget item with ID ${itemId} not found`);
    }

    const budgetId = budgetItem.budgetId;
    await this.budgetItemRepository.remove(budgetItem);
    
    // Update budget totals
    await this.updateBudgetTotals(budgetId);
  }

  async approveBudget(id: string, approvedBy: string): Promise<Budget> {
    const budget = await this.findOne(id);
    
    budget.status = BudgetStatus.APPROVED;
    budget.approvedBy = approvedBy;
    budget.approvedAt = new Date();
    
    return this.budgetRepository.save(budget);
  }

  async activateBudget(id: string): Promise<Budget> {
    const budget = await this.findOne(id);
    
    if (budget.status !== BudgetStatus.APPROVED) {
      throw new BadRequestException('Budget must be approved before activation');
    }
    
    budget.status = BudgetStatus.ACTIVE;
    return this.budgetRepository.save(budget);
  }

  async getBudgetVarianceReport(id: string): Promise<any> {
    const budget = await this.findOne(id);
    
    const report = {
      budget: {
        id: budget.id,
        name: budget.name,
        type: budget.type,
        period: {
          startDate: budget.startDate,
          endDate: budget.endDate,
        },
      },
      summary: {
        totalBudgeted: budget.totalBudgetAmount,
        totalActual: budget.totalActualAmount,
        totalVariance: budget.totalVariance,
        variancePercentage: budget.variancePercentage,
      },
      items: budget.budgetItems.map(item => ({
        id: item.id,
        itemName: item.itemName,
        accountName: item.account?.name,
        budgeted: item.budgetedAmount,
        actual: item.actualAmount,
        variance: item.variance,
        variancePercentage: item.variancePercentage,
        available: item.availableAmount,
      })),
    };

    return report;
  }

  private async updateBudgetTotals(budgetId: string): Promise<void> {
    const budget = await this.findOne(budgetId);
    
    const totals = budget.budgetItems.reduce(
      (acc, item) => ({
        budgeted: acc.budgeted + item.budgetedAmount,
        actual: acc.actual + item.actualAmount,
      }),
      { budgeted: 0, actual: 0 }
    );

    budget.totalBudgetAmount = totals.budgeted;
    budget.totalActualAmount = totals.actual;
    budget.totalVariance = totals.actual - totals.budgeted;
    budget.variancePercentage = totals.budgeted > 0 
      ? (budget.totalVariance / totals.budgeted) * 100 
      : 0;

    await this.budgetRepository.save(budget);
  }
}
