{"version": 3, "file": "role.service.js", "sourceRoot": "", "sources": ["../../../src/auth/services/role.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,yDAAyD;AACzD,qEAA6E;AAGtE,IAAM,WAAW,GAAjB,MAAM,WAAW;IAGZ;IAEA;IAJV,YAEU,cAAgC,EAEhC,oBAA4C;QAF5C,mBAAc,GAAd,cAAc,CAAkB;QAEhC,yBAAoB,GAApB,oBAAoB,CAAwB;IACnD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,QAAuB;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YAC9B,SAAS,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;YACnC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;SACpC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAyB;QAChD,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAuB;QAC7D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAE7E,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAuB;QAC7D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAExC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CACxC,UAAU,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CACrD,CAAC;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,YAAY,GAAG;YACnB;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,yCAAyC;gBACtD,IAAI,EAAE,sBAAQ,CAAC,WAAW;gBAC1B,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE;oBAChB,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI;oBACxD,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU;oBAC3D,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,iBAAiB;iBACvD;aACF;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,6CAA6C;gBAC1D,IAAI,EAAE,sBAAQ,CAAC,KAAK;gBACpB,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE;oBAChB,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI;oBACxD,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU;oBAC3D,OAAO,EAAE,UAAU;iBACpB;aACF;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,uDAAuD;gBACpE,IAAI,EAAE,sBAAQ,CAAC,OAAO;gBACtB,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,EAAE;aACrB;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,4DAA4D;gBACzE,IAAI,EAAE,sBAAQ,CAAC,UAAU;gBACzB,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,EAAE;aACrB;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,0BAA0B;gBACvC,IAAI,EAAE,sBAAQ,CAAC,QAAQ;gBACvB,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,EAAE;aACrB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,0CAA0C;gBACvD,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,EAAE;aACrB;YAED;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,iCAAiC;gBAC9C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC;aACtD;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,yBAAyB;gBACtC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;aACzC;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,mCAAmC;gBAChD,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC;aAC1D;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,2BAA2B;gBACxC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,SAAS,CAAC;aAC9B;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,qCAAqC;gBAClD,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;aAC5D;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,6BAA6B;gBAC1C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,WAAW,CAAC;aAChC;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,gCAAgC;gBAC7C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;aACtC;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,sBAAsB;gBACnC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,IAAI,CAAC;aACzB;YACD;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,2BAA2B;gBACxC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;aAC1D;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,8BAA8B;gBAC3C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,YAAY,CAAC;aACjC;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,mCAAmC;gBAChD,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;aAC5C;YACD;gBACE,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,gCAAgC;gBAC7C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,UAAU,CAAC;aAC/B;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,0BAA0B;gBACvC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,KAAK,CAAC;aAC1B;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,6BAA6B;gBAC1C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;aACpD;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,4BAA4B;gBACzC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC;aAC5D;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,+BAA+B;gBAC5C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,aAAa,CAAC;aAClC;YACD;gBACE,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,6BAA6B;gBAC1C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;aAC5D;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,6BAA6B;gBAC1C,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,WAAW,CAAC;aAChC;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,mCAAmC;gBAChD,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;aACvE;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,wBAAwB;gBACrC,IAAI,EAAE,sBAAQ,CAAC,MAAM;gBACrB,YAAY,EAAE,KAAK;gBACnB,gBAAgB,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;aAC/C;SACF,CAAC;QAEF,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACjD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE;aAC/B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACzC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,wBAAwB;QAE5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,IAAI,EAAE,sBAAQ,CAAC,WAAW,EAAE;SACtC,CAAC,CAAC;QAEH,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;YAC9D,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjF,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAClD,KAAK,EAAE,EAAE,IAAI,EAAE,sBAAQ,CAAC,KAAK,EAAE;SAChC,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,oBAAoB;iBACrD,kBAAkB,CAAC,YAAY,CAAC;iBAChC,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBAC3D,QAAQ,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;iBACjE,OAAO,EAAE,CAAC;YAEb,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,CAAC;QAGD,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;IAC3C,CAAC;IAEO,KAAK,CAAC,2BAA2B;QACvC,MAAM,eAAe,GAAG;YACtB,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,oCAAgB,CAAC,KAAK,EAAE,oCAAgB,CAAC,SAAS,CAAC,EAAE;YAC5F,EAAE,QAAQ,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,oCAAgB,CAAC,OAAO,EAAE,oCAAgB,CAAC,WAAW,CAAC,EAAE;YAClG,EAAE,QAAQ,EAAE,mBAAmB,EAAE,OAAO,EAAE,CAAC,oCAAgB,CAAC,SAAS,EAAE,oCAAgB,CAAC,WAAW,CAAC,EAAE;YACtG,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,oCAAgB,CAAC,EAAE,CAAC,EAAE;YAC1D,EAAE,QAAQ,EAAE,oBAAoB,EAAE,OAAO,EAAE,CAAC,oCAAgB,CAAC,UAAU,CAAC,EAAE;YAC1E,EAAE,QAAQ,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,oCAAgB,CAAC,QAAQ,CAAC,EAAE;SACtE,CAAC;QAEF,KAAK,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,eAAe,EAAE,CAAC;YACpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB;qBAChD,kBAAkB,CAAC,YAAY,CAAC;qBAChC,KAAK,CAAC,oCAAoC,EAAE,EAAE,OAAO,EAAE,CAAC;qBACxD,OAAO,EAAE,CAAC;gBAEb,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QAC3C,OAAO,IAAI,CAAC,cAAc;aACvB,kBAAkB,CAAC,MAAM,CAAC;aAC1B,KAAK,CAAC,sCAAsC,EAAE,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;aAC3E,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,YAAoB,EAAE,WAAmB,EAAE,WAAoB;QAC7E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEpD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,WAAW,IAAI,eAAe,UAAU,CAAC,IAAI,EAAE;YAC5D,IAAI,EAAE,sBAAQ,CAAC,MAAM;YACrB,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;YAC7C,YAAY,EAAE,KAAK;SACpB,CAAC,CAAC;QAGH,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,CAAC,iBAAiB,CAC1B,OAAO,CAAC,EAAE,EACV,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CACtC,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;CACF,CAAA;AA/WY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,8BAAU,CAAC,CAAA;qCADL,oBAAU;QAEJ,oBAAU;GAL/B,WAAW,CA+WvB"}