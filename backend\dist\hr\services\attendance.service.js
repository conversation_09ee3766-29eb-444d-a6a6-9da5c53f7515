"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const attendance_entity_1 = require("../entities/attendance.entity");
let AttendanceService = class AttendanceService {
    attendanceRepository;
    constructor(attendanceRepository) {
        this.attendanceRepository = attendanceRepository;
    }
    async create(createAttendanceDto) {
        const attendance = this.attendanceRepository.create(createAttendanceDto);
        if (attendance.checkInTime && attendance.checkOutTime) {
            attendance.hoursWorked = this.calculateHoursWorked(attendance.checkInTime, attendance.checkOutTime, attendance.breakStartTime, attendance.breakEndTime);
        }
        return this.attendanceRepository.save(attendance);
    }
    async findAll(filters) {
        const queryBuilder = this.attendanceRepository.createQueryBuilder('attendance')
            .leftJoinAndSelect('attendance.employee', 'employee');
        if (filters?.employeeId) {
            queryBuilder.andWhere('attendance.employeeId = :employeeId', { employeeId: filters.employeeId });
        }
        if (filters?.startDate && filters.endDate) {
            queryBuilder.andWhere('attendance.date BETWEEN :startDate AND :endDate', {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        if (filters?.status) {
            queryBuilder.andWhere('attendance.status = :status', { status: filters.status });
        }
        return queryBuilder
            .orderBy('attendance.date', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const attendance = await this.attendanceRepository.findOne({
            where: { id },
            relations: ['employee'],
        });
        if (!attendance) {
            throw new common_1.NotFoundException(`Attendance record with ID ${id} not found`);
        }
        return attendance;
    }
    async update(id, updateAttendanceDto) {
        const attendance = await this.findOne(id);
        Object.assign(attendance, updateAttendanceDto);
        if (attendance.checkInTime && attendance.checkOutTime) {
            attendance.hoursWorked = this.calculateHoursWorked(attendance.checkInTime, attendance.checkOutTime, attendance.breakStartTime, attendance.breakEndTime);
        }
        return this.attendanceRepository.save(attendance);
    }
    async remove(id) {
        const attendance = await this.findOne(id);
        await this.attendanceRepository.remove(attendance);
    }
    async checkIn(employeeId, checkInData) {
        const today = new Date().toISOString().split('T')[0];
        const existingAttendance = await this.attendanceRepository.findOne({
            where: { employeeId, date: new Date(today) },
        });
        if (existingAttendance) {
            throw new Error('Employee already checked in today');
        }
        const attendance = this.attendanceRepository.create({
            employeeId,
            date: new Date(today),
            checkInTime: new Date().toTimeString().split(' ')[0],
            status: attendance_entity_1.AttendanceStatus.PRESENT,
            ...checkInData,
        });
        return this.attendanceRepository.save(attendance);
    }
    async checkOut(employeeId, checkOutData) {
        const today = new Date().toISOString().split('T')[0];
        const attendance = await this.attendanceRepository.findOne({
            where: { employeeId, date: new Date(today) },
        });
        if (!attendance) {
            throw new Error('No check-in record found for today');
        }
        attendance.checkOutTime = new Date().toTimeString().split(' ')[0];
        Object.assign(attendance, checkOutData);
        attendance.hoursWorked = this.calculateHoursWorked(attendance.checkInTime, attendance.checkOutTime, attendance.breakStartTime, attendance.breakEndTime);
        return this.attendanceRepository.save(attendance);
    }
    async getAttendanceReport(employeeId, startDate, endDate) {
        const attendances = await this.findAll({ employeeId, startDate, endDate });
        const totalDays = attendances.length;
        const presentDays = attendances.filter(a => a.status === attendance_entity_1.AttendanceStatus.PRESENT).length;
        const absentDays = attendances.filter(a => a.status === attendance_entity_1.AttendanceStatus.ABSENT).length;
        const lateDays = attendances.filter(a => a.status === attendance_entity_1.AttendanceStatus.LATE).length;
        const totalHours = attendances.reduce((sum, a) => sum + a.hoursWorked, 0);
        const totalOvertimeHours = attendances.reduce((sum, a) => sum + a.overtimeHours, 0);
        return {
            employeeId,
            period: { startDate, endDate },
            summary: {
                totalDays,
                presentDays,
                absentDays,
                lateDays,
                totalHours,
                totalOvertimeHours,
                attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0,
            },
            attendances,
        };
    }
    calculateHoursWorked(checkIn, checkOut, breakStart, breakEnd) {
        const checkInTime = new Date(`1970-01-01T${checkIn}`);
        const checkOutTime = new Date(`1970-01-01T${checkOut}`);
        let totalMinutes = (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60);
        if (breakStart && breakEnd) {
            const breakStartTime = new Date(`1970-01-01T${breakStart}`);
            const breakEndTime = new Date(`1970-01-01T${breakEnd}`);
            const breakMinutes = (breakEndTime.getTime() - breakStartTime.getTime()) / (1000 * 60);
            totalMinutes -= breakMinutes;
        }
        return Math.max(0, totalMinutes / 60);
    }
};
exports.AttendanceService = AttendanceService;
exports.AttendanceService = AttendanceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(attendance_entity_1.Attendance)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AttendanceService);
//# sourceMappingURL=attendance.service.js.map