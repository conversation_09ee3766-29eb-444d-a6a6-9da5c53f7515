"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const stock_entity_1 = require("../entities/stock.entity");
const stock_movement_entity_1 = require("../entities/stock-movement.entity");
const stock_adjustment_entity_1 = require("../entities/stock-adjustment.entity");
let StockService = class StockService {
    stockRepository;
    stockMovementRepository;
    stockAdjustmentRepository;
    constructor(stockRepository, stockMovementRepository, stockAdjustmentRepository) {
        this.stockRepository = stockRepository;
        this.stockMovementRepository = stockMovementRepository;
        this.stockAdjustmentRepository = stockAdjustmentRepository;
    }
    async findAll() {
        return this.stockRepository.find({
            relations: ['product', 'warehouse'],
            order: { 'product.name': 'ASC' },
        });
    }
    async findOne(id) {
        const stock = await this.stockRepository.findOne({
            where: { id },
            relations: ['product', 'warehouse'],
        });
        if (!stock) {
            throw new common_1.NotFoundException(`Stock with ID ${id} not found`);
        }
        return stock;
    }
    async findByProduct(productId) {
        return this.stockRepository.find({
            where: { productId },
            relations: ['warehouse'],
            order: { 'warehouse.name': 'ASC' },
        });
    }
    async findByWarehouse(warehouseId) {
        return this.stockRepository.find({
            where: { warehouseId },
            relations: ['product'],
            order: { 'product.name': 'ASC' },
        });
    }
    async findByProductAndWarehouse(productId, warehouseId) {
        return this.stockRepository.findOne({
            where: { productId, warehouseId },
            relations: ['product', 'warehouse'],
        });
    }
    async adjustStock(productId, warehouseId, adjustment, reason, adjustedBy) {
        let stock = await this.findByProductAndWarehouse(productId, warehouseId);
        if (!stock) {
            stock = this.stockRepository.create({
                productId,
                warehouseId,
                quantity: Math.max(0, adjustment),
                reservedQuantity: 0,
                availableQuantity: Math.max(0, adjustment),
            });
        }
        else {
            const oldQuantity = stock.quantity;
            stock.quantity = Math.max(0, stock.quantity + adjustment);
            stock.availableQuantity = stock.quantity - stock.reservedQuantity;
        }
        const savedStock = await this.stockRepository.save(stock);
        await this.stockAdjustmentRepository.save({
            productId,
            warehouseId,
            adjustmentQuantity: adjustment,
            reason,
            adjustedBy,
            adjustmentDate: new Date(),
        });
        await this.recordStockMovement(productId, warehouseId, adjustment > 0 ? 'IN' : 'OUT', Math.abs(adjustment), 'ADJUSTMENT', reason);
        return savedStock;
    }
    async recordStockMovement(productId, warehouseId, type, quantity, movementType, reference) {
        const movement = this.stockMovementRepository.create({
            productId,
            warehouseId,
            type,
            quantity,
            movementType,
            reference,
            movementDate: new Date(),
        });
        return this.stockMovementRepository.save(movement);
    }
    async getStockMovements(productId, warehouseId, startDate, endDate) {
        const query = this.stockMovementRepository
            .createQueryBuilder('movement')
            .leftJoinAndSelect('movement.product', 'product')
            .leftJoinAndSelect('movement.warehouse', 'warehouse')
            .orderBy('movement.movementDate', 'DESC');
        if (productId) {
            query.andWhere('movement.productId = :productId', { productId });
        }
        if (warehouseId) {
            query.andWhere('movement.warehouseId = :warehouseId', { warehouseId });
        }
        if (startDate) {
            query.andWhere('movement.movementDate >= :startDate', { startDate });
        }
        if (endDate) {
            query.andWhere('movement.movementDate <= :endDate', { endDate });
        }
        return query.getMany();
    }
    async getLowStockItems(threshold = 10) {
        return this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .where('stock.availableQuantity <= :threshold', { threshold })
            .andWhere('stock.availableQuantity > 0')
            .orderBy('stock.availableQuantity', 'ASC')
            .getMany();
    }
    async getOutOfStockItems() {
        return this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .where('stock.availableQuantity = 0')
            .orderBy('product.name', 'ASC')
            .getMany();
    }
    async getOverstockItems(threshold = 1000) {
        return this.stockRepository
            .createQueryBuilder('stock')
            .leftJoinAndSelect('stock.product', 'product')
            .leftJoinAndSelect('stock.warehouse', 'warehouse')
            .where('stock.quantity >= :threshold', { threshold })
            .orderBy('stock.quantity', 'DESC')
            .getMany();
    }
    async reserveStock(productId, warehouseId, quantity) {
        const stock = await this.findByProductAndWarehouse(productId, warehouseId);
        if (!stock || stock.availableQuantity < quantity) {
            return false;
        }
        stock.reservedQuantity += quantity;
        stock.availableQuantity -= quantity;
        await this.stockRepository.save(stock);
        await this.recordStockMovement(productId, warehouseId, 'OUT', quantity, 'RESERVATION', 'Stock reserved');
        return true;
    }
    async releaseReservation(productId, warehouseId, quantity) {
        const stock = await this.findByProductAndWarehouse(productId, warehouseId);
        if (stock) {
            stock.reservedQuantity = Math.max(0, stock.reservedQuantity - quantity);
            stock.availableQuantity = stock.quantity - stock.reservedQuantity;
            await this.stockRepository.save(stock);
            await this.recordStockMovement(productId, warehouseId, 'IN', quantity, 'RELEASE_RESERVATION', 'Reservation released');
        }
    }
    async fulfillReservation(productId, warehouseId, quantity) {
        const stock = await this.findByProductAndWarehouse(productId, warehouseId);
        if (!stock || stock.reservedQuantity < quantity) {
            return false;
        }
        stock.quantity -= quantity;
        stock.reservedQuantity -= quantity;
        await this.stockRepository.save(stock);
        await this.recordStockMovement(productId, warehouseId, 'OUT', quantity, 'FULFILLMENT', 'Reservation fulfilled');
        return true;
    }
    async getStockStatistics() {
        const totalStockItems = await this.stockRepository.count();
        const lowStockItems = await this.getLowStockItems();
        const outOfStockItems = await this.getOutOfStockItems();
        const totalQuantity = await this.stockRepository
            .createQueryBuilder('stock')
            .select('SUM(stock.quantity)', 'total')
            .getRawOne();
        const totalReserved = await this.stockRepository
            .createQueryBuilder('stock')
            .select('SUM(stock.reservedQuantity)', 'total')
            .getRawOne();
        const totalValue = await this.stockRepository
            .createQueryBuilder('stock')
            .leftJoin('stock.product', 'product')
            .select('SUM(stock.quantity * product.costPrice)', 'totalValue')
            .getRawOne();
        return {
            totalStockItems,
            lowStockCount: lowStockItems.length,
            outOfStockCount: outOfStockItems.length,
            totalQuantity: parseInt(totalQuantity.total) || 0,
            totalReserved: parseInt(totalReserved.total) || 0,
            totalAvailable: (parseInt(totalQuantity.total) || 0) - (parseInt(totalReserved.total) || 0),
            totalStockValue: parseFloat(totalValue.totalValue) || 0,
        };
    }
    async getStockAdjustments(productId, warehouseId, startDate, endDate) {
        const query = this.stockAdjustmentRepository
            .createQueryBuilder('adjustment')
            .leftJoinAndSelect('adjustment.product', 'product')
            .leftJoinAndSelect('adjustment.warehouse', 'warehouse')
            .orderBy('adjustment.adjustmentDate', 'DESC');
        if (productId) {
            query.andWhere('adjustment.productId = :productId', { productId });
        }
        if (warehouseId) {
            query.andWhere('adjustment.warehouseId = :warehouseId', { warehouseId });
        }
        if (startDate) {
            query.andWhere('adjustment.adjustmentDate >= :startDate', { startDate });
        }
        if (endDate) {
            query.andWhere('adjustment.adjustmentDate <= :endDate', { endDate });
        }
        return query.getMany();
    }
    async bulkStockUpdate(updates) {
        for (const update of updates) {
            const currentStock = await this.findByProductAndWarehouse(update.productId, update.warehouseId);
            const currentQuantity = currentStock?.quantity || 0;
            const adjustment = update.quantity - currentQuantity;
            if (adjustment !== 0) {
                await this.adjustStock(update.productId, update.warehouseId, adjustment, update.reason);
            }
        }
    }
};
exports.StockService = StockService;
exports.StockService = StockService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(stock_entity_1.Stock)),
    __param(1, (0, typeorm_1.InjectRepository)(stock_movement_entity_1.StockMovement)),
    __param(2, (0, typeorm_1.InjectRepository)(stock_adjustment_entity_1.StockAdjustment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], StockService);
//# sourceMappingURL=stock.service.js.map