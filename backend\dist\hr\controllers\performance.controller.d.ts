import { PerformanceService } from '../services/performance.service';
export declare class PerformanceController {
    private readonly performanceService;
    constructor(performanceService: PerformanceService);
    create(createPerformanceDto: any): Promise<import("../entities/performance.entity").Performance>;
    findAll(employeeId?: string, type?: string, status?: string): Promise<import("../entities/performance.entity").Performance[]>;
    findOne(id: string): Promise<import("../entities/performance.entity").Performance>;
    update(id: string, updatePerformanceDto: any): Promise<import("../entities/performance.entity").Performance>;
    submitReview(id: string): Promise<import("../entities/performance.entity").Performance>;
    approveReview(id: string, approveDto: {
        approvedBy: string;
    }): Promise<import("../entities/performance.entity").Performance>;
}
