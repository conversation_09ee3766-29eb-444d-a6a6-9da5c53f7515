import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
export declare enum TransferStatus {
    PENDING = "pending",
    IN_TRANSIT = "in_transit",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare class StockTransfer {
    id: string;
    transferNumber: string;
    productId: string;
    product: Product;
    fromWarehouseId: string;
    fromWarehouse: Warehouse;
    toWarehouseId: string;
    toWarehouse: Warehouse;
    quantity: number;
    status: TransferStatus;
    transferDate: Date;
    expectedArrivalDate: Date;
    actualArrivalDate: Date;
    notes: string;
    initiatedBy: string;
    approvedBy: string;
    approvedAt: Date;
    receivedBy: string;
    receivedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
