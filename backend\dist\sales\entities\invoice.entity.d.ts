import { Customer } from './customer.entity';
import { InvoiceItem } from './invoice-item.entity';
import { Payment } from './payment.entity';
import { CreditNote } from './credit-note.entity';
export declare class Invoice {
    id: string;
    invoiceNumber: string;
    customerId: string;
    customer: Customer;
    invoiceDate: Date;
    issueDate: Date;
    dueDate: Date;
    paymentTerms: string;
    salesOfficer: string;
    governorAccount: string;
    subtotal: number;
    discountAmount: number;
    discountType: 'percentage' | 'amount';
    discountValue: number;
    settlementAmount: number;
    taxAmount: number;
    advancePayment: number;
    totalAmount: number;
    paidAmount: number;
    remainingAmount: number;
    status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
    deliveryMethod: 'email' | 'print';
    internalNotes: string;
    invoiceTerms: string;
    additionalDetails: string;
    items: InvoiceItem[];
    payments: Payment[];
    creditNotes: CreditNote[];
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
