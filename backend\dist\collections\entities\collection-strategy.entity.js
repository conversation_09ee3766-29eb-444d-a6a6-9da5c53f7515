"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionStrategy = exports.StrategyType = void 0;
const typeorm_1 = require("typeorm");
var StrategyType;
(function (StrategyType) {
    StrategyType["SOFT"] = "soft";
    StrategyType["MEDIUM"] = "medium";
    StrategyType["AGGRESSIVE"] = "aggressive";
    StrategyType["LEGAL"] = "legal";
    StrategyType["CUSTOM"] = "custom";
})(StrategyType || (exports.StrategyType = StrategyType = {}));
let CollectionStrategy = class CollectionStrategy {
    id;
    name;
    description;
    type;
    rules;
    actions;
    triggers;
    maxContactAttempts;
    contactFrequencyDays;
    escalationDays;
    isActive;
    isDefault;
    createdBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionStrategy = CollectionStrategy;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionStrategy.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CollectionStrategy.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionStrategy.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: StrategyType,
        default: StrategyType.MEDIUM,
    }),
    __metadata("design:type", String)
], CollectionStrategy.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], CollectionStrategy.prototype, "rules", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], CollectionStrategy.prototype, "actions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionStrategy.prototype, "triggers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CollectionStrategy.prototype, "maxContactAttempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 1 }),
    __metadata("design:type", Number)
], CollectionStrategy.prototype, "contactFrequencyDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 90 }),
    __metadata("design:type", Number)
], CollectionStrategy.prototype, "escalationDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CollectionStrategy.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionStrategy.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionStrategy.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionStrategy.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionStrategy.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionStrategy.prototype, "updatedAt", void 0);
exports.CollectionStrategy = CollectionStrategy = __decorate([
    (0, typeorm_1.Entity)('collection_strategies')
], CollectionStrategy);
//# sourceMappingURL=collection-strategy.entity.js.map