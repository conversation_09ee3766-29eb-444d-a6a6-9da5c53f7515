"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetController = void 0;
const common_1 = require("@nestjs/common");
const asset_service_1 = require("../services/asset.service");
const asset_entity_1 = require("../entities/asset.entity");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let AssetController = class AssetController {
    assetService;
    constructor(assetService) {
        this.assetService = assetService;
    }
    async create(createAssetDto) {
        return this.assetService.create(createAssetDto);
    }
    async findAll(status, category, location) {
        if (status) {
            return this.assetService.findByStatus(status);
        }
        if (category) {
            return this.assetService.findByCategory(category);
        }
        if (location) {
            return this.assetService.findByLocation(location);
        }
        return this.assetService.findAll();
    }
    async getStatistics() {
        return this.assetService.getAssetStatistics();
    }
    async getDashboardMetrics() {
        return this.assetService.getDashboardMetrics();
    }
    async getAssetsByCategory() {
        return this.assetService.getAssetsByCategory();
    }
    async getAssetsNearingWarrantyExpiry(days) {
        const daysNum = days ? parseInt(days) : 30;
        return this.assetService.getAssetsNearingWarrantyExpiry(daysNum);
    }
    async searchAssets(searchTerm) {
        return this.assetService.searchAssets(searchTerm);
    }
    async getUserAssets(userId) {
        return this.assetService.getUserAssets(userId);
    }
    async findByAssetTag(assetTag) {
        return this.assetService.findByAssetTag(assetTag);
    }
    async findOne(id) {
        return this.assetService.findOne(id);
    }
    async getAssetHistory(id) {
        return this.assetService.getAssetHistory(id);
    }
    async assignAsset(id, assignmentData) {
        return this.assetService.assignAsset(id, assignmentData.assignedToId, assignmentData.assignedById, assignmentData.notes);
    }
    async unassignAsset(id, unassignmentData) {
        await this.assetService.unassignAsset(id, unassignmentData.unassignedById, unassignmentData.notes);
        return { message: 'Asset unassigned successfully' };
    }
    async update(id, updateAssetDto) {
        return this.assetService.update(id, updateAssetDto);
    }
    async updateStatus(id, statusData) {
        return this.assetService.updateAssetStatus(id, statusData.status, statusData.notes);
    }
    async retireAsset(id, retirementData) {
        return this.assetService.retireAsset(id, retirementData.retiredById, retirementData.reason);
    }
    async remove(id) {
        return this.assetService.remove(id);
    }
};
exports.AssetController = AssetController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Query)('location')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Get)('by-category'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getAssetsByCategory", null);
__decorate([
    (0, common_1.Get)('warranty-expiring'),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getAssetsNearingWarrantyExpiry", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "searchAssets", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getUserAssets", null);
__decorate([
    (0, common_1.Get)('tag/:assetTag'),
    __param(0, (0, common_1.Param)('assetTag')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "findByAssetTag", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/history'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getAssetHistory", null);
__decorate([
    (0, common_1.Post)(':id/assign'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "assignAsset", null);
__decorate([
    (0, common_1.Post)(':id/unassign'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "unassignAsset", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Post)(':id/retire'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "retireAsset", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "remove", null);
exports.AssetController = AssetController = __decorate([
    (0, common_1.Controller)('assets'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [asset_service_1.AssetService])
], AssetController);
//# sourceMappingURL=asset.controller.js.map