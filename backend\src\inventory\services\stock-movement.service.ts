import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { StockMovement } from '../entities/stock-movement.entity';

@Injectable()
export class StockMovementService {
  constructor(
    @InjectRepository(StockMovement)
    private stockMovementRepository: Repository<StockMovement>,
  ) {}

  async findAll(): Promise<StockMovement[]> {
    return this.stockMovementRepository.find({
      relations: ['product', 'warehouse'],
      order: { movementDate: 'DESC' },
    });
  }

  async findByProduct(productId: string): Promise<StockMovement[]> {
    return this.stockMovementRepository.find({
      where: { productId },
      relations: ['warehouse'],
      order: { movementDate: 'DESC' },
    });
  }

  async findByWarehouse(warehouseId: string): Promise<StockMovement[]> {
    return this.stockMovementRepository.find({
      where: { warehouseId },
      relations: ['product'],
      order: { movementDate: 'DESC' },
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<StockMovement[]> {
    return this.stockMovementRepository.find({
      where: {
        movementDate: Between(startDate, endDate),
      },
      relations: ['product', 'warehouse'],
      order: { movementDate: 'DESC' },
    });
  }

  async getMovementStatistics(startDate?: Date, endDate?: Date): Promise<any> {
    const query = this.stockMovementRepository.createQueryBuilder('movement');

    if (startDate && endDate) {
      query.where('movement.movementDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const totalMovements = await query.getCount();
    
    const inMovements = await query
      .clone()
      .andWhere('movement.type = :type', { type: 'IN' })
      .getCount();

    const outMovements = await query
      .clone()
      .andWhere('movement.type = :type', { type: 'OUT' })
      .getCount();

    const totalInQuantity = await query
      .clone()
      .andWhere('movement.type = :type', { type: 'IN' })
      .select('SUM(movement.quantity)', 'total')
      .getRawOne();

    const totalOutQuantity = await query
      .clone()
      .andWhere('movement.type = :type', { type: 'OUT' })
      .select('SUM(movement.quantity)', 'total')
      .getRawOne();

    return {
      totalMovements,
      inMovements,
      outMovements,
      totalInQuantity: parseInt(totalInQuantity.total) || 0,
      totalOutQuantity: parseInt(totalOutQuantity.total) || 0,
      netMovement: (parseInt(totalInQuantity.total) || 0) - (parseInt(totalOutQuantity.total) || 0),
    };
  }

  async getMovementsByType(movementType: string): Promise<StockMovement[]> {
    return this.stockMovementRepository.find({
      where: { movementType },
      relations: ['product', 'warehouse'],
      order: { movementDate: 'DESC' },
    });
  }

  async getRecentMovements(limit: number = 50): Promise<StockMovement[]> {
    return this.stockMovementRepository.find({
      relations: ['product', 'warehouse'],
      order: { movementDate: 'DESC' },
      take: limit,
    });
  }
}
