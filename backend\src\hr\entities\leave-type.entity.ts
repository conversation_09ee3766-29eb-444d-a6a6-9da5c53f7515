import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Leave } from './leave.entity';

@Entity('hr_leave_types')
export class LeaveType {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 20, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'int', default: 0 })
  maxDaysPerYear: number;

  @Column({ type: 'int', default: 0 })
  maxConsecutiveDays: number;

  @Column({ default: true })
  isPaid: boolean;

  @Column({ default: true })
  requiresApproval: boolean;

  @Column({ default: false })
  carryForward: boolean;

  @Column({ type: 'int', default: 0 })
  maxCarryForwardDays: number;

  @Column({ type: 'int', default: 0 })
  minServiceMonths: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  applicableGenders: string[];

  @Column({ type: 'json', nullable: true })
  applicableEmploymentTypes: string[];

  @Column({ type: 'json', nullable: true })
  requiredDocuments: string[];

  @OneToMany(() => Leave, leave => leave.leaveType)
  leaves: Leave[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
