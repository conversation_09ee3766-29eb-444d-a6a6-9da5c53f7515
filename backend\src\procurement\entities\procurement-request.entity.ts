import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ProcurementItem } from './procurement-item.entity';
import { ProcurementCategory } from './procurement-category.entity';
import { ProcurementApproval } from './procurement-approval.entity';

export enum RequestStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IN_PROCUREMENT = 'in_procurement',
  PARTIALLY_FULFILLED = 'partially_fulfilled',
  FULFILLED = 'fulfilled',
  CANCELLED = 'cancelled',
}

export enum RequestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

export enum RequestType {
  GOODS = 'goods',
  SERVICES = 'services',
  CAPITAL_EXPENDITURE = 'capital_expenditure',
  MAINTENANCE = 'maintenance',
  EMERGENCY = 'emergency',
  RECURRING = 'recurring',
}

@Entity('procurement_requests')
export class ProcurementRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  requestNumber: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: RequestType,
    default: RequestType.GOODS,
  })
  type: RequestType;

  @Column({
    type: 'enum',
    enum: RequestStatus,
    default: RequestStatus.DRAFT,
  })
  status: RequestStatus;

  @Column({
    type: 'enum',
    enum: RequestPriority,
    default: RequestPriority.MEDIUM,
  })
  priority: RequestPriority;

  @Column()
  requestedBy: string;

  @Column({ nullable: true })
  departmentId: string;

  @Column({ nullable: true })
  categoryId: string;

  @ManyToOne(() => ProcurementCategory, { nullable: true })
  @JoinColumn({ name: 'categoryId' })
  category: ProcurementCategory;

  @Column({ type: 'date' })
  requestDate: Date;

  @Column({ type: 'date' })
  requiredDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  estimatedBudget: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'text', nullable: true })
  justification: string;

  @Column({ type: 'text', nullable: true })
  specifications: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'text', nullable: true })
  deliveryAddress: string;

  @Column({ type: 'text', nullable: true })
  specialInstructions: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'text', nullable: true })
  approvalNotes: string;

  @Column({ nullable: true })
  rejectedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  rejectedAt: Date;

  @Column({ type: 'text', nullable: true })
  rejectionReason: string;

  @OneToMany(() => ProcurementItem, item => item.request, { cascade: true })
  items: ProcurementItem[];

  @OneToMany(() => ProcurementApproval, approval => approval.request)
  approvals: ProcurementApproval[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
