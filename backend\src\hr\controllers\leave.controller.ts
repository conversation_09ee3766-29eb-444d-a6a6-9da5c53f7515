import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { LeaveService } from '../services/leave.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/leaves')
@UseGuards(JwtAuthGuard)
export class LeaveController {
  constructor(private readonly leaveService: LeaveService) {}

  @Post()
  create(@Body() createLeaveDto: any) {
    return this.leaveService.create(createLeaveDto);
  }

  @Get()
  findAll(
    @Query('employeeId') employeeId?: string,
    @Query('status') status?: string,
    @Query('leaveTypeId') leaveTypeId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const filters: any = {};
    if (employeeId) filters.employeeId = employeeId;
    if (status) filters.status = status;
    if (leaveTypeId) filters.leaveTypeId = leaveTypeId;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    return this.leaveService.findAll(filters);
  }

  @Get('types')
  findAllLeaveTypes() {
    return this.leaveService.findAllLeaveTypes();
  }

  @Post('types')
  createLeaveType(@Body() createLeaveTypeDto: any) {
    return this.leaveService.createLeaveType(createLeaveTypeDto);
  }

  @Get('balance/:employeeId/:leaveTypeId')
  getLeaveBalance(
    @Param('employeeId') employeeId: string,
    @Param('leaveTypeId') leaveTypeId: string,
    @Query('year') year?: string
  ) {
    const yearNum = year ? parseInt(year) : undefined;
    return this.leaveService.getLeaveBalance(employeeId, leaveTypeId, yearNum);
  }

  @Get('report/:employeeId')
  getLeaveReport(
    @Param('employeeId') employeeId: string,
    @Query('year') year?: string
  ) {
    const yearNum = year ? parseInt(year) : undefined;
    return this.leaveService.getLeaveReport(employeeId, yearNum);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.leaveService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateLeaveDto: any) {
    return this.leaveService.update(id, updateLeaveDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.leaveService.remove(id);
  }

  @Post(':id/approve')
  approveLeave(
    @Param('id') id: string,
    @Body() approveDto: { approvedBy: string; approvalComments?: string }
  ) {
    return this.leaveService.approveLeave(id, approveDto.approvedBy, approveDto.approvalComments);
  }

  @Post(':id/reject')
  rejectLeave(
    @Param('id') id: string,
    @Body() rejectDto: { rejectedBy: string; rejectionComments: string }
  ) {
    return this.leaveService.rejectLeave(id, rejectDto.rejectedBy, rejectDto.rejectionComments);
  }

  @Post(':id/cancel')
  cancelLeave(@Param('id') id: string, @Body() cancelDto: { reason?: string }) {
    return this.leaveService.cancelLeave(id, cancelDto.reason);
  }
}
