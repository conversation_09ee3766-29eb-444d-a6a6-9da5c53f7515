import { ProcurementItem } from './procurement-item.entity';
import { ProcurementCategory } from './procurement-category.entity';
import { ProcurementApproval } from './procurement-approval.entity';
export declare enum RequestStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    PENDING_APPROVAL = "pending_approval",
    APPROVED = "approved",
    REJECTED = "rejected",
    IN_PROCUREMENT = "in_procurement",
    PARTIALLY_FULFILLED = "partially_fulfilled",
    FULFILLED = "fulfilled",
    CANCELLED = "cancelled"
}
export declare enum RequestPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent",
    CRITICAL = "critical"
}
export declare enum RequestType {
    GOODS = "goods",
    SERVICES = "services",
    CAPITAL_EXPENDITURE = "capital_expenditure",
    MAINTENANCE = "maintenance",
    EMERGENCY = "emergency",
    RECURRING = "recurring"
}
export declare class ProcurementRequest {
    id: string;
    requestNumber: string;
    title: string;
    description: string;
    type: RequestType;
    status: RequestStatus;
    priority: RequestPriority;
    requestedBy: string;
    departmentId: string;
    categoryId: string;
    category: ProcurementCategory;
    requestDate: Date;
    requiredDate: Date;
    estimatedBudget: number;
    totalAmount: number;
    currency: string;
    justification: string;
    specifications: string;
    attachments: string[];
    deliveryAddress: string;
    specialInstructions: string;
    approvedBy: string;
    approvedAt: Date;
    approvalNotes: string;
    rejectedBy: string;
    rejectedAt: Date;
    rejectionReason: string;
    items: ProcurementItem[];
    approvals: ProcurementApproval[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
