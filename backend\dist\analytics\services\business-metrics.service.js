"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessMetricsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("../../sales/entities/customer.entity");
const invoice_entity_1 = require("../../sales/entities/invoice.entity");
let BusinessMetricsService = class BusinessMetricsService {
    customerRepository;
    invoiceRepository;
    constructor(customerRepository, invoiceRepository) {
        this.customerRepository = customerRepository;
        this.invoiceRepository = invoiceRepository;
    }
    async getBusinessMetrics(companyId, period = '30d') {
        const days = this.parsePeriod(period);
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const currentMetrics = await this.getMetricsForPeriod(companyId, startDate, new Date());
        const previousStartDate = new Date(startDate);
        previousStartDate.setDate(previousStartDate.getDate() - days);
        const previousMetrics = await this.getMetricsForPeriod(companyId, previousStartDate, startDate);
        const revenueGrowth = this.calculateGrowth(currentMetrics.totalRevenue, previousMetrics.totalRevenue);
        const customerGrowth = this.calculateGrowth(currentMetrics.totalCustomers, previousMetrics.totalCustomers);
        const profitGrowth = this.calculateGrowth(currentMetrics.netProfit, previousMetrics.netProfit);
        return {
            totalRevenue: currentMetrics.totalRevenue,
            totalExpenses: currentMetrics.totalExpenses,
            netProfit: currentMetrics.netProfit,
            totalCustomers: currentMetrics.totalCustomers,
            totalProjects: currentMetrics.totalProjects,
            totalEmployees: currentMetrics.totalEmployees,
            revenueGrowth,
            customerGrowth,
            profitGrowth,
            projectGrowth: 15.7,
            departmentMetrics: await this.getDepartmentMetrics(companyId, period),
        };
    }
    async getRevenueMetrics(companyId, period = '30d') {
        const days = this.parsePeriod(period);
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const invoices = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.companyId = :companyId', { companyId })
            .andWhere('invoice.createdAt >= :startDate', { startDate })
            .andWhere('invoice.status = :status', { status: 'paid' })
            .getMany();
        const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
        const totalInvoices = invoices.length;
        const averageInvoiceValue = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;
        return {
            totalRevenue,
            totalInvoices,
            averageInvoiceValue,
            revenueByMonth: await this.getRevenueByMonth(companyId, days),
        };
    }
    async getDepartmentMetrics(companyId, period = '30d') {
        return [
            {
                department: 'Sales',
                revenue: 850000,
                expenses: 320000,
                profit: 530000,
                efficiency: 95,
                trend: 'up',
            },
            {
                department: 'Projects',
                revenue: 650000,
                expenses: 280000,
                profit: 370000,
                efficiency: 88,
                trend: 'up',
            },
            {
                department: 'Marketing',
                revenue: 450000,
                expenses: 180000,
                profit: 270000,
                efficiency: 92,
                trend: 'stable',
            },
        ];
    }
    async getGrowthMetrics(companyId, period = '30d') {
        const days = this.parsePeriod(period);
        const currentDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const growthData = await this.calculateGrowthTrends(companyId, startDate, currentDate);
        return {
            revenueGrowthTrend: growthData.revenueGrowth,
            customerGrowthTrend: growthData.customerGrowth,
            profitGrowthTrend: growthData.profitGrowth,
            monthlyGrowthRates: growthData.monthlyRates,
        };
    }
    async getMetricsForPeriod(companyId, startDate, endDate) {
        const invoices = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.companyId = :companyId', { companyId })
            .andWhere('invoice.createdAt >= :startDate', { startDate })
            .andWhere('invoice.createdAt <= :endDate', { endDate })
            .andWhere('invoice.status = :status', { status: 'paid' })
            .getMany();
        const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
        const totalCustomers = await this.customerRepository
            .createQueryBuilder('customer')
            .where('customer.companyId = :companyId', { companyId })
            .andWhere('customer.createdAt <= :endDate', { endDate })
            .getCount();
        const totalExpenses = totalRevenue * 0.65;
        const netProfit = totalRevenue - totalExpenses;
        return {
            totalRevenue,
            totalExpenses,
            netProfit,
            totalCustomers,
            totalProjects: 89,
            totalEmployees: 156,
        };
    }
    async getRevenueByMonth(companyId, days) {
        const months = Math.ceil(days / 30);
        const monthlyData = [];
        for (let i = 0; i < months; i++) {
            monthlyData.push({
                month: new Date(Date.now() - (i * 30 * 24 * 60 * 60 * 1000)).toISOString().slice(0, 7),
                revenue: Math.floor(Math.random() * 500000) + 200000,
            });
        }
        return monthlyData.reverse();
    }
    async calculateGrowthTrends(companyId, startDate, endDate) {
        return {
            revenueGrowth: 12.5,
            customerGrowth: 8.3,
            profitGrowth: 18.2,
            monthlyRates: [
                { month: '2024-01', growth: 10.2 },
                { month: '2024-02', growth: 15.8 },
                { month: '2024-03', growth: 12.5 },
                { month: '2024-04', growth: 18.2 },
                { month: '2024-05', growth: 14.7 },
            ],
        };
    }
    parsePeriod(period) {
        const match = period.match(/(\d+)([dmy])/);
        if (!match)
            return 30;
        const [, value, unit] = match;
        const num = parseInt(value, 10);
        switch (unit) {
            case 'd': return num;
            case 'm': return num * 30;
            case 'y': return num * 365;
            default: return 30;
        }
    }
    calculateGrowth(current, previous) {
        if (previous === 0)
            return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
    }
};
exports.BusinessMetricsService = BusinessMetricsService;
exports.BusinessMetricsService = BusinessMetricsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __param(1, (0, typeorm_1.InjectRepository)(invoice_entity_1.Invoice)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BusinessMetricsService);
//# sourceMappingURL=business-metrics.service.js.map