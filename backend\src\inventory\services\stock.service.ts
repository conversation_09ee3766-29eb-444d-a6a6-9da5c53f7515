import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Stock } from '../entities/stock.entity';
import { StockMovement } from '../entities/stock-movement.entity';
import { StockAdjustment } from '../entities/stock-adjustment.entity';

@Injectable()
export class StockService {
  constructor(
    @InjectRepository(Stock)
    private stockRepository: Repository<Stock>,
    @InjectRepository(StockMovement)
    private stockMovementRepository: Repository<StockMovement>,
    @InjectRepository(StockAdjustment)
    private stockAdjustmentRepository: Repository<StockAdjustment>,
  ) {}

  async findAll(): Promise<Stock[]> {
    return this.stockRepository.find({
      relations: ['product', 'warehouse'],
      order: { 'product.name': 'ASC' },
    });
  }

  async findOne(id: string): Promise<Stock> {
    const stock = await this.stockRepository.findOne({
      where: { id },
      relations: ['product', 'warehouse'],
    });

    if (!stock) {
      throw new NotFoundException(`Stock with ID ${id} not found`);
    }

    return stock;
  }

  async findByProduct(productId: string): Promise<Stock[]> {
    return this.stockRepository.find({
      where: { productId },
      relations: ['warehouse'],
      order: { 'warehouse.name': 'ASC' },
    });
  }

  async findByWarehouse(warehouseId: string): Promise<Stock[]> {
    return this.stockRepository.find({
      where: { warehouseId },
      relations: ['product'],
      order: { 'product.name': 'ASC' },
    });
  }

  async findByProductAndWarehouse(productId: string, warehouseId: string): Promise<Stock | null> {
    return this.stockRepository.findOne({
      where: { productId, warehouseId },
      relations: ['product', 'warehouse'],
    });
  }

  async adjustStock(
    productId: string,
    warehouseId: string,
    adjustment: number,
    reason: string,
    adjustedBy?: string,
  ): Promise<Stock> {
    let stock = await this.findByProductAndWarehouse(productId, warehouseId);

    if (!stock) {
      // Create new stock record if it doesn't exist
      stock = this.stockRepository.create({
        productId,
        warehouseId,
        quantity: Math.max(0, adjustment),
        reservedQuantity: 0,
        availableQuantity: Math.max(0, adjustment),
      });
    } else {
      const oldQuantity = stock.quantity;
      stock.quantity = Math.max(0, stock.quantity + adjustment);
      stock.availableQuantity = stock.quantity - stock.reservedQuantity;
    }

    const savedStock = await this.stockRepository.save(stock);

    // Record the adjustment
    await this.stockAdjustmentRepository.save({
      productId,
      warehouseId,
      adjustmentQuantity: adjustment,
      reason,
      adjustedBy,
      adjustmentDate: new Date(),
    });

    // Record stock movement
    await this.recordStockMovement(
      productId,
      warehouseId,
      adjustment > 0 ? 'IN' : 'OUT',
      Math.abs(adjustment),
      'ADJUSTMENT',
      reason,
    );

    return savedStock;
  }

  async recordStockMovement(
    productId: string,
    warehouseId: string,
    type: 'IN' | 'OUT',
    quantity: number,
    movementType: string,
    reference?: string,
  ): Promise<StockMovement> {
    const movement = this.stockMovementRepository.create({
      productId,
      warehouseId,
      type,
      quantity,
      movementType,
      reference,
      movementDate: new Date(),
    });

    return this.stockMovementRepository.save(movement);
  }

  async getStockMovements(
    productId?: string,
    warehouseId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<StockMovement[]> {
    const query = this.stockMovementRepository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.product', 'product')
      .leftJoinAndSelect('movement.warehouse', 'warehouse')
      .orderBy('movement.movementDate', 'DESC');

    if (productId) {
      query.andWhere('movement.productId = :productId', { productId });
    }

    if (warehouseId) {
      query.andWhere('movement.warehouseId = :warehouseId', { warehouseId });
    }

    if (startDate) {
      query.andWhere('movement.movementDate >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('movement.movementDate <= :endDate', { endDate });
    }

    return query.getMany();
  }

  async getLowStockItems(threshold: number = 10): Promise<Stock[]> {
    return this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .where('stock.availableQuantity <= :threshold', { threshold })
      .andWhere('stock.availableQuantity > 0')
      .orderBy('stock.availableQuantity', 'ASC')
      .getMany();
  }

  async getOutOfStockItems(): Promise<Stock[]> {
    return this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .where('stock.availableQuantity = 0')
      .orderBy('product.name', 'ASC')
      .getMany();
  }

  async getOverstockItems(threshold: number = 1000): Promise<Stock[]> {
    return this.stockRepository
      .createQueryBuilder('stock')
      .leftJoinAndSelect('stock.product', 'product')
      .leftJoinAndSelect('stock.warehouse', 'warehouse')
      .where('stock.quantity >= :threshold', { threshold })
      .orderBy('stock.quantity', 'DESC')
      .getMany();
  }

  async reserveStock(productId: string, warehouseId: string, quantity: number): Promise<boolean> {
    const stock = await this.findByProductAndWarehouse(productId, warehouseId);

    if (!stock || stock.availableQuantity < quantity) {
      return false;
    }

    stock.reservedQuantity += quantity;
    stock.availableQuantity -= quantity;
    await this.stockRepository.save(stock);

    await this.recordStockMovement(
      productId,
      warehouseId,
      'OUT',
      quantity,
      'RESERVATION',
      'Stock reserved',
    );

    return true;
  }

  async releaseReservation(productId: string, warehouseId: string, quantity: number): Promise<void> {
    const stock = await this.findByProductAndWarehouse(productId, warehouseId);

    if (stock) {
      stock.reservedQuantity = Math.max(0, stock.reservedQuantity - quantity);
      stock.availableQuantity = stock.quantity - stock.reservedQuantity;
      await this.stockRepository.save(stock);

      await this.recordStockMovement(
        productId,
        warehouseId,
        'IN',
        quantity,
        'RELEASE_RESERVATION',
        'Reservation released',
      );
    }
  }

  async fulfillReservation(productId: string, warehouseId: string, quantity: number): Promise<boolean> {
    const stock = await this.findByProductAndWarehouse(productId, warehouseId);

    if (!stock || stock.reservedQuantity < quantity) {
      return false;
    }

    stock.quantity -= quantity;
    stock.reservedQuantity -= quantity;
    await this.stockRepository.save(stock);

    await this.recordStockMovement(
      productId,
      warehouseId,
      'OUT',
      quantity,
      'FULFILLMENT',
      'Reservation fulfilled',
    );

    return true;
  }

  async getStockStatistics(): Promise<any> {
    const totalStockItems = await this.stockRepository.count();
    const lowStockItems = await this.getLowStockItems();
    const outOfStockItems = await this.getOutOfStockItems();

    const totalQuantity = await this.stockRepository
      .createQueryBuilder('stock')
      .select('SUM(stock.quantity)', 'total')
      .getRawOne();

    const totalReserved = await this.stockRepository
      .createQueryBuilder('stock')
      .select('SUM(stock.reservedQuantity)', 'total')
      .getRawOne();

    const totalValue = await this.stockRepository
      .createQueryBuilder('stock')
      .leftJoin('stock.product', 'product')
      .select('SUM(stock.quantity * product.costPrice)', 'totalValue')
      .getRawOne();

    return {
      totalStockItems,
      lowStockCount: lowStockItems.length,
      outOfStockCount: outOfStockItems.length,
      totalQuantity: parseInt(totalQuantity.total) || 0,
      totalReserved: parseInt(totalReserved.total) || 0,
      totalAvailable: (parseInt(totalQuantity.total) || 0) - (parseInt(totalReserved.total) || 0),
      totalStockValue: parseFloat(totalValue.totalValue) || 0,
    };
  }

  async getStockAdjustments(
    productId?: string,
    warehouseId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<StockAdjustment[]> {
    const query = this.stockAdjustmentRepository
      .createQueryBuilder('adjustment')
      .leftJoinAndSelect('adjustment.product', 'product')
      .leftJoinAndSelect('adjustment.warehouse', 'warehouse')
      .orderBy('adjustment.adjustmentDate', 'DESC');

    if (productId) {
      query.andWhere('adjustment.productId = :productId', { productId });
    }

    if (warehouseId) {
      query.andWhere('adjustment.warehouseId = :warehouseId', { warehouseId });
    }

    if (startDate) {
      query.andWhere('adjustment.adjustmentDate >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('adjustment.adjustmentDate <= :endDate', { endDate });
    }

    return query.getMany();
  }

  async bulkStockUpdate(updates: Array<{
    productId: string;
    warehouseId: string;
    quantity: number;
    reason: string;
  }>): Promise<void> {
    for (const update of updates) {
      const currentStock = await this.findByProductAndWarehouse(update.productId, update.warehouseId);
      const currentQuantity = currentStock?.quantity || 0;
      const adjustment = update.quantity - currentQuantity;

      if (adjustment !== 0) {
        await this.adjustStock(
          update.productId,
          update.warehouseId,
          adjustment,
          update.reason,
        );
      }
    }
  }
}
