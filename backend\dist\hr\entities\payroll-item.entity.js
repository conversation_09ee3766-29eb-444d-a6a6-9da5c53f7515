"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayrollItem = exports.PayrollItemCategory = exports.PayrollItemType = void 0;
const typeorm_1 = require("typeorm");
const payroll_entity_1 = require("./payroll.entity");
var PayrollItemType;
(function (PayrollItemType) {
    PayrollItemType["EARNING"] = "earning";
    PayrollItemType["DEDUCTION"] = "deduction";
    PayrollItemType["BENEFIT"] = "benefit";
    PayrollItemType["TAX"] = "tax";
})(PayrollItemType || (exports.PayrollItemType = PayrollItemType = {}));
var PayrollItemCategory;
(function (PayrollItemCategory) {
    PayrollItemCategory["BASIC_SALARY"] = "basic_salary";
    PayrollItemCategory["OVERTIME"] = "overtime";
    PayrollItemCategory["BONUS"] = "bonus";
    PayrollItemCategory["COMMISSION"] = "commission";
    PayrollItemCategory["ALLOWANCE"] = "allowance";
    PayrollItemCategory["INCOME_TAX"] = "income_tax";
    PayrollItemCategory["SOCIAL_SECURITY"] = "social_security";
    PayrollItemCategory["MEDICARE"] = "medicare";
    PayrollItemCategory["HEALTH_INSURANCE"] = "health_insurance";
    PayrollItemCategory["RETIREMENT"] = "retirement";
    PayrollItemCategory["LOAN_REPAYMENT"] = "loan_repayment";
    PayrollItemCategory["HEALTH_BENEFIT"] = "health_benefit";
    PayrollItemCategory["DENTAL_BENEFIT"] = "dental_benefit";
    PayrollItemCategory["VISION_BENEFIT"] = "vision_benefit";
    PayrollItemCategory["LIFE_INSURANCE"] = "life_insurance";
    PayrollItemCategory["OTHER"] = "other";
})(PayrollItemCategory || (exports.PayrollItemCategory = PayrollItemCategory = {}));
let PayrollItem = class PayrollItem {
    id;
    payrollId;
    payroll;
    name;
    code;
    type;
    category;
    amount;
    rate;
    hours;
    percentage;
    isTaxable;
    isStatutory;
    description;
    metadata;
    createdAt;
    updatedAt;
};
exports.PayrollItem = PayrollItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PayrollItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PayrollItem.prototype, "payrollId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => payroll_entity_1.Payroll, payroll => payroll.payrollItems, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'payrollId' }),
    __metadata("design:type", payroll_entity_1.Payroll)
], PayrollItem.prototype, "payroll", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], PayrollItem.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], PayrollItem.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PayrollItemType,
    }),
    __metadata("design:type", String)
], PayrollItem.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PayrollItemCategory,
    }),
    __metadata("design:type", String)
], PayrollItem.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PayrollItem.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PayrollItem.prototype, "rate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PayrollItem.prototype, "hours", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PayrollItem.prototype, "percentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], PayrollItem.prototype, "isTaxable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PayrollItem.prototype, "isStatutory", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PayrollItem.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PayrollItem.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PayrollItem.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PayrollItem.prototype, "updatedAt", void 0);
exports.PayrollItem = PayrollItem = __decorate([
    (0, typeorm_1.Entity)('hr_payroll_items')
], PayrollItem);
//# sourceMappingURL=payroll-item.entity.js.map