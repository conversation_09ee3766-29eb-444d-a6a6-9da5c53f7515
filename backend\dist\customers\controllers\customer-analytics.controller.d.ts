import { CustomerAnalyticsService } from '../services/customer-analytics.service';
export declare class CustomerAnalyticsController {
    private readonly customerAnalyticsService;
    constructor(customerAnalyticsService: CustomerAnalyticsService);
    getCustomerOverview(): Promise<any>;
    getCustomerDistribution(): Promise<any>;
    getFinancialMetrics(): Promise<any>;
    getCustomerLifecycleMetrics(): Promise<any>;
    getInteractionAnalytics(): Promise<any>;
    getSegmentAnalytics(): Promise<any>;
    getDashboardMetrics(): Promise<any>;
    getCustomerHealthScore(customerId: string): Promise<any>;
}
