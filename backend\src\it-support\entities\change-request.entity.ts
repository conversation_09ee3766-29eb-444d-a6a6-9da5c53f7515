import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum ChangeType {
  STANDARD = 'standard',
  NORMAL = 'normal',
  EMERGENCY = 'emergency',
  MAJOR = 'major',
}

export enum ChangeStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum ChangeRisk {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

@Entity('change_requests')
export class ChangeRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  changeNumber: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: ChangeType,
    default: ChangeType.NORMAL,
  })
  type: ChangeType;

  @Column({
    type: 'enum',
    enum: ChangeStatus,
    default: ChangeStatus.DRAFT,
  })
  status: ChangeStatus;

  @Column({
    type: 'enum',
    enum: ChangeRisk,
    default: ChangeRisk.MEDIUM,
  })
  risk: ChangeRisk;

  @Column()
  requesterId: string;

  @Column({ length: 255 })
  requesterName: string;

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ type: 'text' })
  justification: string;

  @Column({ type: 'text', nullable: true })
  implementationPlan: string;

  @Column({ type: 'text', nullable: true })
  rollbackPlan: string;

  @Column({ type: 'text', nullable: true })
  testPlan: string;

  @Column({ type: 'timestamp', nullable: true })
  scheduledStart: Date;

  @Column({ type: 'timestamp', nullable: true })
  scheduledEnd: Date;

  @Column({ type: 'timestamp', nullable: true })
  actualStart: Date;

  @Column({ type: 'timestamp', nullable: true })
  actualEnd: Date;

  @Column({ type: 'json', nullable: true })
  affectedSystems: string[];

  @Column({ type: 'json', nullable: true })
  stakeholders: string[];

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'text', nullable: true })
  approvalNotes: string;

  @Column({ type: 'text', nullable: true })
  implementationNotes: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
