export declare enum ArticleStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived",
    UNDER_REVIEW = "under_review"
}
export declare enum ArticleType {
    HOW_TO = "how_to",
    TROUBLESHOOTING = "troubleshooting",
    FAQ = "faq",
    POLICY = "policy",
    PROCEDURE = "procedure",
    ANNOUNCEMENT = "announcement",
    TUTORIAL = "tutorial"
}
export declare class KnowledgeBase {
    id: string;
    title: string;
    content: string;
    summary: string;
    type: ArticleType;
    status: ArticleStatus;
    category: string;
    tags: string[];
    keywords: string[];
    authorId: string;
    reviewedBy: string;
    reviewedAt: Date;
    publishedAt: Date;
    viewCount: number;
    helpfulCount: number;
    notHelpfulCount: number;
    rating: number;
    attachments: string[];
    relatedArticles: string[];
    isFeatured: boolean;
    isInternal: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
