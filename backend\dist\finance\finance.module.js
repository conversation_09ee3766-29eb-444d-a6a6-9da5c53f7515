"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const account_entity_1 = require("./entities/account.entity");
const transaction_entity_1 = require("./entities/transaction.entity");
const budget_entity_1 = require("./entities/budget.entity");
const budget_item_entity_1 = require("./entities/budget-item.entity");
const expense_entity_1 = require("./entities/expense.entity");
const expense_category_entity_1 = require("./entities/expense-category.entity");
const financial_report_entity_1 = require("./entities/financial-report.entity");
const tax_record_entity_1 = require("./entities/tax-record.entity");
const bank_account_entity_1 = require("./entities/bank-account.entity");
const bank_transaction_entity_1 = require("./entities/bank-transaction.entity");
const account_service_1 = require("./services/account.service");
const transaction_service_1 = require("./services/transaction.service");
const budget_service_1 = require("./services/budget.service");
const expense_service_1 = require("./services/expense.service");
const financial_report_service_1 = require("./services/financial-report.service");
const tax_service_1 = require("./services/tax.service");
const bank_account_service_1 = require("./services/bank-account.service");
const account_controller_1 = require("./controllers/account.controller");
const transaction_controller_1 = require("./controllers/transaction.controller");
const budget_controller_1 = require("./controllers/budget.controller");
const expense_controller_1 = require("./controllers/expense.controller");
const financial_report_controller_1 = require("./controllers/financial-report.controller");
const tax_controller_1 = require("./controllers/tax.controller");
const bank_account_controller_1 = require("./controllers/bank-account.controller");
let FinanceModule = class FinanceModule {
};
exports.FinanceModule = FinanceModule;
exports.FinanceModule = FinanceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                account_entity_1.Account,
                transaction_entity_1.Transaction,
                budget_entity_1.Budget,
                budget_item_entity_1.BudgetItem,
                expense_entity_1.Expense,
                expense_category_entity_1.ExpenseCategory,
                financial_report_entity_1.FinancialReport,
                tax_record_entity_1.TaxRecord,
                bank_account_entity_1.BankAccount,
                bank_transaction_entity_1.BankTransaction,
            ]),
        ],
        controllers: [
            account_controller_1.AccountController,
            transaction_controller_1.TransactionController,
            budget_controller_1.BudgetController,
            expense_controller_1.ExpenseController,
            financial_report_controller_1.FinancialReportController,
            tax_controller_1.TaxController,
            bank_account_controller_1.BankAccountController,
        ],
        providers: [
            account_service_1.AccountService,
            transaction_service_1.TransactionService,
            budget_service_1.BudgetService,
            expense_service_1.ExpenseService,
            financial_report_service_1.FinancialReportService,
            tax_service_1.TaxService,
            bank_account_service_1.BankAccountService,
        ],
        exports: [
            account_service_1.AccountService,
            transaction_service_1.TransactionService,
            budget_service_1.BudgetService,
            expense_service_1.ExpenseService,
            financial_report_service_1.FinancialReportService,
            tax_service_1.TaxService,
            bank_account_service_1.BankAccountService,
        ],
    })
], FinanceModule);
//# sourceMappingURL=finance.module.js.map