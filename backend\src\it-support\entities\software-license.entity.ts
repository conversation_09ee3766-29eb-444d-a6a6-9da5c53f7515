import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum LicenseType {
  PERPETUAL = 'perpetual',
  SUBSCRIPTION = 'subscription',
  VOLUME = 'volume',
  OEM = 'oem',
  TRIAL = 'trial',
  FREEWARE = 'freeware',
  OPEN_SOURCE = 'open_source',
}

export enum LicenseStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  TRIAL = 'trial',
}

@Entity('software_licenses')
export class SoftwareLicense {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  softwareName: string;

  @Column({ length: 255, nullable: true })
  vendor: string;

  @Column({ length: 100, nullable: true })
  version: string;

  @Column({
    type: 'enum',
    enum: LicenseType,
  })
  type: LicenseType;

  @Column({
    type: 'enum',
    enum: LicenseStatus,
    default: LicenseStatus.ACTIVE,
  })
  status: LicenseStatus;

  @Column({ length: 255, nullable: true })
  licenseKey: string;

  @Column({ type: 'int' })
  totalLicenses: number;

  @Column({ type: 'int', default: 0 })
  usedLicenses: number;

  @Column({ type: 'int', default: 0 })
  availableLicenses: number;

  @Column({ type: 'date', nullable: true })
  purchaseDate: Date;

  @Column({ type: 'date', nullable: true })
  expiryDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  cost: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  annualCost: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ length: 255, nullable: true })
  purchaseOrder: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  assignedUsers: string[];

  @Column({ type: 'json', nullable: true })
  assignedAssets: string[];

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
