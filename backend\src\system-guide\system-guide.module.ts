import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Guide } from './entities/guide.entity';
import { GuideSection } from './entities/guide-section.entity';
import { GuideStep } from './entities/guide-step.entity';
import { Tutorial } from './entities/tutorial.entity';
import { TutorialStep } from './entities/tutorial-step.entity';
import { HelpArticle } from './entities/help-article.entity';
import { HelpCategory } from './entities/help-category.entity';
import { UserProgress } from './entities/user-progress.entity';
import { Tooltip } from './entities/tooltip.entity';
import { Announcement } from './entities/announcement.entity';
import { FeatureFlag } from './entities/feature-flag.entity';
import { OnboardingFlow } from './entities/onboarding-flow.entity';

// Services
import { GuideService } from './services/guide.service';
import { TutorialService } from './services/tutorial.service';
import { HelpArticleService } from './services/help-article.service';
import { UserProgressService } from './services/user-progress.service';
import { TooltipService } from './services/tooltip.service';
import { AnnouncementService } from './services/announcement.service';
import { FeatureFlagService } from './services/feature-flag.service';
import { OnboardingService } from './services/onboarding.service';

// Controllers
import { GuideController } from './controllers/guide.controller';
import { TutorialController } from './controllers/tutorial.controller';
import { HelpArticleController } from './controllers/help-article.controller';
import { UserProgressController } from './controllers/user-progress.controller';
import { TooltipController } from './controllers/tooltip.controller';
import { AnnouncementController } from './controllers/announcement.controller';
import { FeatureFlagController } from './controllers/feature-flag.controller';
import { OnboardingController } from './controllers/onboarding.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Guide,
      GuideSection,
      GuideStep,
      Tutorial,
      TutorialStep,
      HelpArticle,
      HelpCategory,
      UserProgress,
      Tooltip,
      Announcement,
      FeatureFlag,
      OnboardingFlow,
    ]),
  ],
  controllers: [
    GuideController,
    TutorialController,
    HelpArticleController,
    UserProgressController,
    TooltipController,
    AnnouncementController,
    FeatureFlagController,
    OnboardingController,
  ],
  providers: [
    GuideService,
    TutorialService,
    HelpArticleService,
    UserProgressService,
    TooltipService,
    AnnouncementService,
    FeatureFlagService,
    OnboardingService,
  ],
  exports: [
    GuideService,
    TutorialService,
    HelpArticleService,
    UserProgressService,
    TooltipService,
    AnnouncementService,
    FeatureFlagService,
    OnboardingService,
  ],
})
export class SystemGuideModule {}
