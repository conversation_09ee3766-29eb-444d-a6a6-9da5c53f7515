import { BankAccount } from './bank-account.entity';
export declare enum BankTransactionType {
    DEPOSIT = "deposit",
    WITHDRAWAL = "withdrawal",
    TRANSFER = "transfer",
    FEE = "fee",
    INTEREST = "interest",
    CHECK = "check",
    ACH = "ach",
    WIRE = "wire",
    ATM = "atm",
    DEBIT_CARD = "debit_card",
    CREDIT_CARD = "credit_card",
    OTHER = "other"
}
export declare enum BankTransactionStatus {
    PENDING = "pending",
    CLEARED = "cleared",
    CANCELLED = "cancelled",
    RETURNED = "returned",
    RECONCILED = "reconciled"
}
export declare class BankTransaction {
    id: string;
    bankAccountId: string;
    bankAccount: BankAccount;
    transactionId: string;
    type: BankTransactionType;
    status: BankTransactionStatus;
    transactionDate: Date;
    valueDate: Date;
    amount: number;
    runningBalance: number;
    description: string;
    reference: string;
    checkNumber: string;
    payee: string;
    category: string;
    relatedTransactionId: string;
    isReconciled: boolean;
    reconciledDate: Date;
    reconciledBy: string;
    bankData: any;
    memo: string;
    tags: string[];
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
