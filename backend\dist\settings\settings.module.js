"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const system_setting_entity_1 = require("./entities/system-setting.entity");
const user_setting_entity_1 = require("./entities/user-setting.entity");
const company_setting_entity_1 = require("./entities/company-setting.entity");
const email_template_entity_1 = require("./entities/email-template.entity");
const notification_template_entity_1 = require("./entities/notification-template.entity");
const workflow_template_entity_1 = require("./entities/workflow-template.entity");
const custom_field_entity_1 = require("./entities/custom-field.entity");
const custom_field_value_entity_1 = require("./entities/custom-field-value.entity");
const integration_entity_1 = require("./entities/integration.entity");
const api_key_entity_1 = require("./entities/api-key.entity");
const audit_log_entity_1 = require("./entities/audit-log.entity");
const system_backup_entity_1 = require("./entities/system-backup.entity");
const system_setting_service_1 = require("./services/system-setting.service");
const user_setting_service_1 = require("./services/user-setting.service");
const company_setting_service_1 = require("./services/company-setting.service");
const email_template_service_1 = require("./services/email-template.service");
const notification_service_1 = require("./services/notification.service");
const workflow_service_1 = require("./services/workflow.service");
const custom_field_service_1 = require("./services/custom-field.service");
const integration_service_1 = require("./services/integration.service");
const api_key_service_1 = require("./services/api-key.service");
const audit_log_service_1 = require("./services/audit-log.service");
const backup_service_1 = require("./services/backup.service");
const system_setting_controller_1 = require("./controllers/system-setting.controller");
const user_setting_controller_1 = require("./controllers/user-setting.controller");
const company_setting_controller_1 = require("./controllers/company-setting.controller");
const email_template_controller_1 = require("./controllers/email-template.controller");
const notification_controller_1 = require("./controllers/notification.controller");
const workflow_controller_1 = require("./controllers/workflow.controller");
const custom_field_controller_1 = require("./controllers/custom-field.controller");
const integration_controller_1 = require("./controllers/integration.controller");
const api_key_controller_1 = require("./controllers/api-key.controller");
const audit_log_controller_1 = require("./controllers/audit-log.controller");
const backup_controller_1 = require("./controllers/backup.controller");
let SettingsModule = class SettingsModule {
};
exports.SettingsModule = SettingsModule;
exports.SettingsModule = SettingsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                system_setting_entity_1.SystemSetting,
                user_setting_entity_1.UserSetting,
                company_setting_entity_1.CompanySetting,
                email_template_entity_1.EmailTemplate,
                notification_template_entity_1.NotificationTemplate,
                workflow_template_entity_1.WorkflowTemplate,
                custom_field_entity_1.CustomField,
                custom_field_value_entity_1.CustomFieldValue,
                integration_entity_1.Integration,
                api_key_entity_1.APIKey,
                audit_log_entity_1.AuditLog,
                system_backup_entity_1.SystemBackup,
            ]),
        ],
        controllers: [
            system_setting_controller_1.SystemSettingController,
            user_setting_controller_1.UserSettingController,
            company_setting_controller_1.CompanySettingController,
            email_template_controller_1.EmailTemplateController,
            notification_controller_1.NotificationController,
            workflow_controller_1.WorkflowController,
            custom_field_controller_1.CustomFieldController,
            integration_controller_1.IntegrationController,
            api_key_controller_1.APIKeyController,
            audit_log_controller_1.AuditLogController,
            backup_controller_1.BackupController,
        ],
        providers: [
            system_setting_service_1.SystemSettingService,
            user_setting_service_1.UserSettingService,
            company_setting_service_1.CompanySettingService,
            email_template_service_1.EmailTemplateService,
            notification_service_1.NotificationService,
            workflow_service_1.WorkflowService,
            custom_field_service_1.CustomFieldService,
            integration_service_1.IntegrationService,
            api_key_service_1.APIKeyService,
            audit_log_service_1.AuditLogService,
            backup_service_1.BackupService,
        ],
        exports: [
            system_setting_service_1.SystemSettingService,
            user_setting_service_1.UserSettingService,
            company_setting_service_1.CompanySettingService,
            email_template_service_1.EmailTemplateService,
            notification_service_1.NotificationService,
            workflow_service_1.WorkflowService,
            custom_field_service_1.CustomFieldService,
            integration_service_1.IntegrationService,
            api_key_service_1.APIKeyService,
            audit_log_service_1.AuditLogService,
            backup_service_1.BackupService,
        ],
    })
], SettingsModule);
//# sourceMappingURL=settings.module.js.map