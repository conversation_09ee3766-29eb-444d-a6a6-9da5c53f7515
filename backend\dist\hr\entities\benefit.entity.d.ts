import { EmployeeBenefit } from './employee-benefit.entity';
export declare enum BenefitType {
    HEALTH_INSURANCE = "health_insurance",
    DENTAL_INSURANCE = "dental_insurance",
    VISION_INSURANCE = "vision_insurance",
    LIFE_INSURANCE = "life_insurance",
    DISABILITY_INSURANCE = "disability_insurance",
    RETIREMENT_PLAN = "retirement_plan",
    PAID_TIME_OFF = "paid_time_off",
    SICK_LEAVE = "sick_leave",
    MATERNITY_LEAVE = "maternity_leave",
    PATERNITY_LEAVE = "paternity_leave",
    FLEXIBLE_SPENDING = "flexible_spending",
    COMMUTER_BENEFITS = "commuter_benefits",
    GYM_MEMBERSHIP = "gym_membership",
    EDUCATION_ASSISTANCE = "education_assistance",
    EMPLOYEE_DISCOUNT = "employee_discount",
    OTHER = "other"
}
export declare enum BenefitCategory {
    INSURANCE = "insurance",
    RETIREMENT = "retirement",
    TIME_OFF = "time_off",
    WELLNESS = "wellness",
    FINANCIAL = "financial",
    LIFESTYLE = "lifestyle",
    PROFESSIONAL_DEVELOPMENT = "professional_development"
}
export declare class Benefit {
    id: string;
    name: string;
    code: string;
    description: string;
    type: BenefitType;
    category: BenefitCategory;
    employerCost: number;
    employeeCost: number;
    employerContributionPercentage: number;
    employeeContributionPercentage: number;
    currency: string;
    isActive: boolean;
    isMandatory: boolean;
    isElective: boolean;
    waitingPeriodDays: number;
    eligibilityCriteria: any;
    coverageDetails: any;
    provider: string;
    providerContact: string;
    effectiveDate: Date;
    expiryDate: Date;
    employeeBenefits: EmployeeBenefit[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
