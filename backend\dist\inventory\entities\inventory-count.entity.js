"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryCount = exports.CountType = exports.CountStatus = void 0;
const typeorm_1 = require("typeorm");
const product_entity_1 = require("./product.entity");
const warehouse_entity_1 = require("./warehouse.entity");
var CountStatus;
(function (CountStatus) {
    CountStatus["SCHEDULED"] = "scheduled";
    CountStatus["IN_PROGRESS"] = "in_progress";
    CountStatus["COMPLETED"] = "completed";
    CountStatus["CANCELLED"] = "cancelled";
})(CountStatus || (exports.CountStatus = CountStatus = {}));
var CountType;
(function (CountType) {
    CountType["FULL"] = "full";
    CountType["PARTIAL"] = "partial";
    CountType["CYCLE"] = "cycle";
    CountType["SPOT"] = "spot";
})(CountType || (exports.CountType = CountType = {}));
let InventoryCount = class InventoryCount {
    id;
    countNumber;
    type;
    status;
    warehouseId;
    warehouse;
    productId;
    product;
    scheduledDate;
    startDate;
    completedDate;
    systemQuantity;
    countedQuantity;
    variance;
    varianceValue;
    notes;
    countedBy;
    verifiedBy;
    verifiedAt;
    adjustmentCreated;
    metadata;
    createdAt;
    updatedAt;
};
exports.InventoryCount = InventoryCount;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], InventoryCount.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], InventoryCount.prototype, "countNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CountType,
        default: CountType.FULL,
    }),
    __metadata("design:type", String)
], InventoryCount.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CountStatus,
        default: CountStatus.SCHEDULED,
    }),
    __metadata("design:type", String)
], InventoryCount.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], InventoryCount.prototype, "warehouseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => warehouse_entity_1.Warehouse),
    (0, typeorm_1.JoinColumn)({ name: 'warehouseId' }),
    __metadata("design:type", warehouse_entity_1.Warehouse)
], InventoryCount.prototype, "warehouse", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], InventoryCount.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.Product, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'productId' }),
    __metadata("design:type", product_entity_1.Product)
], InventoryCount.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], InventoryCount.prototype, "scheduledDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], InventoryCount.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], InventoryCount.prototype, "completedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], InventoryCount.prototype, "systemQuantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], InventoryCount.prototype, "countedQuantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], InventoryCount.prototype, "variance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], InventoryCount.prototype, "varianceValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], InventoryCount.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], InventoryCount.prototype, "countedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], InventoryCount.prototype, "verifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], InventoryCount.prototype, "verifiedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], InventoryCount.prototype, "adjustmentCreated", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], InventoryCount.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], InventoryCount.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], InventoryCount.prototype, "updatedAt", void 0);
exports.InventoryCount = InventoryCount = __decorate([
    (0, typeorm_1.Entity)('inventory_counts')
], InventoryCount);
//# sourceMappingURL=inventory-count.entity.js.map