"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RFQ = exports.RFQStatus = void 0;
const typeorm_1 = require("typeorm");
const rfq_response_entity_1 = require("./rfq-response.entity");
var RFQStatus;
(function (RFQStatus) {
    RFQStatus["DRAFT"] = "draft";
    RFQStatus["PUBLISHED"] = "published";
    RFQStatus["RESPONSES_RECEIVED"] = "responses_received";
    RFQStatus["EVALUATION_IN_PROGRESS"] = "evaluation_in_progress";
    RFQStatus["AWARDED"] = "awarded";
    RFQStatus["CANCELLED"] = "cancelled";
    RFQStatus["CLOSED"] = "closed";
})(RFQStatus || (exports.RFQStatus = RFQStatus = {}));
let RFQ = class RFQ {
    id;
    rfqNumber;
    title;
    description;
    status;
    publishDate;
    responseDeadline;
    awardDate;
    requirements;
    evaluationCriteria;
    invitedVendors;
    attachments;
    createdBy;
    awardedTo;
    awardReason;
    responses;
    metadata;
    createdAt;
    updatedAt;
};
exports.RFQ = RFQ;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RFQ.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], RFQ.prototype, "rfqNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], RFQ.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], RFQ.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RFQStatus,
        default: RFQStatus.DRAFT,
    }),
    __metadata("design:type", String)
], RFQ.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], RFQ.prototype, "publishDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], RFQ.prototype, "responseDeadline", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], RFQ.prototype, "awardDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], RFQ.prototype, "requirements", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], RFQ.prototype, "evaluationCriteria", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], RFQ.prototype, "invitedVendors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], RFQ.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RFQ.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RFQ.prototype, "awardedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RFQ.prototype, "awardReason", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => rfq_response_entity_1.RFQResponse, response => response.rfq),
    __metadata("design:type", Array)
], RFQ.prototype, "responses", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], RFQ.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], RFQ.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], RFQ.prototype, "updatedAt", void 0);
exports.RFQ = RFQ = __decorate([
    (0, typeorm_1.Entity)('rfqs')
], RFQ);
//# sourceMappingURL=rfq.entity.js.map