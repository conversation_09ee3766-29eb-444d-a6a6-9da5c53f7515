import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Training, TrainingStatus } from '../entities/training.entity';

@Injectable()
export class TrainingService {
  constructor(
    @InjectRepository(Training)
    private trainingRepository: Repository<Training>,
  ) {}

  async create(createTrainingDto: any): Promise<Training> {
    const training = this.trainingRepository.create(createTrainingDto);
    return this.trainingRepository.save(training);
  }

  async findAll(filters?: any): Promise<Training[]> {
    const queryBuilder = this.trainingRepository.createQueryBuilder('training')
      .leftJoinAndSelect('training.employee', 'employee');

    if (filters?.employeeId) {
      queryBuilder.andWhere('training.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters?.type) {
      queryBuilder.andWhere('training.type = :type', { type: filters.type });
    }

    if (filters?.status) {
      queryBuilder.andWhere('training.status = :status', { status: filters.status });
    }

    return queryBuilder
      .orderBy('training.startDate', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Training> {
    const training = await this.trainingRepository.findOne({
      where: { id },
      relations: ['employee'],
    });

    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }

    return training;
  }

  async update(id: string, updateTrainingDto: any): Promise<Training> {
    const training = await this.findOne(id);
    Object.assign(training, updateTrainingDto);
    return this.trainingRepository.save(training);
  }

  async completeTraining(id: string, score?: number, feedback?: string): Promise<Training> {
    const training = await this.findOne(id);
    training.status = TrainingStatus.COMPLETED;
    training.completionDate = new Date();
    
    if (score !== undefined) {
      training.score = score;
      training.isPassed = training.passingScore ? score >= training.passingScore : true;
    }
    
    if (feedback) {
      training.feedback = feedback;
    }

    return this.trainingRepository.save(training);
  }
}
