import { CollectionCase } from './collection-case.entity';
export declare enum DisputeReason {
    AMOUNT_INCORRECT = "amount_incorrect",
    ALREADY_PAID = "already_paid",
    NOT_MY_DEBT = "not_my_debt",
    IDENTITY_THEFT = "identity_theft",
    STATUTE_OF_LIMITATIONS = "statute_of_limitations",
    BANKRUPTCY = "bankruptcy",
    DECEASED = "deceased",
    QUALITY_ISSUE = "quality_issue",
    SERVICE_NOT_RECEIVED = "service_not_received",
    BILLING_ERROR = "billing_error",
    OTHER = "other"
}
export declare enum DisputeStatus {
    OPEN = "open",
    INVESTIGATING = "investigating",
    RESOLVED = "resolved",
    REJECTED = "rejected",
    ESCALATED = "escalated",
    CLOSED = "closed"
}
export declare class CollectionDispute {
    id: string;
    caseId: string;
    case: CollectionCase;
    disputeNumber: string;
    reason: DisputeReason;
    status: DisputeStatus;
    description: string;
    disputedAmount: number;
    disputeDate: Date;
    responseDate: Date;
    resolutionDate: Date;
    customerEvidence: string;
    companyResponse: string;
    resolution: string;
    assignedTo: string;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
