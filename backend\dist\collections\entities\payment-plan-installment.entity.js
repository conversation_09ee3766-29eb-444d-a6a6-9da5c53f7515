"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentPlanInstallment = exports.InstallmentStatus = void 0;
const typeorm_1 = require("typeorm");
const payment_plan_entity_1 = require("./payment-plan.entity");
var InstallmentStatus;
(function (InstallmentStatus) {
    InstallmentStatus["PENDING"] = "pending";
    InstallmentStatus["PAID"] = "paid";
    InstallmentStatus["OVERDUE"] = "overdue";
    InstallmentStatus["PARTIAL"] = "partial";
    InstallmentStatus["SKIPPED"] = "skipped";
    InstallmentStatus["WAIVED"] = "waived";
})(InstallmentStatus || (exports.InstallmentStatus = InstallmentStatus = {}));
let PaymentPlanInstallment = class PaymentPlanInstallment {
    id;
    paymentPlanId;
    paymentPlan;
    installmentNumber;
    dueDate;
    amount;
    paidAmount;
    remainingAmount;
    status;
    paidDate;
    daysOverdue;
    lateFee;
    notes;
    paymentDetails;
    metadata;
    createdAt;
    updatedAt;
};
exports.PaymentPlanInstallment = PaymentPlanInstallment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PaymentPlanInstallment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PaymentPlanInstallment.prototype, "paymentPlanId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => payment_plan_entity_1.PaymentPlan, plan => plan.installments, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'paymentPlanId' }),
    __metadata("design:type", payment_plan_entity_1.PaymentPlan)
], PaymentPlanInstallment.prototype, "paymentPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], PaymentPlanInstallment.prototype, "installmentNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], PaymentPlanInstallment.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PaymentPlanInstallment.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlanInstallment.prototype, "paidAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlanInstallment.prototype, "remainingAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: InstallmentStatus,
        default: InstallmentStatus.PENDING,
    }),
    __metadata("design:type", String)
], PaymentPlanInstallment.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], PaymentPlanInstallment.prototype, "paidDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PaymentPlanInstallment.prototype, "daysOverdue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PaymentPlanInstallment.prototype, "lateFee", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PaymentPlanInstallment.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PaymentPlanInstallment.prototype, "paymentDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PaymentPlanInstallment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PaymentPlanInstallment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PaymentPlanInstallment.prototype, "updatedAt", void 0);
exports.PaymentPlanInstallment = PaymentPlanInstallment = __decorate([
    (0, typeorm_1.Entity)('payment_plan_installments')
], PaymentPlanInstallment);
//# sourceMappingURL=payment-plan-installment.entity.js.map