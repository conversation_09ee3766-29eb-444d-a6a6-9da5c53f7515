"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanySetting = exports.CompanySettingType = void 0;
const typeorm_1 = require("typeorm");
var CompanySettingType;
(function (CompanySettingType) {
    CompanySettingType["GENERAL"] = "general";
    CompanySettingType["BRANDING"] = "branding";
    CompanySettingType["BUSINESS"] = "business";
    CompanySettingType["FINANCIAL"] = "financial";
    CompanySettingType["LOCALIZATION"] = "localization";
    CompanySettingType["INTEGRATION"] = "integration";
    CompanySettingType["WORKFLOW"] = "workflow";
    CompanySettingType["SECURITY"] = "security";
})(CompanySettingType || (exports.CompanySettingType = CompanySettingType = {}));
let CompanySetting = class CompanySetting {
    id;
    tenantId;
    key;
    value;
    type;
    name;
    description;
    isEncrypted;
    lastModifiedBy;
    metadata;
    createdAt;
    updatedAt;
};
exports.CompanySetting = CompanySetting;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CompanySetting.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CompanySetting.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CompanySetting.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CompanySetting.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CompanySettingType,
        default: CompanySettingType.GENERAL,
    }),
    __metadata("design:type", String)
], CompanySetting.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], CompanySetting.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CompanySetting.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CompanySetting.prototype, "isEncrypted", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CompanySetting.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CompanySetting.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CompanySetting.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CompanySetting.prototype, "updatedAt", void 0);
exports.CompanySetting = CompanySetting = __decorate([
    (0, typeorm_1.Entity)('company_settings')
], CompanySetting);
//# sourceMappingURL=company-setting.entity.js.map