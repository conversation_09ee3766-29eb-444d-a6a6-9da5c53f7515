import { Repository } from 'typeorm';
import { FinancialReport } from '../entities/financial-report.entity';
import { AccountService } from './account.service';
import { TransactionService } from './transaction.service';
export declare class FinancialReportService {
    private reportRepository;
    private accountService;
    private transactionService;
    constructor(reportRepository: Repository<FinancialReport>, accountService: AccountService, transactionService: TransactionService);
    create(createReportDto: any): Promise<FinancialReport>;
    findAll(filters?: any): Promise<FinancialReport[]>;
    findOne(id: string): Promise<FinancialReport>;
    generateBalanceSheet(startDate: Date, endDate: Date): Promise<any>;
    generateIncomeStatement(startDate: Date, endDate: Date): Promise<any>;
    generateCashFlowStatement(startDate: Date, endDate: Date): Promise<any>;
    scheduleReport(reportConfig: any): Promise<FinancialReport>;
    private generateReportData;
}
