import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Account } from './account.entity';

export enum TransactionType {
  JOURNAL_ENTRY = 'journal_entry',
  PAYMENT = 'payment',
  RECEIPT = 'receipt',
  TRANSFER = 'transfer',
  ADJUSTMENT = 'adjustment',
  INVOICE = 'invoice',
  BILL = 'bill',
  EXPENSE = 'expense',
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
}

export enum TransactionStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  POSTED = 'posted',
  CANCELLED = 'cancelled',
  REVERSED = 'reversed',
}

@Entity('finance_transactions')
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  transactionNumber: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type: TransactionType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.DRAFT,
  })
  status: TransactionStatus;

  @Column({ type: 'date' })
  transactionDate: Date;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text', nullable: true })
  reference: string;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column()
  debitAccountId: string;

  @ManyToOne(() => Account, account => account.debitTransactions)
  @JoinColumn({ name: 'debitAccountId' })
  debitAccount: Account;

  @Column()
  creditAccountId: string;

  @ManyToOne(() => Account, account => account.creditTransactions)
  @JoinColumn({ name: 'creditAccountId' })
  creditAccount: Account;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 1 })
  exchangeRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  baseCurrencyAmount: number;

  @Column({ nullable: true })
  relatedEntityType: string; // 'invoice', 'bill', 'payment', etc.

  @Column({ nullable: true })
  relatedEntityId: string;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
