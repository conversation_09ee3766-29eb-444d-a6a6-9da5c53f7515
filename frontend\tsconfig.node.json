{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Type checking */
    "types": [],
    "skipDefaultLibCheck": true,
    "typeRoots": ["./src/types"],
    "noResolve": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,

    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedSideEffectImports": false
  },
  "include": ["vite.config.ts", "src/types/global.d.ts", "src/types/external-libs.d.ts"],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "**/node_modules/conventional-commits-parser/**",
    "**/node_modules/long/**",
    "**/node_modules/offscreencanvas/**",
    "**/node_modules/@types/conventional-commits-parser/**",
    "**/node_modules/@types/long/**",
    "**/node_modules/@types/offscreencanvas/**"
  ]
}
