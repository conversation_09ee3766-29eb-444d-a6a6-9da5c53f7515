import { CustomerInteractionService } from '../services/customer-interaction.service';
import { CustomerInteraction } from '../entities/customer-interaction.entity';
export declare class CustomerInteractionController {
    private readonly customerInteractionService;
    constructor(customerInteractionService: CustomerInteractionService);
    create(createInteractionDto: Partial<CustomerInteraction>): Promise<CustomerInteraction>;
    findAll(customerId?: string, type?: string, channel?: string, startDate?: string, endDate?: string, page?: string, limit?: string): Promise<{
        interactions: CustomerInteraction[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    getStatistics(customerId?: string): Promise<any>;
    getUpcomingFollowUps(days?: string): Promise<CustomerInteraction[]>;
    findByCustomer(customerId: string): Promise<CustomerInteraction[]>;
    getCustomerTimeline(customerId: string, days?: string): Promise<CustomerInteraction[]>;
    getCustomerInteractionSummary(customerId: string): Promise<any>;
    findOne(id: string): Promise<CustomerInteraction>;
    logCall(callData: {
        customerId: string;
        duration?: number;
        outcome?: string;
        notes?: string;
        followUpRequired?: boolean;
        followUpDate?: Date;
        contactedBy?: string;
    }): Promise<CustomerInteraction>;
    logEmail(emailData: {
        customerId: string;
        subject?: string;
        outcome?: string;
        notes?: string;
        followUpRequired?: boolean;
        followUpDate?: Date;
        contactedBy?: string;
    }): Promise<CustomerInteraction>;
    logMeeting(meetingData: {
        customerId: string;
        location?: string;
        duration?: number;
        outcome?: string;
        notes?: string;
        followUpRequired?: boolean;
        followUpDate?: Date;
        contactedBy?: string;
    }): Promise<CustomerInteraction>;
    update(id: string, updateInteractionDto: Partial<CustomerInteraction>): Promise<CustomerInteraction>;
    completeFollowUp(id: string, followUpData: {
        notes?: string;
    }): Promise<CustomerInteraction>;
    remove(id: string): Promise<void>;
}
