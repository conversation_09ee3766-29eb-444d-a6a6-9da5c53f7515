import { AuditReport } from './audit-report.entity';
import { User } from '../../user/entities/user.entity';
export declare class AuditFinding {
    id: string;
    auditReportId: string;
    category: string;
    subcategory: string;
    severity: string;
    title: string;
    description: string;
    criteria: string;
    condition: string;
    cause: string;
    effect: string;
    recommendation: string;
    managementResponse: string;
    correctiveAction: string;
    status: string;
    department: string;
    processArea: string;
    assignedTo: string;
    dueDate: Date;
    resolvedDate: Date;
    verifiedDate: Date;
    riskRating: {
        likelihood: string;
        impact: string;
        overallRisk: string;
    };
    evidence: {
        type: string;
        description: string;
        reference: string;
        attachments?: string[];
    }[];
    rootCauseAnalysis: {
        method: string;
        primaryCause: string;
        contributingFactors: string[];
        systemicIssues: string[];
    };
    remediation: {
        immediateActions: string[];
        longTermActions: string[];
        preventiveControls: string[];
        monitoringPlan: string;
    };
    financialImpact: number;
    complianceReferences: {
        standard: string;
        section: string;
        requirement: string;
    }[];
    recurrenceCount: number;
    lastOccurrence: Date;
    relatedFindings: {
        findingId: string;
        relationship: string;
        description: string;
    }[];
    followUpActions: {
        action: string;
        responsible: string;
        dueDate: Date;
        status: string;
        completedDate?: Date;
    }[];
    auditorsNotes: string;
    managementNotes: string;
    createdBy: string;
    updatedBy: string;
    createdDate: Date;
    updatedDate: Date;
    auditReport: AuditReport;
    creator: User;
    updater: User;
    assignee: User;
}
