/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import './engine';
import * as device_util from './device_util';
import { env } from './environment';
const ENV = env();
/**
 * This file contains environment-related flag registrations.
 */
/** Whether to enable debug mode. */
ENV.registerFlag('DEBUG', () => false, debugValue => {
    if (debugValue) {
        console.warn('Debugging mode is ON. The output of every math call will ' +
            'be downloaded to CPU and checked for NaNs. ' +
            'This significantly impacts performance.');
    }
});
/** Whether we are in a browser (as versus, say, node.js) environment. */
ENV.registerFlag('IS_BROWSER', () => device_util.isBrowser());
/** Whether we are in a browser (as versus, say, node.js) environment. */
ENV.registerFlag('IS_NODE', () => (typeof process !== 'undefined') &&
    (typeof process.versions !== 'undefined') &&
    (typeof process.versions.node !== 'undefined'));
/** Whether this browser is Chrome. */
ENV.registerFlag('IS_CHROME', () => typeof navigator !== 'undefined' && navigator != null &&
    navigator.userAgent != null && /Chrome/.test(navigator.userAgent) &&
    /Google Inc/.test(navigator.vendor));
/** Whether this browser is Safari. */
ENV.registerFlag('IS_SAFARI', () => typeof navigator !== 'undefined' && navigator != null &&
    navigator.userAgent != null && /Safari/.test(navigator.userAgent) &&
    /Apple/.test(navigator.vendor));
/**
 * True when the environment is "production" where we disable safety checks
 * to gain performance.
 */
ENV.registerFlag('PROD', () => false);
/**
 * Whether to do sanity checks when inferring a shape from user-provided
 * values, used when creating a new tensor.
 */
ENV.registerFlag('TENSORLIKE_CHECK_SHAPE_CONSISTENCY', () => ENV.getBool('DEBUG'));
/** Whether deprecation warnings are enabled. */
ENV.registerFlag('DEPRECATION_WARNINGS_ENABLED', () => true);
/** True if running unit tests. */
ENV.registerFlag('IS_TEST', () => false);
/** Whether to check computation result for errors. */
ENV.registerFlag('CHECK_COMPUTATION_FOR_ERRORS', () => ENV.getBool('DEBUG'));
/** Whether the backend needs to wrap input to imageBitmap. */
ENV.registerFlag('WRAP_TO_IMAGEBITMAP', () => false);
/** Whether to enable canvas2d willReadFrequently for GPU backends */
ENV.registerFlag('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU', () => false);
/** Whether to use setTimeoutCustom */
ENV.registerFlag('USE_SETTIMEOUTCUSTOM', () => false);
//# sourceMappingURL=data:application/json;base64,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