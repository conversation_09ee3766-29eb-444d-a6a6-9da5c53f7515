export declare enum TooltipType {
    HELP = "help",
    FEATURE_HIGHLIGHT = "feature_highlight",
    WARNING = "warning",
    TIP = "tip",
    ONBOARDING = "onboarding",
    ANNOUNCEMENT = "announcement"
}
export declare enum TooltipPosition {
    TOP = "top",
    BOTTOM = "bottom",
    LEFT = "left",
    RIGHT = "right",
    AUTO = "auto"
}
export declare class Tooltip {
    id: string;
    title: string;
    content: string;
    type: TooltipType;
    targetElement: string;
    targetPage: string;
    position: TooltipPosition;
    isActive: boolean;
    isDismissible: boolean;
    autoHideDelay: number;
    targetRoles: string[];
    conditions: any;
    priority: number;
    startDate: Date;
    endDate: Date;
    createdBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
