{"version": 3, "file": "business-metrics.service.js", "sourceRoot": "", "sources": ["../../../src/analytics/services/business-metrics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,0EAAgE;AAChE,wEAA8D;AAGvD,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGvB;IAEA;IAJV,YAEU,kBAAwC,EAExC,iBAAsC;QAFtC,uBAAkB,GAAlB,kBAAkB,CAAsB;QAExC,sBAAiB,GAAjB,iBAAiB,CAAqB;IAC7C,CAAC;IAEJ,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,SAAiB,KAAK;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACtC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAG9C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAGxF,MAAM,iBAAiB,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAC;QAGhG,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC;QACtG,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,cAAc,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;QAC3G,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;QAE/F,OAAO;YACL,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,aAAa,EAAE,cAAc,CAAC,aAAa;YAC3C,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,cAAc,EAAE,cAAc,CAAC,cAAc;YAC7C,aAAa,EAAE,cAAc,CAAC,aAAa;YAC3C,cAAc,EAAE,cAAc,CAAC,cAAc;YAC7C,aAAa;YACb,cAAc;YACd,YAAY;YACZ,aAAa,EAAE,IAAI;YACnB,iBAAiB,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC;SACtE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,SAAiB,KAAK;QAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACtC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAE9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB;aAC1C,kBAAkB,CAAC,SAAS,CAAC;aAC7B,KAAK,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,CAAC;aACtD,QAAQ,CAAC,iCAAiC,EAAE,EAAE,SAAS,EAAE,CAAC;aAC1D,QAAQ,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;aACxD,OAAO,EAAE,CAAC;QAEb,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QACtC,MAAM,mBAAmB,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,OAAO;YACL,YAAY;YACZ,aAAa;YACb,mBAAmB;YACnB,cAAc,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC;SAC9D,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAiB,EAAE,SAAiB,KAAK;QAGlE,OAAO;YACL;gBACE,UAAU,EAAE,OAAO;gBACnB,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,IAAI;aACZ;YACD;gBACE,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,IAAI;aACZ;YACD;gBACE,UAAU,EAAE,WAAW;gBACvB,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,QAAQ;aAChB;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,SAAiB,KAAK;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAG9C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QAEvF,OAAO;YACL,kBAAkB,EAAE,UAAU,CAAC,aAAa;YAC5C,mBAAmB,EAAE,UAAU,CAAC,cAAc;YAC9C,iBAAiB,EAAE,UAAU,CAAC,YAAY;YAC1C,kBAAkB,EAAE,UAAU,CAAC,YAAY;SAC5C,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,SAAe,EAAE,OAAa;QAEjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB;aAC1C,kBAAkB,CAAC,SAAS,CAAC;aAC7B,KAAK,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,CAAC;aACtD,QAAQ,CAAC,iCAAiC,EAAE,EAAE,SAAS,EAAE,CAAC;aAC1D,QAAQ,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,CAAC;aACtD,QAAQ,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;aACxD,OAAO,EAAE,CAAC;QAEb,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAGrF,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB;aACjD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,KAAK,CAAC,iCAAiC,EAAE,EAAE,SAAS,EAAE,CAAC;aACvD,QAAQ,CAAC,gCAAgC,EAAE,EAAE,OAAO,EAAE,CAAC;aACvD,QAAQ,EAAE,CAAC;QAGd,MAAM,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC;QAC1C,MAAM,SAAS,GAAG,YAAY,GAAG,aAAa,CAAC;QAE/C,OAAO;YACL,YAAY;YACZ,aAAa;YACb,SAAS;YACT,cAAc;YACd,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,GAAG;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,IAAY;QAE7D,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACpC,MAAM,WAAW,GAA4C,EAAE,CAAC;QAEhE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,WAAW,CAAC,IAAI,CAAC;gBACf,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBACtF,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,MAAM;aACrD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,SAAe,EAAE,OAAa;QAEnF,OAAO;YACL,aAAa,EAAE,IAAI;YACnB,cAAc,EAAE,GAAG;YACnB,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE;gBACZ,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE;gBAClC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE;gBAClC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE;gBAClC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE;gBAClC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE;aACnC;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,MAAc;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAEtB,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEhC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC;YACrB,KAAK,GAAG,CAAC,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YAC1B,KAAK,GAAG,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;YAC3B,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,OAAe,EAAE,QAAgB;QACvD,IAAI,QAAQ,KAAK,CAAC;YAAE,OAAO,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,OAAO,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC;IACjD,CAAC;CACF,CAAA;AApMY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAQ,CAAC,CAAA;IAE1B,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;qCADE,oBAAU;QAEX,oBAAU;GAL5B,sBAAsB,CAoMlC"}