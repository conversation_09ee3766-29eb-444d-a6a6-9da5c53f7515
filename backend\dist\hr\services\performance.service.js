"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const performance_entity_1 = require("../entities/performance.entity");
let PerformanceService = class PerformanceService {
    performanceRepository;
    constructor(performanceRepository) {
        this.performanceRepository = performanceRepository;
    }
    async create(createPerformanceDto) {
        const performance = this.performanceRepository.create(createPerformanceDto);
        return this.performanceRepository.save(performance);
    }
    async findAll(filters) {
        const queryBuilder = this.performanceRepository.createQueryBuilder('performance')
            .leftJoinAndSelect('performance.employee', 'employee');
        if (filters?.employeeId) {
            queryBuilder.andWhere('performance.employeeId = :employeeId', { employeeId: filters.employeeId });
        }
        if (filters?.type) {
            queryBuilder.andWhere('performance.type = :type', { type: filters.type });
        }
        if (filters?.status) {
            queryBuilder.andWhere('performance.status = :status', { status: filters.status });
        }
        return queryBuilder
            .orderBy('performance.reviewPeriodStart', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const performance = await this.performanceRepository.findOne({
            where: { id },
            relations: ['employee'],
        });
        if (!performance) {
            throw new common_1.NotFoundException(`Performance review with ID ${id} not found`);
        }
        return performance;
    }
    async update(id, updatePerformanceDto) {
        const performance = await this.findOne(id);
        Object.assign(performance, updatePerformanceDto);
        return this.performanceRepository.save(performance);
    }
    async submitReview(id) {
        const performance = await this.findOne(id);
        performance.status = performance_entity_1.PerformanceStatus.COMPLETED;
        return this.performanceRepository.save(performance);
    }
    async approveReview(id, approvedBy) {
        const performance = await this.findOne(id);
        performance.status = performance_entity_1.PerformanceStatus.APPROVED;
        performance.approvedBy = approvedBy;
        performance.approvedAt = new Date();
        return this.performanceRepository.save(performance);
    }
};
exports.PerformanceService = PerformanceService;
exports.PerformanceService = PerformanceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(performance_entity_1.Performance)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PerformanceService);
//# sourceMappingURL=performance.service.js.map