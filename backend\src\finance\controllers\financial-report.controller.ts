import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { FinancialReportService } from '../services/financial-report.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/reports')
@UseGuards(JwtAuthGuard)
export class FinancialReportController {
  constructor(private readonly reportService: FinancialReportService) {}

  @Post()
  create(@Body() createReportDto: any) {
    return this.reportService.create(createReportDto);
  }

  @Get()
  findAll(@Query('type') type?: string, @Query('status') status?: string) {
    const filters: any = {};
    if (type) filters.type = type;
    if (status) filters.status = status;

    return this.reportService.findAll(filters);
  }

  @Get('balance-sheet')
  generateBalanceSheet(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    return this.reportService.generateBalanceSheet(new Date(startDate), new Date(endDate));
  }

  @Get('income-statement')
  generateIncomeStatement(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    return this.reportService.generateIncomeStatement(new Date(startDate), new Date(endDate));
  }

  @Get('cash-flow')
  generateCashFlowStatement(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    return this.reportService.generateCashFlowStatement(new Date(startDate), new Date(endDate));
  }

  @Post('schedule')
  scheduleReport(@Body() scheduleReportDto: any) {
    return this.reportService.scheduleReport(scheduleReportDto);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.reportService.findOne(id);
  }
}
