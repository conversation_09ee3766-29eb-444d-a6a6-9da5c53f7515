export declare enum TemplateType {
    WELCOME = "welcome",
    PASSWORD_RESET = "password_reset",
    INVOICE = "invoice",
    RECEIPT = "receipt",
    REMINDER = "reminder",
    NOTIFICATION = "notification",
    MARKETING = "marketing",
    SYSTEM = "system",
    CUSTOM = "custom"
}
export declare enum TemplateStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    ARCHIVED = "archived"
}
export declare class EmailTemplate {
    id: string;
    name: string;
    code: string;
    description: string;
    type: TemplateType;
    status: TemplateStatus;
    subject: string;
    htmlContent: string;
    textContent: string;
    fromEmail: string;
    fromName: string;
    replyTo: string;
    variables: string[];
    attachments: string[];
    isSystem: boolean;
    createdBy: string;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
