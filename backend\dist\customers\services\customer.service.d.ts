import { Repository } from 'typeorm';
import { Customer, CustomerStatus, CustomerType, CustomerTier } from '../entities/customer.entity';
import { CustomerContact } from '../entities/customer-contact.entity';
import { CustomerAddress } from '../entities/customer-address.entity';
import { CustomerNote } from '../entities/customer-note.entity';
export declare class CustomerService {
    private customerRepository;
    private contactRepository;
    private addressRepository;
    private noteRepository;
    constructor(customerRepository: Repository<Customer>, contactRepository: Repository<CustomerContact>, addressRepository: Repository<CustomerAddress>, noteRepository: Repository<CustomerNote>);
    create(customerData: Partial<Customer>): Promise<Customer>;
    findAll(options?: {
        page?: number;
        limit?: number;
        search?: string;
        status?: CustomerStatus;
        type?: CustomerType;
        tier?: CustomerTier;
        groupId?: string;
    }): Promise<{
        customers: Customer[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<Customer>;
    findByCustomerNumber(customerNumber: string): Promise<Customer>;
    update(id: string, updateData: Partial<Customer>): Promise<Customer>;
    remove(id: string): Promise<void>;
    updateStatus(id: string, status: CustomerStatus, reason?: string): Promise<Customer>;
    updateTier(id: string, tier: CustomerTier, reason?: string): Promise<Customer>;
    addContact(customerId: string, contactData: Partial<CustomerContact>): Promise<CustomerContact>;
    addAddress(customerId: string, addressData: Partial<CustomerAddress>): Promise<CustomerAddress>;
    addNote(customerId: string, content: string, type?: string, createdBy?: string): Promise<CustomerNote>;
    updateFinancials(id: string, financials: {
        creditLimit?: number;
        currentBalance?: number;
        totalSpent?: number;
        averageOrderValue?: number;
        paymentTerms?: number;
    }): Promise<Customer>;
    updateLoyaltyPoints(id: string, points: number, operation?: 'add' | 'subtract' | 'set'): Promise<Customer>;
    getCustomerStats(): Promise<any>;
    private generateCustomerNumber;
    private validateCustomerData;
    private isValidEmail;
}
