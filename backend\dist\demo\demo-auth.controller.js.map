{"version": 3, "file": "demo-auth.controller.js", "sourceRoot": "", "sources": ["../../src/demo/demo-auth.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiF;AACjF,qCAAyC;AACzC,mCAAmC;AAGnC,MAAM,SAAS,GAAU,EAAE,CAAC;AAC5B,MAAM,KAAK,GAAU,EAAE,CAAC;AAGjB,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IACT;IAApB,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAGxC,AAAN,KAAK,CAAC,eAAe,CAAS,WAAgB;QAC5C,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CACpC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,WAAW,CAC9E,CAAC;YAEF,IAAI,eAAe,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YAGD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,WAAW,CAAC,UAAU,CAAC,CAAC;YACzE,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YAGD,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC3B,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,WAAW,EAAE,WAAW,CAAC,kBAAkB;gBAC3C,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,KAAK,EAAE,WAAW,CAAC,YAAY;gBAC/B,KAAK,EAAE,WAAW,CAAC,YAAY;gBAC/B,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,QAAQ,EAAE,WAAW,CAAC,eAAe;gBACrC,QAAQ,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;gBAChC,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAGxB,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG;gBAChB,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBACxB,KAAK,EAAE,WAAW,CAAC,UAAU;gBAC7B,QAAQ,EAAE,cAAc;gBACxB,SAAS,EAAE,WAAW,CAAC,cAAc;gBACrC,QAAQ,EAAE,WAAW,CAAC,aAAa;gBACnC,KAAK,EAAE,WAAW,CAAC,UAAU;gBAC7B,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,QAAQ,EAAE,IAAI;gBACd,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAGtB,MAAM,OAAO,GAAG;gBACd,GAAG,EAAE,SAAS,CAAC,EAAE;gBACjB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC;YAEF,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE5C,OAAO;gBACL,IAAI,EAAE;oBACJ,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;oBAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,OAAO,EAAE;wBACP,EAAE,EAAE,OAAO,CAAC,EAAE;wBACd,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;qBAC3B;iBACF;gBACD,KAAK;aACN,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,qBAAqB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,KAAK,CAAS,QAAa;QAC/B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;YAEvE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE/E,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;YAED,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC;YAG7D,MAAM,OAAO,GAAG;gBACd,GAAG,EAAE,IAAI,CAAC,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC;YAEF,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE5C,OAAO;gBACL,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE;wBACP,EAAE,EAAE,OAAO,CAAC,EAAE;wBACd,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;qBAC3B;iBACF;gBACD,KAAK;aACN,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,cAAc,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,UAAU,CAAY,GAAG;QAE7B,OAAO;YACL,IAAI,EAAE;gBACJ,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,kBAAkB;gBACzB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,cAAc;gBACzB,QAAQ,EAAE,aAAa;aACxB;SACF,CAAC;IACJ,CAAC;IAGK,AAAN,KAAK,CAAC,MAAM;QACV,OAAO;YACL,OAAO,EAAE,yBAAyB;SACnC,CAAC;IACJ,CAAC;CACF,CAAA;AAhKY,gDAAkB;AAIvB;IADL,IAAA,aAAI,EAAC,kBAAkB,CAAC;IACF,WAAA,IAAA,aAAI,GAAE,CAAA;;;;yDAqF5B;AAGK;IADL,IAAA,aAAI,EAAC,OAAO,CAAC;IACD,WAAA,IAAA,aAAI,GAAE,CAAA;;;;+CA8ClB;AAGK;IADL,IAAA,YAAG,EAAC,SAAS,CAAC;IACG,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;oDAW1B;AAGK;IADL,IAAA,aAAI,EAAC,QAAQ,CAAC;;;;gDAKd;6BA/JU,kBAAkB;IAD9B,IAAA,mBAAU,EAAC,MAAM,CAAC;qCAEe,gBAAU;GAD/B,kBAAkB,CAgK9B"}