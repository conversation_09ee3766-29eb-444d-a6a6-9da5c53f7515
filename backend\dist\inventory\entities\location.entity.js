"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Location = exports.LocationType = void 0;
const typeorm_1 = require("typeorm");
const warehouse_entity_1 = require("./warehouse.entity");
const stock_entity_1 = require("./stock.entity");
var LocationType;
(function (LocationType) {
    LocationType["SHELF"] = "shelf";
    LocationType["BIN"] = "bin";
    LocationType["RACK"] = "rack";
    LocationType["FLOOR"] = "floor";
    LocationType["PALLET"] = "pallet";
    LocationType["COOLER"] = "cooler";
    LocationType["FREEZER"] = "freezer";
    LocationType["QUARANTINE"] = "quarantine";
    LocationType["STAGING"] = "staging";
    LocationType["RECEIVING"] = "receiving";
    LocationType["SHIPPING"] = "shipping";
})(LocationType || (exports.LocationType = LocationType = {}));
let Location = class Location {
    id;
    warehouseId;
    warehouse;
    name;
    code;
    description;
    type;
    aisle;
    bay;
    level;
    position;
    maxWeight;
    maxVolume;
    maxItems;
    isActive;
    isPickable;
    isReceivable;
    latitude;
    longitude;
    environmentalConditions;
    stocks;
    metadata;
    createdAt;
    updatedAt;
};
exports.Location = Location;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Location.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Location.prototype, "warehouseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => warehouse_entity_1.Warehouse, warehouse => warehouse.locations),
    (0, typeorm_1.JoinColumn)({ name: 'warehouseId' }),
    __metadata("design:type", warehouse_entity_1.Warehouse)
], Location.prototype, "warehouse", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], Location.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], Location.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Location.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: LocationType,
        default: LocationType.SHELF,
    }),
    __metadata("design:type", String)
], Location.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Location.prototype, "aisle", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Location.prototype, "bay", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Location.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Location.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Location.prototype, "maxWeight", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Location.prototype, "maxVolume", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], Location.prototype, "maxItems", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Location.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Location.prototype, "isPickable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Location.prototype, "isReceivable", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 6, nullable: true }),
    __metadata("design:type", Number)
], Location.prototype, "latitude", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 9, scale: 6, nullable: true }),
    __metadata("design:type", Number)
], Location.prototype, "longitude", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Location.prototype, "environmentalConditions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => stock_entity_1.Stock, stock => stock.location),
    __metadata("design:type", Array)
], Location.prototype, "stocks", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Location.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Location.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Location.prototype, "updatedAt", void 0);
exports.Location = Location = __decorate([
    (0, typeorm_1.Entity)('inventory_locations')
], Location);
//# sourceMappingURL=location.entity.js.map