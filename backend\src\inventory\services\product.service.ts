import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { Category } from '../entities/category.entity';
import { Stock } from '../entities/stock.entity';

@Injectable()
export class ProductService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Stock)
    private stockRepository: Repository<Stock>,
  ) {}

  async create(productData: Partial<Product>): Promise<Product> {
    const product = this.productRepository.create(productData);
    const savedProduct = await this.productRepository.save(product);

    // Create initial stock record
    if (productData.initialStock !== undefined) {
      await this.stockRepository.save({
        productId: savedProduct.id,
        quantity: productData.initialStock,
        reservedQuantity: 0,
        availableQuantity: productData.initialStock,
      });
    }

    return savedProduct;
  }

  async findAll(): Promise<Product[]> {
    return this.productRepository.find({
      relations: ['category', 'stocks'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: ['category', 'stocks', 'stocks.warehouse'],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }

  async update(id: string, updateData: Partial<Product>): Promise<Product> {
    await this.productRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const product = await this.findOne(id);
    await this.productRepository.remove(product);
  }

  async findByCategory(categoryId: string): Promise<Product[]> {
    return this.productRepository.find({
      where: { categoryId },
      relations: ['category', 'stocks'],
      order: { name: 'ASC' },
    });
  }

  async findBySku(sku: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { sku },
      relations: ['category', 'stocks'],
    });

    if (!product) {
      throw new NotFoundException(`Product with SKU ${sku} not found`);
    }

    return product;
  }

  async findByBarcode(barcode: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { barcode },
      relations: ['category', 'stocks'],
    });

    if (!product) {
      throw new NotFoundException(`Product with barcode ${barcode} not found`);
    }

    return product;
  }

  async searchProducts(searchTerm: string): Promise<Product[]> {
    return this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('product.stocks', 'stocks')
      .where('product.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('product.sku ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('product.barcode ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('product.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('product.name', 'ASC')
      .getMany();
  }

  async getLowStockProducts(threshold: number = 10): Promise<Product[]> {
    return this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.stocks', 'stocks')
      .leftJoinAndSelect('product.category', 'category')
      .where('stocks.availableQuantity <= :threshold', { threshold })
      .orderBy('stocks.availableQuantity', 'ASC')
      .getMany();
  }

  async getOutOfStockProducts(): Promise<Product[]> {
    return this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.stocks', 'stocks')
      .leftJoinAndSelect('product.category', 'category')
      .where('stocks.availableQuantity = 0')
      .orderBy('product.name', 'ASC')
      .getMany();
  }

  async updateStock(productId: string, warehouseId: string, quantity: number): Promise<void> {
    const stock = await this.stockRepository.findOne({
      where: { productId, warehouseId },
    });

    if (stock) {
      stock.quantity = quantity;
      stock.availableQuantity = quantity - stock.reservedQuantity;
      await this.stockRepository.save(stock);
    } else {
      await this.stockRepository.save({
        productId,
        warehouseId,
        quantity,
        reservedQuantity: 0,
        availableQuantity: quantity,
      });
    }
  }

  async reserveStock(productId: string, warehouseId: string, quantity: number): Promise<boolean> {
    const stock = await this.stockRepository.findOne({
      where: { productId, warehouseId },
    });

    if (!stock || stock.availableQuantity < quantity) {
      return false;
    }

    stock.reservedQuantity += quantity;
    stock.availableQuantity -= quantity;
    await this.stockRepository.save(stock);
    return true;
  }

  async releaseStock(productId: string, warehouseId: string, quantity: number): Promise<void> {
    const stock = await this.stockRepository.findOne({
      where: { productId, warehouseId },
    });

    if (stock) {
      stock.reservedQuantity = Math.max(0, stock.reservedQuantity - quantity);
      stock.availableQuantity = stock.quantity - stock.reservedQuantity;
      await this.stockRepository.save(stock);
    }
  }

  async getProductStatistics(): Promise<any> {
    const totalProducts = await this.productRepository.count();
    const activeProducts = await this.productRepository.count({ where: { isActive: true } });
    const lowStockProducts = await this.getLowStockProducts();
    const outOfStockProducts = await this.getOutOfStockProducts();

    const totalValue = await this.productRepository
      .createQueryBuilder('product')
      .leftJoin('product.stocks', 'stocks')
      .select('SUM(product.costPrice * stocks.quantity)', 'totalValue')
      .getRawOne();

    return {
      totalProducts,
      activeProducts,
      inactiveProducts: totalProducts - activeProducts,
      lowStockCount: lowStockProducts.length,
      outOfStockCount: outOfStockProducts.length,
      totalInventoryValue: parseFloat(totalValue.totalValue) || 0,
    };
  }

  async getTopSellingProducts(limit: number = 10): Promise<Product[]> {
    // This would require sales data integration
    // For now, return products ordered by name
    return this.productRepository.find({
      relations: ['category', 'stocks'],
      order: { name: 'ASC' },
      take: limit,
    });
  }

  async bulkUpdatePrices(updates: Array<{ id: string; salePrice: number; costPrice?: number }>): Promise<void> {
    for (const update of updates) {
      await this.productRepository.update(update.id, {
        salePrice: update.salePrice,
        ...(update.costPrice && { costPrice: update.costPrice }),
      });
    }
  }

  async generateSku(categoryCode: string): Promise<string> {
    const count = await this.productRepository.count();
    const sequence = (count + 1).toString().padStart(4, '0');
    return `${categoryCode}-${sequence}`;
  }
}
