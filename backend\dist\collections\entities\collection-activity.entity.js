"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionActivity = exports.ActivityResult = exports.ActivityType = void 0;
const typeorm_1 = require("typeorm");
const collection_case_entity_1 = require("./collection-case.entity");
var ActivityType;
(function (ActivityType) {
    ActivityType["CALL"] = "call";
    ActivityType["EMAIL"] = "email";
    ActivityType["LETTER"] = "letter";
    ActivityType["SMS"] = "sms";
    ActivityType["VISIT"] = "visit";
    ActivityType["PAYMENT"] = "payment";
    ActivityType["PROMISE_TO_PAY"] = "promise_to_pay";
    ActivityType["DISPUTE"] = "dispute";
    ActivityType["LEGAL_ACTION"] = "legal_action";
    ActivityType["SETTLEMENT"] = "settlement";
    ActivityType["WRITE_OFF"] = "write_off";
    ActivityType["NOTE"] = "note";
    ActivityType["STATUS_CHANGE"] = "status_change";
})(ActivityType || (exports.ActivityType = ActivityType = {}));
var ActivityResult;
(function (ActivityResult) {
    ActivityResult["SUCCESSFUL"] = "successful";
    ActivityResult["NO_ANSWER"] = "no_answer";
    ActivityResult["BUSY"] = "busy";
    ActivityResult["DISCONNECTED"] = "disconnected";
    ActivityResult["WRONG_NUMBER"] = "wrong_number";
    ActivityResult["LEFT_MESSAGE"] = "left_message";
    ActivityResult["PROMISED_TO_PAY"] = "promised_to_pay";
    ActivityResult["DISPUTED"] = "disputed";
    ActivityResult["REFUSED_TO_PAY"] = "refused_to_pay";
    ActivityResult["PAYMENT_RECEIVED"] = "payment_received";
    ActivityResult["CALLBACK_REQUESTED"] = "callback_requested";
    ActivityResult["OTHER"] = "other";
})(ActivityResult || (exports.ActivityResult = ActivityResult = {}));
let CollectionActivity = class CollectionActivity {
    id;
    caseId;
    case;
    type;
    result;
    activityDate;
    duration;
    description;
    outcome;
    performedBy;
    contactMethod;
    contactNumber;
    paymentAmount;
    promiseDate;
    promiseAmount;
    followUpDate;
    followUpAction;
    isAutomated;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionActivity = CollectionActivity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionActivity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionActivity.prototype, "caseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_case_entity_1.CollectionCase, collectionCase => collectionCase.activities),
    (0, typeorm_1.JoinColumn)({ name: 'caseId' }),
    __metadata("design:type", collection_case_entity_1.CollectionCase)
], CollectionActivity.prototype, "case", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ActivityType,
    }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ActivityResult,
        nullable: true,
    }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], CollectionActivity.prototype, "activityDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], CollectionActivity.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "outcome", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionActivity.prototype, "performedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "contactMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "contactNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CollectionActivity.prototype, "paymentAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionActivity.prototype, "promiseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CollectionActivity.prototype, "promiseAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionActivity.prototype, "followUpDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionActivity.prototype, "followUpAction", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionActivity.prototype, "isAutomated", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CollectionActivity.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionActivity.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionActivity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionActivity.prototype, "updatedAt", void 0);
exports.CollectionActivity = CollectionActivity = __decorate([
    (0, typeorm_1.Entity)('collection_activities')
], CollectionActivity);
//# sourceMappingURL=collection-activity.entity.js.map