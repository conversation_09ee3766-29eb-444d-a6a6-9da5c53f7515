import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_prolog=__commonJS({"../../node_modules/highlight.js/lib/languages/prolog.js"(exports,module){function prolog(hljs){let ATOM={begin:/[a-z][A-Za-z0-9_]*/,relevance:0},VAR={className:"symbol",variants:[{begin:/[A-Z][a-zA-Z0-9_]*/},{begin:/_[A-Za-z0-9_]*/}],relevance:0},PARENTED={begin:/\(/,end:/\)/,relevance:0},LIST={begin:/\[/,end:/\]/},LINE_COMMENT={className:"comment",begin:/%/,end:/$/,contains:[hljs.PHRASAL_WORDS_MODE]},BACKTICK_STRING={className:"string",begin:/`/,end:/`/,contains:[hljs.BACKSLASH_ESCAPE]},CHAR_CODE={className:"string",begin:/0'(\\'|.)/},SPACE_CODE={className:"string",begin:/0'\\s/},inner=[ATOM,VAR,PARENTED,{begin:/:-/},LIST,LINE_COMMENT,hljs.C_BLOCK_COMMENT_MODE,hljs.QUOTE_STRING_MODE,hljs.APOS_STRING_MODE,BACKTICK_STRING,CHAR_CODE,SPACE_CODE,hljs.C_NUMBER_MODE];return PARENTED.contains=inner,LIST.contains=inner,{name:"Prolog",contains:inner.concat([{begin:/\.$/}])}}module.exports=prolog;}});var prologYHPZKJLJ = require_prolog();

export { prologYHPZKJLJ as default };
