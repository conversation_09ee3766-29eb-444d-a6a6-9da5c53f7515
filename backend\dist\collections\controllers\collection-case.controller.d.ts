import { CollectionCaseService } from '../services/collection-case.service';
import { CollectionCase, CaseStatus } from '../entities/collection-case.entity';
export declare class CollectionCaseController {
    private readonly collectionCaseService;
    constructor(collectionCaseService: CollectionCaseService);
    create(createCaseDto: Partial<CollectionCase>): Promise<CollectionCase>;
    findAll(status?: CaseStatus): Promise<CollectionCase[]>;
    getStatistics(): Promise<any>;
    getDashboardMetrics(): Promise<any>;
    getOverdueCases(): Promise<CollectionCase[]>;
    getCasesByPriority(priority: string): Promise<CollectionCase[]>;
    findByCustomer(customerId: string): Promise<CollectionCase[]>;
    findOne(id: string): Promise<CollectionCase>;
    update(id: string, updateCaseDto: Partial<CollectionCase>): Promise<CollectionCase>;
    updateStatus(id: string, statusUpdate: {
        status: CaseStatus;
        notes?: string;
    }): Promise<CollectionCase>;
    assignAgent(id: string, assignment: {
        agentId: string;
    }): Promise<CollectionCase>;
    escalateCase(id: string, escalation: {
        reason: string;
    }): Promise<CollectionCase>;
    recordPayment(id: string, payment: {
        amount: number;
        paymentDate: Date;
        notes?: string;
    }): Promise<CollectionCase>;
    remove(id: string): Promise<void>;
}
