"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuideSection = void 0;
const typeorm_1 = require("typeorm");
const guide_entity_1 = require("./guide.entity");
const guide_step_entity_1 = require("./guide-step.entity");
let GuideSection = class GuideSection {
    id;
    guideId;
    guide;
    title;
    description;
    content;
    sortOrder;
    isVisible;
    steps;
    metadata;
    createdAt;
    updatedAt;
};
exports.GuideSection = GuideSection;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], GuideSection.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], GuideSection.prototype, "guideId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => guide_entity_1.Guide, guide => guide.sections, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'guideId' }),
    __metadata("design:type", guide_entity_1.Guide)
], GuideSection.prototype, "guide", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], GuideSection.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], GuideSection.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], GuideSection.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], GuideSection.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], GuideSection.prototype, "isVisible", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => guide_step_entity_1.GuideStep, step => step.section, { cascade: true }),
    __metadata("design:type", Array)
], GuideSection.prototype, "steps", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], GuideSection.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], GuideSection.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], GuideSection.prototype, "updatedAt", void 0);
exports.GuideSection = GuideSection = __decorate([
    (0, typeorm_1.Entity)('guide_sections')
], GuideSection);
//# sourceMappingURL=guide-section.entity.js.map