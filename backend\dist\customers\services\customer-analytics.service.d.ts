import { Repository } from 'typeorm';
import { Customer } from '../entities/customer.entity';
import { CustomerInteraction } from '../entities/customer-interaction.entity';
import { CustomerSegment } from '../entities/customer-segment.entity';
export declare class CustomerAnalyticsService {
    private customerRepository;
    private interactionRepository;
    private segmentRepository;
    constructor(customerRepository: Repository<Customer>, interactionRepository: Repository<CustomerInteraction>, segmentRepository: Repository<CustomerSegment>);
    getCustomerOverview(): Promise<any>;
    getCustomerDistribution(): Promise<any>;
    getFinancialMetrics(): Promise<any>;
    getCustomerLifecycleMetrics(): Promise<any>;
    getInteractionAnalytics(): Promise<any>;
    getSegmentAnalytics(): Promise<any>;
    getCustomerHealthScore(customerId: string): Promise<any>;
    private generateHealthRecommendations;
    getDashboardMetrics(): Promise<any>;
}
