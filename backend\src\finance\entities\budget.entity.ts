import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { BudgetItem } from './budget-item.entity';

export enum BudgetType {
  ANNUAL = 'annual',
  QUARTERLY = 'quarterly',
  MONTHLY = 'monthly',
  PROJECT = 'project',
  DEPARTMENT = 'department',
}

export enum BudgetStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  APPROVED = 'approved',
  CLOSED = 'closed',
  CANCELLED = 'cancelled',
}

@Entity('finance_budgets')
export class Budget {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: BudgetType,
  })
  type: BudgetType;

  @Column({
    type: 'enum',
    enum: BudgetStatus,
    default: BudgetStatus.DRAFT,
  })
  status: BudgetStatus;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalBudgetAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalActualAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalVariance: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  variancePercentage: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ nullable: true })
  departmentId: string;

  @Column({ nullable: true })
  projectId: string;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @OneToMany(() => BudgetItem, budgetItem => budgetItem.budget, { cascade: true })
  budgetItems: BudgetItem[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
