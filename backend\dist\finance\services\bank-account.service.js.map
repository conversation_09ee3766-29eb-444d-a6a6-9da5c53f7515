{"version": 3, "file": "bank-account.service.js", "sourceRoot": "", "sources": ["../../../src/finance/services/bank-account.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoF;AACpF,6CAAmD;AACnD,qCAAqC;AACrC,yEAAiF;AACjF,iFAA6F;AAGtF,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAGnB;IAEA;IAJV,YAEU,qBAA8C,EAE9C,yBAAsD;QAFtD,0BAAqB,GAArB,qBAAqB,CAAyB;QAE9C,8BAAyB,GAAzB,yBAAyB,CAA6B;IAC7D,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,oBAAyB;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACrC,KAAK,EAAE,EAAE,MAAM,EAAE,uCAAiB,CAAC,MAAM,EAAE;YAC3C,KAAK,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,cAAc,CAAC;SAC5B,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,oBAAyB;QAChD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,WAAW,CAAC,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,4BAAmB,CAAC,8CAA8C,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE,eAAoB;QAC9D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEtD,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;YACxD,GAAG,eAAe;YAClB,aAAa;SACd,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGhF,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAEjE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,aAAqB,EAAE,YAAmB;QACjE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACtD,MAAM,iBAAiB,GAAsB,EAAE,CAAC;QAEhD,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE,CAAC;YAE3C,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;gBACvE,KAAK,EAAE;oBACL,aAAa;oBACb,aAAa,EAAE,eAAe,CAAC,aAAa;iBAC7C;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;oBACxD,GAAG,eAAe;oBAClB,aAAa;oBACb,MAAM,EAAE,+CAAqB,CAAC,OAAO;iBACtC,CAAC,CAAC;gBAEH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAChF,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAGzC,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,aAAqB,EAAE,iBAAyB,EAAE,YAAoB;QAC3F,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEtD,WAAW,CAAC,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5C,WAAW,CAAC,qBAAqB,GAAG,iBAAiB,CAAC;QAGtD,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CACzC;YACE,aAAa;YACb,MAAM,EAAE,+CAAqB,CAAC,OAAO;YACrC,YAAY,EAAE,KAAK;SACpB,EACD;YACE,YAAY,EAAE,IAAI;YAClB,cAAc,EAAE,IAAI,IAAI,EAAE;YAC1B,YAAY;SACb,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,aAAqB,EAAE,SAAgB,EAAE,OAAc;QAC/E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEtD,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,aAAa,CAAC;aAClF,KAAK,CAAC,4CAA4C,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QAE1E,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,CAAC,6DAA6D,EAAE;gBACnF,SAAS;gBACT,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,YAAY;aACpC,OAAO,CAAC,6BAA6B,EAAE,KAAK,CAAC;aAC7C,UAAU,CAAC,uBAAuB,EAAE,KAAK,CAAC;aAC1C,OAAO,EAAE,CAAC;QAEb,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACtD,cAAc,IAAI,WAAW,CAAC,MAAM,CAAC;YACrC,OAAO;gBACL,IAAI,EAAE,WAAW,CAAC,eAAe;gBACjC,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,YAAY,EAAE,WAAW,CAAC,YAAY;aACvC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE;gBACP,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,aAAa,EAAE,WAAW,CAAC,aAAa;gBACxC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,cAAc,EAAE,WAAW,CAAC,cAAc;aAC3C;YACD,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;YAC9B,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE;gBACP,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;gBACzF,YAAY,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC9F,cAAc,EAAE,cAAc;aAC/B;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,aAAqB;QACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;YACzC,KAAK,EAAE;gBACL,aAAa;gBACb,YAAY,EAAE,KAAK;gBACnB,MAAM,EAAE,+CAAqB,CAAC,OAAO;aACtC;YACD,KAAK,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE;SAClC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU;QAEhC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAGlE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3C,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;QAC7B,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,aAAqB,EAAE,WAA4B;QACpF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAGtD,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,MAAM,CAAC;QAGjD,IAAI,WAAW,CAAC,MAAM,KAAK,+CAAqB,CAAC,OAAO,EAAE,CAAC;YACzD,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,MAAM,CAAC;QACrD,CAAC;QAGD,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;QACxD,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEvD,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;CACF,CAAA;AAhNY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCADH,oBAAU;QAEN,oBAAU;GALpC,kBAAkB,CAgN9B"}