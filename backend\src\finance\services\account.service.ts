import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Account, AccountType, AccountSubType } from '../entities/account.entity';

@Injectable()
export class AccountService {
  constructor(
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
  ) {}

  async create(createAccountDto: any): Promise<Account> {
    // Generate account number
    const accountNumber = await this.generateAccountNumber(createAccountDto.type);
    
    const account = this.accountRepository.create({
      ...createAccountDto,
      accountNumber,
    });

    return this.accountRepository.save(account);
  }

  async findAll(): Promise<Account[]> {
    return this.accountRepository.find({
      relations: ['parentAccount', 'childAccounts'],
      order: { accountNumber: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Account> {
    const account = await this.accountRepository.findOne({
      where: { id },
      relations: ['parentAccount', 'childAccounts', 'debitTransactions', 'creditTransactions'],
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${id} not found`);
    }

    return account;
  }

  async findByType(type: AccountType): Promise<Account[]> {
    return this.accountRepository.find({
      where: { type, isActive: true },
      order: { accountNumber: 'ASC' },
    });
  }

  async findBySubType(subType: AccountSubType): Promise<Account[]> {
    return this.accountRepository.find({
      where: { subType, isActive: true },
      order: { accountNumber: 'ASC' },
    });
  }

  async update(id: string, updateAccountDto: any): Promise<Account> {
    const account = await this.findOne(id);
    
    if (updateAccountDto.parentAccountId && updateAccountDto.parentAccountId === id) {
      throw new BadRequestException('Account cannot be its own parent');
    }

    Object.assign(account, updateAccountDto);
    return this.accountRepository.save(account);
  }

  async remove(id: string): Promise<void> {
    const account = await this.findOne(id);
    
    if (account.isSystemAccount) {
      throw new BadRequestException('Cannot delete system account');
    }

    // Check if account has transactions
    if (account.debitTransactions?.length > 0 || account.creditTransactions?.length > 0) {
      throw new BadRequestException('Cannot delete account with transactions');
    }

    await this.accountRepository.remove(account);
  }

  async updateBalance(accountId: string, amount: number, isDebit: boolean): Promise<Account> {
    const account = await this.findOne(accountId);
    
    if (isDebit) {
      account.debitBalance += amount;
    } else {
      account.creditBalance += amount;
    }

    // Calculate net balance based on account type
    switch (account.type) {
      case AccountType.ASSET:
      case AccountType.EXPENSE:
        account.balance = account.debitBalance - account.creditBalance;
        break;
      case AccountType.LIABILITY:
      case AccountType.EQUITY:
      case AccountType.REVENUE:
        account.balance = account.creditBalance - account.debitBalance;
        break;
    }

    return this.accountRepository.save(account);
  }

  async getAccountHierarchy(): Promise<Account[]> {
    const accounts = await this.accountRepository.find({
      where: { parentAccountId: null },
      relations: ['childAccounts'],
      order: { accountNumber: 'ASC' },
    });

    return this.buildAccountTree(accounts);
  }

  async getTrialBalance(asOfDate?: Date): Promise<any> {
    const accounts = await this.accountRepository.find({
      where: { isActive: true },
      order: { accountNumber: 'ASC' },
    });

    const trialBalance = {
      asOfDate: asOfDate || new Date(),
      accounts: accounts.map(account => ({
        accountNumber: account.accountNumber,
        accountName: account.name,
        accountType: account.type,
        debitBalance: account.debitBalance,
        creditBalance: account.creditBalance,
        balance: account.balance,
      })),
      totalDebits: accounts.reduce((sum, acc) => sum + acc.debitBalance, 0),
      totalCredits: accounts.reduce((sum, acc) => sum + acc.creditBalance, 0),
    };

    return trialBalance;
  }

  private async generateAccountNumber(type: AccountType): Promise<string> {
    const prefix = this.getAccountPrefix(type);
    const lastAccount = await this.accountRepository.findOne({
      where: { type },
      order: { accountNumber: 'DESC' },
    });

    let nextNumber = 1;
    if (lastAccount) {
      const lastNumber = parseInt(lastAccount.accountNumber.substring(1));
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }

  private getAccountPrefix(type: AccountType): string {
    switch (type) {
      case AccountType.ASSET: return '1';
      case AccountType.LIABILITY: return '2';
      case AccountType.EQUITY: return '3';
      case AccountType.REVENUE: return '4';
      case AccountType.EXPENSE: return '5';
      default: return '9';
    }
  }

  private async buildAccountTree(accounts: Account[]): Promise<Account[]> {
    for (const account of accounts) {
      if (account.childAccounts?.length > 0) {
        account.childAccounts = await this.buildAccountTree(account.childAccounts);
      }
    }
    return accounts;
  }
}
