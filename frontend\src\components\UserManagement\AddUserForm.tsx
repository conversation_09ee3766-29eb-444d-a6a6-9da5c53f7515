import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Checkbox,
  Grid,
  Alert,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface Department {
  id: string;
  name: string;
  description: string;
  activities: Activity[];
}

interface Activity {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
  action: string;
  resource: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  type: string;
  departmentAccess: string[];
  permissions: Permission[];
}

interface AddUserFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (userData: any) => void;
}

const departments: Department[] = [
  {
    id: 'analytics',
    name: 'Analytics',
    description: 'Business intelligence and data analysis',
    activities: [
      {
        id: 'dashboard',
        name: 'Dashboard Management',
        description: 'View and manage analytics dashboards',
        permissions: [
          { id: 'analytics_read_dashboard', name: 'View Dashboard', description: 'Access analytics dashboard', module: 'analytics', action: 'read', resource: 'dashboard' },
          { id: 'analytics_create_dashboard', name: 'Create Dashboard', description: 'Create custom dashboards', module: 'analytics', action: 'create', resource: 'dashboard' },
          { id: 'analytics_update_dashboard', name: 'Update Dashboard', description: 'Modify dashboard settings', module: 'analytics', action: 'update', resource: 'dashboard' },
        ]
      },
      {
        id: 'reports',
        name: 'Report Management',
        description: 'Generate and manage reports',
        permissions: [
          { id: 'analytics_view_reports', name: 'View Reports', description: 'Access existing reports', module: 'analytics', action: 'view_reports', resource: 'reports' },
          { id: 'analytics_generate_reports', name: 'Generate Reports', description: 'Create new reports', module: 'analytics', action: 'generate_reports', resource: 'reports' },
          { id: 'analytics_export_data', name: 'Export Data', description: 'Export analytics data', module: 'analytics', action: 'export', resource: 'data' },
        ]
      }
    ]
  },
  {
    id: 'customers',
    name: 'Customer Management',
    description: 'Customer relationship management',
    activities: [
      {
        id: 'customer_crud',
        name: 'Customer Operations',
        description: 'Basic customer management operations',
        permissions: [
          { id: 'customers_create_customer', name: 'Create Customer', description: 'Add new customers', module: 'customers', action: 'create', resource: 'customer' },
          { id: 'customers_read_customer', name: 'View Customer', description: 'View customer details', module: 'customers', action: 'read', resource: 'customer' },
          { id: 'customers_update_customer', name: 'Update Customer', description: 'Edit customer information', module: 'customers', action: 'update', resource: 'customer' },
          { id: 'customers_delete_customer', name: 'Delete Customer', description: 'Remove customers', module: 'customers', action: 'delete', resource: 'customer' },
        ]
      },
      {
        id: 'customer_interactions',
        name: 'Customer Interactions',
        description: 'Manage customer communications',
        permissions: [
          { id: 'customers_create_interaction', name: 'Log Interaction', description: 'Record customer interactions', module: 'customers', action: 'create', resource: 'interaction' },
          { id: 'customers_read_interaction', name: 'View Interactions', description: 'View interaction history', module: 'customers', action: 'read', resource: 'interaction' },
        ]
      },
      {
        id: 'customer_segmentation',
        name: 'Customer Segmentation',
        description: 'Manage customer segments',
        permissions: [
          { id: 'customers_create_segment', name: 'Create Segment', description: 'Create customer segments', module: 'customers', action: 'create', resource: 'segment' },
          { id: 'customers_manage_loyalty', name: 'Manage Loyalty', description: 'Manage loyalty programs', module: 'customers', action: 'manage_settings', resource: 'loyalty' },
          { id: 'customers_adjust_credit', name: 'Adjust Credit', description: 'Modify credit limits', module: 'customers', action: 'adjust', resource: 'credit' },
        ]
      }
    ]
  },
  {
    id: 'collections',
    name: 'Collections',
    description: 'Debt collection and recovery',
    activities: [
      {
        id: 'collection_cases',
        name: 'Collection Cases',
        description: 'Manage collection cases',
        permissions: [
          { id: 'collections_create_case', name: 'Create Case', description: 'Create collection cases', module: 'collections', action: 'create', resource: 'case' },
          { id: 'collections_read_case', name: 'View Cases', description: 'View collection cases', module: 'collections', action: 'read', resource: 'case' },
          { id: 'collections_update_case', name: 'Update Case', description: 'Update case information', module: 'collections', action: 'update', resource: 'case' },
          { id: 'collections_assign_case', name: 'Assign Case', description: 'Assign cases to agents', module: 'collections', action: 'assign', resource: 'case' },
          { id: 'collections_escalate_case', name: 'Escalate Case', description: 'Escalate cases', module: 'collections', action: 'escalate', resource: 'case' },
          { id: 'collections_close_case', name: 'Close Case', description: 'Close resolved cases', module: 'collections', action: 'close', resource: 'case' },
        ]
      },
      {
        id: 'collection_activities',
        name: 'Collection Activities',
        description: 'Log collection activities',
        permissions: [
          { id: 'collections_create_activity', name: 'Log Activity', description: 'Record collection activities', module: 'collections', action: 'create', resource: 'activity' },
        ]
      },
      {
        id: 'payment_plans',
        name: 'Payment Plans',
        description: 'Manage payment arrangements',
        permissions: [
          { id: 'collections_create_payment_plan', name: 'Create Payment Plan', description: 'Set up payment plans', module: 'collections', action: 'create', resource: 'payment_plan' },
          { id: 'collections_process_payment', name: 'Process Payment', description: 'Process collection payments', module: 'collections', action: 'process_payment', resource: 'payment' },
        ]
      }
    ]
  },
  {
    id: 'finance',
    name: 'Finance',
    description: 'Financial management and accounting',
    activities: [
      {
        id: 'transactions',
        name: 'Financial Transactions',
        description: 'Manage financial transactions',
        permissions: [
          { id: 'finance_create_transaction', name: 'Create Transaction', description: 'Record transactions', module: 'finance', action: 'create', resource: 'transaction' },
          { id: 'finance_read_transaction', name: 'View Transactions', description: 'View transaction details', module: 'finance', action: 'read', resource: 'transaction' },
          { id: 'finance_approve_transaction', name: 'Approve Transaction', description: 'Approve transactions', module: 'finance', action: 'approve', resource: 'transaction' },
        ]
      },
      {
        id: 'invoicing',
        name: 'Invoice Management',
        description: 'Manage customer invoices',
        permissions: [
          { id: 'finance_create_invoice', name: 'Create Invoice', description: 'Generate invoices', module: 'finance', action: 'create', resource: 'invoice' },
          { id: 'finance_update_invoice', name: 'Update Invoice', description: 'Modify invoices', module: 'finance', action: 'update', resource: 'invoice' },
          { id: 'finance_void_invoice', name: 'Void Invoice', description: 'Cancel invoices', module: 'finance', action: 'void', resource: 'invoice' },
        ]
      },
      {
        id: 'payments',
        name: 'Payment Processing',
        description: 'Process payments and refunds',
        permissions: [
          { id: 'finance_process_payment', name: 'Process Payment', description: 'Process customer payments', module: 'finance', action: 'process_payment', resource: 'payment' },
          { id: 'finance_process_refund', name: 'Process Refund', description: 'Process refunds', module: 'finance', action: 'refund', resource: 'payment' },
        ]
      },
      {
        id: 'financial_reports',
        name: 'Financial Reporting',
        description: 'Generate financial reports',
        permissions: [
          { id: 'finance_view_reports', name: 'View Reports', description: 'Access financial reports', module: 'finance', action: 'view_reports', resource: 'reports' },
          { id: 'finance_generate_reports', name: 'Generate Reports', description: 'Create financial reports', module: 'finance', action: 'generate_reports', resource: 'reports' },
        ]
      }
    ]
  },
  {
    id: 'hr',
    name: 'Human Resources',
    description: 'Employee management and HR operations',
    activities: [
      {
        id: 'employee_management',
        name: 'Employee Management',
        description: 'Manage employee records',
        permissions: [
          { id: 'hr_create_employee', name: 'Create Employee', description: 'Add new employees', module: 'hr', action: 'create', resource: 'employee' },
          { id: 'hr_read_employee', name: 'View Employee', description: 'View employee records', module: 'hr', action: 'read', resource: 'employee' },
          { id: 'hr_update_employee', name: 'Update Employee', description: 'Edit employee information', module: 'hr', action: 'update', resource: 'employee' },
          { id: 'hr_delete_employee', name: 'Delete Employee', description: 'Remove employees', module: 'hr', action: 'delete', resource: 'employee' },
        ]
      },
      {
        id: 'payroll',
        name: 'Payroll Management',
        description: 'Manage employee payroll',
        permissions: [
          { id: 'hr_read_payroll', name: 'View Payroll', description: 'Access payroll information', module: 'hr', action: 'read', resource: 'payroll' },
          { id: 'hr_process_payroll', name: 'Process Payroll', description: 'Process employee payroll', module: 'hr', action: 'process_payment', resource: 'payroll' },
        ]
      },
      {
        id: 'leave_management',
        name: 'Leave Management',
        description: 'Manage employee leave',
        permissions: [
          { id: 'hr_create_leave', name: 'Manage Leave', description: 'Create leave requests', module: 'hr', action: 'create', resource: 'leave' },
          { id: 'hr_approve_leave', name: 'Approve Leave', description: 'Approve leave requests', module: 'hr', action: 'approve', resource: 'leave' },
        ]
      },
      {
        id: 'performance',
        name: 'Performance Management',
        description: 'Manage employee performance',
        permissions: [
          { id: 'hr_create_performance', name: 'Manage Performance', description: 'Create performance reviews', module: 'hr', action: 'create', resource: 'performance' },
        ]
      }
    ]
  },
  {
    id: 'inventory',
    name: 'Inventory Management',
    description: 'Product and stock management',
    activities: [
      {
        id: 'product_management',
        name: 'Product Management',
        description: 'Manage product catalog',
        permissions: [
          { id: 'inventory_create_product', name: 'Create Product', description: 'Add new products', module: 'inventory', action: 'create', resource: 'product' },
          { id: 'inventory_read_product', name: 'View Product', description: 'View product information', module: 'inventory', action: 'read', resource: 'product' },
          { id: 'inventory_update_product', name: 'Update Product', description: 'Edit product details', module: 'inventory', action: 'update', resource: 'product' },
          { id: 'inventory_delete_product', name: 'Delete Product', description: 'Remove products', module: 'inventory', action: 'delete', resource: 'product' },
        ]
      },
      {
        id: 'stock_management',
        name: 'Stock Management',
        description: 'Manage inventory levels',
        permissions: [
          { id: 'inventory_adjust_stock', name: 'Adjust Stock', description: 'Adjust stock levels', module: 'inventory', action: 'adjust', resource: 'stock' },
          { id: 'inventory_read_stock', name: 'View Stock', description: 'View stock levels', module: 'inventory', action: 'read', resource: 'stock' },
        ]
      },
      {
        id: 'purchasing',
        name: 'Purchase Management',
        description: 'Manage purchase orders',
        permissions: [
          { id: 'inventory_create_purchase_order', name: 'Create Purchase Order', description: 'Create purchase orders', module: 'inventory', action: 'create', resource: 'purchase_order' },
          { id: 'inventory_approve_purchase_order', name: 'Approve Purchase Order', description: 'Approve purchase orders', module: 'inventory', action: 'approve', resource: 'purchase_order' },
        ]
      },
      {
        id: 'supplier_management',
        name: 'Supplier Management',
        description: 'Manage suppliers',
        permissions: [
          { id: 'inventory_create_supplier', name: 'Manage Suppliers', description: 'Add and manage suppliers', module: 'inventory', action: 'create', resource: 'supplier' },
        ]
      },
      {
        id: 'warehouse_management',
        name: 'Warehouse Management',
        description: 'Manage warehouse locations',
        permissions: [
          { id: 'inventory_create_warehouse', name: 'Manage Warehouses', description: 'Create and manage warehouses', module: 'inventory', action: 'create', resource: 'warehouse' },
        ]
      }
    ]
  }
];

const steps = ['Basic Information', 'Role & Department', 'Permissions'];

export const AddUserForm: React.FC<AddUserFormProps> = ({ open, onClose, onSubmit }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    phone: '',
    department: '',
    position: '',
    employeeId: '',
    roleId: '',
    departmentAccess: [] as string[],
    additionalPermissionIds: [] as string[],
  });

  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(new Set());
  const [errors, setErrors] = useState<any>({});

  useEffect(() => {
    // Fetch roles from API
    // This would be replaced with actual API call
    setRoles([
      { id: '1', name: 'Administrator', description: 'Full system access', type: 'admin', departmentAccess: [], permissions: [] },
      { id: '2', name: 'Manager', description: 'Management access', type: 'manager', departmentAccess: [], permissions: [] },
      { id: '3', name: 'Employee', description: 'Standard access', type: 'employee', departmentAccess: [], permissions: [] },
    ]);
  }, []);

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateStep = (step: number): boolean => {
    const newErrors: any = {};

    switch (step) {
      case 0:
        if (!formData.email) newErrors.email = 'Email is required';
        if (!formData.password) newErrors.password = 'Password is required';
        if (!formData.firstName) newErrors.firstName = 'First name is required';
        if (!formData.lastName) newErrors.lastName = 'Last name is required';
        break;
      case 1:
        if (!formData.roleId) newErrors.roleId = 'Role is required';
        if (formData.departmentAccess.length === 0) newErrors.departmentAccess = 'At least one department is required';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleDepartmentToggle = (departmentId: string) => {
    const newDepartmentAccess = formData.departmentAccess.includes(departmentId)
      ? formData.departmentAccess.filter(id => id !== departmentId)
      : [...formData.departmentAccess, departmentId];
    
    handleInputChange('departmentAccess', newDepartmentAccess);
  };

  const handlePermissionToggle = (permissionId: string) => {
    const newSelectedPermissions = new Set(selectedPermissions);
    if (newSelectedPermissions.has(permissionId)) {
      newSelectedPermissions.delete(permissionId);
    } else {
      newSelectedPermissions.add(permissionId);
    }
    setSelectedPermissions(newSelectedPermissions);
    handleInputChange('additionalPermissionIds', Array.from(newSelectedPermissions));
  };

  const handleSubmit = () => {
    if (validateStep(activeStep)) {
      onSubmit({
        ...formData,
        additionalPermissionIds: Array.from(selectedPermissions),
      });
      onClose();
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={!!errors.email}
                helperText={errors.email}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={!!errors.password}
                helperText={errors.password}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                error={!!errors.firstName}
                helperText={errors.firstName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                error={!!errors.lastName}
                helperText={errors.lastName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Employee ID"
                value={formData.employeeId}
                onChange={(e) => handleInputChange('employeeId', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth error={!!errors.roleId}>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.roleId}
                  onChange={(e) => handleInputChange('roleId', e.target.value)}
                  label="Role"
                >
                  {roles.map((role) => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.name} - {role.description}
                    </MenuItem>
                  ))}
                </Select>
                {errors.roleId && <Typography color="error" variant="caption">{errors.roleId}</Typography>}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Department Access
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Select which departments this user can access
              </Typography>
              {errors.departmentAccess && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {errors.departmentAccess}
                </Alert>
              )}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {departments.map((dept) => (
                  <Chip
                    key={dept.id}
                    label={dept.name}
                    onClick={() => handleDepartmentToggle(dept.id)}
                    color={formData.departmentAccess.includes(dept.id) ? 'primary' : 'default'}
                    variant={formData.departmentAccess.includes(dept.id) ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Additional Permissions
            </Typography>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Grant additional permissions beyond the selected role. Only departments with access are shown.
            </Typography>
            
            {departments
              .filter(dept => formData.departmentAccess.includes(dept.id))
              .map((department) => (
                <Accordion key={department.id}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">{department.name}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {department.description}
                    </Typography>
                    {department.activities.map((activity) => (
                      <Box key={activity.id} sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" gutterBottom>
                          {activity.name}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                          {activity.description}
                        </Typography>
                        <Grid container spacing={1}>
                          {activity.permissions.map((permission) => (
                            <Grid item xs={12} sm={6} md={4} key={permission.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={selectedPermissions.has(permission.id)}
                                    onChange={() => handlePermissionToggle(permission.id)}
                                  />
                                }
                                label={
                                  <Box>
                                    <Typography variant="body2">{permission.name}</Typography>
                                    <Typography variant="caption" color="textSecondary">
                                      {permission.description}
                                    </Typography>
                                  </Box>
                                }
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    ))}
                  </AccordionDetails>
                </Accordion>
              ))}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>Add New User</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Stepper activeStep={activeStep}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
        
        <Box sx={{ mt: 3 }}>
          {renderStepContent(activeStep)}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
        >
          Back
        </Button>
        {activeStep === steps.length - 1 ? (
          <Button onClick={handleSubmit} variant="contained">
            Create User
          </Button>
        ) : (
          <Button onClick={handleNext} variant="contained">
            Next
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};
