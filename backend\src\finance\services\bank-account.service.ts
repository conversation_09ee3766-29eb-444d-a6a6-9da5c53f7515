import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BankAccount, BankAccountStatus } from '../entities/bank-account.entity';
import { BankTransaction, BankTransactionStatus } from '../entities/bank-transaction.entity';

@Injectable()
export class BankAccountService {
  constructor(
    @InjectRepository(BankAccount)
    private bankAccountRepository: Repository<BankAccount>,
    @InjectRepository(BankTransaction)
    private bankTransactionRepository: Repository<BankTransaction>,
  ) {}

  async create(createBankAccountDto: any): Promise<BankAccount> {
    const bankAccount = this.bankAccountRepository.create(createBankAccountDto);
    return this.bankAccountRepository.save(bankAccount);
  }

  async findAll(): Promise<BankAccount[]> {
    return this.bankAccountRepository.find({
      where: { status: BankAccountStatus.ACTIVE },
      order: { accountName: 'ASC' },
    });
  }

  async findOne(id: string): Promise<BankAccount> {
    const bankAccount = await this.bankAccountRepository.findOne({
      where: { id },
      relations: ['transactions'],
    });

    if (!bankAccount) {
      throw new NotFoundException(`Bank account with ID ${id} not found`);
    }

    return bankAccount;
  }

  async update(id: string, updateBankAccountDto: any): Promise<BankAccount> {
    const bankAccount = await this.findOne(id);
    Object.assign(bankAccount, updateBankAccountDto);
    return this.bankAccountRepository.save(bankAccount);
  }

  async remove(id: string): Promise<void> {
    const bankAccount = await this.findOne(id);
    
    if (bankAccount.transactions?.length > 0) {
      throw new BadRequestException('Cannot delete bank account with transactions');
    }

    await this.bankAccountRepository.remove(bankAccount);
  }

  async addTransaction(bankAccountId: string, transactionData: any): Promise<BankTransaction> {
    const bankAccount = await this.findOne(bankAccountId);

    const transaction = this.bankTransactionRepository.create({
      ...transactionData,
      bankAccountId,
    });

    const savedTransaction = await this.bankTransactionRepository.save(transaction);

    // Update account balance
    await this.updateAccountBalance(bankAccountId, savedTransaction);

    return savedTransaction;
  }

  async importTransactions(bankAccountId: string, transactions: any[]): Promise<BankTransaction[]> {
    const bankAccount = await this.findOne(bankAccountId);
    const savedTransactions: BankTransaction[] = [];

    for (const transactionData of transactions) {
      // Check if transaction already exists
      const existingTransaction = await this.bankTransactionRepository.findOne({
        where: {
          bankAccountId,
          transactionId: transactionData.transactionId,
        },
      });

      if (!existingTransaction) {
        const transaction = this.bankTransactionRepository.create({
          ...transactionData,
          bankAccountId,
          status: BankTransactionStatus.CLEARED,
        });

        const savedTransaction = await this.bankTransactionRepository.save(transaction);
        savedTransactions.push(savedTransaction);

        // Update account balance
        await this.updateAccountBalance(bankAccountId, savedTransaction);
      }
    }

    return savedTransactions;
  }

  async reconcileAccount(bankAccountId: string, reconciledBalance: number, reconciledBy: string): Promise<BankAccount> {
    const bankAccount = await this.findOne(bankAccountId);

    bankAccount.lastReconciledDate = new Date();
    bankAccount.lastReconciledBalance = reconciledBalance;

    // Mark all cleared transactions as reconciled
    await this.bankTransactionRepository.update(
      {
        bankAccountId,
        status: BankTransactionStatus.CLEARED,
        isReconciled: false,
      },
      {
        isReconciled: true,
        reconciledDate: new Date(),
        reconciledBy,
      }
    );

    return this.bankAccountRepository.save(bankAccount);
  }

  async getAccountStatement(bankAccountId: string, startDate?: Date, endDate?: Date): Promise<any> {
    const bankAccount = await this.findOne(bankAccountId);

    const queryBuilder = this.bankTransactionRepository.createQueryBuilder('transaction')
      .where('transaction.bankAccountId = :bankAccountId', { bankAccountId });

    if (startDate && endDate) {
      queryBuilder.andWhere('transaction.transactionDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const transactions = await queryBuilder
      .orderBy('transaction.transactionDate', 'ASC')
      .addOrderBy('transaction.createdAt', 'ASC')
      .getMany();

    let runningBalance = 0;
    const statementEntries = transactions.map(transaction => {
      runningBalance += transaction.amount;
      return {
        date: transaction.transactionDate,
        description: transaction.description,
        reference: transaction.reference,
        amount: transaction.amount,
        balance: runningBalance,
        status: transaction.status,
        isReconciled: transaction.isReconciled,
      };
    });

    return {
      account: {
        id: bankAccount.id,
        accountName: bankAccount.accountName,
        accountNumber: bankAccount.accountNumber,
        bankName: bankAccount.bankName,
        currentBalance: bankAccount.currentBalance,
      },
      period: { startDate, endDate },
      entries: statementEntries,
      summary: {
        openingBalance: 0, // Would need to calculate based on previous transactions
        totalDebits: transactions.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0),
        totalCredits: transactions.filter(t => t.amount < 0).reduce((sum, t) => Math.abs(t.amount), 0),
        closingBalance: runningBalance,
      },
    };
  }

  async getUnreconciledTransactions(bankAccountId: string): Promise<BankTransaction[]> {
    return this.bankTransactionRepository.find({
      where: {
        bankAccountId,
        isReconciled: false,
        status: BankTransactionStatus.CLEARED,
      },
      order: { transactionDate: 'ASC' },
    });
  }

  async setDefaultAccount(id: string): Promise<BankAccount> {
    // Remove default flag from all accounts
    await this.bankAccountRepository.update({}, { isDefault: false });

    // Set this account as default
    const bankAccount = await this.findOne(id);
    bankAccount.isDefault = true;
    return this.bankAccountRepository.save(bankAccount);
  }

  private async updateAccountBalance(bankAccountId: string, transaction: BankTransaction): Promise<void> {
    const bankAccount = await this.findOne(bankAccountId);

    // Update current balance
    bankAccount.currentBalance += transaction.amount;

    // Update available balance (considering pending transactions)
    if (transaction.status === BankTransactionStatus.CLEARED) {
      bankAccount.availableBalance += transaction.amount;
    }

    // Update running balance on transaction
    transaction.runningBalance = bankAccount.currentBalance;
    await this.bankTransactionRepository.save(transaction);

    await this.bankAccountRepository.save(bankAccount);
  }
}
