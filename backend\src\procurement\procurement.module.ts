import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { ProcurementRequest } from './entities/procurement-request.entity';
import { ProcurementItem } from './entities/procurement-item.entity';
import { Vendor } from './entities/vendor.entity';
import { VendorContact } from './entities/vendor-contact.entity';
import { VendorEvaluation } from './entities/vendor-evaluation.entity';
import { Contract } from './entities/contract.entity';
import { ContractTerm } from './entities/contract-term.entity';
import { RFQ } from './entities/rfq.entity';
import { RFQResponse } from './entities/rfq-response.entity';
import { ProcurementApproval } from './entities/procurement-approval.entity';
import { ProcurementCategory } from './entities/procurement-category.entity';

// Services
import { ProcurementRequestService } from './services/procurement-request.service';
import { VendorService } from './services/vendor.service';
import { ContractService } from './services/contract.service';
import { RFQService } from './services/rfq.service';
import { ProcurementApprovalService } from './services/procurement-approval.service';
import { ProcurementReportService } from './services/procurement-report.service';

// Controllers
import { ProcurementRequestController } from './controllers/procurement-request.controller';
import { VendorController } from './controllers/vendor.controller';
import { ContractController } from './controllers/contract.controller';
import { RFQController } from './controllers/rfq.controller';
import { ProcurementApprovalController } from './controllers/procurement-approval.controller';
import { ProcurementReportController } from './controllers/procurement-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProcurementRequest,
      ProcurementItem,
      Vendor,
      VendorContact,
      VendorEvaluation,
      Contract,
      ContractTerm,
      RFQ,
      RFQResponse,
      ProcurementApproval,
      ProcurementCategory,
    ]),
  ],
  controllers: [
    ProcurementRequestController,
    VendorController,
    ContractController,
    RFQController,
    ProcurementApprovalController,
    ProcurementReportController,
  ],
  providers: [
    ProcurementRequestService,
    VendorService,
    ContractService,
    RFQService,
    ProcurementApprovalService,
    ProcurementReportService,
  ],
  exports: [
    ProcurementRequestService,
    VendorService,
    ContractService,
    RFQService,
    ProcurementApprovalService,
    ProcurementReportService,
  ],
})
export class ProcurementModule {}
