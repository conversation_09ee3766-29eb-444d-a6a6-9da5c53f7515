import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Expense } from './expense.entity';
import { Account } from './account.entity';

@Entity('finance_expense_categories')
export class ExpenseCategory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 20, unique: true })
  code: string;

  @Column({ nullable: true })
  parentCategoryId: string;

  @ManyToOne(() => ExpenseCategory, { nullable: true })
  @JoinColumn({ name: 'parentCategoryId' })
  parentCategory: ExpenseCategory;

  @OneToMany(() => ExpenseCategory, category => category.parentCategory)
  childCategories: ExpenseCategory[];

  @Column({ nullable: true })
  defaultAccountId: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'defaultAccountId' })
  defaultAccount: Account;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  requiresApproval: boolean;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  approvalLimit: number;

  @Column({ default: false })
  isTaxDeductible: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  defaultTaxRate: number;

  @Column({ type: 'json', nullable: true })
  allowedFileTypes: string[];

  @Column({ default: false })
  requiresReceipt: boolean;

  @OneToMany(() => Expense, expense => expense.category)
  expenses: Expense[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
