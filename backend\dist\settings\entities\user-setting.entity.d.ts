export declare enum UserSettingType {
    PREFERENCE = "preference",
    NOTIFICATION = "notification",
    APPEARANCE = "appearance",
    PRIVACY = "privacy",
    ACCESSIBILITY = "accessibility",
    INTEGRATION = "integration"
}
export declare class UserSetting {
    id: string;
    userId: string;
    key: string;
    value: string;
    type: UserSettingType;
    name: string;
    description: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
