{"version": 3, "file": "payroll.service.js", "sourceRoot": "", "sources": ["../../../src/hr/services/payroll.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAA2C;AAC3C,+DAAoE;AACpE,yEAA8D;AAGvD,IAAM,cAAc,GAApB,MAAM,cAAc;IAGf;IAEA;IAJV,YAEU,iBAAsC,EAEtC,qBAA8C;QAF9C,sBAAiB,GAAjB,iBAAiB,CAAqB;QAEtC,0BAAqB,GAArB,qBAAqB,CAAyB;IACrD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,gBAAqB;QAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEzD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC5C,GAAG,gBAAgB;YACnB,aAAa;SACd,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAa;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,SAAS,CAAC;aACtE,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC;aACjD,iBAAiB,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;QAE7D,IAAI,OAAO,EAAE,UAAU,EAAE,CAAC;YACxB,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,YAAY,CAAC,QAAQ,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YACvB,YAAY,CAAC,QAAQ,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAC5F,CAAC;QAED,OAAO,YAAY;aAChB,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;aAClC,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC;SACxC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU;QAC/B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGvC,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAC9E,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QAClF,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAC9E,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;QAEvE,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACxE,OAAO,CAAC,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACtD,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5E,OAAO,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7E,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC;QAEpF,OAAO,CAAC,MAAM,GAAG,8BAAa,CAAC,UAAU,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,UAAkB;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEvC,OAAO,CAAC,MAAM,GAAG,8BAAa,CAAC,QAAQ,CAAC;QACxC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;QAChC,OAAO,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAEhC,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,MAAc;QAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEvC,OAAO,CAAC,MAAM,GAAG,8BAAa,CAAC,IAAI,CAAC;QACpC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QAE5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,oBAAyB;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACpD,GAAG,oBAAoB;YACvB,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC;QAEtC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,aAAa,EAAE,IAAA,cAAI,EAAC,GAAG,MAAM,GAAG,CAAC,EAAE;YAC5C,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,UAAU,GAAG,UAAU,GAAG,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,GAAG,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC9D,CAAC;CACF,CAAA;AA1HY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;IAEzB,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;qCADH,oBAAU;QAEN,oBAAU;GALhC,cAAc,CA0H1B"}