import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Space, Divider, Row, Col } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, GlobalOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useAuth } from '../../hooks/useAuth';
import { RegisterCompanyRequest } from '../../types/auth';
import LanguageSwitcher from '../common/LanguageSwitcher';

const { Title, Text } = Typography;

const RegisterContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const RegisterCard = styled(Card)`
  width: 100%;
  max-width: 800px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;

  .ant-card-body {
    padding: 40px;
  }
`;

const StyledTitle = styled(Title)`
  text-align: center;
  margin-bottom: 8px !important;
  color: #1f2937;
`;

const StyledSubtitle = styled(Text)`
  display: block;
  text-align: center;
  margin-bottom: 32px;
  color: #6b7280;
`;

const StyledButton = styled(Button)`
  width: 100%;
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
`;

const HeaderActions = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
`;

const SectionTitle = styled(Title)`
  margin-top: 24px !important;
  margin-bottom: 16px !important;
  color: #374151;
`;

const Register: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { registerCompany } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const onFinish = async (values: RegisterCompanyRequest) => {
    setLoading(true);
    try {
      await registerCompany(values);
      message.success(t('common.success'));
      navigate('/dashboard');
    } catch (error: any) {
      message.error(error.response?.data?.message || t('common.error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <RegisterContainer>
      <HeaderActions>
        <LanguageSwitcher />
      </HeaderActions>

      <RegisterCard>
        <StyledTitle level={2}>{t('auth.registerTitle')}</StyledTitle>
        <StyledSubtitle>{t('auth.registerSubtitle')}</StyledSubtitle>

        <Form
          name="register"
          onFinish={onFinish}
          autoComplete="off"
          layout="vertical"
        >
          <SectionTitle level={4}>Company Information</SectionTitle>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="companyName"
                label={t('auth.companyName')}
                rules={[{ required: true, message: 'Please input company name!' }]}
              >
                <Input placeholder={t('auth.companyName')} size="large" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="companySlug"
                label={t('auth.companySlug')}
                rules={[{ required: true, message: 'Please input company slug!' }]}
              >
                <Input placeholder={t('auth.companySlug')} size="large" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="companyEmail"
                label={t('auth.companyEmail')}
                rules={[{ type: 'email', message: 'Please enter a valid email!' }]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder={t('auth.companyEmail')}
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="companyPhone"
                label={t('auth.companyPhone')}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder={t('auth.companyPhone')}
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="companyWebsite"
            label={t('auth.companyWebsite')}
          >
            <Input
              prefix={<GlobalOutlined />}
              placeholder={t('auth.companyWebsite')}
              size="large"
            />
          </Form.Item>

          <SectionTitle level={4}>Administrator Account</SectionTitle>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="adminFirstName"
                label={t('auth.adminFirstName')}
                rules={[{ required: true, message: 'Please input first name!' }]}
              >
                <Input placeholder={t('auth.adminFirstName')} size="large" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="adminLastName"
                label={t('auth.adminLastName')}
                rules={[{ required: true, message: 'Please input last name!' }]}
              >
                <Input placeholder={t('auth.adminLastName')} size="large" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="adminEmail"
                label={t('auth.adminEmail')}
                rules={[
                  { required: true, message: 'Please input email!' },
                  { type: 'email', message: 'Please enter a valid email!' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder={t('auth.adminEmail')}
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="adminPassword"
                label={t('auth.adminPassword')}
                rules={[
                  { required: true, message: 'Please input password!' },
                  { min: 8, message: 'Password must be at least 8 characters!' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={t('auth.adminPassword')}
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <StyledButton
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              {t('auth.register')}
            </StyledButton>
          </Form.Item>
        </Form>

        <Divider />

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text>
            {t('auth.alreadyHaveAccount')}{' '}
            <Link to="/login">{t('auth.signIn')}</Link>
          </Text>
        </Space>
      </RegisterCard>
    </RegisterContainer>
  );
};

export default Register;
