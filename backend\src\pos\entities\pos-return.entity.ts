import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PosSale } from './pos-sale.entity';

export enum ReturnReason {
  DEFECTIVE = 'defective',
  WRONG_ITEM = 'wrong_item',
  CUSTOMER_CHANGED_MIND = 'customer_changed_mind',
  SIZE_ISSUE = 'size_issue',
  COLOR_ISSUE = 'color_issue',
  DAMAGED_IN_SHIPPING = 'damaged_in_shipping',
  NOT_AS_DESCRIBED = 'not_as_described',
  DUPLICATE_ORDER = 'duplicate_order',
  GIFT_RETURN = 'gift_return',
  OTHER = 'other',
}

export enum ReturnStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PROCESSED = 'processed',
  REFUNDED = 'refunded',
}

@Entity('pos_returns')
export class PosReturn {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  returnNumber: string;

  @Column()
  originalSaleId: string;

  @ManyToOne(() => PosSale)
  @JoinColumn({ name: 'originalSaleId' })
  originalSale: PosSale;

  @Column({ nullable: true })
  newSaleId: string;

  @ManyToOne(() => PosSale, { nullable: true })
  @JoinColumn({ name: 'newSaleId' })
  newSale: PosSale;

  @Column({
    type: 'enum',
    enum: ReturnReason,
  })
  reason: ReturnReason;

  @Column({
    type: 'enum',
    enum: ReturnStatus,
    default: ReturnStatus.PENDING,
  })
  status: ReturnStatus;

  @Column({ type: 'timestamp' })
  returnDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  returnAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  refundAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  storeCreditAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  exchangeAmount: number;

  @Column({ type: 'json' })
  returnedItems: any[];

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column()
  processedBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true })
  refundDetails: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
