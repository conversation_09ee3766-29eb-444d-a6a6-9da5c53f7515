"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankAccount = exports.BankAccountStatus = exports.BankAccountType = void 0;
const typeorm_1 = require("typeorm");
const bank_transaction_entity_1 = require("./bank-transaction.entity");
var BankAccountType;
(function (BankAccountType) {
    BankAccountType["CHECKING"] = "checking";
    BankAccountType["SAVINGS"] = "savings";
    BankAccountType["CREDIT"] = "credit";
    BankAccountType["LOAN"] = "loan";
    BankAccountType["INVESTMENT"] = "investment";
    BankAccountType["MONEY_MARKET"] = "money_market";
    BankAccountType["CD"] = "cd";
    BankAccountType["OTHER"] = "other";
})(BankAccountType || (exports.BankAccountType = BankAccountType = {}));
var BankAccountStatus;
(function (BankAccountStatus) {
    BankAccountStatus["ACTIVE"] = "active";
    BankAccountStatus["INACTIVE"] = "inactive";
    BankAccountStatus["CLOSED"] = "closed";
    BankAccountStatus["FROZEN"] = "frozen";
})(BankAccountStatus || (exports.BankAccountStatus = BankAccountStatus = {}));
let BankAccount = class BankAccount {
    id;
    accountName;
    accountNumber;
    bankName;
    routingNumber;
    swiftCode;
    iban;
    accountType;
    status;
    currency;
    currentBalance;
    availableBalance;
    creditLimit;
    interestRate;
    openedDate;
    closedDate;
    lastReconciledDate;
    lastReconciledBalance;
    isDefault;
    allowOnlineTransactions;
    allowMobileDeposits;
    description;
    bankContactInfo;
    accountHolders;
    authorizedSigners;
    transactions;
    metadata;
    createdAt;
    updatedAt;
};
exports.BankAccount = BankAccount;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], BankAccount.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], BankAccount.prototype, "accountName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], BankAccount.prototype, "accountNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], BankAccount.prototype, "bankName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], BankAccount.prototype, "routingNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], BankAccount.prototype, "swiftCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], BankAccount.prototype, "iban", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BankAccountType,
    }),
    __metadata("design:type", String)
], BankAccount.prototype, "accountType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BankAccountStatus,
        default: BankAccountStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], BankAccount.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], BankAccount.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BankAccount.prototype, "currentBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], BankAccount.prototype, "availableBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], BankAccount.prototype, "creditLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], BankAccount.prototype, "interestRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], BankAccount.prototype, "openedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], BankAccount.prototype, "closedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], BankAccount.prototype, "lastReconciledDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], BankAccount.prototype, "lastReconciledBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], BankAccount.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], BankAccount.prototype, "allowOnlineTransactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], BankAccount.prototype, "allowMobileDeposits", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], BankAccount.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], BankAccount.prototype, "bankContactInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], BankAccount.prototype, "accountHolders", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], BankAccount.prototype, "authorizedSigners", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => bank_transaction_entity_1.BankTransaction, transaction => transaction.bankAccount),
    __metadata("design:type", Array)
], BankAccount.prototype, "transactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], BankAccount.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], BankAccount.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], BankAccount.prototype, "updatedAt", void 0);
exports.BankAccount = BankAccount = __decorate([
    (0, typeorm_1.Entity)('finance_bank_accounts')
], BankAccount);
//# sourceMappingURL=bank-account.entity.js.map