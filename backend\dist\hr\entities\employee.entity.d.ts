import { Department } from './department.entity';
import { Position } from './position.entity';
import { Attendance } from './attendance.entity';
import { Leave } from './leave.entity';
import { Payroll } from './payroll.entity';
import { Performance } from './performance.entity';
import { Training } from './training.entity';
import { EmployeeBenefit } from './employee-benefit.entity';
export declare enum EmployeeStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    TERMINATED = "terminated",
    ON_LEAVE = "on_leave",
    SUSPENDED = "suspended"
}
export declare enum EmploymentType {
    FULL_TIME = "full_time",
    PART_TIME = "part_time",
    CONTRACT = "contract",
    TEMPORARY = "temporary",
    INTERN = "intern"
}
export declare enum Gender {
    MALE = "male",
    FEMALE = "female",
    OTHER = "other"
}
export declare enum MaritalStatus {
    SINGLE = "single",
    MARRIED = "married",
    DIVORCED = "divorced",
    WIDOWED = "widowed"
}
export declare class Employee {
    id: string;
    employeeNumber: string;
    firstName: string;
    lastName: string;
    middleName: string;
    email: string;
    phone: string;
    dateOfBirth: Date;
    gender: Gender;
    maritalStatus: MaritalStatus;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    nationalId: string;
    passportNumber: string;
    hireDate: Date;
    terminationDate: Date;
    status: EmployeeStatus;
    employmentType: EmploymentType;
    departmentId: string;
    department: Department;
    positionId: string;
    position: Position;
    managerId: string;
    manager: Employee;
    directReports: Employee[];
    salary: number;
    hourlyRate: number;
    currency: string;
    emergencyContacts: any[];
    bankDetails: any;
    taxInformation: any;
    profilePicture: string;
    notes: string;
    attendances: Attendance[];
    leaves: Leave[];
    payrolls: Payroll[];
    performances: Performance[];
    trainings: Training[];
    benefits: EmployeeBenefit[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
