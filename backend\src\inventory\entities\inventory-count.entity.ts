import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';

export enum CountStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum CountType {
  FULL = 'full',
  PARTIAL = 'partial',
  CYCLE = 'cycle',
  SPOT = 'spot',
}

@Entity('inventory_counts')
export class InventoryCount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  countNumber: string;

  @Column({
    type: 'enum',
    enum: CountType,
    default: CountType.FULL,
  })
  type: CountType;

  @Column({
    type: 'enum',
    enum: CountStatus,
    default: CountStatus.SCHEDULED,
  })
  status: CountStatus;

  @Column()
  warehouseId: string;

  @ManyToOne(() => Warehouse)
  @JoinColumn({ name: 'warehouseId' })
  warehouse: Warehouse;

  @Column({ nullable: true })
  productId: string;

  @ManyToOne(() => Product, { nullable: true })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column({ type: 'date' })
  scheduledDate: Date;

  @Column({ type: 'date', nullable: true })
  startDate: Date;

  @Column({ type: 'date', nullable: true })
  completedDate: Date;

  @Column({ type: 'int', nullable: true })
  systemQuantity: number;

  @Column({ type: 'int', nullable: true })
  countedQuantity: number;

  @Column({ type: 'int', nullable: true })
  variance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  varianceValue: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  countedBy: string;

  @Column({ nullable: true })
  verifiedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  verifiedAt: Date;

  @Column({ default: false })
  adjustmentCreated: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
