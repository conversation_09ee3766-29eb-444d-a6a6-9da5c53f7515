import { Repository } from 'typeorm';
import { TaxRecord } from '../entities/tax-record.entity';
export declare class TaxService {
    private taxRecordRepository;
    constructor(taxRecordRepository: Repository<TaxRecord>);
    create(createTaxRecordDto: any): Promise<TaxRecord>;
    findAll(filters?: any): Promise<TaxRecord[]>;
    findOne(id: string): Promise<TaxRecord>;
    update(id: string, updateTaxRecordDto: any): Promise<TaxRecord>;
    remove(id: string): Promise<void>;
    fileReturn(id: string, filedBy: string, referenceNumber?: string): Promise<TaxRecord>;
    recordPayment(id: string, paymentAmount: number): Promise<TaxRecord>;
    calculatePenaltiesAndInterest(id: string): Promise<TaxRecord>;
    getTaxSummary(year: number): Promise<any>;
    private generateTaxRecordNumber;
    private getTaxTypePrefix;
    private groupByTaxType;
    private groupByStatus;
}
