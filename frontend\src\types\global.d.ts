// Global type declarations for the Ultimate ERP System

declare global {
  interface Window {
    // Add any global window properties here
    gtag?: (...args: any[]) => void
    dataLayer?: any[]
  }
}

// Module declarations for missing type definitions
declare module 'conventional-commits-parser' {
  const conventionalCommitsParser: any
  export = conventionalCommitsParser
}

declare module 'long' {
  const Long: any
  export = Long
}

declare module 'offscreencanvas' {
  const OffscreenCanvas: any
  export = OffscreenCanvas
}

// Chart.js module declarations
declare module 'chart.js/auto' {
  export * from 'chart.js'
}

// React DnD module declarations
declare module 'react-beautiful-dnd' {
  export const DragDropContext: any
  export const Droppable: any
  export const Draggable: any
}

declare module 'react-grid-layout' {
  export const Responsive: any
  export const WidthProvider: any
}

// TensorFlow.js module declarations
declare module '@tensorflow/tfjs' {
  export const tensor: any
  export const sequential: any
  export const layers: any
  export const train: any
  export const loadLayersModel: any
  export const ready: any
}

// ML Matrix module declarations
declare module 'ml-matrix' {
  export class Matrix {
    constructor(data: number[][])
    static from2DArray(array: number[][]): Matrix
    inverse(): Matrix
    mmul(other: Matrix): Matrix
    transpose(): Matrix
  }
}

// Simple Statistics module declarations
declare module 'simple-statistics' {
  export function mean(data: number[]): number
  export function standardDeviation(data: number[]): number
  export function linearRegression(data: [number, number][]): { m: number; b: number }
  export function median(data: number[]): number
  export function mode(data: number[]): number
}

// Crypto-JS module declarations
declare module 'crypto-js' {
  export const AES: any
  export const enc: any
  export const lib: any
  export const PBKDF2: any
}

// JOSE module declarations
declare module 'jose' {
  export function jwtDecode(token: string): any
  export function SignJWT(payload: any): any
  export function jwtVerify(jwt: string, key: any): Promise<any>
}

// Framer Motion module declarations
declare module 'framer-motion' {
  export const motion: any
  export const AnimatePresence: any
  export const useAnimation: any
  export const useMotionValue: any
}

// React Hot Toast module declarations
declare module 'react-hot-toast' {
  export default function toast(message: string, options?: any): void
  export const Toaster: any
}

// Sonner module declarations
declare module 'sonner' {
  export const toast: any
  export const Toaster: any
}

// Zustand module declarations
declare module 'zustand' {
  export function create<T>(fn: (set: any, get: any) => T): () => T
}

// React Router DOM module declarations
declare module 'react-router-dom' {
  export const BrowserRouter: any
  export const Routes: any
  export const Route: any
  export const Navigate: any
  export const useNavigate: any
  export const useLocation: any
  export const useParams: any
}

// Recharts module declarations
declare module 'recharts' {
  export const LineChart: any
  export const BarChart: any
  export const PieChart: any
  export const XAxis: any
  export const YAxis: any
  export const CartesianGrid: any
  export const Tooltip: any
  export const Legend: any
  export const Line: any
  export const Bar: any
  export const Pie: any
  export const Cell: any
  export const ResponsiveContainer: any
}

// React Dropzone module declarations
declare module 'react-dropzone' {
  export function useDropzone(options?: any): any
}

// Styled Components module declarations
declare module 'styled-components' {
  const styled: any
  export default styled
  export const ThemeProvider: any
  export const createGlobalStyle: any
}

// Pusher JS module declarations
declare module 'pusher-js' {
  export default class Pusher {
    constructor(key: string, options?: any)
    subscribe(channel: string): any
    disconnect(): void
  }
}

// Socket.IO Client module declarations
declare module 'socket.io-client' {
  export function io(url: string, options?: any): any
}

// React Table module declarations
declare module '@tanstack/react-table' {
  export function useReactTable(options: any): any
  export function createColumnHelper<T>(): any
  export function getCoreRowModel(): any
  export function getFilteredRowModel(): any
  export function getSortedRowModel(): any
  export function getPaginationRowModel(): any
}

// React Query module declarations
declare module '@tanstack/react-query' {
  export class QueryClient {
    constructor(options?: any)
  }
  export const QueryClientProvider: any
  export function useQuery(options: any): any
  export function useMutation(options: any): any
}

// Hookform Resolvers module declarations
declare module '@hookform/resolvers' {
  export const zodResolver: any
}

// Zod module declarations
declare module 'zod' {
  export const z: any
}

export {}
