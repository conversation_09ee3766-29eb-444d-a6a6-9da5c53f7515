/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-backend-cpu/dist/utils/binary_utils" />
import { DataType, KernelFunc } from '@tensorflow/tfjs-core';
import { ComplexBinaryKernelImpl, ComplexBinaryOperation, SimpleBinaryKernelImpl } from './binary_types';
/**
 * Template that creates a `KernelFunc` for binary ops.
 * @param name Kernel name.
 * @param binaryKernelImpl A `SimpleBinaryKernelImpl` for the kernel.
 * @param binaryKernelComplexImpl Optional. If exists, represents a
 *     `ComplexBinaryKernelImpl` for the kernel, will be used when input dtype
 *     is `complex64`.
 * @param dtype Optional. If set, the result has this dtype. Otherwise, the
 *     result has the same dtype as the first input. This is mainly used in
 *     comparison kernels, such as Equal, Less, Greater, etc.
 */
export declare function binaryKernelFunc(name: string, simpleImpl: SimpleBinaryKernelImpl, complexImpl?: ComplexBinaryKernelImpl, dtype?: DataType): KernelFunc;
/**
 * Template that creates the complex type implementation for binary ops.
 * Supports broadcast.
 */
export declare function createComplexBinaryKernelImpl(op: ComplexBinaryOperation): ComplexBinaryKernelImpl;
