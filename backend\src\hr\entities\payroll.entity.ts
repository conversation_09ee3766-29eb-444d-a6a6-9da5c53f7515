import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Employee } from './employee.entity';
import { PayrollItem } from './payroll-item.entity';

export enum PayrollStatus {
  DRAFT = 'draft',
  CALCULATED = 'calculated',
  APPROVED = 'approved',
  PAID = 'paid',
  CANCELLED = 'cancelled',
}

export enum PayrollPeriod {
  WEEKLY = 'weekly',
  BIWEEKLY = 'biweekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUALLY = 'annually',
}

@Entity('hr_payrolls')
export class Payroll {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  employeeId: string;

  @ManyToOne(() => Employee, employee => employee.payrolls)
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column({ length: 50, unique: true })
  payrollNumber: string;

  @Column({
    type: 'enum',
    enum: PayrollPeriod,
    default: PayrollPeriod.MONTHLY,
  })
  payPeriod: PayrollPeriod;

  @Column({ type: 'date' })
  payPeriodStart: Date;

  @Column({ type: 'date' })
  payPeriodEnd: Date;

  @Column({ type: 'date' })
  payDate: Date;

  @Column({
    type: 'enum',
    enum: PayrollStatus,
    default: PayrollStatus.DRAFT,
  })
  status: PayrollStatus;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  basicSalary: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  grossPay: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalDeductions: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalBenefits: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  netPay: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  hoursWorked: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  overtimeHours: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  overtimePay: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  bonuses: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  commissions: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  incomeTax: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  socialSecurityTax: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  medicareTax: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  otherTaxes: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ nullable: true })
  calculatedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  calculatedAt: Date;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ nullable: true })
  paidBy: string;

  @Column({ type: 'timestamp', nullable: true })
  paidAt: Date;

  @OneToMany(() => PayrollItem, payrollItem => payrollItem.payroll, { cascade: true })
  payrollItems: PayrollItem[];

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
