import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_xquery=__commonJS({"../../node_modules/refractor/lang/xquery.js"(exports,module){module.exports=xquery;xquery.displayName="xquery";xquery.aliases=[];function xquery(Prism){(function(Prism2){Prism2.languages.xquery=Prism2.languages.extend("markup",{"xquery-comment":{pattern:/\(:[\s\S]*?:\)/,greedy:!0,alias:"comment"},string:{pattern:/(["'])(?:\1\1|(?!\1)[\s\S])*\1/,greedy:!0},extension:{pattern:/\(#.+?#\)/,alias:"symbol"},variable:/\$[-\w:]+/,axis:{pattern:/(^|[^-])(?:ancestor(?:-or-self)?|attribute|child|descendant(?:-or-self)?|following(?:-sibling)?|parent|preceding(?:-sibling)?|self)(?=::)/,lookbehind:!0,alias:"operator"},"keyword-operator":{pattern:/(^|[^:-])\b(?:and|castable as|div|eq|except|ge|gt|idiv|instance of|intersect|is|le|lt|mod|ne|or|union)\b(?=$|[^:-])/,lookbehind:!0,alias:"operator"},keyword:{pattern:/(^|[^:-])\b(?:as|ascending|at|base-uri|boundary-space|case|cast as|collation|construction|copy-namespaces|declare|default|descending|else|empty (?:greatest|least)|encoding|every|external|for|function|if|import|in|inherit|lax|let|map|module|namespace|no-inherit|no-preserve|option|order(?: by|ed|ing)?|preserve|return|satisfies|schema|some|stable|strict|strip|then|to|treat as|typeswitch|unordered|validate|variable|version|where|xquery)\b(?=$|[^:-])/,lookbehind:!0},function:/[\w-]+(?::[\w-]+)*(?=\s*\()/,"xquery-element":{pattern:/(element\s+)[\w-]+(?::[\w-]+)*/,lookbehind:!0,alias:"tag"},"xquery-attribute":{pattern:/(attribute\s+)[\w-]+(?::[\w-]+)*/,lookbehind:!0,alias:"attr-name"},builtin:{pattern:/(^|[^:-])\b(?:attribute|comment|document|element|processing-instruction|text|xs:(?:ENTITIES|ENTITY|ID|IDREFS?|NCName|NMTOKENS?|NOTATION|Name|QName|anyAtomicType|anyType|anyURI|base64Binary|boolean|byte|date|dateTime|dayTimeDuration|decimal|double|duration|float|gDay|gMonth|gMonthDay|gYear|gYearMonth|hexBinary|int|integer|language|long|negativeInteger|nonNegativeInteger|nonPositiveInteger|normalizedString|positiveInteger|short|string|time|token|unsigned(?:Byte|Int|Long|Short)|untyped(?:Atomic)?|yearMonthDuration))\b(?=$|[^:-])/,lookbehind:!0},number:/\b\d+(?:\.\d+)?(?:E[+-]?\d+)?/,operator:[/[+*=?|@]|\.\.?|:=|!=|<[=<]?|>[=>]?/,{pattern:/(\s)-(?=\s)/,lookbehind:!0}],punctuation:/[[\](){},;:/]/}),Prism2.languages.xquery.tag.pattern=/<\/?(?!\d)[^\s>\/=$<%]+(?:\s+[^\s>\/=]+(?:=(?:("|')(?:\\[\s\S]|\{(?!\{)(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])+\}|(?!\1)[^\\])*\1|[^\s'">=]+))?)*\s*\/?>/,Prism2.languages.xquery.tag.inside["attr-value"].pattern=/=(?:("|')(?:\\[\s\S]|\{(?!\{)(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])+\}|(?!\1)[^\\])*\1|[^\s'">=]+)/,Prism2.languages.xquery.tag.inside["attr-value"].inside.punctuation=/^="|"$/,Prism2.languages.xquery.tag.inside["attr-value"].inside.expression={pattern:/\{(?!\{)(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])+\}/,inside:Prism2.languages.xquery,alias:"language-xquery"};var stringifyToken=function(token){return typeof token=="string"?token:typeof token.content=="string"?token.content:token.content.map(stringifyToken).join("")},walkTokens=function(tokens){for(var openedTags=[],i=0;i<tokens.length;i++){var token=tokens[i],notTagNorBrace=!1;if(typeof token!="string"&&(token.type==="tag"&&token.content[0]&&token.content[0].type==="tag"?token.content[0].content[0].content==="</"?openedTags.length>0&&openedTags[openedTags.length-1].tagName===stringifyToken(token.content[0].content[1])&&openedTags.pop():token.content[token.content.length-1].content==="/>"||openedTags.push({tagName:stringifyToken(token.content[0].content[1]),openedBraces:0}):openedTags.length>0&&token.type==="punctuation"&&token.content==="{"&&(!tokens[i+1]||tokens[i+1].type!=="punctuation"||tokens[i+1].content!=="{")&&(!tokens[i-1]||tokens[i-1].type!=="plain-text"||tokens[i-1].content!=="{")?openedTags[openedTags.length-1].openedBraces++:openedTags.length>0&&openedTags[openedTags.length-1].openedBraces>0&&token.type==="punctuation"&&token.content==="}"?openedTags[openedTags.length-1].openedBraces--:token.type!=="comment"&&(notTagNorBrace=!0)),(notTagNorBrace||typeof token=="string")&&openedTags.length>0&&openedTags[openedTags.length-1].openedBraces===0){var plainText=stringifyToken(token);i<tokens.length-1&&(typeof tokens[i+1]=="string"||tokens[i+1].type==="plain-text")&&(plainText+=stringifyToken(tokens[i+1]),tokens.splice(i+1,1)),i>0&&(typeof tokens[i-1]=="string"||tokens[i-1].type==="plain-text")&&(plainText=stringifyToken(tokens[i-1])+plainText,tokens.splice(i-1,1),i--),/^\s+$/.test(plainText)?tokens[i]=plainText:tokens[i]=new Prism2.Token("plain-text",plainText,null,plainText);}token.content&&typeof token.content!="string"&&walkTokens(token.content);}};Prism2.hooks.add("after-tokenize",function(env){env.language==="xquery"&&walkTokens(env.tokens);});})(Prism);}}});

export { require_xquery };
