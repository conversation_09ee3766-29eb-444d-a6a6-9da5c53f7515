"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const customer_entity_1 = require("./entities/customer.entity");
const customer_contact_entity_1 = require("./entities/customer-contact.entity");
const customer_address_entity_1 = require("./entities/customer-address.entity");
const customer_group_entity_1 = require("./entities/customer-group.entity");
const customer_note_entity_1 = require("./entities/customer-note.entity");
const customer_document_entity_1 = require("./entities/customer-document.entity");
const customer_interaction_entity_1 = require("./entities/customer-interaction.entity");
const customer_segment_entity_1 = require("./entities/customer-segment.entity");
const customer_loyalty_entity_1 = require("./entities/customer-loyalty.entity");
const customer_credit_entity_1 = require("./entities/customer-credit.entity");
const customer_preference_entity_1 = require("./entities/customer-preference.entity");
const customer_service_1 = require("./services/customer.service");
const customer_group_service_1 = require("./services/customer-group.service");
const customer_interaction_service_1 = require("./services/customer-interaction.service");
const customer_segment_service_1 = require("./services/customer-segment.service");
const customer_loyalty_service_1 = require("./services/customer-loyalty.service");
const customer_credit_service_1 = require("./services/customer-credit.service");
const customer_report_service_1 = require("./services/customer-report.service");
const customer_controller_1 = require("./controllers/customer.controller");
const customer_group_controller_1 = require("./controllers/customer-group.controller");
const customer_interaction_controller_1 = require("./controllers/customer-interaction.controller");
const customer_segment_controller_1 = require("./controllers/customer-segment.controller");
const customer_loyalty_controller_1 = require("./controllers/customer-loyalty.controller");
const customer_credit_controller_1 = require("./controllers/customer-credit.controller");
const customer_report_controller_1 = require("./controllers/customer-report.controller");
let CustomersModule = class CustomersModule {
};
exports.CustomersModule = CustomersModule;
exports.CustomersModule = CustomersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                customer_entity_1.Customer,
                customer_contact_entity_1.CustomerContact,
                customer_address_entity_1.CustomerAddress,
                customer_group_entity_1.CustomerGroup,
                customer_note_entity_1.CustomerNote,
                customer_document_entity_1.CustomerDocument,
                customer_interaction_entity_1.CustomerInteraction,
                customer_segment_entity_1.CustomerSegment,
                customer_loyalty_entity_1.CustomerLoyalty,
                customer_credit_entity_1.CustomerCredit,
                customer_preference_entity_1.CustomerPreference,
            ]),
        ],
        controllers: [
            customer_controller_1.CustomerController,
            customer_group_controller_1.CustomerGroupController,
            customer_interaction_controller_1.CustomerInteractionController,
            customer_segment_controller_1.CustomerSegmentController,
            customer_loyalty_controller_1.CustomerLoyaltyController,
            customer_credit_controller_1.CustomerCreditController,
            customer_report_controller_1.CustomerReportController,
        ],
        providers: [
            customer_service_1.CustomerService,
            customer_group_service_1.CustomerGroupService,
            customer_interaction_service_1.CustomerInteractionService,
            customer_segment_service_1.CustomerSegmentService,
            customer_loyalty_service_1.CustomerLoyaltyService,
            customer_credit_service_1.CustomerCreditService,
            customer_report_service_1.CustomerReportService,
        ],
        exports: [
            customer_service_1.CustomerService,
            customer_group_service_1.CustomerGroupService,
            customer_interaction_service_1.CustomerInteractionService,
            customer_segment_service_1.CustomerSegmentService,
            customer_loyalty_service_1.CustomerLoyaltyService,
            customer_credit_service_1.CustomerCreditService,
            customer_report_service_1.CustomerReportService,
        ],
    })
], CustomersModule);
//# sourceMappingURL=customers.module.js.map