import { TransactionService } from '../services/transaction.service';
export declare class TransactionController {
    private readonly transactionService;
    constructor(transactionService: TransactionService);
    create(createTransactionDto: any): Promise<import("../entities/transaction.entity").Transaction>;
    findAll(startDate?: string, endDate?: string, type?: string, status?: string, accountId?: string): Promise<import("../entities/transaction.entity").Transaction[]>;
    getAccountLedger(accountId: string, startDate?: string, endDate?: string): Promise<any>;
    findOne(id: string): Promise<import("../entities/transaction.entity").Transaction>;
    update(id: string, updateTransactionDto: any): Promise<import("../entities/transaction.entity").Transaction>;
    remove(id: string): Promise<void>;
    postTransaction(id: string, postDto: {
        approvedBy?: string;
    }): Promise<import("../entities/transaction.entity").Transaction>;
    reverseTransaction(id: string, reverseDto: {
        reason: string;
        reversedBy?: string;
    }): Promise<import("../entities/transaction.entity").Transaction>;
}
