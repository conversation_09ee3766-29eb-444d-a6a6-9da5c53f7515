export declare enum FeatureFlagType {
    BOOLEAN = "boolean",
    STRING = "string",
    NUMBER = "number",
    JSON = "json",
    PERCENTAGE = "percentage"
}
export declare enum FeatureFlagStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    TESTING = "testing",
    DEPRECATED = "deprecated"
}
export declare class FeatureFlag {
    id: string;
    key: string;
    name: string;
    description: string;
    type: FeatureFlagType;
    status: FeatureFlagStatus;
    defaultValue: string;
    variations: any;
    targeting: any;
    rolloutPercentage: any;
    environments: string[];
    tags: string[];
    isTemporary: boolean;
    expiresAt: Date;
    createdBy: string;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
