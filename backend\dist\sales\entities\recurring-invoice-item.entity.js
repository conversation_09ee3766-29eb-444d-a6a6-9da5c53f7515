"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecurringInvoiceItem = void 0;
const typeorm_1 = require("typeorm");
const recurring_invoice_entity_1 = require("./recurring-invoice.entity");
let RecurringInvoiceItem = class RecurringInvoiceItem {
    id;
    recurringInvoiceId;
    recurringInvoice;
    lineNumber;
    description;
    productCode;
    unitPrice;
    quantity;
    discount;
    taxType;
    taxAmount;
    lineTotal;
    unit;
    notes;
    tenantId;
};
exports.RecurringInvoiceItem = RecurringInvoiceItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "recurringInvoiceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => recurring_invoice_entity_1.RecurringInvoice, recurringInvoice => recurringInvoice.items, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'recurringInvoiceId' }),
    __metadata("design:type", recurring_invoice_entity_1.RecurringInvoice)
], RecurringInvoiceItem.prototype, "recurringInvoice", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], RecurringInvoiceItem.prototype, "lineNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "productCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], RecurringInvoiceItem.prototype, "unitPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], RecurringInvoiceItem.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoiceItem.prototype, "discount", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "taxType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoiceItem.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], RecurringInvoiceItem.prototype, "lineTotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RecurringInvoiceItem.prototype, "tenantId", void 0);
exports.RecurringInvoiceItem = RecurringInvoiceItem = __decorate([
    (0, typeorm_1.Entity)('recurring_invoice_items')
], RecurringInvoiceItem);
//# sourceMappingURL=recurring-invoice-item.entity.js.map