"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TicketComment = exports.CommentType = void 0;
const typeorm_1 = require("typeorm");
const support_ticket_entity_1 = require("./support-ticket.entity");
var CommentType;
(function (CommentType) {
    CommentType["PUBLIC"] = "public";
    CommentType["INTERNAL"] = "internal";
    CommentType["SYSTEM"] = "system";
})(CommentType || (exports.CommentType = CommentType = {}));
let TicketComment = class TicketComment {
    id;
    ticketId;
    ticket;
    content;
    type;
    authorId;
    authorName;
    isEdited;
    editedAt;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.TicketComment = TicketComment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], TicketComment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], TicketComment.prototype, "ticketId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => support_ticket_entity_1.SupportTicket, ticket => ticket.comments, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'ticketId' }),
    __metadata("design:type", support_ticket_entity_1.SupportTicket)
], TicketComment.prototype, "ticket", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], TicketComment.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CommentType,
        default: CommentType.PUBLIC,
    }),
    __metadata("design:type", String)
], TicketComment.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], TicketComment.prototype, "authorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], TicketComment.prototype, "authorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], TicketComment.prototype, "isEdited", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], TicketComment.prototype, "editedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], TicketComment.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], TicketComment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], TicketComment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], TicketComment.prototype, "updatedAt", void 0);
exports.TicketComment = TicketComment = __decorate([
    (0, typeorm_1.Entity)('ticket_comments')
], TicketComment);
//# sourceMappingURL=ticket-comment.entity.js.map