import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Project } from './project.entity';

export enum ProjectRole {
  MANAGER = 'manager',
  LEAD = 'lead',
  DEVELOPER = 'developer',
  DESIGNER = 'designer',
  TESTER = 'tester',
  ANALYST = 'analyst',
  STAKEHOLDER = 'stakeholder',
  CLIENT = 'client',
  OBSERVER = 'observer',
}

@Entity('project_members')
export class ProjectMember {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  projectId: string;

  @ManyToOne(() => Project, project => project.members, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column()
  userId: string;

  @Column({
    type: 'enum',
    enum: ProjectRole,
    default: ProjectRole.DEVELOPER,
  })
  role: ProjectRole;

  @Column({ type: 'date' })
  joinedDate: Date;

  @Column({ type: 'date', nullable: true })
  leftDate: Date;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  hourlyRate: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'json', nullable: true })
  permissions: string[];

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
