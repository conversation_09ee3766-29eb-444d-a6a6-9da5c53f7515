export declare enum IncidentSeverity {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export declare enum IncidentStatus {
    NEW = "new",
    INVESTIGATING = "investigating",
    IDENTIFIED = "identified",
    MONITORING = "monitoring",
    RESOLVED = "resolved",
    CLOSED = "closed"
}
export declare enum IncidentCategory {
    HARDWARE_FAILURE = "hardware_failure",
    SOFTWARE_ISSUE = "software_issue",
    NETWORK_OUTAGE = "network_outage",
    SECURITY_BREACH = "security_breach",
    DATA_LOSS = "data_loss",
    PERFORMANCE_ISSUE = "performance_issue",
    SERVICE_UNAVAILABLE = "service_unavailable",
    OTHER = "other"
}
export declare class Incident {
    id: string;
    incidentNumber: string;
    title: string;
    description: string;
    category: IncidentCategory;
    severity: IncidentSeverity;
    status: IncidentStatus;
    reportedBy: string;
    reporterName: string;
    assignedTo: string;
    reportedAt: Date;
    acknowledgedAt: Date;
    resolvedAt: Date;
    closedAt: Date;
    affectedServices: string[];
    affectedUsers: string[];
    symptoms: string;
    rootCause: string;
    resolution: string;
    workaround: string;
    preventiveMeasures: string;
    timeline: any[];
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
