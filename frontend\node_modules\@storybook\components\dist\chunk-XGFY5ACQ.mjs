import { require_t4_templating } from './chunk-WR6YVR3X.mjs';
import { require_vbnet } from './chunk-6Q7LO4NN.mjs';
import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_t4_vb=__commonJS({"../../node_modules/refractor/lang/t4-vb.js"(exports,module){var refractorT4Templating=require_t4_templating(),refractorVbnet=require_vbnet();module.exports=t4Vb;t4Vb.displayName="t4Vb";t4Vb.aliases=[];function t4Vb(Prism){Prism.register(refractorT4Templating),Prism.register(refractorVbnet),Prism.languages["t4-vb"]=Prism.languages["t4-templating"].createT4("vbnet");}}});

export { require_t4_vb };
