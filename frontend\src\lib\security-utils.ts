import CryptoJS from 'crypto-js'
import { jwtDecode } from 'jwt-decode'

/**
 * Enterprise Security Utilities for the Ultimate ERP System
 */

export interface SecurityConfig {
  encryptionKey: string
  jwtSecret: string
  sessionTimeout: number
  maxLoginAttempts: number
  passwordMinLength: number
  requireMFA: boolean
}

export interface LoginAttempt {
  email: string
  timestamp: number
  success: boolean
  ipAddress: string
  userAgent: string
}

export interface SecurityEvent {
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'mfa_enabled' | 'suspicious_activity'
  userId?: string
  email?: string
  timestamp: number
  ipAddress: string
  userAgent: string
  details: any
}

/**
 * Password Security Manager
 */
export class PasswordSecurity {
  private static readonly SPECIAL_CHARS = '!@#$%^&*()_+-=[]{}|;:,.<>?'

  static validatePassword(password: string): {
    isValid: boolean
    score: number
    feedback: string[]
  } {
    const feedback: string[] = []
    let score = 0

    // Length check
    if (password.length < 8) {
      feedback.push('Password must be at least 8 characters long')
    } else if (password.length >= 12) {
      score += 2
    } else {
      score += 1
    }

    // Uppercase check
    if (!/[A-Z]/.test(password)) {
      feedback.push('Password must contain at least one uppercase letter')
    } else {
      score += 1
    }

    // Lowercase check
    if (!/[a-z]/.test(password)) {
      feedback.push('Password must contain at least one lowercase letter')
    } else {
      score += 1
    }

    // Number check
    if (!/\d/.test(password)) {
      feedback.push('Password must contain at least one number')
    } else {
      score += 1
    }

    // Special character check
    if (!new RegExp(`[${this.SPECIAL_CHARS.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)) {
      feedback.push('Password must contain at least one special character')
    } else {
      score += 1
    }

    // Common patterns check
    if (/(.)\1{2,}/.test(password)) {
      feedback.push('Password should not contain repeated characters')
      score -= 1
    }

    if (/123|abc|qwe|password|admin/i.test(password)) {
      feedback.push('Password should not contain common patterns')
      score -= 2
    }

    const isValid = feedback.length === 0 && score >= 4
    return { isValid, score: Math.max(0, score), feedback }
  }

  static generateSecurePassword(length: number = 16): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const numbers = '0123456789'
    const special = this.SPECIAL_CHARS

    let password = ''

    // Ensure at least one character from each category
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]
    password += special[Math.floor(Math.random() * special.length)]

    // Fill the rest randomly
    const allChars = uppercase + lowercase + numbers + special
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  static hashPassword(password: string, salt?: string): string {
    const saltToUse = salt || CryptoJS.lib.WordArray.random(128/8).toString()
    const hash = CryptoJS.PBKDF2(password, saltToUse, {
      keySize: 256/32,
      iterations: 10000
    }).toString()
    return `${saltToUse}:${hash}`
  }

  static verifyPassword(password: string, hashedPassword: string): boolean {
    const [salt, hash] = hashedPassword.split(':')
    const newHash = CryptoJS.PBKDF2(password, salt, {
      keySize: 256/32,
      iterations: 10000
    }).toString()
    return hash === newHash
  }
}

/**
 * Data Encryption Manager
 */
export class DataEncryption {
  private static readonly ALGORITHM = 'AES'

  static encrypt(data: string, key: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(data, key).toString()
      return encrypted
    } catch (error) {
      throw new Error('Encryption failed')
    }
  }

  static decrypt(encryptedData: string, key: string): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, key)
      return decrypted.toString(CryptoJS.enc.Utf8)
    } catch (error) {
      throw new Error('Decryption failed')
    }
  }

  static encryptObject(obj: any, key: string): string {
    return this.encrypt(JSON.stringify(obj), key)
  }

  static decryptObject<T>(encryptedData: string, key: string): T {
    const decrypted = this.decrypt(encryptedData, key)
    return JSON.parse(decrypted)
  }

  static generateKey(): string {
    return CryptoJS.lib.WordArray.random(256/8).toString()
  }
}

/**
 * Session Management
 */
export class SessionManager {
  private static readonly SESSION_KEY = 'erp_session'
  private static readonly REFRESH_KEY = 'erp_refresh'

  static setSession(token: string, refreshToken?: string): void {
    localStorage.setItem(this.SESSION_KEY, token)
    if (refreshToken) {
      localStorage.setItem(this.REFRESH_KEY, refreshToken)
    }
  }

  static getSession(): string | null {
    return localStorage.getItem(this.SESSION_KEY)
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_KEY)
  }

  static clearSession(): void {
    localStorage.removeItem(this.SESSION_KEY)
    localStorage.removeItem(this.REFRESH_KEY)
    sessionStorage.clear()
  }

  static isSessionValid(): boolean {
    const token = this.getSession()
    if (!token) return false

    try {
      const decoded = jwtDecode(token)
      const now = Date.now() / 1000
      return decoded.exp ? decoded.exp > now : false
    } catch {
      return false
    }
  }

  static getSessionData(): any {
    const token = this.getSession()
    if (!token) return null

    try {
      return jwtDecode(token)
    } catch {
      return null
    }
  }
}

/**
 * Login Attempt Tracker
 */
export class LoginAttemptTracker {
  private static readonly STORAGE_KEY = 'login_attempts'
  private static readonly MAX_ATTEMPTS = 5
  private static readonly LOCKOUT_DURATION = 15 * 60 * 1000 // 15 minutes

  static recordAttempt(email: string, success: boolean): void {
    const attempts = this.getAttempts()
    const attempt: LoginAttempt = {
      email,
      timestamp: Date.now(),
      success,
      ipAddress: 'unknown', // Would be set by server
      userAgent: navigator.userAgent
    }

    attempts.push(attempt)

    // Keep only last 100 attempts
    if (attempts.length > 100) {
      attempts.splice(0, attempts.length - 100)
    }

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(attempts))
  }

  static getAttempts(): LoginAttempt[] {
    const stored = localStorage.getItem(this.STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  }

  static isAccountLocked(email: string): boolean {
    const attempts = this.getAttempts()
    const recentAttempts = attempts.filter(
      a => a.email === email &&
           Date.now() - a.timestamp < this.LOCKOUT_DURATION &&
           !a.success
    )

    return recentAttempts.length >= this.MAX_ATTEMPTS
  }

  static getTimeUntilUnlock(email: string): number {
    const attempts = this.getAttempts()
    const failedAttempts = attempts.filter(
      a => a.email === email && !a.success
    ).sort((a, b) => b.timestamp - a.timestamp)

    if (failedAttempts.length < this.MAX_ATTEMPTS) return 0

    const oldestRelevantAttempt = failedAttempts[this.MAX_ATTEMPTS - 1]
    const unlockTime = oldestRelevantAttempt.timestamp + this.LOCKOUT_DURATION

    return Math.max(0, unlockTime - Date.now())
  }

  static clearAttempts(email: string): void {
    const attempts = this.getAttempts()
    const filtered = attempts.filter(a => a.email !== email)
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filtered))
  }
}

/**
 * Security Event Logger
 */
export class SecurityEventLogger {
  private static readonly STORAGE_KEY = 'security_events'
  private static readonly MAX_EVENTS = 1000

  static logEvent(event: Omit<SecurityEvent, 'timestamp' | 'ipAddress' | 'userAgent'>): void {
    const events = this.getEvents()
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: Date.now(),
      ipAddress: 'unknown', // Would be set by server
      userAgent: navigator.userAgent
    }

    events.push(securityEvent)

    // Keep only recent events
    if (events.length > this.MAX_EVENTS) {
      events.splice(0, events.length - this.MAX_EVENTS)
    }

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(events))
  }

  static getEvents(): SecurityEvent[] {
    const stored = localStorage.getItem(this.STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  }

  static getEventsByType(type: SecurityEvent['type']): SecurityEvent[] {
    return this.getEvents().filter(e => e.type === type)
  }

  static getEventsByUser(userId: string): SecurityEvent[] {
    return this.getEvents().filter(e => e.userId === userId)
  }

  static clearEvents(): void {
    localStorage.removeItem(this.STORAGE_KEY)
  }
}

/**
 * Input Sanitization
 */
export class InputSanitizer {
  static sanitizeHtml(input: string): string {
    const div = document.createElement('div')
    div.textContent = input
    return div.innerHTML
  }

  static sanitizeSQL(input: string): string {
    return input.replace(/['";\\]/g, '')
  }

  static sanitizeXSS(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  static validateInput(input: string, type: 'email' | 'phone' | 'url' | 'alphanumeric'): boolean {
    const patterns = {
      email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      phone: /^\+?[\d\s\-\(\)]+$/,
      url: /^https?:\/\/.+/,
      alphanumeric: /^[a-zA-Z0-9]+$/
    }

    return patterns[type].test(input)
  }
}

/**
 * CSRF Protection
 */
export class CSRFProtection {
  private static readonly TOKEN_KEY = 'csrf_token'

  static generateToken(): string {
    const token = CryptoJS.lib.WordArray.random(256/8).toString()
    sessionStorage.setItem(this.TOKEN_KEY, token)
    return token
  }

  static getToken(): string | null {
    return sessionStorage.getItem(this.TOKEN_KEY)
  }

  static validateToken(token: string): boolean {
    const storedToken = this.getToken()
    return storedToken === token
  }

  static clearToken(): void {
    sessionStorage.removeItem(this.TOKEN_KEY)
  }
}

/**
 * Content Security Policy Helper
 */
export class CSPHelper {
  static generateNonce(): string {
    return CryptoJS.lib.WordArray.random(128/8).toString()
  }

  static isScriptAllowed(src: string, allowedDomains: string[]): boolean {
    try {
      const url = new URL(src)
      return allowedDomains.includes(url.hostname)
    } catch {
      return false
    }
  }
}

// Export utility instances
export const passwordSecurity = PasswordSecurity
export const dataEncryption = DataEncryption
export const sessionManager = SessionManager
export const loginAttemptTracker = LoginAttemptTracker
export const securityEventLogger = SecurityEventLogger
export const inputSanitizer = InputSanitizer
export const csrfProtection = CSRFProtection
export const cspHelper = CSPHelper
