// Type declarations for external libraries that don't have proper TypeScript support

declare module 'conventional-commits-parser' {
  interface ParsedCommit {
    type?: string
    scope?: string
    subject?: string
    body?: string
    footer?: string
    notes?: Array<{
      title: string
      text: string
    }>
    references?: Array<{
      action: string
      owner?: string
      repository?: string
      issue: string
      raw: string
      prefix: string
    }>
    mentions?: string[]
    revert?: {
      header?: string
      hash?: string
    }
    merge?: {
      branch?: string
      tag?: string
    }
    header?: string
    hash?: string
  }

  interface ParserOptions {
    headerPattern?: RegExp
    headerCorrespondence?: string[]
    referenceActions?: string[]
    issuePrefixes?: string[]
    noteKeywords?: string[]
    fieldPattern?: RegExp
    revertPattern?: RegExp
    revertCorrespondence?: string[]
    warn?: boolean | ((warning: string) => void)
    mergePattern?: RegExp
    mergeCorrespondence?: string[]
  }

  function parser(commit: string, options?: ParserOptions): ParsedCommit
  function parser(options?: ParserOptions): (commit: string) => ParsedCommit

  export = parser
}

declare module 'long' {
  class Long {
    static readonly MAX_VALUE: Long
    static readonly MIN_VALUE: Long
    static readonly NEG_ONE: Long
    static readonly ONE: Long
    static readonly ZERO: Long
    static readonly UZERO: Long
    static readonly MAX_SAFE_INTEGER: Long
    static readonly MIN_SAFE_INTEGER: Long

    constructor(low: number, high?: number, unsigned?: boolean)

    static fromBits(lowBits: number, highBits: number, unsigned?: boolean): Long
    static fromInt(value: number, unsigned?: boolean): Long
    static fromNumber(value: number, unsigned?: boolean): Long
    static fromString(str: string, unsigned?: boolean | number, radix?: number): Long
    static fromValue(val: Long | number | string | { low: number; high: number; unsigned: boolean }): Long
    static isLong(obj: any): obj is Long

    readonly high: number
    readonly low: number
    readonly unsigned: boolean

    add(addend: Long | number | string): Long
    and(other: Long | number | string): Long
    compare(other: Long | number | string): number
    div(divisor: Long | number | string): Long
    divide(divisor: Long | number | string): Long
    equals(other: Long | number | string): boolean
    getHighBits(): number
    getHighBitsUnsigned(): number
    getLowBits(): number
    getLowBitsUnsigned(): number
    getNumBitsAbs(): number
    greaterThan(other: Long | number | string): boolean
    greaterThanOrEqual(other: Long | number | string): boolean
    gt(other: Long | number | string): boolean
    gte(other: Long | number | string): boolean
    isEven(): boolean
    isNegative(): boolean
    isOdd(): boolean
    isPositive(): boolean
    isZero(): boolean
    lessThan(other: Long | number | string): boolean
    lessThanOrEqual(other: Long | number | string): boolean
    lt(other: Long | number | string): boolean
    lte(other: Long | number | string): boolean
    modulo(divisor: Long | number | string): Long
    multiply(multiplier: Long | number | string): Long
    negate(): Long
    not(): Long
    notEquals(other: Long | number | string): boolean
    or(other: Long | number | string): Long
    shiftLeft(numBits: number | Long): Long
    shiftRight(numBits: number | Long): Long
    shiftRightUnsigned(numBits: number | Long): Long
    shl(numBits: number | Long): Long
    shr(numBits: number | Long): Long
    shru(numBits: number | Long): Long
    subtract(subtrahend: Long | number | string): Long
    toBytes(le?: boolean): number[]
    toBytesLE(): number[]
    toBytesBE(): number[]
    toInt(): number
    toNumber(): number
    toString(radix?: number): string
    toUnsigned(): Long
    xor(other: Long | number | string): Long
  }

  export = Long
}

declare module 'offscreencanvas' {
  interface OffscreenCanvasRenderingContext2D extends CanvasRenderingContext2D {
    // Additional methods specific to OffscreenCanvas
  }

  interface OffscreenCanvas extends EventTarget {
    width: number
    height: number
    
    getContext(contextId: '2d', options?: CanvasRenderingContext2DSettings): OffscreenCanvasRenderingContext2D | null
    getContext(contextId: 'webgl', options?: WebGLContextAttributes): WebGLRenderingContext | null
    getContext(contextId: 'webgl2', options?: WebGLContextAttributes): WebGL2RenderingContext | null
    getContext(contextId: string, options?: any): RenderingContext | null
    
    transferToImageBitmap(): ImageBitmap
    convertToBlob(options?: ImageEncodeOptions): Promise<Blob>
  }

  interface OffscreenCanvasConstructor {
    new(width: number, height: number): OffscreenCanvas
    prototype: OffscreenCanvas
  }

  declare const OffscreenCanvas: OffscreenCanvasConstructor

  export = OffscreenCanvas
}

// Additional type augmentations for better TypeScript support
declare global {
  interface Window {
    OffscreenCanvas?: typeof OffscreenCanvas
  }
}

export {}
