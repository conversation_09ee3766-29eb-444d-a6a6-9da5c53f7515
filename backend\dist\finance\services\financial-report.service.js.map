{"version": 3, "file": "financial-report.service.js", "sourceRoot": "", "sources": ["../../../src/finance/services/financial-report.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,iFAAgG;AAChG,uDAAmD;AACnD,+DAA2D;AAGpD,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGvB;IACA;IACA;IAJV,YAEU,gBAA6C,EAC7C,cAA8B,EAC9B,kBAAsC;QAFtC,qBAAgB,GAAhB,gBAAgB,CAA6B;QAC7C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,uBAAkB,GAAlB,kBAAkB,CAAoB;IAC7C,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,eAAoB;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC1C,GAAG,eAAe;YAClB,MAAM,EAAE,sCAAY,CAAC,UAAU;SAChC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAG7D,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAExC,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAa;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAExE,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;YAClB,YAAY,CAAC,QAAQ,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,YAAY,CAAC,QAAQ,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,YAAY;aAChB,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;aACnC,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAe,EAAE,OAAa;QACvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAErD,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;QAC5D,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QACrE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QAE7D,MAAM,YAAY,GAAG;YACnB,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE;gBACN,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,eAAe,CAAC;gBACpE,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,aAAa,CAAC;gBAChE,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;aAC/D;YACD,WAAW,EAAE;gBACX,kBAAkB,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,mBAAmB,CAAC;gBAClF,mBAAmB,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,qBAAqB,CAAC;gBACrF,gBAAgB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;aACzE;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;aAC/D;SACF,CAAC;QAEF,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,SAAe,EAAE,OAAa;QAC1D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAErD,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAEhE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACxE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAE1E,MAAM,eAAe,GAAG;YACtB,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;YAC9B,OAAO,EAAE;gBACP,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,YAAY;aACpB;YACD,QAAQ,EAAE;gBACR,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,mBAAmB,CAAC;gBAC9E,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,oBAAoB,CAAC;gBAC7E,sBAAsB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,wBAAwB,CAAC;gBACxF,KAAK,EAAE,aAAa;aACrB;YACD,SAAS,EAAE,YAAY,GAAG,aAAa;YACvC,WAAW,EAAE,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,oBAAoB,CAAC;iBACrF,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9C,CAAC;QAEF,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,SAAe,EAAE,OAAa;QAG5D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACzD,SAAS;YACT,OAAO;YACP,MAAM,EAAE,QAAQ;SACjB,CAAC,CAAC;QAEH,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAClD,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CACxC,CAAC;QAEF,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAClD,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAC5E,CAAC;QAEF,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAClD,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CACtE,CAAC;QAEF,MAAM,QAAQ,GAAG;YACf,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;YAC9B,mBAAmB,EAAE;gBACnB,YAAY,EAAE,mBAAmB;gBACjC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;aACnE;YACD,mBAAmB,EAAE;gBACnB,YAAY,EAAE,mBAAmB;gBACjC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;aACnE;YACD,mBAAmB,EAAE;gBACnB,YAAY,EAAE,mBAAmB;gBACjC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;aACnE;SACF,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,YAAiB;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC1C,GAAG,YAAY;YACf,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,sCAAY,CAAC,SAAS;SAC/B,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC/C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,UAAe,CAAC;YAEpB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,oCAAU,CAAC,aAAa;oBAC3B,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC/E,MAAM;gBACR,KAAK,oCAAU,CAAC,gBAAgB;oBAC9B,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;oBAClF,MAAM;gBACR,KAAK,oCAAU,CAAC,SAAS;oBACvB,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;oBACpF,MAAM;gBACR,KAAK,oCAAU,CAAC,aAAa;oBAC3B,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACvE,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,GAAG,sCAAY,CAAC,SAAS,CAAC;YACvC,MAAM,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAEhC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,GAAG,sCAAY,CAAC,MAAM,CAAC;YACpC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YACpC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;CACF,CAAA;AA7LY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCACR,oBAAU;QACZ,gCAAc;QACV,wCAAkB;GALrC,sBAAsB,CA6LlC"}