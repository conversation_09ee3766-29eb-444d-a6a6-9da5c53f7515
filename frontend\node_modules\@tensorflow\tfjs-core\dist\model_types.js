/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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