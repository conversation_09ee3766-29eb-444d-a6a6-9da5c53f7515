"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerSegmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_segment_entity_1 = require("../entities/customer-segment.entity");
const customer_entity_1 = require("../entities/customer.entity");
let CustomerSegmentService = class CustomerSegmentService {
    segmentRepository;
    customerRepository;
    constructor(segmentRepository, customerRepository) {
        this.segmentRepository = segmentRepository;
        this.customerRepository = customerRepository;
    }
    async create(segmentData) {
        const existingSegment = await this.segmentRepository.findOne({
            where: { name: segmentData.name },
        });
        if (existingSegment) {
            throw new common_1.BadRequestException(`Segment with name ${segmentData.name} already exists`);
        }
        const segment = this.segmentRepository.create(segmentData);
        const savedSegment = await this.segmentRepository.save(segment);
        if (savedSegment.criteria) {
            await this.refreshSegmentCustomers(savedSegment.id);
        }
        return savedSegment;
    }
    async findAll() {
        return this.segmentRepository.find({
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const segment = await this.segmentRepository.findOne({
            where: { id },
        });
        if (!segment) {
            throw new common_1.NotFoundException(`Customer segment with ID ${id} not found`);
        }
        return segment;
    }
    async update(id, updateData) {
        const segment = await this.findOne(id);
        if (updateData.name && updateData.name !== segment.name) {
            const existingSegment = await this.segmentRepository.findOne({
                where: { name: updateData.name },
            });
            if (existingSegment) {
                throw new common_1.BadRequestException(`Segment with name ${updateData.name} already exists`);
            }
        }
        await this.segmentRepository.update(id, updateData);
        if (updateData.criteria) {
            await this.refreshSegmentCustomers(id);
        }
        return this.findOne(id);
    }
    async remove(id) {
        const segment = await this.findOne(id);
        await this.segmentRepository.remove(segment);
    }
    async refreshSegmentCustomers(segmentId) {
        const segment = await this.findOne(segmentId);
        if (!segment.criteria) {
            return 0;
        }
        const customers = await this.findCustomersByCriteria(segment.criteria);
        await this.segmentRepository.update(segmentId, {
            customerCount: customers.length,
            lastUpdated: new Date(),
        });
        return customers.length;
    }
    async findCustomersByCriteria(criteria) {
        const queryBuilder = this.customerRepository.createQueryBuilder('customer');
        if (criteria.status) {
            if (Array.isArray(criteria.status)) {
                queryBuilder.andWhere('customer.status IN (:...statuses)', { statuses: criteria.status });
            }
            else {
                queryBuilder.andWhere('customer.status = :status', { status: criteria.status });
            }
        }
        if (criteria.type) {
            if (Array.isArray(criteria.type)) {
                queryBuilder.andWhere('customer.type IN (:...types)', { types: criteria.type });
            }
            else {
                queryBuilder.andWhere('customer.type = :type', { type: criteria.type });
            }
        }
        if (criteria.tier) {
            if (Array.isArray(criteria.tier)) {
                queryBuilder.andWhere('customer.tier IN (:...tiers)', { tiers: criteria.tier });
            }
            else {
                queryBuilder.andWhere('customer.tier = :tier', { tier: criteria.tier });
            }
        }
        if (criteria.totalSpent) {
            if (criteria.totalSpent.min !== undefined) {
                queryBuilder.andWhere('customer.totalSpent >= :minSpent', { minSpent: criteria.totalSpent.min });
            }
            if (criteria.totalSpent.max !== undefined) {
                queryBuilder.andWhere('customer.totalSpent <= :maxSpent', { maxSpent: criteria.totalSpent.max });
            }
        }
        if (criteria.loyaltyPoints) {
            if (criteria.loyaltyPoints.min !== undefined) {
                queryBuilder.andWhere('customer.loyaltyPoints >= :minPoints', { minPoints: criteria.loyaltyPoints.min });
            }
            if (criteria.loyaltyPoints.max !== undefined) {
                queryBuilder.andWhere('customer.loyaltyPoints <= :maxPoints', { maxPoints: criteria.loyaltyPoints.max });
            }
        }
        if (criteria.lastPurchaseDate) {
            if (criteria.lastPurchaseDate.after) {
                queryBuilder.andWhere('customer.lastPurchaseDate >= :afterDate', { afterDate: criteria.lastPurchaseDate.after });
            }
            if (criteria.lastPurchaseDate.before) {
                queryBuilder.andWhere('customer.lastPurchaseDate <= :beforeDate', { beforeDate: criteria.lastPurchaseDate.before });
            }
        }
        if (criteria.groupId) {
            if (Array.isArray(criteria.groupId)) {
                queryBuilder.andWhere('customer.groupId IN (:...groupIds)', { groupIds: criteria.groupId });
            }
            else {
                queryBuilder.andWhere('customer.groupId = :groupId', { groupId: criteria.groupId });
            }
        }
        if (criteria.tags && criteria.tags.length > 0) {
            const tagConditions = criteria.tags.map((tag, index) => {
                queryBuilder.setParameter(`tag${index}`, `%"${tag}"%`);
                return `customer.tags LIKE :tag${index}`;
            });
            queryBuilder.andWhere(`(${tagConditions.join(' OR ')})`);
        }
        if (criteria.allowMarketing !== undefined) {
            queryBuilder.andWhere('customer.allowMarketing = :allowMarketing', { allowMarketing: criteria.allowMarketing });
        }
        if (criteria.createdAt) {
            if (criteria.createdAt.after) {
                queryBuilder.andWhere('customer.createdAt >= :createdAfter', { createdAfter: criteria.createdAt.after });
            }
            if (criteria.createdAt.before) {
                queryBuilder.andWhere('customer.createdAt <= :createdBefore', { createdBefore: criteria.createdAt.before });
            }
        }
        return queryBuilder.getMany();
    }
    async getSegmentCustomers(segmentId, page = 1, limit = 20) {
        const segment = await this.findOne(segmentId);
        if (!segment.criteria) {
            return {
                customers: [],
                total: 0,
                page,
                totalPages: 0,
            };
        }
        const allCustomers = await this.findCustomersByCriteria(segment.criteria);
        const total = allCustomers.length;
        const totalPages = Math.ceil(total / limit);
        const offset = (page - 1) * limit;
        const customers = allCustomers.slice(offset, offset + limit);
        return {
            customers,
            total,
            page,
            totalPages,
        };
    }
    async createHighValueSegment(minSpent = 10000) {
        return this.create({
            name: 'High Value Customers',
            description: `Customers who have spent more than $${minSpent}`,
            criteria: {
                totalSpent: { min: minSpent },
                status: ['active'],
            },
            isActive: true,
        });
    }
    async createLoyaltySegment(minPoints = 1000) {
        return this.create({
            name: 'Loyalty Program Members',
            description: `Customers with ${minPoints}+ loyalty points`,
            criteria: {
                loyaltyPoints: { min: minPoints },
                status: ['active'],
            },
            isActive: true,
        });
    }
    async createInactiveSegment(daysSinceLastPurchase = 90) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysSinceLastPurchase);
        return this.create({
            name: 'Inactive Customers',
            description: `Customers who haven't purchased in ${daysSinceLastPurchase} days`,
            criteria: {
                lastPurchaseDate: { before: cutoffDate },
                status: ['active'],
            },
            isActive: true,
        });
    }
    async createNewCustomerSegment(daysSinceRegistration = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysSinceRegistration);
        return this.create({
            name: 'New Customers',
            description: `Customers registered in the last ${daysSinceRegistration} days`,
            criteria: {
                createdAt: { after: cutoffDate },
            },
            isActive: true,
        });
    }
    async getSegmentAnalytics(segmentId) {
        const segment = await this.findOne(segmentId);
        const customers = await this.findCustomersByCriteria(segment.criteria || {});
        const totalCustomers = customers.length;
        const totalSpent = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0);
        const averageSpent = totalCustomers > 0 ? totalSpent / totalCustomers : 0;
        const totalLoyaltyPoints = customers.reduce((sum, c) => sum + (c.loyaltyPoints || 0), 0);
        const statusDistribution = customers.reduce((acc, customer) => {
            acc[customer.status] = (acc[customer.status] || 0) + 1;
            return acc;
        }, {});
        const tierDistribution = customers.reduce((acc, customer) => {
            acc[customer.tier] = (acc[customer.tier] || 0) + 1;
            return acc;
        }, {});
        const typeDistribution = customers.reduce((acc, customer) => {
            acc[customer.type] = (acc[customer.type] || 0) + 1;
            return acc;
        }, {});
        return {
            segmentId,
            segmentName: segment.name,
            totalCustomers,
            totalSpent,
            averageSpent,
            totalLoyaltyPoints,
            statusDistribution,
            tierDistribution,
            typeDistribution,
            lastUpdated: segment.lastUpdated,
        };
    }
    async refreshAllSegments() {
        const segments = await this.segmentRepository.find({
            where: { isActive: true },
        });
        const results = [];
        for (const segment of segments) {
            const customerCount = await this.refreshSegmentCustomers(segment.id);
            results.push({
                segmentId: segment.id,
                customerCount,
            });
        }
        return results;
    }
    async activateSegment(id) {
        await this.segmentRepository.update(id, { isActive: true });
        return this.findOne(id);
    }
    async deactivateSegment(id) {
        await this.segmentRepository.update(id, { isActive: false });
        return this.findOne(id);
    }
};
exports.CustomerSegmentService = CustomerSegmentService;
exports.CustomerSegmentService = CustomerSegmentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_segment_entity_1.CustomerSegment)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerSegmentService);
//# sourceMappingURL=customer-segment.service.js.map