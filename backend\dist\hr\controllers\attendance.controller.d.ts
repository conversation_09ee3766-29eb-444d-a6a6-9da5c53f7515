import { AttendanceService } from '../services/attendance.service';
export declare class AttendanceController {
    private readonly attendanceService;
    constructor(attendanceService: AttendanceService);
    create(createAttendanceDto: any): Promise<import("../entities/attendance.entity").Attendance>;
    findAll(employeeId?: string, startDate?: string, endDate?: string, status?: string): Promise<import("../entities/attendance.entity").Attendance[]>;
    getAttendanceReport(employeeId: string, startDate: string, endDate: string): Promise<any>;
    checkIn(checkInDto: {
        employeeId: string;
        location?: string;
        gpsCoordinates?: any;
    }): Promise<import("../entities/attendance.entity").Attendance>;
    checkOut(checkOutDto: {
        employeeId: string;
        notes?: string;
    }): Promise<import("../entities/attendance.entity").Attendance>;
    findOne(id: string): Promise<import("../entities/attendance.entity").Attendance>;
    update(id: string, updateAttendanceDto: any): Promise<import("../entities/attendance.entity").Attendance>;
    remove(id: string): Promise<void>;
}
