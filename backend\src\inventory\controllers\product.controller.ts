import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ProductService } from '../services/product.service';
import { Product } from '../entities/product.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('products')
@UseGuards(JwtAuthGuard)
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createProductDto: Partial<Product>) {
    return this.productService.create(createProductDto);
  }

  @Get()
  async findAll() {
    return this.productService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.productService.getProductStatistics();
  }

  @Get('low-stock')
  async getLowStockProducts(@Query('threshold') threshold?: string) {
    const thresholdNum = threshold ? parseInt(threshold) : 10;
    return this.productService.getLowStockProducts(thresholdNum);
  }

  @Get('out-of-stock')
  async getOutOfStockProducts() {
    return this.productService.getOutOfStockProducts();
  }

  @Get('top-selling')
  async getTopSellingProducts(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 10;
    return this.productService.getTopSellingProducts(limitNum);
  }

  @Get('search')
  async searchProducts(@Query('q') searchTerm: string) {
    return this.productService.searchProducts(searchTerm);
  }

  @Get('category/:categoryId')
  async findByCategory(@Param('categoryId') categoryId: string) {
    return this.productService.findByCategory(categoryId);
  }

  @Get('sku/:sku')
  async findBySku(@Param('sku') sku: string) {
    return this.productService.findBySku(sku);
  }

  @Get('barcode/:barcode')
  async findByBarcode(@Param('barcode') barcode: string) {
    return this.productService.findByBarcode(barcode);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.productService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: Partial<Product>,
  ) {
    return this.productService.update(id, updateProductDto);
  }

  @Post('bulk-update-prices')
  async bulkUpdatePrices(
    @Body() updates: Array<{ id: string; salePrice: number; costPrice?: number }>,
  ) {
    await this.productService.bulkUpdatePrices(updates);
    return { message: 'Prices updated successfully' };
  }

  @Post(':id/update-stock')
  async updateStock(
    @Param('id') id: string,
    @Body() stockUpdate: { warehouseId: string; quantity: number },
  ) {
    await this.productService.updateStock(
      id,
      stockUpdate.warehouseId,
      stockUpdate.quantity,
    );
    return { message: 'Stock updated successfully' };
  }

  @Post(':id/reserve-stock')
  async reserveStock(
    @Param('id') id: string,
    @Body() reservation: { warehouseId: string; quantity: number },
  ) {
    const success = await this.productService.reserveStock(
      id,
      reservation.warehouseId,
      reservation.quantity,
    );
    return { success, message: success ? 'Stock reserved' : 'Insufficient stock' };
  }

  @Post(':id/release-stock')
  async releaseStock(
    @Param('id') id: string,
    @Body() release: { warehouseId: string; quantity: number },
  ) {
    await this.productService.releaseStock(
      id,
      release.warehouseId,
      release.quantity,
    );
    return { message: 'Stock released successfully' };
  }

  @Post('generate-sku')
  async generateSku(@Body() data: { categoryCode: string }) {
    const sku = await this.productService.generateSku(data.categoryCode);
    return { sku };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.productService.remove(id);
  }
}
