import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CollectionActivity, ActivityType } from '../entities/collection-activity.entity';

@Injectable()
export class CollectionActivityService {
  constructor(
    @InjectRepository(CollectionActivity)
    private collectionActivityRepository: Repository<CollectionActivity>,
  ) {}

  async create(activityData: Partial<CollectionActivity>): Promise<CollectionActivity> {
    const activity = this.collectionActivityRepository.create({
      ...activityData,
      activityDate: activityData.activityDate || new Date(),
    });
    return this.collectionActivityRepository.save(activity);
  }

  async findAll(): Promise<CollectionActivity[]> {
    return this.collectionActivityRepository.find({
      relations: ['case'],
      order: { activityDate: 'DESC' },
    });
  }

  async findOne(id: string): Promise<CollectionActivity> {
    const activity = await this.collectionActivityRepository.findOne({
      where: { id },
      relations: ['case'],
    });

    if (!activity) {
      throw new NotFoundException(`Activity with ID ${id} not found`);
    }

    return activity;
  }

  async findByCase(caseId: string): Promise<CollectionActivity[]> {
    return this.collectionActivityRepository.find({
      where: { caseId },
      order: { activityDate: 'DESC' },
    });
  }

  async findByAgent(agentId: string): Promise<CollectionActivity[]> {
    return this.collectionActivityRepository.find({
      where: { performedBy: agentId },
      relations: ['case'],
      order: { activityDate: 'DESC' },
    });
  }

  async update(id: string, updateData: Partial<CollectionActivity>): Promise<CollectionActivity> {
    await this.collectionActivityRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const activity = await this.findOne(id);
    await this.collectionActivityRepository.remove(activity);
  }

  async logActivity(
    caseId: string,
    type: ActivityType,
    description: string,
    performedBy?: string,
    outcome?: string,
  ): Promise<CollectionActivity> {
    return this.create({
      caseId,
      type,
      description,
      performedBy,
      outcome,
      activityDate: new Date(),
    });
  }

  async logCall(
    caseId: string,
    duration: number,
    outcome: string,
    notes: string,
    performedBy?: string,
  ): Promise<CollectionActivity> {
    return this.create({
      caseId,
      type: ActivityType.CALL,
      description: `Phone call - Duration: ${duration} minutes`,
      outcome,
      notes,
      performedBy,
      activityDate: new Date(),
    });
  }

  async logEmail(
    caseId: string,
    subject: string,
    outcome: string,
    performedBy?: string,
  ): Promise<CollectionActivity> {
    return this.create({
      caseId,
      type: ActivityType.EMAIL,
      description: `Email sent - Subject: ${subject}`,
      outcome,
      performedBy,
      activityDate: new Date(),
    });
  }

  async logLetter(
    caseId: string,
    letterType: string,
    performedBy?: string,
  ): Promise<CollectionActivity> {
    return this.create({
      caseId,
      type: ActivityType.LETTER,
      description: `Collection letter sent - Type: ${letterType}`,
      performedBy,
      activityDate: new Date(),
    });
  }

  async logPayment(
    caseId: string,
    amount: number,
    paymentMethod: string,
    performedBy?: string,
  ): Promise<CollectionActivity> {
    return this.create({
      caseId,
      type: ActivityType.PAYMENT,
      description: `Payment received - Amount: $${amount} via ${paymentMethod}`,
      outcome: 'payment_received',
      performedBy,
      activityDate: new Date(),
    });
  }

  async getActivitySummary(caseId: string): Promise<any> {
    const activities = await this.findByCase(caseId);
    
    const summary = {
      totalActivities: activities.length,
      callsAttempted: activities.filter(a => a.type === ActivityType.CALL).length,
      emailsSent: activities.filter(a => a.type === ActivityType.EMAIL).length,
      lettersSent: activities.filter(a => a.type === ActivityType.LETTER).length,
      paymentsReceived: activities.filter(a => a.type === ActivityType.PAYMENT).length,
      lastActivity: activities.length > 0 ? activities[0].activityDate : null,
      lastActivityType: activities.length > 0 ? activities[0].type : null,
    };

    return summary;
  }

  async getAgentActivityReport(agentId: string, startDate: Date, endDate: Date): Promise<any> {
    const activities = await this.collectionActivityRepository
      .createQueryBuilder('activity')
      .where('activity.performedBy = :agentId', { agentId })
      .andWhere('activity.activityDate BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const report = {
      totalActivities: activities.length,
      activitiesByType: {
        calls: activities.filter(a => a.type === ActivityType.CALL).length,
        emails: activities.filter(a => a.type === ActivityType.EMAIL).length,
        letters: activities.filter(a => a.type === ActivityType.LETTER).length,
        payments: activities.filter(a => a.type === ActivityType.PAYMENT).length,
      },
      successfulOutcomes: activities.filter(a => 
        a.outcome && ['payment_received', 'payment_arranged', 'resolved'].includes(a.outcome)
      ).length,
    };

    return report;
  }

  async getRecentActivities(limit: number = 10): Promise<CollectionActivity[]> {
    return this.collectionActivityRepository.find({
      relations: ['case'],
      order: { activityDate: 'DESC' },
      take: limit,
    });
  }

  async scheduleFollowUp(
    caseId: string,
    followUpDate: Date,
    notes: string,
    performedBy?: string,
  ): Promise<CollectionActivity> {
    return this.create({
      caseId,
      type: ActivityType.FOLLOW_UP,
      description: `Follow-up scheduled for ${followUpDate.toDateString()}`,
      notes,
      performedBy,
      scheduledDate: followUpDate,
      activityDate: new Date(),
    });
  }

  async getScheduledFollowUps(): Promise<CollectionActivity[]> {
    const today = new Date();
    return this.collectionActivityRepository
      .createQueryBuilder('activity')
      .where('activity.type = :type', { type: ActivityType.FOLLOW_UP })
      .andWhere('activity.scheduledDate <= :today', { today })
      .andWhere('activity.completed = false OR activity.completed IS NULL')
      .orderBy('activity.scheduledDate', 'ASC')
      .getMany();
  }

  async markFollowUpCompleted(id: string, outcome?: string): Promise<CollectionActivity> {
    await this.collectionActivityRepository.update(id, {
      completed: true,
      outcome,
      completedDate: new Date(),
    });
    return this.findOne(id);
  }
}
