"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditReportsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const audit_reports_service_1 = require("../services/audit-reports.service");
const create_audit_report_dto_1 = require("../dto/create-audit-report.dto");
const update_audit_report_dto_1 = require("../dto/update-audit-report.dto");
const audit_reports_query_dto_1 = require("../dto/audit-reports-query.dto");
let AuditReportsController = class AuditReportsController {
    auditReportsService;
    constructor(auditReportsService) {
        this.auditReportsService = auditReportsService;
    }
    async getAuditReports(query, req) {
        try {
            const companyId = req.user.companyId;
            const result = await this.auditReportsService.getAuditReports(companyId, query);
            return {
                success: true,
                data: result,
                message: 'Audit reports retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve audit reports',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAuditMetrics(year, req) {
        try {
            const companyId = req.user.companyId;
            const metrics = await this.auditReportsService.getAuditMetrics(companyId, year);
            return {
                success: true,
                data: metrics,
                message: 'Audit metrics retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve audit metrics',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAuditReportById(id, req) {
        try {
            const companyId = req.user.companyId;
            const report = await this.auditReportsService.getAuditReportById(companyId, id);
            if (!report) {
                throw new common_1.HttpException({
                    success: false,
                    message: 'Audit report not found',
                }, common_1.HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                data: report,
                message: 'Audit report retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve audit report',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createAuditReport(createAuditReportDto, req) {
        try {
            const companyId = req.user.companyId;
            const userId = req.user.userId;
            const report = await this.auditReportsService.createAuditReport(companyId, userId, createAuditReportDto);
            return {
                success: true,
                data: report,
                message: 'Audit report created successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to create audit report',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateAuditReport(id, updateAuditReportDto, req) {
        try {
            const companyId = req.user.companyId;
            const userId = req.user.userId;
            const report = await this.auditReportsService.updateAuditReport(companyId, id, userId, updateAuditReportDto);
            return {
                success: true,
                data: report,
                message: 'Audit report updated successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to update audit report',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteAuditReport(id, req) {
        try {
            const companyId = req.user.companyId;
            await this.auditReportsService.deleteAuditReport(companyId, id);
            return {
                success: true,
                message: 'Audit report deleted successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to delete audit report',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async exportAuditReport(id, format, req) {
        try {
            const companyId = req.user.companyId;
            const exportResult = await this.auditReportsService.exportAuditReport(companyId, id, format);
            return {
                success: true,
                data: exportResult,
                message: `Audit report exported as ${format.toUpperCase()} successfully`,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to export audit report',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAuditFindings(id, req) {
        try {
            const companyId = req.user.companyId;
            const findings = await this.auditReportsService.getAuditFindings(companyId, id);
            return {
                success: true,
                data: findings,
                message: 'Audit findings retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve audit findings',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async addAuditFinding(id, findingData, req) {
        try {
            const companyId = req.user.companyId;
            const userId = req.user.userId;
            const finding = await this.auditReportsService.addAuditFinding(companyId, id, userId, findingData);
            return {
                success: true,
                data: finding,
                message: 'Audit finding added successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to add audit finding',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateAuditReportStatus(id, status, req) {
        try {
            const companyId = req.user.companyId;
            const userId = req.user.userId;
            const report = await this.auditReportsService.updateAuditReportStatus(companyId, id, userId, status);
            return {
                success: true,
                data: report,
                message: 'Audit report status updated successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to update audit report status',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getComplianceTrends(years = 3, req) {
        try {
            const companyId = req.user.companyId;
            const trends = await this.auditReportsService.getComplianceTrends(companyId, years);
            return {
                success: true,
                data: trends,
                message: 'Compliance trends retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve compliance trends',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AuditReportsController = AuditReportsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get audit reports with filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit reports retrieved successfully' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [audit_reports_query_dto_1.AuditReportsQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "getAuditReports", null);
__decorate([
    (0, common_1.Get)('metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get audit metrics and statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit metrics retrieved successfully' }),
    __param(0, (0, common_1.Query)('year')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "getAuditMetrics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get audit report by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit report retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "getAuditReportById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create new audit report' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Audit report created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_audit_report_dto_1.CreateAuditReportDto, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "createAuditReport", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update audit report' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit report updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_audit_report_dto_1.UpdateAuditReportDto, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "updateAuditReport", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete audit report' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit report deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "deleteAuditReport", null);
__decorate([
    (0, common_1.Post)(':id/export'),
    (0, swagger_1.ApiOperation)({ summary: 'Export audit report' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit report exported successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('format')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "exportAuditReport", null);
__decorate([
    (0, common_1.Get)(':id/findings'),
    (0, swagger_1.ApiOperation)({ summary: 'Get audit findings for a report' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit findings retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "getAuditFindings", null);
__decorate([
    (0, common_1.Post)(':id/findings'),
    (0, swagger_1.ApiOperation)({ summary: 'Add audit finding to report' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Audit finding added successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "addAuditFinding", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update audit report status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Audit report status updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "updateAuditReportStatus", null);
__decorate([
    (0, common_1.Get)('compliance/trends'),
    (0, swagger_1.ApiOperation)({ summary: 'Get compliance trends over time' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Compliance trends retrieved successfully' }),
    __param(0, (0, common_1.Query)('years')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AuditReportsController.prototype, "getComplianceTrends", null);
exports.AuditReportsController = AuditReportsController = __decorate([
    (0, swagger_1.ApiTags)('Audit Reports'),
    (0, common_1.Controller)('analytics/audit-reports'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [audit_reports_service_1.AuditReportsService])
], AuditReportsController);
//# sourceMappingURL=audit-reports.controller.js.map