export declare enum SegmentType {
    DEMOGRAPHIC = "demographic",
    BEHAVIORAL = "behavioral",
    GEOGRAPHIC = "geographic",
    PSYCHOGRAPHIC = "psychographic",
    TRANSACTIONAL = "transactional",
    CUSTOM = "custom"
}
export declare class CustomerSegment {
    id: string;
    name: string;
    description: string;
    type: SegmentType;
    criteria: any;
    customerCount: number;
    isActive: boolean;
    isAutoUpdate: boolean;
    lastUpdated: Date;
    createdBy: string;
    tags: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
