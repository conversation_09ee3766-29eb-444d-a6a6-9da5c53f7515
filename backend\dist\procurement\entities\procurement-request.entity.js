"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcurementRequest = exports.RequestType = exports.RequestPriority = exports.RequestStatus = void 0;
const typeorm_1 = require("typeorm");
const procurement_item_entity_1 = require("./procurement-item.entity");
const procurement_category_entity_1 = require("./procurement-category.entity");
const procurement_approval_entity_1 = require("./procurement-approval.entity");
var RequestStatus;
(function (RequestStatus) {
    RequestStatus["DRAFT"] = "draft";
    RequestStatus["SUBMITTED"] = "submitted";
    RequestStatus["PENDING_APPROVAL"] = "pending_approval";
    RequestStatus["APPROVED"] = "approved";
    RequestStatus["REJECTED"] = "rejected";
    RequestStatus["IN_PROCUREMENT"] = "in_procurement";
    RequestStatus["PARTIALLY_FULFILLED"] = "partially_fulfilled";
    RequestStatus["FULFILLED"] = "fulfilled";
    RequestStatus["CANCELLED"] = "cancelled";
})(RequestStatus || (exports.RequestStatus = RequestStatus = {}));
var RequestPriority;
(function (RequestPriority) {
    RequestPriority["LOW"] = "low";
    RequestPriority["MEDIUM"] = "medium";
    RequestPriority["HIGH"] = "high";
    RequestPriority["URGENT"] = "urgent";
    RequestPriority["CRITICAL"] = "critical";
})(RequestPriority || (exports.RequestPriority = RequestPriority = {}));
var RequestType;
(function (RequestType) {
    RequestType["GOODS"] = "goods";
    RequestType["SERVICES"] = "services";
    RequestType["CAPITAL_EXPENDITURE"] = "capital_expenditure";
    RequestType["MAINTENANCE"] = "maintenance";
    RequestType["EMERGENCY"] = "emergency";
    RequestType["RECURRING"] = "recurring";
})(RequestType || (exports.RequestType = RequestType = {}));
let ProcurementRequest = class ProcurementRequest {
    id;
    requestNumber;
    title;
    description;
    type;
    status;
    priority;
    requestedBy;
    departmentId;
    categoryId;
    category;
    requestDate;
    requiredDate;
    estimatedBudget;
    totalAmount;
    currency;
    justification;
    specifications;
    attachments;
    deliveryAddress;
    specialInstructions;
    approvedBy;
    approvedAt;
    approvalNotes;
    rejectedBy;
    rejectedAt;
    rejectionReason;
    items;
    approvals;
    metadata;
    createdAt;
    updatedAt;
};
exports.ProcurementRequest = ProcurementRequest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "requestNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RequestType,
        default: RequestType.GOODS,
    }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RequestStatus,
        default: RequestStatus.DRAFT,
    }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RequestPriority,
        default: RequestPriority.MEDIUM,
    }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "requestedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "departmentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "categoryId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => procurement_category_entity_1.ProcurementCategory, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'categoryId' }),
    __metadata("design:type", procurement_category_entity_1.ProcurementCategory)
], ProcurementRequest.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], ProcurementRequest.prototype, "requestDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], ProcurementRequest.prototype, "requiredDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], ProcurementRequest.prototype, "estimatedBudget", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], ProcurementRequest.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "justification", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "specifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ProcurementRequest.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "deliveryAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "specialInstructions", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProcurementRequest.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "approvalNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "rejectedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProcurementRequest.prototype, "rejectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProcurementRequest.prototype, "rejectionReason", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => procurement_item_entity_1.ProcurementItem, item => item.request, { cascade: true }),
    __metadata("design:type", Array)
], ProcurementRequest.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => procurement_approval_entity_1.ProcurementApproval, approval => approval.request),
    __metadata("design:type", Array)
], ProcurementRequest.prototype, "approvals", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ProcurementRequest.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProcurementRequest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ProcurementRequest.prototype, "updatedAt", void 0);
exports.ProcurementRequest = ProcurementRequest = __decorate([
    (0, typeorm_1.Entity)('procurement_requests')
], ProcurementRequest);
//# sourceMappingURL=procurement-request.entity.js.map