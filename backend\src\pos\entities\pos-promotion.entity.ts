import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum PromotionType {
  PERCENTAGE_DISCOUNT = 'percentage_discount',
  FIXED_DISCOUNT = 'fixed_discount',
  BUY_X_GET_Y = 'buy_x_get_y',
  FREE_SHIPPING = 'free_shipping',
  BUNDLE_DEAL = 'bundle_deal',
  LOYALTY_POINTS = 'loyalty_points',
  CASHBACK = 'cashback',
}

export enum PromotionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SCHEDULED = 'scheduled',
  EXPIRED = 'expired',
  PAUSED = 'paused',
}

@Entity('pos_promotions')
export class PosPromotion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PromotionType,
  })
  type: PromotionType;

  @Column({
    type: 'enum',
    enum: PromotionStatus,
    default: PromotionStatus.ACTIVE,
  })
  status: PromotionStatus;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  discountAmount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  discountPercentage: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  minimumPurchase: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  maximumDiscount: number;

  @Column({ type: 'int', nullable: true })
  usageLimit: number;

  @Column({ type: 'int', default: 0 })
  usageCount: number;

  @Column({ type: 'int', nullable: true })
  usageLimitPerCustomer: number;

  @Column({ type: 'json', nullable: true })
  applicableProducts: string[];

  @Column({ type: 'json', nullable: true })
  applicableCategories: string[];

  @Column({ type: 'json', nullable: true })
  excludedProducts: string[];

  @Column({ type: 'json', nullable: true })
  excludedCategories: string[];

  @Column({ type: 'json', nullable: true })
  customerSegments: string[];

  @Column({ type: 'json', nullable: true })
  conditions: any;

  @Column({ default: true })
  isStackable: boolean;

  @Column({ default: false })
  requiresCouponCode: boolean;

  @Column({ type: 'int', default: 0 })
  priority: number;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
