import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Employee, EmployeeStatus } from '../entities/employee.entity';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
  ) {}

  async create(createEmployeeDto: any): Promise<Employee> {
    const employeeNumber = await this.generateEmployeeNumber();
    
    const employee = this.employeeRepository.create({
      ...createEmployeeDto,
      employeeNumber,
    });

    return this.employeeRepository.save(employee);
  }

  async findAll(filters?: any): Promise<Employee[]> {
    const queryBuilder = this.employeeRepository.createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .leftJoinAndSelect('employee.manager', 'manager');

    if (filters?.status) {
      queryBuilder.andWhere('employee.status = :status', { status: filters.status });
    }

    if (filters?.departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', { departmentId: filters.departmentId });
    }

    if (filters?.positionId) {
      queryBuilder.andWhere('employee.positionId = :positionId', { positionId: filters.positionId });
    }

    if (filters?.managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId: filters.managerId });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(employee.firstName LIKE :search OR employee.lastName LIKE :search OR employee.email LIKE :search OR employee.employeeNumber LIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    return queryBuilder
      .orderBy('employee.firstName', 'ASC')
      .addOrderBy('employee.lastName', 'ASC')
      .getMany();
  }

  async findOne(id: string): Promise<Employee> {
    const employee = await this.employeeRepository.findOne({
      where: { id },
      relations: [
        'department',
        'position',
        'manager',
        'directReports',
        'attendances',
        'leaves',
        'payrolls',
        'performances',
        'trainings',
        'benefits',
      ],
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    return employee;
  }

  async findByEmployeeNumber(employeeNumber: string): Promise<Employee> {
    const employee = await this.employeeRepository.findOne({
      where: { employeeNumber },
      relations: ['department', 'position', 'manager'],
    });

    if (!employee) {
      throw new NotFoundException(`Employee with number ${employeeNumber} not found`);
    }

    return employee;
  }

  async update(id: string, updateEmployeeDto: any): Promise<Employee> {
    const employee = await this.findOne(id);
    Object.assign(employee, updateEmployeeDto);
    return this.employeeRepository.save(employee);
  }

  async remove(id: string): Promise<void> {
    const employee = await this.findOne(id);
    
    // Check if employee has dependents (direct reports)
    if (employee.directReports?.length > 0) {
      throw new BadRequestException('Cannot delete employee with direct reports');
    }

    await this.employeeRepository.remove(employee);
  }

  async terminate(id: string, terminationDate: Date, reason?: string): Promise<Employee> {
    const employee = await this.findOne(id);
    
    employee.status = EmployeeStatus.TERMINATED;
    employee.terminationDate = terminationDate;
    
    if (reason) {
      employee.notes = (employee.notes || '') + `\nTermination Reason: ${reason}`;
    }

    return this.employeeRepository.save(employee);
  }

  async reactivate(id: string): Promise<Employee> {
    const employee = await this.findOne(id);
    
    employee.status = EmployeeStatus.ACTIVE;
    employee.terminationDate = null;

    return this.employeeRepository.save(employee);
  }

  async getEmployeeHierarchy(managerId?: string): Promise<Employee[]> {
    const queryBuilder = this.employeeRepository.createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .leftJoinAndSelect('employee.directReports', 'directReports')
      .where('employee.status = :status', { status: EmployeeStatus.ACTIVE });

    if (managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
    } else {
      queryBuilder.andWhere('employee.managerId IS NULL');
    }

    return queryBuilder
      .orderBy('employee.firstName', 'ASC')
      .getMany();
  }

  async getEmployeeStats(): Promise<any> {
    const totalEmployees = await this.employeeRepository.count();
    const activeEmployees = await this.employeeRepository.count({
      where: { status: EmployeeStatus.ACTIVE },
    });
    const inactiveEmployees = await this.employeeRepository.count({
      where: { status: EmployeeStatus.INACTIVE },
    });
    const terminatedEmployees = await this.employeeRepository.count({
      where: { status: EmployeeStatus.TERMINATED },
    });

    // Get employees by department
    const employeesByDepartment = await this.employeeRepository
      .createQueryBuilder('employee')
      .leftJoin('employee.department', 'department')
      .select('department.name', 'departmentName')
      .addSelect('COUNT(employee.id)', 'count')
      .where('employee.status = :status', { status: EmployeeStatus.ACTIVE })
      .groupBy('department.id')
      .getRawMany();

    // Get employees by position
    const employeesByPosition = await this.employeeRepository
      .createQueryBuilder('employee')
      .leftJoin('employee.position', 'position')
      .select('position.title', 'positionTitle')
      .addSelect('COUNT(employee.id)', 'count')
      .where('employee.status = :status', { status: EmployeeStatus.ACTIVE })
      .groupBy('position.id')
      .getRawMany();

    return {
      total: totalEmployees,
      active: activeEmployees,
      inactive: inactiveEmployees,
      terminated: terminatedEmployees,
      byDepartment: employeesByDepartment,
      byPosition: employeesByPosition,
    };
  }

  async searchEmployees(searchTerm: string): Promise<Employee[]> {
    return this.employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .where(
        '(employee.firstName LIKE :search OR employee.lastName LIKE :search OR employee.email LIKE :search OR employee.employeeNumber LIKE :search)',
        { search: `%${searchTerm}%` }
      )
      .andWhere('employee.status = :status', { status: EmployeeStatus.ACTIVE })
      .orderBy('employee.firstName', 'ASC')
      .limit(20)
      .getMany();
  }

  private async generateEmployeeNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const prefix = `EMP-${year}-`;

    const lastEmployee = await this.employeeRepository.findOne({
      where: { employeeNumber: Like(`${prefix}%`) },
      order: { employeeNumber: 'DESC' },
    });

    let nextNumber = 1;
    if (lastEmployee) {
      const lastNumber = parseInt(lastEmployee.employeeNumber.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }
}
