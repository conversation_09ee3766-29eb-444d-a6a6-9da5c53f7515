import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Space, Typo<PERSON>, Button } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
  ProjectOutlined,
  DollarOutlined,
  BankOutlined,
  ShopOutlined,
  ContactsOutlined,
  Bar<PERSON>hartOutlined,
  ToolOutlined,
  SafetyOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { useNavigate, Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useAuth } from '../../hooks/useAuth';
import LanguageSwitcher from '../common/LanguageSwitcher';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

const StyledLayout = styled(Layout)`
  min-height: 100vh;
`;

const StyledHeader = styled(Header)`
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 1;
`;

const StyledSider = styled(Sider)`
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
  }
`;

const LogoContainer = styled.div`
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
`;

const StyledContent = styled(Content)`
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  min-height: 280px;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const DashboardLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('navigation.profile'),
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('navigation.settings'),
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('auth.logout'),
      onClick: handleLogout,
    },
  ];

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: t('dashboard.title'),
      onClick: () => navigate('/dashboard'),
    },
    {
      key: 'general-manager',
      icon: <UserOutlined />,
      label: t('departments.generalManager'),
      onClick: () => navigate('/general-manager'),
    },
    {
      key: 'sales',
      icon: <DollarOutlined />,
      label: t('departments.sales'),
      onClick: () => navigate('/sales'),
    },
    {
      key: 'crm',
      icon: <TeamOutlined />,
      label: 'CRM',
      onClick: () => navigate('/crm'),
    },
    {
      key: 'pos',
      icon: <ShopOutlined />,
      label: t('departments.pos'),
      onClick: () => navigate('/pos'),
    },
    {
      key: 'customers',
      icon: <ContactsOutlined />,
      label: t('departments.customers'),
      onClick: () => navigate('/customers'),
    },
    {
      key: 'projects',
      icon: <ProjectOutlined />,
      label: t('departments.projects'),
      onClick: () => navigate('/projects'),
    },
    {
      key: 'finance',
      icon: <BankOutlined />,
      label: t('departments.finance'),
      onClick: () => navigate('/finance'),
    },
    {
      key: 'collections',
      icon: <DollarOutlined />,
      label: t('departments.collections'),
      onClick: () => navigate('/collections'),
    },
    {
      key: 'procurement',
      icon: <ShoppingCartOutlined />,
      label: t('departments.procurement'),
      onClick: () => navigate('/procurement'),
    },
    {
      key: 'inventory',
      icon: <ShoppingCartOutlined />,
      label: t('departments.inventory'),
      onClick: () => navigate('/inventory'),
    },
    {
      key: 'hr',
      icon: <TeamOutlined />,
      label: t('departments.hr'),
      onClick: () => navigate('/hr'),
    },
    {
      key: 'it-support',
      icon: <ToolOutlined />,
      label: t('departments.itSupport'),
      onClick: () => navigate('/it-support'),
    },
    {
      key: 'legal',
      icon: <SafetyOutlined />,
      label: 'Legal',
      onClick: () => navigate('/legal'),
    },
    {
      key: 'analytics',
      icon: <BarChartOutlined />,
      label: t('departments.analytics'),
      onClick: () => navigate('/analytics'),
    },
    {
      key: 'system-guide',
      icon: <QuestionCircleOutlined />,
      label: t('departments.systemGuide'),
      onClick: () => navigate('/system-guide'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('departments.settings'),
      onClick: () => navigate('/settings'),
    },
  ];

  return (
    <StyledLayout>
      <StyledSider trigger={null} collapsible collapsed={collapsed}>
        <LogoContainer>
          {!collapsed ? (
            <Title level={4} style={{ color: 'white', margin: 0 }}>
              ZaidanOne
            </Title>
          ) : (
            <Text style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
              Z1
            </Text>
          )}
        </LogoContainer>

        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['dashboard']}
          items={menuItems}
          style={{ flex: 1 }}
        />
      </StyledSider>

      <Layout>
        <StyledHeader>
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
            <div>
              <Title level={4} style={{ margin: 0 }}>
                {user?.company?.name}
              </Title>
            </div>
          </Space>

          <Space size="large">
            <LanguageSwitcher />

            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <UserInfo>
                <Avatar icon={<UserOutlined />} />
                <Space direction="vertical" size={0}>
                  <Text strong>{user?.firstName} {user?.lastName}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {user?.role}
                  </Text>
                </Space>
              </UserInfo>
            </Dropdown>
          </Space>
        </StyledHeader>

        <StyledContent>
          <Outlet />
        </StyledContent>
      </Layout>
    </StyledLayout>
  );
};

export default DashboardLayout;
