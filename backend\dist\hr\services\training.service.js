"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrainingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const training_entity_1 = require("../entities/training.entity");
let TrainingService = class TrainingService {
    trainingRepository;
    constructor(trainingRepository) {
        this.trainingRepository = trainingRepository;
    }
    async create(createTrainingDto) {
        const training = this.trainingRepository.create(createTrainingDto);
        return this.trainingRepository.save(training);
    }
    async findAll(filters) {
        const queryBuilder = this.trainingRepository.createQueryBuilder('training')
            .leftJoinAndSelect('training.employee', 'employee');
        if (filters?.employeeId) {
            queryBuilder.andWhere('training.employeeId = :employeeId', { employeeId: filters.employeeId });
        }
        if (filters?.type) {
            queryBuilder.andWhere('training.type = :type', { type: filters.type });
        }
        if (filters?.status) {
            queryBuilder.andWhere('training.status = :status', { status: filters.status });
        }
        return queryBuilder
            .orderBy('training.startDate', 'DESC')
            .getMany();
    }
    async findOne(id) {
        const training = await this.trainingRepository.findOne({
            where: { id },
            relations: ['employee'],
        });
        if (!training) {
            throw new common_1.NotFoundException(`Training with ID ${id} not found`);
        }
        return training;
    }
    async update(id, updateTrainingDto) {
        const training = await this.findOne(id);
        Object.assign(training, updateTrainingDto);
        return this.trainingRepository.save(training);
    }
    async completeTraining(id, score, feedback) {
        const training = await this.findOne(id);
        training.status = training_entity_1.TrainingStatus.COMPLETED;
        training.completionDate = new Date();
        if (score !== undefined) {
            training.score = score;
            training.isPassed = training.passingScore ? score >= training.passingScore : true;
        }
        if (feedback) {
            training.feedback = feedback;
        }
        return this.trainingRepository.save(training);
    }
};
exports.TrainingService = TrainingService;
exports.TrainingService = TrainingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(training_entity_1.Training)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], TrainingService);
//# sourceMappingURL=training.service.js.map