import { Customer } from './customer.entity';
export declare enum PreferenceType {
    COMMUNICATION = "communication",
    PRODUCT = "product",
    SERVICE = "service",
    DELIVERY = "delivery",
    PAYMENT = "payment",
    MARKETING = "marketing",
    PRIVACY = "privacy",
    NOTIFICATION = "notification",
    CUSTOM = "custom"
}
export declare class CustomerPreference {
    id: string;
    customerId: string;
    customer: Customer;
    type: PreferenceType;
    key: string;
    value: string;
    description: string;
    isActive: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
