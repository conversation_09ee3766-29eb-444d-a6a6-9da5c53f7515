import { BankTransaction } from './bank-transaction.entity';
export declare enum BankAccountType {
    CHECKING = "checking",
    SAVINGS = "savings",
    CREDIT = "credit",
    LOAN = "loan",
    INVESTMENT = "investment",
    MONEY_MARKET = "money_market",
    CD = "cd",
    OTHER = "other"
}
export declare enum BankAccountStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    CLOSED = "closed",
    FROZEN = "frozen"
}
export declare class BankAccount {
    id: string;
    accountName: string;
    accountNumber: string;
    bankName: string;
    routingNumber: string;
    swiftCode: string;
    iban: string;
    accountType: BankAccountType;
    status: BankAccountStatus;
    currency: string;
    currentBalance: number;
    availableBalance: number;
    creditLimit: number;
    interestRate: number;
    openedDate: Date;
    closedDate: Date;
    lastReconciledDate: Date;
    lastReconciledBalance: number;
    isDefault: boolean;
    allowOnlineTransactions: boolean;
    allowMobileDeposits: boolean;
    description: string;
    bankContactInfo: any;
    accountHolders: string[];
    authorizedSigners: string[];
    transactions: BankTransaction[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
