"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReturnedInvoiceItem = void 0;
const typeorm_1 = require("typeorm");
const returned_invoice_entity_1 = require("./returned-invoice.entity");
let ReturnedInvoiceItem = class ReturnedInvoiceItem {
    id;
    returnedInvoiceId;
    returnedInvoice;
    originalItemId;
    lineNumber;
    description;
    productCode;
    originalQuantity;
    returnQuantity;
    unitPrice;
    discount;
    taxType;
    taxAmount;
    lineTotal;
    unit;
    returnReason;
    itemCondition;
    notes;
    tenantId;
};
exports.ReturnedInvoiceItem = ReturnedInvoiceItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "returnedInvoiceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => returned_invoice_entity_1.ReturnedInvoice, returnedInvoice => returnedInvoice.items, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'returnedInvoiceId' }),
    __metadata("design:type", returned_invoice_entity_1.ReturnedInvoice)
], ReturnedInvoiceItem.prototype, "returnedInvoice", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "originalItemId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "lineNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "productCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "originalQuantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "returnQuantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "unitPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "discount", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "taxType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], ReturnedInvoiceItem.prototype, "lineTotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "returnReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'good' }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "itemCondition", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReturnedInvoiceItem.prototype, "tenantId", void 0);
exports.ReturnedInvoiceItem = ReturnedInvoiceItem = __decorate([
    (0, typeorm_1.Entity)('returned_invoice_items')
], ReturnedInvoiceItem);
//# sourceMappingURL=returned-invoice-item.entity.js.map