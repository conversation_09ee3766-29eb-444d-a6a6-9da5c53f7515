import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './strategies/jwt.strategy';
import { UserModule } from '../user/user.module';
import { CompanyModule } from '../company/company.module';

// New Permission System - Temporarily disabled for compilation
// import { Permission } from './entities/permission.entity';
// import { Role } from './entities/role.entity';
// import { User } from '../user/entities/user.entity';
// import { PermissionService } from './services/permission.service';
// import { RoleService } from './services/role.service';
// import { UserManagementService } from './services/user-management.service';
// import { PermissionController } from './controllers/permission.controller';
// import { RoleController } from './controllers/role.controller';
// import { UserManagementController } from './controllers/user-management.controller';

@Module({
  imports: [
    // TypeOrmModule.forFeature([Permission, Role, User]),
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
    UserModule,
    CompanyModule,
  ],
  controllers: [
    AuthController,
    // PermissionController,
    // RoleController,
    // UserManagementController,
  ],
  providers: [
    AuthService,
    JwtStrategy,
    // PermissionService,
    // RoleService,
    // UserManagementService,
  ],
  exports: [
    AuthService,
    // PermissionService,
    // RoleService,
    // UserManagementService,
  ],
})
export class AuthModule {}
