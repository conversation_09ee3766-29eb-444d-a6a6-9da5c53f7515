import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
import { Location } from './location.entity';
import { Stock } from './stock.entity';

export enum MovementType {
  RECEIPT = 'receipt',
  ISSUE = 'issue',
  TRANSFER = 'transfer',
  ADJUSTMENT = 'adjustment',
  RETURN = 'return',
  DAMAGE = 'damage',
  EXPIRY = 'expiry',
  SALE = 'sale',
  PURCHASE = 'purchase',
  PRODUCTION = 'production',
  CONSUMPTION = 'consumption',
}

export enum MovementReason {
  PURCHASE_ORDER = 'purchase_order',
  SALES_ORDER = 'sales_order',
  STOCK_TRANSFER = 'stock_transfer',
  STOCK_ADJUSTMENT = 'stock_adjustment',
  PHYSICAL_COUNT = 'physical_count',
  DAMAGE_WRITE_OFF = 'damage_write_off',
  EXPIRY_WRITE_OFF = 'expiry_write_off',
  CUSTOMER_RETURN = 'customer_return',
  SUPPLIER_RETURN = 'supplier_return',
  PRODUCTION_CONSUMPTION = 'production_consumption',
  PRODUCTION_OUTPUT = 'production_output',
  MANUAL_ADJUSTMENT = 'manual_adjustment',
}

@Entity('inventory_stock_movements')
export class StockMovement {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  productId: string;

  @ManyToOne(() => Product, product => product.stockMovements)
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column()
  warehouseId: string;

  @ManyToOne(() => Warehouse)
  @JoinColumn({ name: 'warehouseId' })
  warehouse: Warehouse;

  @Column({ nullable: true })
  locationId: string;

  @ManyToOne(() => Location, { nullable: true })
  @JoinColumn({ name: 'locationId' })
  location: Location;

  @Column({ nullable: true })
  stockId: string;

  @ManyToOne(() => Stock, stock => stock.stockMovements, { nullable: true })
  @JoinColumn({ name: 'stockId' })
  stock: Stock;

  @Column({
    type: 'enum',
    enum: MovementType,
  })
  type: MovementType;

  @Column({
    type: 'enum',
    enum: MovementReason,
  })
  reason: MovementReason;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'int' })
  quantityBefore: number;

  @Column({ type: 'int' })
  quantityAfter: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  unitCost: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalCost: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'date' })
  movementDate: Date;

  @Column({ length: 100, nullable: true })
  referenceNumber: string;

  @Column({ nullable: true })
  relatedEntityType: string; // 'purchase_order', 'sales_order', etc.

  @Column({ nullable: true })
  relatedEntityId: string;

  @Column({ length: 100, nullable: true })
  batchNumber: string;

  @Column({ length: 100, nullable: true })
  serialNumber: string;

  @Column({ type: 'date', nullable: true })
  expiryDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  performedBy: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
