import { Repository } from 'typeorm';
import { User, UserStatus } from '../../user/entities/user.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
export interface CreateUserDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    department?: string;
    position?: string;
    employeeId?: string;
    roleId: string;
    departmentAccess?: string[];
    additionalPermissionIds?: string[];
    companyId: string;
}
export interface UpdateUserDto {
    firstName?: string;
    lastName?: string;
    phone?: string;
    department?: string;
    position?: string;
    employeeId?: string;
    roleId?: string;
    departmentAccess?: string[];
    additionalPermissionIds?: string[];
    status?: UserStatus;
    isActive?: boolean;
}
export interface UserPermissionCheck {
    hasPermission: boolean;
    source: 'role' | 'additional' | 'none';
    permission?: Permission;
}
export declare class UserManagementService {
    private userRepository;
    private roleRepository;
    private permissionRepository;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, permissionRepository: Repository<Permission>);
    createUser(createUserDto: CreateUserDto): Promise<User>;
    findAllUsers(): Promise<User[]>;
    findUserById(id: string): Promise<User>;
    findUserByEmail(email: string): Promise<User>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<User>;
    deleteUser(id: string): Promise<void>;
    changeUserPassword(id: string, newPassword: string): Promise<void>;
    activateUser(id: string): Promise<User>;
    deactivateUser(id: string): Promise<User>;
    suspendUser(id: string): Promise<User>;
    assignRole(userId: string, roleId: string): Promise<User>;
    assignAdditionalPermissions(userId: string, permissionIds: string[]): Promise<User>;
    removeAdditionalPermissions(userId: string, permissionIds: string[]): Promise<User>;
    getUserPermissions(userId: string): Promise<Permission[]>;
    checkUserPermission(userId: string, module: string, action: string, resource: string): Promise<UserPermissionCheck>;
    getUsersByRole(roleId: string): Promise<User[]>;
    getUsersByDepartment(department: string): Promise<User[]>;
    getUsersWithDepartmentAccess(department: string): Promise<User[]>;
    searchUsers(searchTerm: string): Promise<User[]>;
    getUserStatistics(): Promise<any>;
    updateLastLogin(userId: string): Promise<void>;
    bulkUpdateUsers(userIds: string[], updateData: Partial<UpdateUserDto>): Promise<void>;
    exportUsers(): Promise<any[]>;
}
