"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosReturn = exports.ReturnStatus = exports.ReturnReason = void 0;
const typeorm_1 = require("typeorm");
const pos_sale_entity_1 = require("./pos-sale.entity");
var ReturnReason;
(function (ReturnReason) {
    ReturnReason["DEFECTIVE"] = "defective";
    ReturnReason["WRONG_ITEM"] = "wrong_item";
    ReturnReason["CUSTOMER_CHANGED_MIND"] = "customer_changed_mind";
    ReturnReason["SIZE_ISSUE"] = "size_issue";
    ReturnReason["COLOR_ISSUE"] = "color_issue";
    ReturnReason["DAMAGED_IN_SHIPPING"] = "damaged_in_shipping";
    ReturnReason["NOT_AS_DESCRIBED"] = "not_as_described";
    ReturnReason["DUPLICATE_ORDER"] = "duplicate_order";
    ReturnReason["GIFT_RETURN"] = "gift_return";
    ReturnReason["OTHER"] = "other";
})(ReturnReason || (exports.ReturnReason = ReturnReason = {}));
var ReturnStatus;
(function (ReturnStatus) {
    ReturnStatus["PENDING"] = "pending";
    ReturnStatus["APPROVED"] = "approved";
    ReturnStatus["REJECTED"] = "rejected";
    ReturnStatus["PROCESSED"] = "processed";
    ReturnStatus["REFUNDED"] = "refunded";
})(ReturnStatus || (exports.ReturnStatus = ReturnStatus = {}));
let PosReturn = class PosReturn {
    id;
    returnNumber;
    originalSaleId;
    originalSale;
    newSaleId;
    newSale;
    reason;
    status;
    returnDate;
    returnAmount;
    refundAmount;
    storeCreditAmount;
    exchangeAmount;
    returnedItems;
    notes;
    processedBy;
    approvedBy;
    approvedAt;
    refundDetails;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosReturn = PosReturn;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosReturn.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PosReturn.prototype, "returnNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosReturn.prototype, "originalSaleId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_sale_entity_1.PosSale),
    (0, typeorm_1.JoinColumn)({ name: 'originalSaleId' }),
    __metadata("design:type", pos_sale_entity_1.PosSale)
], PosReturn.prototype, "originalSale", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosReturn.prototype, "newSaleId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_sale_entity_1.PosSale, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'newSaleId' }),
    __metadata("design:type", pos_sale_entity_1.PosSale)
], PosReturn.prototype, "newSale", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ReturnReason,
    }),
    __metadata("design:type", String)
], PosReturn.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ReturnStatus,
        default: ReturnStatus.PENDING,
    }),
    __metadata("design:type", String)
], PosReturn.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], PosReturn.prototype, "returnDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], PosReturn.prototype, "returnAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosReturn.prototype, "refundAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosReturn.prototype, "storeCreditAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], PosReturn.prototype, "exchangeAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Array)
], PosReturn.prototype, "returnedItems", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosReturn.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosReturn.prototype, "processedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosReturn.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosReturn.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosReturn.prototype, "refundDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosReturn.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosReturn.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosReturn.prototype, "updatedAt", void 0);
exports.PosReturn = PosReturn = __decorate([
    (0, typeorm_1.Entity)('pos_returns')
], PosReturn);
//# sourceMappingURL=pos-return.entity.js.map