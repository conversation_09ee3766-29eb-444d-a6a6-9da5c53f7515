import { Account } from './account.entity';
export declare enum TransactionType {
    JOURNAL_ENTRY = "journal_entry",
    PAYMENT = "payment",
    RECEIPT = "receipt",
    TRANSFER = "transfer",
    ADJUSTMENT = "adjustment",
    INVOICE = "invoice",
    BILL = "bill",
    EXPENSE = "expense",
    DEPOSIT = "deposit",
    WITHDRAWAL = "withdrawal"
}
export declare enum TransactionStatus {
    DRAFT = "draft",
    PENDING = "pending",
    APPROVED = "approved",
    POSTED = "posted",
    CANCELLED = "cancelled",
    REVERSED = "reversed"
}
export declare class Transaction {
    id: string;
    transactionNumber: string;
    type: TransactionType;
    status: TransactionStatus;
    transactionDate: Date;
    description: string;
    reference: string;
    amount: number;
    debitAccountId: string;
    debitAccount: Account;
    creditAccountId: string;
    creditAccount: Account;
    currency: string;
    exchangeRate: number;
    baseCurrencyAmount: number;
    relatedEntityType: string;
    relatedEntityId: string;
    createdBy: string;
    approvedBy: string;
    approvedAt: Date;
    attachments: string[];
    metadata: any;
    notes: string;
    createdAt: Date;
    updatedAt: Date;
}
