import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Leave, LeaveStatus } from '../entities/leave.entity';
import { LeaveType } from '../entities/leave-type.entity';

@Injectable()
export class LeaveService {
  constructor(
    @InjectRepository(Leave)
    private leaveRepository: Repository<Leave>,
    @InjectRepository(LeaveType)
    private leaveTypeRepository: Repository<LeaveType>,
  ) {}

  async create(createLeaveDto: any): Promise<Leave> {
    // Calculate days requested
    const startDate = new Date(createLeaveDto.startDate);
    const endDate = new Date(createLeaveDto.endDate);
    const daysRequested = this.calculateLeaveDays(startDate, endDate, createLeaveDto.isHalfDay);

    const leave = this.leaveRepository.create({
      ...createLeaveDto,
      daysRequested,
      appliedDate: new Date(),
    });

    return this.leaveRepository.save(leave);
  }

  async findAll(filters?: any): Promise<Leave[]> {
    const queryBuilder = this.leaveRepository.createQueryBuilder('leave')
      .leftJoinAndSelect('leave.employee', 'employee')
      .leftJoinAndSelect('leave.leaveType', 'leaveType');

    if (filters?.employeeId) {
      queryBuilder.andWhere('leave.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters?.status) {
      queryBuilder.andWhere('leave.status = :status', { status: filters.status });
    }

    if (filters?.leaveTypeId) {
      queryBuilder.andWhere('leave.leaveTypeId = :leaveTypeId', { leaveTypeId: filters.leaveTypeId });
    }

    if (filters?.startDate && filters.endDate) {
      queryBuilder.andWhere('leave.startDate >= :startDate AND leave.endDate <= :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    return queryBuilder
      .orderBy('leave.startDate', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Leave> {
    const leave = await this.leaveRepository.findOne({
      where: { id },
      relations: ['employee', 'leaveType'],
    });

    if (!leave) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    return leave;
  }

  async update(id: string, updateLeaveDto: any): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status === LeaveStatus.APPROVED || leave.status === LeaveStatus.TAKEN) {
      throw new BadRequestException('Cannot update approved or taken leave');
    }

    Object.assign(leave, updateLeaveDto);

    // Recalculate days if dates changed
    if (updateLeaveDto.startDate || updateLeaveDto.endDate || updateLeaveDto.isHalfDay !== undefined) {
      leave.daysRequested = this.calculateLeaveDays(leave.startDate, leave.endDate, leave.isHalfDay);
    }

    return this.leaveRepository.save(leave);
  }

  async remove(id: string): Promise<void> {
    const leave = await this.findOne(id);

    if (leave.status === LeaveStatus.APPROVED || leave.status === LeaveStatus.TAKEN) {
      throw new BadRequestException('Cannot delete approved or taken leave');
    }

    await this.leaveRepository.remove(leave);
  }

  async approveLeave(id: string, approvedBy: string, approvalComments?: string): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.PENDING) {
      throw new BadRequestException('Only pending leave requests can be approved');
    }

    leave.status = LeaveStatus.APPROVED;
    leave.daysApproved = leave.daysRequested;
    leave.approvedBy = approvedBy;
    leave.approvedAt = new Date();
    leave.approvalComments = approvalComments;

    return this.leaveRepository.save(leave);
  }

  async rejectLeave(id: string, rejectedBy: string, rejectionComments: string): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.PENDING) {
      throw new BadRequestException('Only pending leave requests can be rejected');
    }

    leave.status = LeaveStatus.REJECTED;
    leave.approvedBy = rejectedBy;
    leave.approvedAt = new Date();
    leave.approvalComments = rejectionComments;

    return this.leaveRepository.save(leave);
  }

  async cancelLeave(id: string, reason?: string): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status === LeaveStatus.TAKEN) {
      throw new BadRequestException('Cannot cancel leave that has already been taken');
    }

    leave.status = LeaveStatus.CANCELLED;
    if (reason) {
      leave.comments = (leave.comments || '') + `\nCancellation Reason: ${reason}`;
    }

    return this.leaveRepository.save(leave);
  }

  async getLeaveBalance(employeeId: string, leaveTypeId: string, year?: number): Promise<any> {
    const currentYear = year || new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const endDate = new Date(currentYear, 11, 31);

    const leaveType = await this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } });
    if (!leaveType) {
      throw new NotFoundException('Leave type not found');
    }

    const approvedLeaves = await this.leaveRepository.find({
      where: {
        employeeId,
        leaveTypeId,
        status: LeaveStatus.APPROVED,
        startDate: Between(startDate, endDate),
      },
    });

    const takenLeaves = await this.leaveRepository.find({
      where: {
        employeeId,
        leaveTypeId,
        status: LeaveStatus.TAKEN,
        startDate: Between(startDate, endDate),
      },
    });

    const totalApproved = approvedLeaves.reduce((sum, leave) => sum + leave.daysApproved, 0);
    const totalTaken = takenLeaves.reduce((sum, leave) => sum + leave.daysApproved, 0);
    const totalUsed = totalApproved + totalTaken;

    return {
      leaveType: leaveType.name,
      year: currentYear,
      allocated: leaveType.maxDaysPerYear,
      used: totalUsed,
      remaining: Math.max(0, leaveType.maxDaysPerYear - totalUsed),
      pending: totalApproved,
      taken: totalTaken,
    };
  }

  async getLeaveReport(employeeId: string, year?: number): Promise<any> {
    const currentYear = year || new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const endDate = new Date(currentYear, 11, 31);

    const leaves = await this.findAll({
      employeeId,
      startDate,
      endDate,
    });

    const leaveTypes = await this.leaveTypeRepository.find({ where: { isActive: true } });
    const balances = await Promise.all(
      leaveTypes.map(type => this.getLeaveBalance(employeeId, type.id, year))
    );

    return {
      employeeId,
      year: currentYear,
      balances,
      leaves,
      summary: {
        totalLeavesTaken: leaves.filter(l => l.status === LeaveStatus.TAKEN).length,
        totalDaysTaken: leaves
          .filter(l => l.status === LeaveStatus.TAKEN)
          .reduce((sum, l) => sum + l.daysApproved, 0),
        pendingRequests: leaves.filter(l => l.status === LeaveStatus.PENDING).length,
      },
    };
  }

  // Leave Type management
  async createLeaveType(createLeaveTypeDto: any): Promise<LeaveType> {
    const leaveType = this.leaveTypeRepository.create(createLeaveTypeDto);
    return this.leaveTypeRepository.save(leaveType);
  }

  async findAllLeaveTypes(): Promise<LeaveType[]> {
    return this.leaveTypeRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  private calculateLeaveDays(startDate: Date, endDate: Date, isHalfDay: boolean): number {
    const timeDiff = endDate.getTime() - startDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates
    
    return isHalfDay ? 0.5 : daysDiff;
  }
}
