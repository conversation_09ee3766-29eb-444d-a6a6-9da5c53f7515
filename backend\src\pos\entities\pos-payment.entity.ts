import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PosSale } from './pos-sale.entity';

export enum PaymentMethod {
  CASH = 'cash',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  GIFT_CARD = 'gift_card',
  STORE_CREDIT = 'store_credit',
  CHECK = 'check',
  MOBILE_PAYMENT = 'mobile_payment',
  BANK_TRANSFER = 'bank_transfer',
  CRYPTOCURRENCY = 'cryptocurrency',
  LOYALTY_POINTS = 'loyalty_points',
}

export enum PaymentStatus {
  PENDING = 'pending',
  AUTHORIZED = 'authorized',
  CAPTURED = 'captured',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

@Entity('pos_payments')
export class PosPayment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  saleId: string;

  @ManyToOne(() => PosSale, sale => sale.payments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'saleId' })
  sale: PosSale;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  method: PaymentMethod;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  refundedAmount: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ length: 255, nullable: true })
  reference: string;

  @Column({ length: 255, nullable: true })
  authorizationCode: string;

  @Column({ length: 255, nullable: true })
  transactionId: string;

  @Column({ length: 100, nullable: true })
  cardType: string; // Visa, MasterCard, etc.

  @Column({ length: 20, nullable: true })
  cardLastFour: string;

  @Column({ length: 255, nullable: true })
  cardHolderName: string;

  @Column({ type: 'timestamp', nullable: true })
  processedAt: Date;

  @Column({ type: 'json', nullable: true })
  gatewayResponse: any;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  processingFee: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  processingFeeAmount: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ default: false })
  isRefund: boolean;

  @Column({ nullable: true })
  originalPaymentId: string;

  @ManyToOne(() => PosPayment, { nullable: true })
  @JoinColumn({ name: 'originalPaymentId' })
  originalPayment: PosPayment;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
