export declare enum StrategyType {
    SOFT = "soft",
    MEDIUM = "medium",
    AGGRESSIVE = "aggressive",
    LEGAL = "legal",
    CUSTOM = "custom"
}
export declare class CollectionStrategy {
    id: string;
    name: string;
    description: string;
    type: StrategyType;
    rules: any;
    actions: any;
    triggers: any;
    maxContactAttempts: number;
    contactFrequencyDays: number;
    escalationDays: number;
    isActive: boolean;
    isDefault: boolean;
    createdBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
