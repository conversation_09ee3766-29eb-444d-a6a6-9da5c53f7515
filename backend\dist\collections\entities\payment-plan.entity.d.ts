import { CollectionCase } from './collection-case.entity';
import { PaymentPlanInstallment } from './payment-plan-installment.entity';
export declare enum PaymentPlanStatus {
    DRAFT = "draft",
    PENDING_APPROVAL = "pending_approval",
    ACTIVE = "active",
    COMPLETED = "completed",
    DEFAULTED = "defaulted",
    CANCELLED = "cancelled",
    MODIFIED = "modified"
}
export declare enum PaymentFrequency {
    WEEKLY = "weekly",
    BIWEEKLY = "biweekly",
    MONTHLY = "monthly",
    QUARTERLY = "quarterly",
    CUSTOM = "custom"
}
export declare class PaymentPlan {
    id: string;
    caseId: string;
    case: CollectionCase;
    planNumber: string;
    status: PaymentPlanStatus;
    totalAmount: number;
    downPayment: number;
    remainingAmount: number;
    numberOfInstallments: number;
    installmentAmount: number;
    frequency: PaymentFrequency;
    startDate: Date;
    endDate: Date;
    firstPaymentDate: Date;
    interestRate: number;
    setupFee: number;
    lateFee: number;
    gracePeriodDays: number;
    terms: string;
    notes: string;
    createdBy: string;
    approvedBy: string;
    approvedAt: Date;
    customerSignature: string;
    customerSignedAt: Date;
    missedPayments: number;
    totalPaid: number;
    totalOutstanding: number;
    installments: PaymentPlanInstallment[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
