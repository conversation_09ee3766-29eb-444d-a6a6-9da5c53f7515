"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeRequest = exports.ChangeRisk = exports.ChangeStatus = exports.ChangeType = void 0;
const typeorm_1 = require("typeorm");
var ChangeType;
(function (ChangeType) {
    ChangeType["STANDARD"] = "standard";
    ChangeType["NORMAL"] = "normal";
    ChangeType["EMERGENCY"] = "emergency";
    ChangeType["MAJOR"] = "major";
})(ChangeType || (exports.ChangeType = ChangeType = {}));
var ChangeStatus;
(function (ChangeStatus) {
    ChangeStatus["DRAFT"] = "draft";
    ChangeStatus["SUBMITTED"] = "submitted";
    ChangeStatus["APPROVED"] = "approved";
    ChangeStatus["REJECTED"] = "rejected";
    ChangeStatus["SCHEDULED"] = "scheduled";
    ChangeStatus["IN_PROGRESS"] = "in_progress";
    ChangeStatus["COMPLETED"] = "completed";
    ChangeStatus["FAILED"] = "failed";
    ChangeStatus["CANCELLED"] = "cancelled";
})(ChangeStatus || (exports.ChangeStatus = ChangeStatus = {}));
var ChangeRisk;
(function (ChangeRisk) {
    ChangeRisk["LOW"] = "low";
    ChangeRisk["MEDIUM"] = "medium";
    ChangeRisk["HIGH"] = "high";
    ChangeRisk["CRITICAL"] = "critical";
})(ChangeRisk || (exports.ChangeRisk = ChangeRisk = {}));
let ChangeRequest = class ChangeRequest {
    id;
    changeNumber;
    title;
    description;
    type;
    status;
    risk;
    requesterId;
    requesterName;
    assignedTo;
    justification;
    implementationPlan;
    rollbackPlan;
    testPlan;
    scheduledStart;
    scheduledEnd;
    actualStart;
    actualEnd;
    affectedSystems;
    stakeholders;
    approvedBy;
    approvedAt;
    approvalNotes;
    implementationNotes;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.ChangeRequest = ChangeRequest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ChangeRequest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "changeNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ChangeType,
        default: ChangeType.NORMAL,
    }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ChangeStatus,
        default: ChangeStatus.DRAFT,
    }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ChangeRisk,
        default: ChangeRisk.MEDIUM,
    }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "risk", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ChangeRequest.prototype, "requesterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "requesterName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "justification", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "implementationPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "rollbackPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "testPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "scheduledStart", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "scheduledEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "actualStart", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "actualEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ChangeRequest.prototype, "affectedSystems", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ChangeRequest.prototype, "stakeholders", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "approvalNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ChangeRequest.prototype, "implementationNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], ChangeRequest.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ChangeRequest.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ChangeRequest.prototype, "updatedAt", void 0);
exports.ChangeRequest = ChangeRequest = __decorate([
    (0, typeorm_1.Entity)('change_requests')
], ChangeRequest);
//# sourceMappingURL=change-request.entity.js.map