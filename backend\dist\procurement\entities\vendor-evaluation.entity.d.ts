import { Vendor } from './vendor.entity';
export declare enum EvaluationStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export declare class VendorEvaluation {
    id: string;
    vendorId: string;
    vendor: Vendor;
    evaluationDate: Date;
    periodStart: Date;
    periodEnd: Date;
    status: EvaluationStatus;
    qualityScore: number;
    deliveryScore: number;
    serviceScore: number;
    priceScore: number;
    overallScore: number;
    strengths: string;
    weaknesses: string;
    recommendations: string;
    evaluatedBy: string;
    approvedBy: string;
    approvedAt: Date;
    criteria: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
