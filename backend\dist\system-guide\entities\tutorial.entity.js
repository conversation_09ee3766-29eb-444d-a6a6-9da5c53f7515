"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tutorial = exports.TutorialDifficulty = exports.TutorialType = void 0;
const typeorm_1 = require("typeorm");
const tutorial_step_entity_1 = require("./tutorial-step.entity");
var TutorialType;
(function (TutorialType) {
    TutorialType["INTERACTIVE"] = "interactive";
    TutorialType["VIDEO"] = "video";
    TutorialType["STEP_BY_STEP"] = "step_by_step";
    TutorialType["WALKTHROUGH"] = "walkthrough";
    TutorialType["DEMO"] = "demo";
})(TutorialType || (exports.TutorialType = TutorialType = {}));
var TutorialDifficulty;
(function (TutorialDifficulty) {
    TutorialDifficulty["BEGINNER"] = "beginner";
    TutorialDifficulty["INTERMEDIATE"] = "intermediate";
    TutorialDifficulty["ADVANCED"] = "advanced";
    TutorialDifficulty["EXPERT"] = "expert";
})(TutorialDifficulty || (exports.TutorialDifficulty = TutorialDifficulty = {}));
let Tutorial = class Tutorial {
    id;
    title;
    description;
    type;
    difficulty;
    estimatedDuration;
    thumbnailImage;
    videoUrl;
    prerequisites;
    learningObjectives;
    tags;
    targetRoles;
    isActive;
    isFeatured;
    authorId;
    viewCount;
    completionCount;
    rating;
    ratingCount;
    steps;
    metadata;
    createdAt;
    updatedAt;
};
exports.Tutorial = Tutorial;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Tutorial.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Tutorial.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Tutorial.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TutorialType,
        default: TutorialType.STEP_BY_STEP,
    }),
    __metadata("design:type", String)
], Tutorial.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TutorialDifficulty,
        default: TutorialDifficulty.BEGINNER,
    }),
    __metadata("design:type", String)
], Tutorial.prototype, "difficulty", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], Tutorial.prototype, "estimatedDuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], Tutorial.prototype, "thumbnailImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], Tutorial.prototype, "videoUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Tutorial.prototype, "prerequisites", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Tutorial.prototype, "learningObjectives", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Tutorial.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Tutorial.prototype, "targetRoles", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Tutorial.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Tutorial.prototype, "isFeatured", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tutorial.prototype, "authorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Tutorial.prototype, "viewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Tutorial.prototype, "completionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Tutorial.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Tutorial.prototype, "ratingCount", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => tutorial_step_entity_1.TutorialStep, step => step.tutorial, { cascade: true }),
    __metadata("design:type", Array)
], Tutorial.prototype, "steps", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Tutorial.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Tutorial.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Tutorial.prototype, "updatedAt", void 0);
exports.Tutorial = Tutorial = __decorate([
    (0, typeorm_1.Entity)('tutorials')
], Tutorial);
//# sourceMappingURL=tutorial.entity.js.map