{"version": 3, "file": "task-assignment.entity.js", "sourceRoot": "", "sources": ["../../../src/projects/entities/task-assignment.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQiB;AACjB,+CAAqC;AAG9B,IAAM,cAAc,GAApB,MAAM,cAAc;IAEzB,EAAE,CAAS;IAGX,MAAM,CAAS;IAIf,IAAI,CAAO;IAGX,MAAM,CAAS;IAGf,YAAY,CAAO;IAGnB,UAAU,CAAS;IAGnB,QAAQ,CAAU;IAGlB,KAAK,CAAS;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AA/BY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAGX;IADC,IAAA,gBAAM,GAAE;;8CACM;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IACxE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;8BACzB,kBAAI;4CAAC;AAGX;IADC,IAAA,gBAAM,GAAE;;8CACM;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACX,IAAI;oDAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDACR;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gDACR;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;yBA9BL,cAAc;IAD1B,IAAA,gBAAM,EAAC,kBAAkB,CAAC;GACd,cAAc,CA+B1B"}