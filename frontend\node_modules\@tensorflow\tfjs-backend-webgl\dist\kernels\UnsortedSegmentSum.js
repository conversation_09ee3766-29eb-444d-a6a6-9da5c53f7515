/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, sumOutType, UnsortedSegmentSum, util } from '@tensorflow/tfjs-core';
import { SegmentOpProgram } from '../segment_gpu';
import { range } from './Range';
import { reshape } from './Reshape';
import { tile } from './Tile';
import { transpose } from './Transpose';
export function unsortedSegmentSum(args) {
    const { inputs, backend, attrs } = args;
    const { x, segmentIds } = inputs;
    const { numSegments } = attrs;
    const xRank = x.shape.length;
    const toDispose = [];
    let axis = 0;
    const permutation = backend_util.getAxesPermutation([axis], xRank);
    let permutedX = x;
    if (permutation != null) {
        permutedX = transpose({ inputs: { x }, backend, attrs: { perm: permutation } });
        toDispose.push(permutedX);
        axis = backend_util.getInnerMostAxes(1, xRank)[0];
    }
    const outShape = backend_util.segment_util.computeOutShape(permutedX.shape, axis, numSegments);
    const inSize = util.sizeFromShape([permutedX.shape[axis]]);
    const a2D = reshape({ inputs: { x: permutedX }, backend, attrs: { shape: [-1, inSize] } });
    toDispose.push(a2D);
    const outputDType = sumOutType(x.dtype);
    const segOpCompute = (x, segOpType, segmentIds, dtype, numSegments) => {
        const batchSize = x.shape[0];
        const inSize = x.shape[1];
        const windowSize = backend_util.segment_util.segOpComputeOptimalWindowSize(inSize, numSegments);
        const segOpInfo = { windowSize, inSize, batchSize, numSegments };
        const program = new SegmentOpProgram(segOpInfo, segOpType);
        const output = backend.compileAndRun(program, [x, segmentIds], dtype);
        toDispose.push(output);
        // No need to run another GPGPU program.
        if (output.shape[1] === numSegments) {
            return output;
        }
        const rangeInfo = range({
            backend,
            attrs: { start: 0, stop: numSegments, step: 1, dtype: 'float32' }
        });
        const tileInfo = tile({
            inputs: { x: rangeInfo },
            backend,
            attrs: { reps: [inSize / windowSize] }
        });
        toDispose.push(rangeInfo);
        toDispose.push(tileInfo);
        const result = segOpCompute(output, segOpType, tileInfo, dtype, numSegments);
        return result;
    };
    const segOpResult = segOpCompute(a2D, 'unsortedSegmentSum', segmentIds, outputDType, numSegments);
    const reshaped = reshape({ inputs: { x: segOpResult }, backend, attrs: { shape: outShape } });
    let result = reshaped;
    if (permutation != null) {
        toDispose.push(reshaped);
        const perm = backend_util.getUndoAxesPermutation(permutation);
        result = transpose({ inputs: { x: result }, backend, attrs: { perm } });
    }
    toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));
    return result;
}
export const unsortedSegmentSumConfig = {
    kernelName: UnsortedSegmentSum,
    backendName: 'webgl',
    kernelFunc: unsortedSegmentSum
};
//# sourceMappingURL=data:application/json;base64,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