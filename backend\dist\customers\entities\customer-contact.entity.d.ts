import { Customer } from './customer.entity';
export declare enum ContactType {
    PRIMARY = "primary",
    BILLING = "billing",
    SHIPPING = "shipping",
    TECHNICAL = "technical",
    EMERGENCY = "emergency",
    ALTERNATE = "alternate"
}
export declare class CustomerContact {
    id: string;
    customerId: string;
    customer: Customer;
    type: ContactType;
    firstName: string;
    lastName: string;
    jobTitle: string;
    department: string;
    email: string;
    phone: string;
    mobile: string;
    fax: string;
    isPrimary: boolean;
    isActive: boolean;
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
