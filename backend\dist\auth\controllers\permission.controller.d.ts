import { PermissionService } from '../services/permission.service';
import { PermissionModule } from '../entities/permission.entity';
export declare class PermissionController {
    private readonly permissionService;
    constructor(permissionService: PermissionService);
    findAll(module?: PermissionModule): Promise<import("../entities/permission.entity").Permission[]>;
    getGroupedPermissions(): Promise<any>;
    getPermissionsByRole(roleId: string): Promise<import("../entities/permission.entity").Permission[]>;
    createDefaultPermissions(): Promise<import("../entities/permission.entity").Permission[]>;
}
