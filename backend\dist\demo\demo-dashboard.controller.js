"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DemoDashboardController = void 0;
const common_1 = require("@nestjs/common");
let DemoDashboardController = class DemoDashboardController {
    async getDashboard() {
        return {
            message: 'Welcome to ZaidanOne Management System',
            user: {
                userId: 'demo_user',
                email: '<EMAIL>',
                role: 'admin',
            },
            stats: {
                totalUsers: 1,
                totalProjects: 0,
                totalSales: 0,
                totalRevenue: 0,
            },
            recentActivities: [],
        };
    }
    async getStats() {
        return {
            overview: {
                totalUsers: 1,
                totalProjects: 5,
                totalSales: 12,
                totalRevenue: 45000,
            },
            departments: {
                sales: { active: 8, pending: 4 },
                projects: { active: 5, completed: 12 },
                hr: { employees: 25, departments: 6 },
                finance: { income: 125000, expenses: 80000 },
            },
        };
    }
};
exports.DemoDashboardController = DemoDashboardController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DemoDashboardController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DemoDashboardController.prototype, "getStats", null);
exports.DemoDashboardController = DemoDashboardController = __decorate([
    (0, common_1.Controller)('dashboard')
], DemoDashboardController);
//# sourceMappingURL=demo-dashboard.controller.js.map