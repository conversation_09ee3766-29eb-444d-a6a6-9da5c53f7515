import { Customer } from './customer.entity';
import { Invoice } from './invoice.entity';
import { CreditNoteItem } from './credit-note-item.entity';
export declare class CreditNote {
    id: string;
    creditNoteNumber: string;
    customerId: string;
    customer: Customer;
    invoiceId: string;
    invoice: Invoice;
    creditNoteDate: Date;
    reason: string;
    type: 'return' | 'discount' | 'error' | 'other';
    subtotal: number;
    taxAmount: number;
    totalAmount: number;
    status: 'draft' | 'issued' | 'applied' | 'cancelled';
    notes: string;
    appliedToInvoice: string;
    items: CreditNoteItem[];
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
