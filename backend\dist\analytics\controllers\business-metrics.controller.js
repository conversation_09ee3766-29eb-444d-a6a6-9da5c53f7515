"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessMetricsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const business_metrics_service_1 = require("../services/business-metrics.service");
let BusinessMetricsController = class BusinessMetricsController {
    businessMetricsService;
    constructor(businessMetricsService) {
        this.businessMetricsService = businessMetricsService;
    }
    async getBusinessMetrics(period = '30d', req) {
        try {
            const companyId = req.user.companyId;
            const metrics = await this.businessMetricsService.getBusinessMetrics(companyId, period);
            return {
                success: true,
                data: metrics,
                message: 'Business metrics retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve business metrics',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getRevenueMetrics(period = '30d', req) {
        try {
            const companyId = req.user.companyId;
            const metrics = await this.businessMetricsService.getRevenueMetrics(companyId, period);
            return {
                success: true,
                data: metrics,
                message: 'Revenue metrics retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve revenue metrics',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDepartmentMetrics(period = '30d', req) {
        try {
            const companyId = req.user.companyId;
            const metrics = await this.businessMetricsService.getDepartmentMetrics(companyId, period);
            return {
                success: true,
                data: metrics,
                message: 'Department metrics retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve department metrics',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getGrowthMetrics(period = '30d', req) {
        try {
            const companyId = req.user.companyId;
            const metrics = await this.businessMetricsService.getGrowthMetrics(companyId, period);
            return {
                success: true,
                data: metrics,
                message: 'Growth metrics retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to retrieve growth metrics',
                error: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.BusinessMetricsController = BusinessMetricsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get business metrics for specified period' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, description: 'Time period (e.g., 30d, 90d, 1y)', example: '30d' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Business metrics retrieved successfully' }),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessMetricsController.prototype, "getBusinessMetrics", null);
__decorate([
    (0, common_1.Get)('revenue'),
    (0, swagger_1.ApiOperation)({ summary: 'Get revenue metrics' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, description: 'Time period', example: '30d' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Revenue metrics retrieved successfully' }),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessMetricsController.prototype, "getRevenueMetrics", null);
__decorate([
    (0, common_1.Get)('departments'),
    (0, swagger_1.ApiOperation)({ summary: 'Get department-wise metrics' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, description: 'Time period', example: '30d' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Department metrics retrieved successfully' }),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessMetricsController.prototype, "getDepartmentMetrics", null);
__decorate([
    (0, common_1.Get)('growth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get growth metrics and trends' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, description: 'Time period', example: '30d' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Growth metrics retrieved successfully' }),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessMetricsController.prototype, "getGrowthMetrics", null);
exports.BusinessMetricsController = BusinessMetricsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics - Business Metrics'),
    (0, common_1.Controller)('analytics/business-metrics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [business_metrics_service_1.BusinessMetricsService])
], BusinessMetricsController);
//# sourceMappingURL=business-metrics.controller.js.map