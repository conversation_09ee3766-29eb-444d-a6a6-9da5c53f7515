import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_node_repl=__commonJS({"../../node_modules/highlight.js/lib/languages/node-repl.js"(exports,module){function nodeRepl(hljs){return {name:"Node REPL",contains:[{className:"meta",starts:{end:/ |$/,starts:{end:"$",subLanguage:"javascript"}},variants:[{begin:/^>(?=[ ]|$)/},{begin:/^\.\.\.(?=[ ]|$)/}]}]}}module.exports=nodeRepl;}});var nodeReplUDFY22TZ = require_node_repl();

export { nodeReplUDFY22TZ as default };
