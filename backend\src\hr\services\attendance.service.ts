import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Attendance, AttendanceStatus } from '../entities/attendance.entity';

@Injectable()
export class AttendanceService {
  constructor(
    @InjectRepository(Attendance)
    private attendanceRepository: Repository<Attendance>,
  ) {}

  async create(createAttendanceDto: any): Promise<Attendance> {
    const attendance = this.attendanceRepository.create(createAttendanceDto);
    
    // Calculate hours worked
    if (attendance.checkInTime && attendance.checkOutTime) {
      attendance.hoursWorked = this.calculateHoursWorked(
        attendance.checkInTime,
        attendance.checkOutTime,
        attendance.breakStartTime,
        attendance.breakEndTime
      );
    }

    return this.attendanceRepository.save(attendance);
  }

  async findAll(filters?: any): Promise<Attendance[]> {
    const queryBuilder = this.attendanceRepository.createQueryBuilder('attendance')
      .leftJoinAndSelect('attendance.employee', 'employee');

    if (filters?.employeeId) {
      queryBuilder.andWhere('attendance.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters?.startDate && filters.endDate) {
      queryBuilder.andWhere('attendance.date BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    if (filters?.status) {
      queryBuilder.andWhere('attendance.status = :status', { status: filters.status });
    }

    return queryBuilder
      .orderBy('attendance.date', 'DESC')
      .getMany();
  }

  async findOne(id: string): Promise<Attendance> {
    const attendance = await this.attendanceRepository.findOne({
      where: { id },
      relations: ['employee'],
    });

    if (!attendance) {
      throw new NotFoundException(`Attendance record with ID ${id} not found`);
    }

    return attendance;
  }

  async update(id: string, updateAttendanceDto: any): Promise<Attendance> {
    const attendance = await this.findOne(id);
    Object.assign(attendance, updateAttendanceDto);

    // Recalculate hours worked
    if (attendance.checkInTime && attendance.checkOutTime) {
      attendance.hoursWorked = this.calculateHoursWorked(
        attendance.checkInTime,
        attendance.checkOutTime,
        attendance.breakStartTime,
        attendance.breakEndTime
      );
    }

    return this.attendanceRepository.save(attendance);
  }

  async remove(id: string): Promise<void> {
    const attendance = await this.findOne(id);
    await this.attendanceRepository.remove(attendance);
  }

  async checkIn(employeeId: string, checkInData: any): Promise<Attendance> {
    const today = new Date().toISOString().split('T')[0];
    
    // Check if already checked in today
    const existingAttendance = await this.attendanceRepository.findOne({
      where: { employeeId, date: new Date(today) },
    });

    if (existingAttendance) {
      throw new Error('Employee already checked in today');
    }

    const attendance = this.attendanceRepository.create({
      employeeId,
      date: new Date(today),
      checkInTime: new Date().toTimeString().split(' ')[0],
      status: AttendanceStatus.PRESENT,
      ...checkInData,
    });

    return this.attendanceRepository.save(attendance);
  }

  async checkOut(employeeId: string, checkOutData: any): Promise<Attendance> {
    const today = new Date().toISOString().split('T')[0];
    
    const attendance = await this.attendanceRepository.findOne({
      where: { employeeId, date: new Date(today) },
    });

    if (!attendance) {
      throw new Error('No check-in record found for today');
    }

    attendance.checkOutTime = new Date().toTimeString().split(' ')[0];
    Object.assign(attendance, checkOutData);

    // Calculate hours worked
    attendance.hoursWorked = this.calculateHoursWorked(
      attendance.checkInTime,
      attendance.checkOutTime,
      attendance.breakStartTime,
      attendance.breakEndTime
    );

    return this.attendanceRepository.save(attendance);
  }

  async getAttendanceReport(employeeId: string, startDate: Date, endDate: Date): Promise<any> {
    const attendances = await this.findAll({ employeeId, startDate, endDate });

    const totalDays = attendances.length;
    const presentDays = attendances.filter(a => a.status === AttendanceStatus.PRESENT).length;
    const absentDays = attendances.filter(a => a.status === AttendanceStatus.ABSENT).length;
    const lateDays = attendances.filter(a => a.status === AttendanceStatus.LATE).length;
    const totalHours = attendances.reduce((sum, a) => sum + a.hoursWorked, 0);
    const totalOvertimeHours = attendances.reduce((sum, a) => sum + a.overtimeHours, 0);

    return {
      employeeId,
      period: { startDate, endDate },
      summary: {
        totalDays,
        presentDays,
        absentDays,
        lateDays,
        totalHours,
        totalOvertimeHours,
        attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0,
      },
      attendances,
    };
  }

  private calculateHoursWorked(checkIn: string, checkOut: string, breakStart?: string, breakEnd?: string): number {
    const checkInTime = new Date(`1970-01-01T${checkIn}`);
    const checkOutTime = new Date(`1970-01-01T${checkOut}`);
    
    let totalMinutes = (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60);

    // Subtract break time if provided
    if (breakStart && breakEnd) {
      const breakStartTime = new Date(`1970-01-01T${breakStart}`);
      const breakEndTime = new Date(`1970-01-01T${breakEnd}`);
      const breakMinutes = (breakEndTime.getTime() - breakStartTime.getTime()) / (1000 * 60);
      totalMinutes -= breakMinutes;
    }

    return Math.max(0, totalMinutes / 60); // Convert to hours
  }
}
