"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const user_service_1 = require("../user/user.service");
const company_service_1 = require("../company/company.service");
const user_entity_1 = require("../user/entities/user.entity");
let AuthService = class AuthService {
    userService;
    companyService;
    jwtService;
    constructor(userService, companyService, jwtService) {
        this.userService = userService;
        this.companyService = companyService;
        this.jwtService = jwtService;
    }
    async registerCompany(registerDto) {
        const company = await this.companyService.create({
            name: registerDto.companyName,
            slug: registerDto.companySlug,
            description: registerDto.companyDescription,
            website: registerDto.companyWebsite,
            phone: registerDto.companyPhone,
            email: registerDto.companyEmail,
            address: registerDto.companyAddress,
            city: registerDto.companyCity,
            country: registerDto.companyCountry,
            timezone: registerDto.companyTimezone,
        });
        const adminUser = await this.userService.create({
            email: registerDto.adminEmail,
            password: registerDto.adminPassword,
            firstName: registerDto.adminFirstName,
            lastName: registerDto.adminLastName,
            phone: registerDto.adminPhone,
            role: user_entity_1.UserRole.ADMIN,
            companyId: company.id,
            emailVerified: true,
        });
        const payload = {
            sub: adminUser.id,
            email: adminUser.email,
            role: adminUser.role,
            companyId: company.id,
            tenantId: company.tenantId,
        };
        const token = this.jwtService.sign(payload);
        return {
            user: {
                id: adminUser.id,
                email: adminUser.email,
                firstName: adminUser.firstName,
                lastName: adminUser.lastName,
                role: adminUser.role,
                company: {
                    id: company.id,
                    name: company.name,
                    slug: company.slug,
                    tenantId: company.tenantId,
                },
            },
            token,
        };
    }
    async login(loginDto) {
        const user = await this.userService.findByEmailWithPassword(loginDto.email);
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await this.userService.validatePassword(loginDto.password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        await this.userService.updateLastLogin(user.id);
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role,
            companyId: user.companyId,
            tenantId: user.company.tenantId,
        };
        const token = this.jwtService.sign(payload);
        return {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                company: {
                    id: user.company.id,
                    name: user.company.name,
                    slug: user.company.slug,
                    tenantId: user.company.tenantId,
                },
            },
            token,
        };
    }
    async validateUser(payload) {
        const user = await this.userService.findOne(payload.sub);
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        return user;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_service_1.UserService,
        company_service_1.CompanyService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map