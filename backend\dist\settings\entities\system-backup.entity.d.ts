export declare enum BackupType {
    FULL = "full",
    INCREMENTAL = "incremental",
    DIFFERENTIAL = "differential",
    DATABASE = "database",
    FILES = "files",
    CONFIGURATION = "configuration"
}
export declare enum BackupStatus {
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
    EXPIRED = "expired"
}
export declare class SystemBackup {
    id: string;
    name: string;
    description: string;
    type: BackupType;
    status: BackupStatus;
    filePath: string;
    fileSize: number;
    checksum: string;
    startedAt: Date;
    completedAt: Date;
    duration: number;
    errorMessage: string;
    includedTables: string[];
    excludedTables: string[];
    isScheduled: boolean;
    scheduleConfig: any;
    retentionDays: number;
    expiresAt: Date;
    createdBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
