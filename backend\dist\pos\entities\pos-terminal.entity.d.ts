import { PosSale } from './pos-sale.entity';
import { PosShift } from './pos-shift.entity';
export declare enum TerminalStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    MAINTENANCE = "maintenance",
    OFFLINE = "offline"
}
export declare enum TerminalType {
    STANDARD = "standard",
    MOBILE = "mobile",
    KIOSK = "kiosk",
    TABLET = "tablet",
    HANDHELD = "handheld"
}
export declare class PosTerminal {
    id: string;
    terminalNumber: string;
    name: string;
    description: string;
    type: TerminalType;
    status: TerminalStatus;
    location: string;
    warehouseId: string;
    ipAddress: string;
    macAddress: string;
    deviceModel: string;
    serialNumber: string;
    printerSettings: any;
    cashDrawerSettings: any;
    scannerSettings: any;
    displaySettings: any;
    allowCashPayments: boolean;
    allowCardPayments: boolean;
    allowMobilePayments: boolean;
    allowGiftCards: boolean;
    allowLayaway: boolean;
    allowReturns: boolean;
    allowDiscounts: boolean;
    maxDiscountAmount: number;
    maxDiscountPercentage: number;
    lastHeartbeat: Date;
    configuration: any;
    sales: PosSale[];
    shifts: PosShift[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
