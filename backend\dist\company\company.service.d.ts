import { Repository } from 'typeorm';
import { Company } from './entities/company.entity';
import { TenantConnectionService } from '../tenant/tenant-connection.service';
export declare class CompanyService {
    private companyRepository;
    private tenantConnectionService;
    constructor(companyRepository: Repository<Company>, tenantConnectionService: TenantConnectionService);
    create(companyData: Partial<Company>): Promise<Company>;
    findAll(): Promise<Company[]>;
    findOne(id: string): Promise<Company>;
    findBySlug(slug: string): Promise<Company>;
    findByTenantId(tenantId: string): Promise<Company>;
    update(id: string, updateData: Partial<Company>): Promise<Company>;
    remove(id: string): Promise<void>;
}
