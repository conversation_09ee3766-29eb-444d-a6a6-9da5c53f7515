"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionDocument = exports.DocumentType = void 0;
const typeorm_1 = require("typeorm");
const collection_case_entity_1 = require("./collection-case.entity");
var DocumentType;
(function (DocumentType) {
    DocumentType["DEMAND_LETTER"] = "demand_letter";
    DocumentType["PAYMENT_AGREEMENT"] = "payment_agreement";
    DocumentType["SETTLEMENT_AGREEMENT"] = "settlement_agreement";
    DocumentType["LEGAL_NOTICE"] = "legal_notice";
    DocumentType["COURT_DOCUMENT"] = "court_document";
    DocumentType["PAYMENT_RECEIPT"] = "payment_receipt";
    DocumentType["CORRESPONDENCE"] = "correspondence";
    DocumentType["DISPUTE_DOCUMENT"] = "dispute_document";
    DocumentType["IDENTIFICATION"] = "identification";
    DocumentType["FINANCIAL_STATEMENT"] = "financial_statement";
    DocumentType["OTHER"] = "other";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
let CollectionDocument = class CollectionDocument {
    id;
    caseId;
    case;
    name;
    description;
    type;
    fileName;
    originalName;
    filePath;
    mimeType;
    fileSize;
    uploadedBy;
    documentDate;
    tags;
    isActive;
    isConfidential;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionDocument = CollectionDocument;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionDocument.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionDocument.prototype, "caseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_case_entity_1.CollectionCase, collectionCase => collectionCase.documents),
    (0, typeorm_1.JoinColumn)({ name: 'caseId' }),
    __metadata("design:type", collection_case_entity_1.CollectionCase)
], CollectionDocument.prototype, "case", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DocumentType,
        default: DocumentType.OTHER,
    }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "fileName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "originalName", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500 }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "filePath", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], CollectionDocument.prototype, "mimeType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], CollectionDocument.prototype, "fileSize", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionDocument.prototype, "uploadedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionDocument.prototype, "documentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CollectionDocument.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CollectionDocument.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionDocument.prototype, "isConfidential", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionDocument.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionDocument.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionDocument.prototype, "updatedAt", void 0);
exports.CollectionDocument = CollectionDocument = __decorate([
    (0, typeorm_1.Entity)('collection_documents')
], CollectionDocument);
//# sourceMappingURL=collection-document.entity.js.map