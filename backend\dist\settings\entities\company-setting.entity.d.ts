export declare enum CompanySettingType {
    GENERAL = "general",
    BRANDING = "branding",
    BUSINESS = "business",
    FINANCIAL = "financial",
    LOCALIZATION = "localization",
    INTEGRATION = "integration",
    WORKFLOW = "workflow",
    SECURITY = "security"
}
export declare class CompanySetting {
    id: string;
    tenantId: string;
    key: string;
    value: string;
    type: CompanySettingType;
    name: string;
    description: string;
    isEncrypted: boolean;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
