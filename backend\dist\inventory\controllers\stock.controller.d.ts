import { StockService } from '../services/stock.service';
export declare class StockController {
    private readonly stockService;
    constructor(stockService: StockService);
    findAll(): Promise<import("../entities/stock.entity").Stock[]>;
    getStatistics(): Promise<any>;
    getLowStockItems(threshold?: string): Promise<import("../entities/stock.entity").Stock[]>;
    getOutOfStockItems(): Promise<import("../entities/stock.entity").Stock[]>;
    getOverstockItems(threshold?: string): Promise<import("../entities/stock.entity").Stock[]>;
    getStockMovements(productId?: string, warehouseId?: string, startDate?: string, endDate?: string): Promise<import("../entities/stock-movement.entity").StockMovement[]>;
    getStockAdjustments(productId?: string, warehouseId?: string, startDate?: string, endDate?: string): Promise<import("../entities/stock-adjustment.entity").StockAdjustment[]>;
    findByProduct(productId: string): Promise<import("../entities/stock.entity").Stock[]>;
    findByWarehouse(warehouseId: string): Promise<import("../entities/stock.entity").Stock[]>;
    findByProductAndWarehouse(productId: string, warehouseId: string): Promise<import("../entities/stock.entity").Stock | null>;
    findOne(id: string): Promise<import("../entities/stock.entity").Stock>;
    adjustStock(adjustmentData: {
        productId: string;
        warehouseId: string;
        adjustment: number;
        reason: string;
        adjustedBy?: string;
    }): Promise<import("../entities/stock.entity").Stock>;
    reserveStock(reservationData: {
        productId: string;
        warehouseId: string;
        quantity: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    releaseReservation(releaseData: {
        productId: string;
        warehouseId: string;
        quantity: number;
    }): Promise<{
        message: string;
    }>;
    fulfillReservation(fulfillmentData: {
        productId: string;
        warehouseId: string;
        quantity: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    bulkStockUpdate(updates: Array<{
        productId: string;
        warehouseId: string;
        quantity: number;
        reason: string;
    }>): Promise<{
        message: string;
    }>;
}
