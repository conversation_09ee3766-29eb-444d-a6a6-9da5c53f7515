"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const tax_record_entity_1 = require("../entities/tax-record.entity");
let TaxService = class TaxService {
    taxRecordRepository;
    constructor(taxRecordRepository) {
        this.taxRecordRepository = taxRecordRepository;
    }
    async create(createTaxRecordDto) {
        const taxRecordNumber = await this.generateTaxRecordNumber(createTaxRecordDto.taxType);
        const taxRecord = this.taxRecordRepository.create({
            ...createTaxRecordDto,
            taxRecordNumber,
            taxAmount: createTaxRecordDto.taxableAmount * (createTaxRecordDto.taxRate / 100),
        });
        taxRecord.totalAmount = taxRecord.taxAmount + (taxRecord.penaltyAmount || 0) + (taxRecord.interestAmount || 0);
        taxRecord.outstandingAmount = taxRecord.totalAmount;
        return this.taxRecordRepository.save(taxRecord);
    }
    async findAll(filters) {
        const queryBuilder = this.taxRecordRepository.createQueryBuilder('taxRecord');
        if (filters?.taxType) {
            queryBuilder.andWhere('taxRecord.taxType = :taxType', { taxType: filters.taxType });
        }
        if (filters?.status) {
            queryBuilder.andWhere('taxRecord.status = :status', { status: filters.status });
        }
        if (filters?.startDate && filters?.endDate) {
            queryBuilder.andWhere('taxRecord.taxPeriodStart >= :startDate AND taxRecord.taxPeriodEnd <= :endDate', {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        return queryBuilder
            .orderBy('taxRecord.dueDate', 'ASC')
            .getMany();
    }
    async findOne(id) {
        const taxRecord = await this.taxRecordRepository.findOne({ where: { id } });
        if (!taxRecord) {
            throw new common_1.NotFoundException(`Tax record with ID ${id} not found`);
        }
        return taxRecord;
    }
    async update(id, updateTaxRecordDto) {
        const taxRecord = await this.findOne(id);
        Object.assign(taxRecord, updateTaxRecordDto);
        if (updateTaxRecordDto.taxableAmount || updateTaxRecordDto.taxRate) {
            taxRecord.taxAmount = taxRecord.taxableAmount * (taxRecord.taxRate / 100);
        }
        taxRecord.totalAmount = taxRecord.taxAmount + (taxRecord.penaltyAmount || 0) + (taxRecord.interestAmount || 0);
        taxRecord.outstandingAmount = taxRecord.totalAmount - taxRecord.paidAmount;
        return this.taxRecordRepository.save(taxRecord);
    }
    async remove(id) {
        const taxRecord = await this.findOne(id);
        if (taxRecord.status === tax_record_entity_1.TaxStatus.FILED || taxRecord.status === tax_record_entity_1.TaxStatus.PAID) {
            throw new Error('Cannot delete filed or paid tax record');
        }
        await this.taxRecordRepository.remove(taxRecord);
    }
    async fileReturn(id, filedBy, referenceNumber) {
        const taxRecord = await this.findOne(id);
        taxRecord.status = tax_record_entity_1.TaxStatus.FILED;
        taxRecord.filedDate = new Date();
        taxRecord.filedBy = filedBy;
        if (referenceNumber) {
            taxRecord.referenceNumber = referenceNumber;
        }
        return this.taxRecordRepository.save(taxRecord);
    }
    async recordPayment(id, paymentAmount) {
        const taxRecord = await this.findOne(id);
        taxRecord.paidAmount += paymentAmount;
        taxRecord.outstandingAmount = taxRecord.totalAmount - taxRecord.paidAmount;
        if (taxRecord.outstandingAmount <= 0) {
            taxRecord.status = tax_record_entity_1.TaxStatus.PAID;
            taxRecord.paidDate = new Date();
        }
        return this.taxRecordRepository.save(taxRecord);
    }
    async calculatePenaltiesAndInterest(id) {
        const taxRecord = await this.findOne(id);
        const today = new Date();
        if (today > taxRecord.dueDate && taxRecord.status !== tax_record_entity_1.TaxStatus.PAID) {
            const daysOverdue = Math.floor((today.getTime() - taxRecord.dueDate.getTime()) / (1000 * 60 * 60 * 24));
            const monthsOverdue = Math.ceil(daysOverdue / 30);
            taxRecord.penaltyAmount = taxRecord.taxAmount * 0.01 * monthsOverdue;
            taxRecord.interestAmount = taxRecord.taxAmount * 0.005 * monthsOverdue;
            taxRecord.totalAmount = taxRecord.taxAmount + taxRecord.penaltyAmount + taxRecord.interestAmount;
            taxRecord.outstandingAmount = taxRecord.totalAmount - taxRecord.paidAmount;
            if (taxRecord.status === tax_record_entity_1.TaxStatus.FILED) {
                taxRecord.status = tax_record_entity_1.TaxStatus.OVERDUE;
            }
        }
        return this.taxRecordRepository.save(taxRecord);
    }
    async getTaxSummary(year) {
        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year, 11, 31);
        const taxRecords = await this.findAll({
            startDate,
            endDate,
        });
        const summary = {
            year,
            totalTaxLiability: taxRecords.reduce((sum, record) => sum + record.taxAmount, 0),
            totalPaid: taxRecords.reduce((sum, record) => sum + record.paidAmount, 0),
            totalOutstanding: taxRecords.reduce((sum, record) => sum + record.outstandingAmount, 0),
            totalPenalties: taxRecords.reduce((sum, record) => sum + (record.penaltyAmount || 0), 0),
            totalInterest: taxRecords.reduce((sum, record) => sum + (record.interestAmount || 0), 0),
            byTaxType: this.groupByTaxType(taxRecords),
            byStatus: this.groupByStatus(taxRecords),
            overdueRecords: taxRecords.filter(record => record.status === tax_record_entity_1.TaxStatus.OVERDUE),
        };
        return summary;
    }
    async generateTaxRecordNumber(taxType) {
        const year = new Date().getFullYear();
        const typePrefix = this.getTaxTypePrefix(taxType);
        const prefix = `${typePrefix}-${year}-`;
        const lastRecord = await this.taxRecordRepository.findOne({
            where: { taxRecordNumber: (0, typeorm_2.Like)(`${prefix}%`) },
            order: { taxRecordNumber: 'DESC' },
        });
        let nextNumber = 1;
        if (lastRecord) {
            const lastNumber = parseInt(lastRecord.taxRecordNumber.split('-')[2]);
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
    }
    getTaxTypePrefix(taxType) {
        switch (taxType) {
            case tax_record_entity_1.TaxType.INCOME_TAX: return 'IT';
            case tax_record_entity_1.TaxType.SALES_TAX: return 'ST';
            case tax_record_entity_1.TaxType.VAT: return 'VAT';
            case tax_record_entity_1.TaxType.PAYROLL_TAX: return 'PT';
            case tax_record_entity_1.TaxType.PROPERTY_TAX: return 'PROP';
            default: return 'TAX';
        }
    }
    groupByTaxType(records) {
        return records.reduce((acc, record) => {
            if (!acc[record.taxType]) {
                acc[record.taxType] = {
                    count: 0,
                    totalTax: 0,
                    totalPaid: 0,
                    totalOutstanding: 0,
                };
            }
            acc[record.taxType].count++;
            acc[record.taxType].totalTax += record.taxAmount;
            acc[record.taxType].totalPaid += record.paidAmount;
            acc[record.taxType].totalOutstanding += record.outstandingAmount;
            return acc;
        }, {});
    }
    groupByStatus(records) {
        return records.reduce((acc, record) => {
            if (!acc[record.status]) {
                acc[record.status] = {
                    count: 0,
                    totalAmount: 0,
                };
            }
            acc[record.status].count++;
            acc[record.status].totalAmount += record.totalAmount;
            return acc;
        }, {});
    }
};
exports.TaxService = TaxService;
exports.TaxService = TaxService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(tax_record_entity_1.TaxRecord)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], TaxService);
//# sourceMappingURL=tax.service.js.map