"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionCall = exports.CallDisposition = exports.CallResult = void 0;
const typeorm_1 = require("typeorm");
var CallResult;
(function (CallResult) {
    CallResult["CONTACT_MADE"] = "contact_made";
    CallResult["NO_ANSWER"] = "no_answer";
    CallResult["BUSY"] = "busy";
    CallResult["DISCONNECTED"] = "disconnected";
    CallResult["WRONG_NUMBER"] = "wrong_number";
    CallResult["LEFT_VOICEMAIL"] = "left_voicemail";
    CallResult["CALL_BACK_REQUESTED"] = "call_back_requested";
    CallResult["REFUSED_TO_TALK"] = "refused_to_talk";
    CallResult["HUNG_UP"] = "hung_up";
    CallResult["TECHNICAL_ISSUE"] = "technical_issue";
})(CallResult || (exports.CallResult = CallResult = {}));
var CallDisposition;
(function (CallDisposition) {
    CallDisposition["PROMISE_TO_PAY"] = "promise_to_pay";
    CallDisposition["PAYMENT_MADE"] = "payment_made";
    CallDisposition["DISPUTE_RAISED"] = "dispute_raised";
    CallDisposition["HARDSHIP_CLAIMED"] = "hardship_claimed";
    CallDisposition["PAYMENT_PLAN_REQUESTED"] = "payment_plan_requested";
    CallDisposition["REFUSED_TO_PAY"] = "refused_to_pay";
    CallDisposition["WILL_CALL_BACK"] = "will_call_back";
    CallDisposition["REQUESTED_VALIDATION"] = "requested_validation";
    CallDisposition["CEASE_AND_DESIST"] = "cease_and_desist";
    CallDisposition["BANKRUPTCY_CLAIMED"] = "bankruptcy_claimed";
    CallDisposition["DECEASED_CLAIMED"] = "deceased_claimed";
    CallDisposition["NOT_RESPONSIBLE"] = "not_responsible";
    CallDisposition["OTHER"] = "other";
})(CallDisposition || (exports.CallDisposition = CallDisposition = {}));
let CollectionCall = class CollectionCall {
    id;
    caseId;
    callDateTime;
    phoneNumber;
    duration;
    result;
    disposition;
    agentId;
    notes;
    summary;
    promiseAmount;
    promiseDate;
    callbackDate;
    recordingPath;
    isRecorded;
    isInbound;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionCall = CollectionCall;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionCall.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionCall.prototype, "caseId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], CollectionCall.prototype, "callDateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20 }),
    __metadata("design:type", String)
], CollectionCall.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], CollectionCall.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CallResult,
    }),
    __metadata("design:type", String)
], CollectionCall.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CallDisposition,
        nullable: true,
    }),
    __metadata("design:type", String)
], CollectionCall.prototype, "disposition", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionCall.prototype, "agentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionCall.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionCall.prototype, "summary", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CollectionCall.prototype, "promiseAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionCall.prototype, "promiseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionCall.prototype, "callbackDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true }),
    __metadata("design:type", String)
], CollectionCall.prototype, "recordingPath", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCall.prototype, "isRecorded", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CollectionCall.prototype, "isInbound", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionCall.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionCall.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionCall.prototype, "updatedAt", void 0);
exports.CollectionCall = CollectionCall = __decorate([
    (0, typeorm_1.Entity)('collection_calls')
], CollectionCall);
//# sourceMappingURL=collection-call.entity.js.map