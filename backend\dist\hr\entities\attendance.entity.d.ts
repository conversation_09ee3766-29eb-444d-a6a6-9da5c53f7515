import { Employee } from './employee.entity';
export declare enum AttendanceStatus {
    PRESENT = "present",
    ABSENT = "absent",
    LATE = "late",
    HALF_DAY = "half_day",
    WORK_FROM_HOME = "work_from_home",
    ON_LEAVE = "on_leave",
    HOLIDAY = "holiday"
}
export declare class Attendance {
    id: string;
    employeeId: string;
    employee: Employee;
    date: Date;
    checkInTime: string;
    checkOutTime: string;
    breakStartTime: string;
    breakEndTime: string;
    status: AttendanceStatus;
    hoursWorked: number;
    overtimeHours: number;
    breakHours: number;
    notes: string;
    location: string;
    gpsCoordinates: any;
    isManualEntry: boolean;
    approvedBy: string;
    approvedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
