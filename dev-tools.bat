@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: ZaidanOne Ultimate ERP System - Development Tools
:: =============================================================================
:: This script provides development utilities and tools for the ERP system
:: =============================================================================

title ZaidanOne ERP - Development Tools

color 0E

:MENU
cls
echo.
echo ===============================================================================
echo                    ZAIDANONE ERP SYSTEM - DEVELOPMENT TOOLS
echo ===============================================================================
echo.
echo Development Utilities:
echo.
echo [1] Install Dependencies (Frontend + Backend)
echo [2] Update All Dependencies
echo [3] Run Frontend Tests
echo [4] Run Backend Tests
echo [5] Build Production Version
echo [6] Lint Code (Frontend)
echo [7] Type Check (Frontend)
echo [8] Database Setup (Backend)
echo [9] Generate API Documentation
echo [A] Clean Node Modules
echo [B] Check System Requirements
echo [C] View Logs
echo [D] Performance Analysis
echo [0] Back to Main Menu
echo.
set /p choice="Enter your choice: "

if /i "%choice%"=="1" goto INSTALL_DEPS
if /i "%choice%"=="2" goto UPDATE_DEPS
if /i "%choice%"=="3" goto TEST_FRONTEND
if /i "%choice%"=="4" goto TEST_BACKEND
if /i "%choice%"=="5" goto BUILD_PROD
if /i "%choice%"=="6" goto LINT_CODE
if /i "%choice%"=="7" goto TYPE_CHECK
if /i "%choice%"=="8" goto DB_SETUP
if /i "%choice%"=="9" goto API_DOCS
if /i "%choice%"=="a" goto CLEAN_MODULES
if /i "%choice%"=="b" goto CHECK_REQUIREMENTS
if /i "%choice%"=="c" goto VIEW_LOGS
if /i "%choice%"=="d" goto PERFORMANCE
if "%choice%"=="0" goto EXIT

echo [ERROR] Invalid choice. Please try again.
timeout /t 2 >nul
goto MENU

:INSTALL_DEPS
echo.
echo [INFO] Installing Dependencies...
echo.

echo [INFO] Installing Frontend Dependencies...
cd frontend
npm install --legacy-peer-deps
if errorlevel 1 (
    echo [ERROR] Frontend installation failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [INFO] Installing Backend Dependencies...
cd backend
npm install
if errorlevel 1 (
    echo [ERROR] Backend installation failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [SUCCESS] All dependencies installed!
pause
goto MENU

:UPDATE_DEPS
echo.
echo [INFO] Updating Dependencies...
echo.

echo [INFO] Updating Frontend Dependencies...
cd frontend
npm update
cd ..

echo [INFO] Updating Backend Dependencies...
cd backend
npm update
cd ..

echo [SUCCESS] Dependencies updated!
pause
goto MENU

:TEST_FRONTEND
echo.
echo [INFO] Running Frontend Tests...
echo.
cd frontend
npm run test
cd ..
pause
goto MENU

:TEST_BACKEND
echo.
echo [INFO] Running Backend Tests...
echo.
cd backend
npm run test
cd ..
pause
goto MENU

:BUILD_PROD
echo.
echo [INFO] Building Production Version...
echo.

echo [INFO] Building Frontend...
cd frontend
npm run build
if errorlevel 1 (
    echo [ERROR] Frontend build failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [INFO] Building Backend...
cd backend
npm run build
if errorlevel 1 (
    echo [ERROR] Backend build failed!
    cd ..
    pause
    goto MENU
)
cd ..

echo [SUCCESS] Production build completed!
pause
goto MENU

:LINT_CODE
echo.
echo [INFO] Running Code Linting...
echo.
cd frontend
npm run lint
cd ..
pause
goto MENU

:TYPE_CHECK
echo.
echo [INFO] Running TypeScript Type Check...
echo.
cd frontend
npm run type-check
cd ..
pause
goto MENU

:DB_SETUP
echo.
echo [INFO] Setting up Database...
echo.
cd backend
npm run db:setup
cd ..
echo [SUCCESS] Database setup completed!
pause
goto MENU

:API_DOCS
echo.
echo [INFO] Generating API Documentation...
echo.
cd backend
npm run docs:generate
cd ..
echo [SUCCESS] API documentation generated!
pause
goto MENU

:CLEAN_MODULES
echo.
echo [WARNING] This will delete all node_modules directories!
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto MENU

echo [INFO] Cleaning node_modules...
if exist "frontend\node_modules" rmdir /s /q "frontend\node_modules"
if exist "backend\node_modules" rmdir /s /q "backend\node_modules"
echo [SUCCESS] Cleanup completed!
pause
goto MENU

:CHECK_REQUIREMENTS
echo.
echo [INFO] Checking System Requirements...
echo.

echo [INFO] Node.js Version:
node --version

echo [INFO] npm Version:
npm --version

echo [INFO] Available Memory:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | findstr "="

echo [INFO] Disk Space:
dir /-c | find "bytes free"

pause
goto MENU

:VIEW_LOGS
echo.
echo [INFO] Recent Log Files:
echo.
if exist "backend\logs" (
    dir "backend\logs" /o-d
) else (
    echo [INFO] No log directory found
)
pause
goto MENU

:PERFORMANCE
echo.
echo [INFO] Performance Analysis...
echo.
echo [INFO] Running performance checks...
cd frontend
npm run analyze 2>nul || echo [INFO] Performance analysis not available
cd ..
pause
goto MENU

:EXIT
echo.
echo [INFO] Returning to main menu...
call start-erp-system.bat
