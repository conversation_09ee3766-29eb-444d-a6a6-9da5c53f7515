"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionStrategyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const collection_strategy_entity_1 = require("../entities/collection-strategy.entity");
let CollectionStrategyService = class CollectionStrategyService {
    collectionStrategyRepository;
    constructor(collectionStrategyRepository) {
        this.collectionStrategyRepository = collectionStrategyRepository;
    }
    async create(strategyData) {
        const strategy = this.collectionStrategyRepository.create(strategyData);
        return this.collectionStrategyRepository.save(strategy);
    }
    async findAll() {
        return this.collectionStrategyRepository.find({
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const strategy = await this.collectionStrategyRepository.findOne({
            where: { id },
        });
        if (!strategy) {
            throw new common_1.NotFoundException(`Collection strategy with ID ${id} not found`);
        }
        return strategy;
    }
    async update(id, updateData) {
        await this.collectionStrategyRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const strategy = await this.findOne(id);
        await this.collectionStrategyRepository.remove(strategy);
    }
    async findByDebtRange(minAmount, maxAmount) {
        return this.collectionStrategyRepository
            .createQueryBuilder('strategy')
            .where('strategy.minDebtAmount <= :maxAmount', { maxAmount })
            .andWhere('strategy.maxDebtAmount >= :minAmount', { minAmount })
            .orderBy('strategy.name', 'ASC')
            .getMany();
    }
    async findByDaysOverdue(daysOverdue) {
        return this.collectionStrategyRepository
            .createQueryBuilder('strategy')
            .where('strategy.minDaysOverdue <= :daysOverdue', { daysOverdue })
            .andWhere('strategy.maxDaysOverdue >= :daysOverdue', { daysOverdue })
            .orderBy('strategy.name', 'ASC')
            .getMany();
    }
    async getRecommendedStrategy(debtAmount, daysOverdue) {
        const strategies = await this.collectionStrategyRepository
            .createQueryBuilder('strategy')
            .where('strategy.minDebtAmount <= :debtAmount', { debtAmount })
            .andWhere('strategy.maxDebtAmount >= :debtAmount', { debtAmount })
            .andWhere('strategy.minDaysOverdue <= :daysOverdue', { daysOverdue })
            .andWhere('strategy.maxDaysOverdue >= :daysOverdue', { daysOverdue })
            .andWhere('strategy.isActive = :isActive', { isActive: true })
            .orderBy('strategy.name', 'ASC')
            .getMany();
        return strategies.length > 0 ? strategies[0] : null;
    }
    async activateStrategy(id) {
        await this.collectionStrategyRepository.update(id, { isActive: true });
        return this.findOne(id);
    }
    async deactivateStrategy(id) {
        await this.collectionStrategyRepository.update(id, { isActive: false });
        return this.findOne(id);
    }
    async getActiveStrategies() {
        return this.collectionStrategyRepository.find({
            where: { isActive: true },
            order: { name: 'ASC' },
        });
    }
    async createDefaultStrategies() {
        const defaultStrategies = [
            {
                name: 'Early Stage Collection',
                description: 'For debts 1-30 days overdue',
                minDebtAmount: 0,
                maxDebtAmount: 999999,
                minDaysOverdue: 1,
                maxDaysOverdue: 30,
                actions: [
                    'Send friendly reminder email',
                    'Make courtesy phone call',
                    'Send first notice letter',
                ],
                escalationRules: 'Escalate if no response after 30 days',
                isActive: true,
            },
            {
                name: 'Mid Stage Collection',
                description: 'For debts 31-60 days overdue',
                minDebtAmount: 0,
                maxDebtAmount: 999999,
                minDaysOverdue: 31,
                maxDaysOverdue: 60,
                actions: [
                    'Send formal demand letter',
                    'Make multiple phone calls',
                    'Offer payment plan',
                ],
                escalationRules: 'Escalate if no payment arrangement after 60 days',
                isActive: true,
            },
            {
                name: 'Late Stage Collection',
                description: 'For debts 61-90 days overdue',
                minDebtAmount: 0,
                maxDebtAmount: 999999,
                minDaysOverdue: 61,
                maxDaysOverdue: 90,
                actions: [
                    'Send final demand letter',
                    'Daily phone calls',
                    'Consider legal action',
                ],
                escalationRules: 'Escalate to legal department after 90 days',
                isActive: true,
            },
            {
                name: 'High Value Collection',
                description: 'For high-value debts requiring special attention',
                minDebtAmount: 10000,
                maxDebtAmount: 999999,
                minDaysOverdue: 1,
                maxDaysOverdue: 999,
                actions: [
                    'Immediate personal contact',
                    'Senior agent assignment',
                    'Flexible payment terms',
                ],
                escalationRules: 'Manager involvement from day 1',
                isActive: true,
            },
        ];
        const createdStrategies = [];
        for (const strategyData of defaultStrategies) {
            const existing = await this.collectionStrategyRepository.findOne({
                where: { name: strategyData.name },
            });
            if (!existing) {
                const strategy = await this.create(strategyData);
                createdStrategies.push(strategy);
            }
        }
        return createdStrategies;
    }
    async getStrategyEffectiveness() {
        const strategies = await this.findAll();
        return strategies.map(strategy => ({
            id: strategy.id,
            name: strategy.name,
            description: strategy.description,
            isActive: strategy.isActive,
            debtRange: `$${strategy.minDebtAmount} - $${strategy.maxDebtAmount}`,
            daysRange: `${strategy.minDaysOverdue} - ${strategy.maxDaysOverdue} days`,
            casesAssigned: 0,
            successRate: 0,
            averageResolutionTime: 0,
        }));
    }
    async assignStrategyToCase(caseId, strategyId) {
    }
    async getStrategyRecommendations(caseData) {
        const primaryStrategy = await this.getRecommendedStrategy(caseData.debtAmount, caseData.daysOverdue);
        const alternativeStrategies = await this.collectionStrategyRepository
            .createQueryBuilder('strategy')
            .where('strategy.isActive = :isActive', { isActive: true })
            .andWhere('strategy.id != :primaryId', {
            primaryId: primaryStrategy?.id || '00000000-0000-0000-0000-000000000000'
        })
            .take(3)
            .getMany();
        const recommendations = [];
        if (caseData.debtAmount > 10000) {
            recommendations.push('Consider assigning to senior collection agent');
        }
        if (caseData.daysOverdue > 60) {
            recommendations.push('Escalate to legal review');
        }
        if (caseData.customerType === 'business') {
            recommendations.push('Contact during business hours');
        }
        return {
            primaryStrategy,
            alternativeStrategies,
            recommendations,
        };
    }
};
exports.CollectionStrategyService = CollectionStrategyService;
exports.CollectionStrategyService = CollectionStrategyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(collection_strategy_entity_1.CollectionStrategy)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CollectionStrategyService);
//# sourceMappingURL=collection-strategy.service.js.map