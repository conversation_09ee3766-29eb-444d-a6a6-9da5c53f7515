import { BusinessMetricsService } from '../services/business-metrics.service';
export declare class BusinessMetricsController {
    private readonly businessMetricsService;
    constructor(businessMetricsService: BusinessMetricsService);
    getBusinessMetrics(period: string | undefined, req: any): Promise<{
        success: boolean;
        data: {
            totalRevenue: number;
            totalExpenses: number;
            netProfit: number;
            totalCustomers: number;
            totalProjects: number;
            totalEmployees: number;
            revenueGrowth: number;
            customerGrowth: number;
            profitGrowth: number;
            projectGrowth: number;
            departmentMetrics: {
                department: string;
                revenue: number;
                expenses: number;
                profit: number;
                efficiency: number;
                trend: string;
            }[];
        };
        message: string;
    }>;
    getRevenueMetrics(period: string | undefined, req: any): Promise<{
        success: boolean;
        data: {
            totalRevenue: number;
            totalInvoices: number;
            averageInvoiceValue: number;
            revenueByMonth: {
                month: string;
                revenue: number;
            }[];
        };
        message: string;
    }>;
    getDepartmentMetrics(period: string | undefined, req: any): Promise<{
        success: boolean;
        data: {
            department: string;
            revenue: number;
            expenses: number;
            profit: number;
            efficiency: number;
            trend: string;
        }[];
        message: string;
    }>;
    getGrowthMetrics(period: string | undefined, req: any): Promise<{
        success: boolean;
        data: {
            revenueGrowthTrend: number;
            customerGrowthTrend: number;
            profitGrowthTrend: number;
            monthlyGrowthRates: {
                month: string;
                growth: number;
            }[];
        };
        message: string;
    }>;
}
