"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerReportController = void 0;
const common_1 = require("@nestjs/common");
const customer_report_service_1 = require("../services/customer-report.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerReportController = class CustomerReportController {
    customerReportService;
    constructor(customerReportService) {
        this.customerReportService = customerReportService;
    }
    async getCustomerSummaryReport() {
        return this.customerReportService.generateCustomerSummaryReport();
    }
    async getCustomerSegmentReport() {
        return this.customerReportService.generateCustomerSegmentReport();
    }
    async getInteractionReport(startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();
        return this.customerReportService.generateInteractionReport(start, end);
    }
    async getCustomerLifecycleReport() {
        return this.customerReportService.generateCustomerLifecycleReport();
    }
    async getTopCustomersReport(limit) {
        const limitNum = limit ? parseInt(limit) : 20;
        return this.customerReportService.generateTopCustomersReport(limitNum);
    }
    async getCustomerHealthReport() {
        return this.customerReportService.generateCustomerHealthReport();
    }
    async exportCustomerData(format) {
        return this.customerReportService.exportCustomerData(format || 'csv');
    }
};
exports.CustomerReportController = CustomerReportController;
__decorate([
    (0, common_1.Get)('summary'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "getCustomerSummaryReport", null);
__decorate([
    (0, common_1.Get)('segments'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "getCustomerSegmentReport", null);
__decorate([
    (0, common_1.Get)('interactions'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "getInteractionReport", null);
__decorate([
    (0, common_1.Get)('lifecycle'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "getCustomerLifecycleReport", null);
__decorate([
    (0, common_1.Get)('top-customers'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "getTopCustomersReport", null);
__decorate([
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "getCustomerHealthReport", null);
__decorate([
    (0, common_1.Get)('export'),
    __param(0, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerReportController.prototype, "exportCustomerData", null);
exports.CustomerReportController = CustomerReportController = __decorate([
    (0, common_1.Controller)('customer-reports'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_report_service_1.CustomerReportService])
], CustomerReportController);
//# sourceMappingURL=customer-report.controller.js.map