import { Company } from '../../company/entities/company.entity';
export declare enum UserRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    MANAGER = "manager",
    EMPLOYEE = "employee",
    USER = "user"
}
export declare class User {
    id: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone: string;
    avatar: string;
    role: UserRole;
    isActive: boolean;
    emailVerified: boolean;
    lastLoginAt: Date;
    preferredLanguage: string;
    createdAt: Date;
    updatedAt: Date;
    companyId: string;
    company: Company;
}
