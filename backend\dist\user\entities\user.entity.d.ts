import { Company } from '../../company/entities/company.entity';
export declare enum UserRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    MANAGER = "manager",
    EMPLOYEE = "employee",
    USER = "user"
}
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    PENDING = "pending"
}
export declare class User {
    id: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone: string;
    avatar: string;
    department: string;
    position: string;
    employeeId: string;
    status: UserStatus;
    role: UserRole;
    roleId: string;
    departmentAccess: string[];
    isActive: boolean;
    emailVerified: boolean;
    lastLoginAt: Date;
    preferredLanguage: string;
    preferences: any;
    createdAt: Date;
    updatedAt: Date;
    companyId: string;
    company: Company;
}
