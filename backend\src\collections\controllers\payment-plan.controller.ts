import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { PaymentPlanService } from '../services/payment-plan.service';
import { PaymentPlan, PaymentPlanStatus } from '../entities/payment-plan.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('payment-plans')
@UseGuards(JwtAuthGuard)
export class PaymentPlanController {
  constructor(private readonly paymentPlanService: PaymentPlanService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createPaymentPlanDto: Partial<PaymentPlan>) {
    return this.paymentPlanService.create(createPaymentPlanDto);
  }

  @Get()
  async findAll(@Query('status') status?: PaymentPlanStatus) {
    if (status) {
      return this.paymentPlanService.findByStatus(status);
    }
    return this.paymentPlanService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.paymentPlanService.getPaymentPlanStatistics();
  }

  @Get('overdue-installments')
  async getOverdueInstallments() {
    return this.paymentPlanService.getOverdueInstallments();
  }

  @Get('upcoming-installments')
  async getUpcomingInstallments(@Query('days') days?: string) {
    const daysAhead = days ? parseInt(days) : 7;
    return this.paymentPlanService.getUpcomingInstallments(daysAhead);
  }

  @Get('customer/:customerId')
  async findByCustomer(@Param('customerId') customerId: string) {
    return this.paymentPlanService.findByCustomer(customerId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.paymentPlanService.findOne(id);
  }

  @Get(':id/summary')
  async getPaymentPlanSummary(@Param('id') id: string) {
    return this.paymentPlanService.getPaymentPlanSummary(id);
  }

  @Post(':id/installments')
  async createInstallments(
    @Param('id') id: string,
    @Body() installmentData: { numberOfInstallments: number },
  ) {
    return this.paymentPlanService.createInstallments(
      id,
      installmentData.numberOfInstallments,
    );
  }

  @Post('installments/:installmentId/payment')
  async recordPayment(
    @Param('installmentId') installmentId: string,
    @Body() paymentData: { amount: number; paymentDate: Date },
  ) {
    return this.paymentPlanService.recordPayment(
      installmentId,
      paymentData.amount,
      new Date(paymentData.paymentDate),
    );
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updatePaymentPlanDto: Partial<PaymentPlan>,
  ) {
    return this.paymentPlanService.update(id, updatePaymentPlanDto);
  }

  @Patch(':id/default')
  async markAsDefaulted(
    @Param('id') id: string,
    @Body() defaultData: { reason: string },
  ) {
    return this.paymentPlanService.markPlanAsDefaulted(id, defaultData.reason);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.paymentPlanService.remove(id);
  }
}
