import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Input, Statistic, Row, Col, Progress, Tag, Space, Typography, List } from 'antd'
import {
  salesForecaster,
  customerAnalytics,
  anomalyDetector,
  nlQueryProcessor,
  type PredictionResult
} from '../../lib/ai-utils'
import {
  RiseOutlined,
  FallOutlined,
  TeamOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  WarningOutlined,
  BulbOutlined,
  <PERSON>boltOutlined,
  AimOutlined,
  Bar<PERSON>hartOutlined,
  PieChartOutlined,
  ActivityOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

interface DashboardData {
  revenue: {
    current: number
    previous: number
    forecast: PredictionResult
  }
  customers: {
    total: number
    new: number
    churnRisk: number
  }
  sales: {
    total: number
    growth: number
    topProducts: Array<{ name: string; sales: number }>
  }
  inventory: {
    lowStock: number
    outOfStock: number
    reorderAlerts: number
  }
  anomalies: Array<{
    type: string
    description: string
    severity: 'low' | 'medium' | 'high'
  }>
}

export function IntelligentDashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [aiInsights, setAiInsights] = useState<string[]>([])
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<string[]>([])

  useEffect(() => {
    loadDashboardData()
    generateAIInsights()
  }, [])

  const loadDashboardData = async () => {
    setLoading(true)

    // Simulate API call with realistic data
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock data - in real app, this would come from your API
    const mockData: DashboardData = {
      revenue: {
        current: 125000,
        previous: 118000,
        forecast: {
          value: 135000,
          confidence: 0.85,
          trend: 'up',
          factors: ['Seasonal trends', 'Marketing campaigns', 'Product launches']
        }
      },
      customers: {
        total: 1250,
        new: 45,
        churnRisk: 23
      },
      sales: {
        total: 89,
        growth: 12.5,
        topProducts: [
          { name: 'Product A', sales: 25000 },
          { name: 'Product B', sales: 18000 },
          { name: 'Product C', sales: 15000 }
        ]
      },
      inventory: {
        lowStock: 12,
        outOfStock: 3,
        reorderAlerts: 8
      },
      anomalies: [
        {
          type: 'expense',
          description: 'Unusual spike in office supplies expense',
          severity: 'medium'
        },
        {
          type: 'sales',
          description: 'Unexpected drop in Product D sales',
          severity: 'high'
        }
      ]
    }

    setData(mockData)
    setLoading(false)
  }

  const generateAIInsights = () => {
    const insights = [
      "📈 Revenue is trending upward with 85% confidence for next month",
      "⚠️ 23 customers are at high risk of churning - consider retention campaigns",
      "🎯 Product A is your top performer - consider expanding this line",
      "📦 8 products need reordering to avoid stockouts",
      "💡 Office supplies expense anomaly detected - review recent purchases"
    ]
    setAiInsights(insights)
  }

  const handleQueryChange = (value: string) => {
    setQuery(value)
    if (value.length > 2) {
      const suggestions = nlQueryProcessor.generateSuggestions(value)
      setSuggestions(suggestions.slice(0, 5))
    } else {
      setSuggestions([])
    }
  }

  const handleQuerySubmit = () => {
    const parsed = nlQueryProcessor.parseQuery(query)
    if (parsed) {
      console.log('Executing query:', parsed)
      // Handle the parsed query
    }
    setQuery('')
    setSuggestions([])
  }

  if (loading || !data) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* AI Query Interface */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 border-blue-200 dark:border-blue-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-600" />
            AI Assistant
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <input
              type="text"
              placeholder="Ask me anything about your business... (e.g., 'Show me top 10 customers')"
              value={query}
              onChange={(e) => handleQueryChange(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleQuerySubmit()}
              className="w-full p-3 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Button
              onClick={handleQuerySubmit}
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <Zap className="h-4 w-4" />
            </Button>
          </div>

          {suggestions.length > 0 && (
            <div className="mt-2 space-y-1">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setQuery(suggestion)}
                  className="block w-full text-left p-2 text-sm hover:bg-blue-100 dark:hover:bg-blue-900 rounded"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Revenue"
          value={`$${data.revenue.current.toLocaleString()}`}
          change={((data.revenue.current - data.revenue.previous) / data.revenue.previous * 100)}
          changeLabel="vs last month"
          trend={data.revenue.current > data.revenue.previous ? 'up' : 'down'}
          icon={<DollarSign className="h-8 w-8" />}
          hover
        />

        <StatsCard
          title="Total Customers"
          value={data.customers.total.toLocaleString()}
          change={data.customers.new}
          changeLabel="new this month"
          trend="up"
          icon={<Users className="h-8 w-8" />}
          hover
        />

        <StatsCard
          title="Sales Orders"
          value={data.sales.total}
          change={data.sales.growth}
          changeLabel="growth rate"
          trend="up"
          icon={<ShoppingCart className="h-8 w-8" />}
          hover
        />

        <StatsCard
          title="Inventory Alerts"
          value={data.inventory.lowStock + data.inventory.outOfStock}
          change={data.inventory.reorderAlerts}
          changeLabel="need attention"
          trend="down"
          icon={<AlertTriangle className="h-8 w-8" />}
          hover
        />
      </div>

      {/* AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-green-600" />
            AI-Powered Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <AnimatePresence>
              {aiInsights.map((insight, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <p className="text-sm">{insight}</p>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Revenue Forecast */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Revenue Forecast
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Next Month Prediction</span>
                <span className="text-2xl font-bold text-green-600">
                  ${data.revenue.forecast.value.toLocaleString()}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <div className="flex-1 bg-muted rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${data.revenue.forecast.confidence * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm text-muted-foreground">
                  {Math.round(data.revenue.forecast.confidence * 100)}% confidence
                </span>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Key Factors:</p>
                {data.revenue.forecast.factors.map((factor, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                    {factor}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Anomaly Detection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-orange-600" />
              Anomaly Detection
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.anomalies.map((anomaly, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border-l-4 ${
                    anomaly.severity === 'high'
                      ? 'border-red-500 bg-red-50 dark:bg-red-950'
                      : anomaly.severity === 'medium'
                      ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950'
                      : 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium text-sm capitalize">{anomaly.type} Anomaly</p>
                      <p className="text-sm text-muted-foreground mt-1">{anomaly.description}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      anomaly.severity === 'high'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        : anomaly.severity === 'medium'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    }`}>
                      {anomaly.severity}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <BarChart3 className="h-6 w-6" />
              <span className="text-sm">Generate Report</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <PieChart className="h-6 w-6" />
              <span className="text-sm">View Analytics</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <Users className="h-6 w-6" />
              <span className="text-sm">Customer Insights</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <Target className="h-6 w-6" />
              <span className="text-sm">Set Goals</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
