import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Input, Statistic, Row, Col, Progress, Tag, Space, Typography, List, Spin } from 'antd'
import {
  RiseOutlined,
  FallOutlined,
  TeamOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Dash<PERSON>Outlined,
  <PERSON>Outlined,
  SendOutlined,
  TrophyOutlined,
  AlertOutlined,
  LineChartOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

// Simple AI utilities for the component
const simpleNLProcessor = {
  generateSuggestions: (query: string): string[] => {
    const suggestions = [
      'Show me top 10 customers',
      'Sales this month',
      'Inventory low stock',
      'Revenue for January',
      'Best selling products',
      'Overdue invoices',
      'Employee performance',
      'Profit margin analysis'
    ]

    if (!query) return suggestions

    return suggestions.filter(s =>
      s.toLowerCase().includes(query.toLowerCase())
    )
  },

  parseQuery: (query: string) => {
    // Simple pattern matching
    if (query.toLowerCase().includes('top') && query.toLowerCase().includes('customer')) {
      return { type: 'top_customers', params: { limit: 10 } }
    }
    if (query.toLowerCase().includes('sales')) {
      return { type: 'sales_data', params: { period: 'current' } }
    }
    return null
  }
}

interface PredictionResult {
  value: number
  confidence: number
  trend: 'up' | 'down' | 'stable'
  factors: string[]
}

interface DashboardData {
  revenue: {
    current: number
    previous: number
    forecast: PredictionResult
  }
  customers: {
    total: number
    new: number
    churnRisk: number
  }
  sales: {
    total: number
    growth: number
    topProducts: Array<{ name: string; sales: number }>
  }
  inventory: {
    lowStock: number
    outOfStock: number
    reorderAlerts: number
  }
  anomalies: Array<{
    type: string
    description: string
    severity: 'low' | 'medium' | 'high'
  }>
}

export function IntelligentDashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [aiInsights, setAiInsights] = useState<string[]>([])
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<string[]>([])

  useEffect(() => {
    loadDashboardData()
    generateAIInsights()
  }, [])

  const loadDashboardData = async () => {
    setLoading(true)

    try {
      // Try to fetch from our backend API
      const response = await fetch('http://localhost:3000/api/analytics/ai-insights')

      if (response.ok) {
        const apiData = await response.json()
        setData(apiData.dashboardData)
        setAiInsights(apiData.insights)
        console.log('✅ Successfully loaded AI insights from backend')
      } else {
        throw new Error('Backend not available')
      }
    } catch (error) {
      console.log('⚠️ Backend not available, using mock data')

      // Fallback to mock data
      const mockData: DashboardData = {
        revenue: {
          current: 125000,
          previous: 118000,
          forecast: {
            value: 135000,
            confidence: 0.85,
            trend: 'up',
            factors: ['Seasonal trends', 'Marketing campaigns', 'Product launches']
          }
        },
        customers: {
          total: 1250,
          new: 45,
          churnRisk: 23
        },
        sales: {
          total: 89,
          growth: 12.5,
          topProducts: [
            { name: 'Product A', sales: 25000 },
            { name: 'Product B', sales: 18000 },
            { name: 'Product C', sales: 15000 }
          ]
        },
        inventory: {
          lowStock: 12,
          outOfStock: 3,
          reorderAlerts: 8
        },
        anomalies: [
          {
            type: 'expense',
            description: 'Unusual spike in office supplies expense',
            severity: 'medium'
          },
          {
            type: 'sales',
            description: 'Unexpected drop in Product D sales',
            severity: 'high'
          }
        ]
      }

      setData(mockData)
      generateAIInsights() // Use local insights if backend fails
    }

    setLoading(false)
  }

  const generateAIInsights = () => {
    const insights = [
      "📈 Revenue is trending upward with 85% confidence for next month",
      "⚠️ 23 customers are at high risk of churning - consider retention campaigns",
      "🎯 Product A is your top performer - consider expanding this line",
      "📦 8 products need reordering to avoid stockouts",
      "💡 Office supplies expense anomaly detected - review recent purchases"
    ]
    setAiInsights(insights)
  }

  const handleQueryChange = (value: string) => {
    setQuery(value)
    if (value.length > 2) {
      const suggestions = simpleNLProcessor.generateSuggestions(value)
      setSuggestions(suggestions.slice(0, 5))
    } else {
      setSuggestions([])
    }
  }

  const handleQuerySubmit = () => {
    const parsed = simpleNLProcessor.parseQuery(query)
    if (parsed) {
      console.log('Executing query:', parsed)
      // Handle the parsed query - you can add actual functionality here
      generateAIInsights() // Refresh insights as an example
    }
    setQuery('')
    setSuggestions([])
  }

  if (loading || !data) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>Loading AI Insights...</Text>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* AI Query Interface */}
        <Card
          title={
            <span>
              <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
              AI Assistant
            </span>
          }
          style={{
            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',
            border: '1px solid #bfdbfe'
          }}
        >
          <div style={{ position: 'relative' }}>
            <Input
              placeholder="Ask me anything about your business... (e.g., 'Show me top 10 customers')"
              value={query}
              onChange={(e) => handleQueryChange(e.target.value)}
              onPressEnter={handleQuerySubmit}
              suffix={
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleQuerySubmit}
                  size="small"
                />
              }
              size="large"
            />
          </div>

          {suggestions.length > 0 && (
            <div style={{ marginTop: '12px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    type="text"
                    onClick={() => setQuery(suggestion)}
                    style={{
                      textAlign: 'left',
                      width: '100%',
                      height: 'auto',
                      padding: '8px 12px'
                    }}
                  >
                    {suggestion}
                  </Button>
                ))}
              </Space>
            </div>
          )}
        </Card>

        {/* Key Metrics */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Revenue"
                value={data.revenue.current}
                prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
                suffix="USD"
                precision={0}
                valueStyle={{ color: '#52c41a' }}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                <RiseOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                {((data.revenue.current - data.revenue.previous) / data.revenue.previous * 100).toFixed(1)}% vs last month
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Customers"
                value={data.customers.total}
                prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                <RiseOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                {data.customers.new} new this month
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Sales Orders"
                value={data.sales.total}
                prefix={<ShoppingCartOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                <RiseOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                {data.sales.growth}% growth rate
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Inventory Alerts"
                value={data.inventory.lowStock + data.inventory.outOfStock}
                prefix={<WarningOutlined style={{ color: '#fa8c16' }} />}
                valueStyle={{ color: '#fa8c16' }}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                <AlertOutlined style={{ color: '#fa8c16', marginRight: '4px' }} />
                {data.inventory.reorderAlerts} need attention
              </div>
            </Card>
          </Col>
        </Row>

        {/* AI Insights */}
        <Card
          title={
            <span>
              <AimOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
              AI-Powered Insights
            </span>
          }
        >
          <List
            dataSource={aiInsights}
            renderItem={(insight, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <List.Item style={{ border: 'none', padding: '12px 0' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '12px',
                    padding: '12px',
                    backgroundColor: '#f6ffed',
                    borderRadius: '8px',
                    border: '1px solid #b7eb8f',
                    width: '100%'
                  }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      backgroundColor: '#52c41a',
                      borderRadius: '50%',
                      marginTop: '6px',
                      flexShrink: 0
                    }}></div>
                    <Text style={{ fontSize: '14px', lineHeight: '1.5' }}>{insight}</Text>
                  </div>
                </List.Item>
              </motion.div>
            )}
          />
        </Card>

        {/* Revenue Forecast and Anomaly Detection */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card
              title={
                <span>
                  <LineChartOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  Revenue Forecast
                </span>
              }
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text type="secondary">Next Month Prediction</Text>
                  <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
                    ${data.revenue.forecast.value.toLocaleString()}
                  </Title>
                </div>

                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <Text type="secondary">Confidence Level</Text>
                    <Text>{Math.round(data.revenue.forecast.confidence * 100)}%</Text>
                  </div>
                  <Progress
                    percent={Math.round(data.revenue.forecast.confidence * 100)}
                    strokeColor="#52c41a"
                    showInfo={false}
                  />
                </div>

                <div>
                  <Text strong style={{ fontSize: '14px' }}>Key Factors:</Text>
                  <List
                    size="small"
                    dataSource={data.revenue.forecast.factors}
                    renderItem={(factor) => (
                      <List.Item style={{ padding: '4px 0', border: 'none' }}>
                        <Text style={{ fontSize: '13px' }}>• {factor}</Text>
                      </List.Item>
                    )}
                  />
                </div>
              </Space>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              title={
                <span>
                  <WarningOutlined style={{ marginRight: '8px', color: '#fa8c16' }} />
                  Anomaly Detection
                </span>
              }
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {data.anomalies.map((anomaly, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      borderLeft: `4px solid ${
                        anomaly.severity === 'high' ? '#ff4d4f' :
                        anomaly.severity === 'medium' ? '#fa8c16' : '#1890ff'
                      }`,
                      backgroundColor:
                        anomaly.severity === 'high' ? '#fff2f0' :
                        anomaly.severity === 'medium' ? '#fff7e6' : '#f0f9ff'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div style={{ flex: 1 }}>
                        <Text strong style={{ fontSize: '14px', textTransform: 'capitalize' }}>
                          {anomaly.type} Anomaly
                        </Text>
                        <div style={{ marginTop: '4px' }}>
                          <Text type="secondary" style={{ fontSize: '13px' }}>
                            {anomaly.description}
                          </Text>
                        </div>
                      </div>
                      <Tag
                        color={
                          anomaly.severity === 'high' ? 'red' :
                          anomaly.severity === 'medium' ? 'orange' : 'blue'
                        }
                        style={{ marginLeft: '8px' }}
                      >
                        {anomaly.severity.toUpperCase()}
                      </Tag>
                    </div>
                  </div>
                ))}
              </Space>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        <Card title="Quick Actions">
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={6}>
              <Button
                type="default"
                style={{
                  height: 'auto',
                  padding: '16px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%'
                }}
              >
                <BarChartOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                <span style={{ fontSize: '12px' }}>Generate Report</span>
              </Button>
            </Col>
            <Col xs={12} sm={6}>
              <Button
                type="default"
                style={{
                  height: 'auto',
                  padding: '16px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%'
                }}
              >
                <PieChartOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                <span style={{ fontSize: '12px' }}>View Analytics</span>
              </Button>
            </Col>
            <Col xs={12} sm={6}>
              <Button
                type="default"
                style={{
                  height: 'auto',
                  padding: '16px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%'
                }}
              >
                <TeamOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                <span style={{ fontSize: '12px' }}>Customer Insights</span>
              </Button>
            </Col>
            <Col xs={12} sm={6}>
              <Button
                type="default"
                style={{
                  height: 'auto',
                  padding: '16px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%'
                }}
              >
                <TrophyOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                <span style={{ fontSize: '12px' }}>Set Goals</span>
              </Button>
            </Col>
          </Row>
        </Card>
      </Space>
    </div>
  )
}
