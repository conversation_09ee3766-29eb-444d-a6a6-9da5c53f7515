export declare enum AnnouncementType {
    GENERAL = "general",
    FEATURE_RELEASE = "feature_release",
    MAINTENANCE = "maintenance",
    SECURITY = "security",
    PROMOTION = "promotion",
    TRAINING = "training",
    POLICY = "policy",
    URGENT = "urgent"
}
export declare enum AnnouncementPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export declare enum AnnouncementStatus {
    DRAFT = "draft",
    SCHEDULED = "scheduled",
    PUBLISHED = "published",
    EXPIRED = "expired",
    ARCHIVED = "archived"
}
export declare class Announcement {
    id: string;
    title: string;
    content: string;
    summary: string;
    type: AnnouncementType;
    priority: AnnouncementPriority;
    status: AnnouncementStatus;
    publishAt: Date;
    expiresAt: Date;
    targetAudience: string[];
    targetRoles: string[];
    targetDepartments: string[];
    isPinned: boolean;
    requiresAcknowledgment: boolean;
    sendEmail: boolean;
    sendPush: boolean;
    bannerImage: string;
    actionButtonText: string;
    actionButtonUrl: string;
    createdBy: string;
    viewCount: number;
    acknowledgmentCount: number;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
