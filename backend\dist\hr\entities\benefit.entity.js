"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Benefit = exports.BenefitCategory = exports.BenefitType = void 0;
const typeorm_1 = require("typeorm");
const employee_benefit_entity_1 = require("./employee-benefit.entity");
var BenefitType;
(function (BenefitType) {
    BenefitType["HEALTH_INSURANCE"] = "health_insurance";
    BenefitType["DENTAL_INSURANCE"] = "dental_insurance";
    BenefitType["VISION_INSURANCE"] = "vision_insurance";
    BenefitType["LIFE_INSURANCE"] = "life_insurance";
    BenefitType["DISABILITY_INSURANCE"] = "disability_insurance";
    BenefitType["RETIREMENT_PLAN"] = "retirement_plan";
    BenefitType["PAID_TIME_OFF"] = "paid_time_off";
    BenefitType["SICK_LEAVE"] = "sick_leave";
    BenefitType["MATERNITY_LEAVE"] = "maternity_leave";
    BenefitType["PATERNITY_LEAVE"] = "paternity_leave";
    BenefitType["FLEXIBLE_SPENDING"] = "flexible_spending";
    BenefitType["COMMUTER_BENEFITS"] = "commuter_benefits";
    BenefitType["GYM_MEMBERSHIP"] = "gym_membership";
    BenefitType["EDUCATION_ASSISTANCE"] = "education_assistance";
    BenefitType["EMPLOYEE_DISCOUNT"] = "employee_discount";
    BenefitType["OTHER"] = "other";
})(BenefitType || (exports.BenefitType = BenefitType = {}));
var BenefitCategory;
(function (BenefitCategory) {
    BenefitCategory["INSURANCE"] = "insurance";
    BenefitCategory["RETIREMENT"] = "retirement";
    BenefitCategory["TIME_OFF"] = "time_off";
    BenefitCategory["WELLNESS"] = "wellness";
    BenefitCategory["FINANCIAL"] = "financial";
    BenefitCategory["LIFESTYLE"] = "lifestyle";
    BenefitCategory["PROFESSIONAL_DEVELOPMENT"] = "professional_development";
})(BenefitCategory || (exports.BenefitCategory = BenefitCategory = {}));
let Benefit = class Benefit {
    id;
    name;
    code;
    description;
    type;
    category;
    employerCost;
    employeeCost;
    employerContributionPercentage;
    employeeContributionPercentage;
    currency;
    isActive;
    isMandatory;
    isElective;
    waitingPeriodDays;
    eligibilityCriteria;
    coverageDetails;
    provider;
    providerContact;
    effectiveDate;
    expiryDate;
    employeeBenefits;
    metadata;
    createdAt;
    updatedAt;
};
exports.Benefit = Benefit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Benefit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], Benefit.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], Benefit.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Benefit.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BenefitType,
    }),
    __metadata("design:type", String)
], Benefit.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: BenefitCategory,
    }),
    __metadata("design:type", String)
], Benefit.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Benefit.prototype, "employerCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Benefit.prototype, "employeeCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Benefit.prototype, "employerContributionPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Benefit.prototype, "employeeContributionPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], Benefit.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Benefit.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Benefit.prototype, "isMandatory", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Benefit.prototype, "isElective", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], Benefit.prototype, "waitingPeriodDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Benefit.prototype, "eligibilityCriteria", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Benefit.prototype, "coverageDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Benefit.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], Benefit.prototype, "providerContact", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Benefit.prototype, "effectiveDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Benefit.prototype, "expiryDate", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => employee_benefit_entity_1.EmployeeBenefit, employeeBenefit => employeeBenefit.benefit),
    __metadata("design:type", Array)
], Benefit.prototype, "employeeBenefits", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Benefit.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Benefit.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Benefit.prototype, "updatedAt", void 0);
exports.Benefit = Benefit = __decorate([
    (0, typeorm_1.Entity)('hr_benefits')
], Benefit);
//# sourceMappingURL=benefit.entity.js.map