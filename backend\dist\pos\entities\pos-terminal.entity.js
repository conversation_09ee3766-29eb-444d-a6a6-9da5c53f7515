"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosTerminal = exports.TerminalType = exports.TerminalStatus = void 0;
const typeorm_1 = require("typeorm");
const pos_sale_entity_1 = require("./pos-sale.entity");
const pos_shift_entity_1 = require("./pos-shift.entity");
var TerminalStatus;
(function (TerminalStatus) {
    TerminalStatus["ACTIVE"] = "active";
    TerminalStatus["INACTIVE"] = "inactive";
    TerminalStatus["MAINTENANCE"] = "maintenance";
    TerminalStatus["OFFLINE"] = "offline";
})(TerminalStatus || (exports.TerminalStatus = TerminalStatus = {}));
var TerminalType;
(function (TerminalType) {
    TerminalType["STANDARD"] = "standard";
    TerminalType["MOBILE"] = "mobile";
    TerminalType["KIOSK"] = "kiosk";
    TerminalType["TABLET"] = "tablet";
    TerminalType["HANDHELD"] = "handheld";
})(TerminalType || (exports.TerminalType = TerminalType = {}));
let PosTerminal = class PosTerminal {
    id;
    terminalNumber;
    name;
    description;
    type;
    status;
    location;
    warehouseId;
    ipAddress;
    macAddress;
    deviceModel;
    serialNumber;
    printerSettings;
    cashDrawerSettings;
    scannerSettings;
    displaySettings;
    allowCashPayments;
    allowCardPayments;
    allowMobilePayments;
    allowGiftCards;
    allowLayaway;
    allowReturns;
    allowDiscounts;
    maxDiscountAmount;
    maxDiscountPercentage;
    lastHeartbeat;
    configuration;
    sales;
    shifts;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosTerminal = PosTerminal;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosTerminal.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "terminalNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PosTerminal.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TerminalType,
        default: TerminalType.STANDARD,
    }),
    __metadata("design:type", String)
], PosTerminal.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TerminalStatus,
        default: TerminalStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], PosTerminal.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "warehouseId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "macAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "deviceModel", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], PosTerminal.prototype, "serialNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTerminal.prototype, "printerSettings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTerminal.prototype, "cashDrawerSettings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTerminal.prototype, "scannerSettings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTerminal.prototype, "displaySettings", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowCashPayments", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowCardPayments", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowMobilePayments", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowGiftCards", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowLayaway", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowReturns", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosTerminal.prototype, "allowDiscounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosTerminal.prototype, "maxDiscountAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], PosTerminal.prototype, "maxDiscountPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosTerminal.prototype, "lastHeartbeat", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTerminal.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_sale_entity_1.PosSale, sale => sale.terminal),
    __metadata("design:type", Array)
], PosTerminal.prototype, "sales", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pos_shift_entity_1.PosShift, shift => shift.terminal),
    __metadata("design:type", Array)
], PosTerminal.prototype, "shifts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosTerminal.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosTerminal.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosTerminal.prototype, "updatedAt", void 0);
exports.PosTerminal = PosTerminal = __decorate([
    (0, typeorm_1.Entity)('pos_terminals')
], PosTerminal);
//# sourceMappingURL=pos-terminal.entity.js.map