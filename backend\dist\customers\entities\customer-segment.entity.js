"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerSegment = exports.SegmentType = void 0;
const typeorm_1 = require("typeorm");
var SegmentType;
(function (SegmentType) {
    SegmentType["DEMOGRAPHIC"] = "demographic";
    SegmentType["BEHAVIORAL"] = "behavioral";
    SegmentType["GEOGRAPHIC"] = "geographic";
    SegmentType["PSYCHOGRAPHIC"] = "psychographic";
    SegmentType["TRANSACTIONAL"] = "transactional";
    SegmentType["CUSTOM"] = "custom";
})(SegmentType || (exports.SegmentType = SegmentType = {}));
let CustomerSegment = class CustomerSegment {
    id;
    name;
    description;
    type;
    criteria;
    customerCount;
    isActive;
    isAutoUpdate;
    lastUpdated;
    createdBy;
    tags;
    metadata;
    createdAt;
    updatedAt;
};
exports.CustomerSegment = CustomerSegment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CustomerSegment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CustomerSegment.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomerSegment.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SegmentType,
        default: SegmentType.CUSTOM,
    }),
    __metadata("design:type", String)
], CustomerSegment.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], CustomerSegment.prototype, "criteria", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CustomerSegment.prototype, "customerCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CustomerSegment.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CustomerSegment.prototype, "isAutoUpdate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CustomerSegment.prototype, "lastUpdated", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CustomerSegment.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CustomerSegment.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomerSegment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomerSegment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomerSegment.prototype, "updatedAt", void 0);
exports.CustomerSegment = CustomerSegment = __decorate([
    (0, typeorm_1.Entity)('customer_segments')
], CustomerSegment);
//# sourceMappingURL=customer-segment.entity.js.map