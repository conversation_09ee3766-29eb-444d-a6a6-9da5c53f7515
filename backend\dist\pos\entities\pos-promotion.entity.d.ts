export declare enum PromotionType {
    PERCENTAGE_DISCOUNT = "percentage_discount",
    FIXED_DISCOUNT = "fixed_discount",
    BUY_X_GET_Y = "buy_x_get_y",
    FREE_SHIPPING = "free_shipping",
    BUNDLE_DEAL = "bundle_deal",
    LOYALTY_POINTS = "loyalty_points",
    CASHBACK = "cashback"
}
export declare enum PromotionStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SCHEDULED = "scheduled",
    EXPIRED = "expired",
    PAUSED = "paused"
}
export declare class PosPromotion {
    id: string;
    code: string;
    name: string;
    description: string;
    type: PromotionType;
    status: PromotionStatus;
    startDate: Date;
    endDate: Date;
    discountAmount: number;
    discountPercentage: number;
    minimumPurchase: number;
    maximumDiscount: number;
    usageLimit: number;
    usageCount: number;
    usageLimitPerCustomer: number;
    applicableProducts: string[];
    applicableCategories: string[];
    excludedProducts: string[];
    excludedCategories: string[];
    customerSegments: string[];
    conditions: any;
    isStackable: boolean;
    requiresCouponCode: boolean;
    priority: number;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
