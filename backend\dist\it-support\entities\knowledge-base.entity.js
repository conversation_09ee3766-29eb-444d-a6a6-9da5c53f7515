"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBase = exports.ArticleType = exports.ArticleStatus = void 0;
const typeorm_1 = require("typeorm");
var ArticleStatus;
(function (ArticleStatus) {
    ArticleStatus["DRAFT"] = "draft";
    ArticleStatus["PUBLISHED"] = "published";
    ArticleStatus["ARCHIVED"] = "archived";
    ArticleStatus["UNDER_REVIEW"] = "under_review";
})(ArticleStatus || (exports.ArticleStatus = ArticleStatus = {}));
var ArticleType;
(function (ArticleType) {
    ArticleType["HOW_TO"] = "how_to";
    ArticleType["TROUBLESHOOTING"] = "troubleshooting";
    ArticleType["FAQ"] = "faq";
    ArticleType["POLICY"] = "policy";
    ArticleType["PROCEDURE"] = "procedure";
    ArticleType["ANNOUNCEMENT"] = "announcement";
    ArticleType["TUTORIAL"] = "tutorial";
})(ArticleType || (exports.ArticleType = ArticleType = {}));
let KnowledgeBase = class KnowledgeBase {
    id;
    title;
    content;
    summary;
    type;
    status;
    category;
    tags;
    keywords;
    authorId;
    reviewedBy;
    reviewedAt;
    publishedAt;
    viewCount;
    helpfulCount;
    notHelpfulCount;
    rating;
    attachments;
    relatedArticles;
    isFeatured;
    isInternal;
    metadata;
    createdAt;
    updatedAt;
};
exports.KnowledgeBase = KnowledgeBase;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "summary", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ArticleType,
        default: ArticleType.HOW_TO,
    }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ArticleStatus,
        default: ArticleStatus.DRAFT,
    }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], KnowledgeBase.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], KnowledgeBase.prototype, "keywords", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "authorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], KnowledgeBase.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], KnowledgeBase.prototype, "reviewedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], KnowledgeBase.prototype, "publishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], KnowledgeBase.prototype, "viewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], KnowledgeBase.prototype, "helpfulCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], KnowledgeBase.prototype, "notHelpfulCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], KnowledgeBase.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], KnowledgeBase.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], KnowledgeBase.prototype, "relatedArticles", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], KnowledgeBase.prototype, "isFeatured", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], KnowledgeBase.prototype, "isInternal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], KnowledgeBase.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], KnowledgeBase.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], KnowledgeBase.prototype, "updatedAt", void 0);
exports.KnowledgeBase = KnowledgeBase = __decorate([
    (0, typeorm_1.Entity)('knowledge_base')
], KnowledgeBase);
//# sourceMappingURL=knowledge-base.entity.js.map