import { DepartmentService } from '../services/department.service';
export declare class DepartmentController {
    private readonly departmentService;
    constructor(departmentService: DepartmentService);
    create(createDepartmentDto: any): Promise<import("../entities/department.entity").Department>;
    findAll(): Promise<import("../entities/department.entity").Department[]>;
    getDepartmentHierarchy(): Promise<import("../entities/department.entity").Department[]>;
    findOne(id: string): Promise<import("../entities/department.entity").Department>;
    update(id: string, updateDepartmentDto: any): Promise<import("../entities/department.entity").Department>;
    remove(id: string): Promise<void>;
}
