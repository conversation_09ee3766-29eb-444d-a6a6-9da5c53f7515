import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { CollectionCaseService } from '../services/collection-case.service';
import { CollectionCase, CaseStatus } from '../entities/collection-case.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('collection-cases')
@UseGuards(JwtAuthGuard)
export class CollectionCaseController {
  constructor(private readonly collectionCaseService: CollectionCaseService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createCaseDto: Partial<CollectionCase>) {
    return this.collectionCaseService.create(createCaseDto);
  }

  @Get()
  async findAll(@Query('status') status?: CaseStatus) {
    if (status) {
      return this.collectionCaseService.findByStatus(status);
    }
    return this.collectionCaseService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.collectionCaseService.getStatistics();
  }

  @Get('dashboard')
  async getDashboardMetrics() {
    return this.collectionCaseService.getDashboardMetrics();
  }

  @Get('overdue')
  async getOverdueCases() {
    return this.collectionCaseService.getOverdueCases();
  }

  @Get('priority/:priority')
  async getCasesByPriority(@Param('priority') priority: string) {
    return this.collectionCaseService.getCasesByPriority(priority);
  }

  @Get('customer/:customerId')
  async findByCustomer(@Param('customerId') customerId: string) {
    return this.collectionCaseService.findByCustomer(customerId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.collectionCaseService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCaseDto: Partial<CollectionCase>,
  ) {
    return this.collectionCaseService.update(id, updateCaseDto);
  }

  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body() statusUpdate: { status: CaseStatus; notes?: string },
  ) {
    return this.collectionCaseService.updateStatus(
      id,
      statusUpdate.status,
      statusUpdate.notes,
    );
  }

  @Patch(':id/assign')
  async assignAgent(
    @Param('id') id: string,
    @Body() assignment: { agentId: string },
  ) {
    return this.collectionCaseService.assignAgent(id, assignment.agentId);
  }

  @Patch(':id/escalate')
  async escalateCase(
    @Param('id') id: string,
    @Body() escalation: { reason: string },
  ) {
    return this.collectionCaseService.escalateCase(id, escalation.reason);
  }

  @Post(':id/payment')
  async recordPayment(
    @Param('id') id: string,
    @Body() payment: {
      amount: number;
      paymentDate: Date;
      notes?: string;
    },
  ) {
    return this.collectionCaseService.recordPayment(
      id,
      payment.amount,
      new Date(payment.paymentDate),
      payment.notes,
    );
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.collectionCaseService.remove(id);
  }
}
