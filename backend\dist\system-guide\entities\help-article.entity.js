"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HelpArticle = exports.ArticleStatus = exports.ArticleType = void 0;
const typeorm_1 = require("typeorm");
const help_category_entity_1 = require("./help-category.entity");
var ArticleType;
(function (ArticleType) {
    ArticleType["HOW_TO"] = "how_to";
    ArticleType["TROUBLESHOOTING"] = "troubleshooting";
    ArticleType["FAQ"] = "faq";
    ArticleType["FEATURE_EXPLANATION"] = "feature_explanation";
    ArticleType["BEST_PRACTICES"] = "best_practices";
    ArticleType["KNOWN_ISSUES"] = "known_issues";
    ArticleType["RELEASE_NOTES"] = "release_notes";
})(ArticleType || (exports.ArticleType = ArticleType = {}));
var ArticleStatus;
(function (ArticleStatus) {
    ArticleStatus["DRAFT"] = "draft";
    ArticleStatus["PUBLISHED"] = "published";
    ArticleStatus["ARCHIVED"] = "archived";
    ArticleStatus["UNDER_REVIEW"] = "under_review";
})(ArticleStatus || (exports.ArticleStatus = ArticleStatus = {}));
let HelpArticle = class HelpArticle {
    id;
    title;
    content;
    summary;
    type;
    status;
    categoryId;
    category;
    tags;
    keywords;
    authorId;
    reviewedBy;
    reviewedAt;
    publishedAt;
    viewCount;
    helpfulCount;
    notHelpfulCount;
    rating;
    attachments;
    relatedArticles;
    isFeatured;
    isPinned;
    metadata;
    createdAt;
    updatedAt;
};
exports.HelpArticle = HelpArticle;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], HelpArticle.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], HelpArticle.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], HelpArticle.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], HelpArticle.prototype, "summary", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ArticleType,
        default: ArticleType.HOW_TO,
    }),
    __metadata("design:type", String)
], HelpArticle.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ArticleStatus,
        default: ArticleStatus.DRAFT,
    }),
    __metadata("design:type", String)
], HelpArticle.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], HelpArticle.prototype, "categoryId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => help_category_entity_1.HelpCategory, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'categoryId' }),
    __metadata("design:type", help_category_entity_1.HelpCategory)
], HelpArticle.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], HelpArticle.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], HelpArticle.prototype, "keywords", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], HelpArticle.prototype, "authorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], HelpArticle.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], HelpArticle.prototype, "reviewedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], HelpArticle.prototype, "publishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], HelpArticle.prototype, "viewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], HelpArticle.prototype, "helpfulCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], HelpArticle.prototype, "notHelpfulCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], HelpArticle.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], HelpArticle.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], HelpArticle.prototype, "relatedArticles", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], HelpArticle.prototype, "isFeatured", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], HelpArticle.prototype, "isPinned", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], HelpArticle.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], HelpArticle.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], HelpArticle.prototype, "updatedAt", void 0);
exports.HelpArticle = HelpArticle = __decorate([
    (0, typeorm_1.Entity)('help_articles')
], HelpArticle);
//# sourceMappingURL=help-article.entity.js.map