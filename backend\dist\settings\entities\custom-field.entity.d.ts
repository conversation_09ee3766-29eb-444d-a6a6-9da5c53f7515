import { CustomFieldValue } from './custom-field-value.entity';
export declare enum FieldType {
    TEXT = "text",
    TEXTAREA = "textarea",
    NUMBER = "number",
    EMAIL = "email",
    URL = "url",
    PHONE = "phone",
    DATE = "date",
    DATETIME = "datetime",
    BOOLEAN = "boolean",
    SELECT = "select",
    MULTISELECT = "multiselect",
    RADIO = "radio",
    CHECKBOX = "checkbox",
    FILE = "file",
    IMAGE = "image"
}
export declare enum EntityType {
    CUSTOMER = "customer",
    PRODUCT = "product",
    ORDER = "order",
    INVOICE = "invoice",
    PROJECT = "project",
    TASK = "task",
    EMPLOYEE = "employee",
    VENDOR = "vendor",
    ASSET = "asset",
    TICKET = "ticket"
}
export declare class CustomField {
    id: string;
    name: string;
    label: string;
    description: string;
    type: FieldType;
    entityType: EntityType;
    isRequired: boolean;
    isActive: boolean;
    isSearchable: boolean;
    defaultValue: string;
    placeholder: string;
    helpText: string;
    options: any;
    validationRules: any;
    sortOrder: number;
    createdBy: string;
    values: CustomFieldValue[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
