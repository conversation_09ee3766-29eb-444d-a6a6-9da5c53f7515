import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CollectionCase } from './collection-case.entity';

export enum ActivityType {
  CALL = 'call',
  EMAIL = 'email',
  LETTER = 'letter',
  SMS = 'sms',
  VISIT = 'visit',
  PAYMENT = 'payment',
  PROMISE_TO_PAY = 'promise_to_pay',
  DISPUTE = 'dispute',
  LEGAL_ACTION = 'legal_action',
  SETTLEMENT = 'settlement',
  WRITE_OFF = 'write_off',
  NOTE = 'note',
  STATUS_CHANGE = 'status_change',
}

export enum ActivityResult {
  SUCCESSFUL = 'successful',
  NO_ANSWER = 'no_answer',
  BUSY = 'busy',
  DISCONNECTED = 'disconnected',
  WRONG_NUMBER = 'wrong_number',
  LEFT_MESSAGE = 'left_message',
  PROMISED_TO_PAY = 'promised_to_pay',
  DISPUTED = 'disputed',
  REFUSED_TO_PAY = 'refused_to_pay',
  PAYMENT_RECEIVED = 'payment_received',
  CALLBACK_REQUESTED = 'callback_requested',
  OTHER = 'other',
}

@Entity('collection_activities')
export class CollectionActivity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  caseId: string;

  @ManyToOne(() => CollectionCase, collectionCase => collectionCase.activities)
  @JoinColumn({ name: 'caseId' })
  case: CollectionCase;

  @Column({
    type: 'enum',
    enum: ActivityType,
  })
  type: ActivityType;

  @Column({
    type: 'enum',
    enum: ActivityResult,
    nullable: true,
  })
  result: ActivityResult;

  @Column({ type: 'timestamp' })
  activityDate: Date;

  @Column({ type: 'int', nullable: true })
  duration: number; // in minutes

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text', nullable: true })
  outcome: string;

  @Column()
  performedBy: string;

  @Column({ length: 255, nullable: true })
  contactMethod: string;

  @Column({ length: 255, nullable: true })
  contactNumber: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  paymentAmount: number;

  @Column({ type: 'date', nullable: true })
  promiseDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  promiseAmount: number;

  @Column({ type: 'date', nullable: true })
  followUpDate: Date;

  @Column({ type: 'text', nullable: true })
  followUpAction: string;

  @Column({ default: false })
  isAutomated: boolean;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
