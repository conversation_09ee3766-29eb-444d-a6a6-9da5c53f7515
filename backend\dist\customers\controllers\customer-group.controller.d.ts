import { CustomerGroupService } from '../services/customer-group.service';
import { CustomerGroup } from '../entities/customer-group.entity';
export declare class CustomerGroupController {
    private readonly customerGroupService;
    constructor(customerGroupService: CustomerGroupService);
    create(createGroupDto: Partial<CustomerGroup>): Promise<CustomerGroup>;
    findAll(): Promise<CustomerGroup[]>;
    findActive(): Promise<CustomerGroup[]>;
    findAllWithCounts(): Promise<any[]>;
    findOne(id: string): Promise<CustomerGroup>;
    findByCode(code: string): Promise<CustomerGroup>;
    getGroupStatistics(id: string): Promise<any>;
    update(id: string, updateGroupDto: Partial<CustomerGroup>): Promise<CustomerGroup>;
    activate(id: string): Promise<CustomerGroup>;
    deactivate(id: string): Promise<CustomerGroup>;
    addCustomerToGroup(groupId: string, customerId: string): Promise<import("../entities/customer.entity").Customer>;
    removeCustomerFromGroup(customerId: string): Promise<import("../entities/customer.entity").Customer>;
    bulkAssignCustomers(groupId: string, assignmentData: {
        customerIds: string[];
    }): Promise<import("../entities/customer.entity").Customer[]>;
    applyGroupDiscount(groupId: string): Promise<{
        message: string;
    }>;
    remove(id: string): Promise<void>;
}
