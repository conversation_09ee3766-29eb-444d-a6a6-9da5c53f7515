"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerLoyaltyController = void 0;
const common_1 = require("@nestjs/common");
const customer_loyalty_service_1 = require("../services/customer-loyalty.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerLoyaltyController = class CustomerLoyaltyController {
    customerLoyaltyService;
    constructor(customerLoyaltyService) {
        this.customerLoyaltyService = customerLoyaltyService;
    }
    async findAll() {
        return this.customerLoyaltyService.findAll();
    }
    async getLoyaltyStatistics() {
        return this.customerLoyaltyService.getLoyaltyStatistics();
    }
    async getTopLoyaltyCustomers() {
        return this.customerLoyaltyService.getTopLoyaltyCustomers();
    }
    async findByCustomer(customerId) {
        return this.customerLoyaltyService.findByCustomer(customerId);
    }
    async getCustomerLoyaltySummary(customerId) {
        return this.customerLoyaltyService.getCustomerLoyaltySummary(customerId);
    }
    async findOne(id) {
        return this.customerLoyaltyService.findOne(id);
    }
    async addPoints(pointsData) {
        return this.customerLoyaltyService.addPoints(pointsData.customerId, pointsData.points, pointsData.reason, pointsData.orderId);
    }
    async redeemPoints(redeemData) {
        return this.customerLoyaltyService.redeemPoints(redeemData.customerId, redeemData.points, redeemData.reason, redeemData.orderId);
    }
    async adjustPoints(adjustData) {
        return this.customerLoyaltyService.adjustPoints(adjustData.customerId, adjustData.adjustment, adjustData.reason);
    }
    async expirePoints(expireData) {
        return this.customerLoyaltyService.expirePoints(expireData.customerId, expireData.points);
    }
};
exports.CustomerLoyaltyController = CustomerLoyaltyController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "getLoyaltyStatistics", null);
__decorate([
    (0, common_1.Get)('top-customers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "getTopLoyaltyCustomers", null);
__decorate([
    (0, common_1.Get)('customer/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "findByCustomer", null);
__decorate([
    (0, common_1.Get)('customer/:customerId/summary'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "getCustomerLoyaltySummary", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('add-points'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "addPoints", null);
__decorate([
    (0, common_1.Post)('redeem-points'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "redeemPoints", null);
__decorate([
    (0, common_1.Post)('adjust-points'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "adjustPoints", null);
__decorate([
    (0, common_1.Post)('expire-points'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerLoyaltyController.prototype, "expirePoints", null);
exports.CustomerLoyaltyController = CustomerLoyaltyController = __decorate([
    (0, common_1.Controller)('customer-loyalty'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_loyalty_service_1.CustomerLoyaltyService])
], CustomerLoyaltyController);
//# sourceMappingURL=customer-loyalty.controller.js.map