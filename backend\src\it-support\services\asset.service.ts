import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Asset, AssetStatus } from '../entities/asset.entity';
import { AssetAssignment } from '../entities/asset-assignment.entity';

@Injectable()
export class AssetService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(AssetAssignment)
    private assetAssignmentRepository: Repository<AssetAssignment>,
  ) {}

  async create(assetData: Partial<Asset>): Promise<Asset> {
    const assetTag = await this.generateAssetTag();
    const asset = this.assetRepository.create({
      ...assetData,
      assetTag,
      status: AssetStatus.AVAILABLE,
      createdAt: new Date(),
    });
    return this.assetRepository.save(asset);
  }

  async findAll(): Promise<Asset[]> {
    return this.assetRepository.find({
      relations: ['currentAssignment', 'currentAssignment.assignedTo'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({
      where: { id },
      relations: ['currentAssignment', 'currentAssignment.assignedTo', 'assignments'],
    });

    if (!asset) {
      throw new NotFoundException(`Asset with ID ${id} not found`);
    }

    return asset;
  }

  async update(id: string, updateData: Partial<Asset>): Promise<Asset> {
    await this.assetRepository.update(id, {
      ...updateData,
      updatedAt: new Date(),
    });
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const asset = await this.findOne(id);
    
    // Check if asset is currently assigned
    if (asset.currentAssignment) {
      throw new Error('Cannot delete asset that is currently assigned');
    }

    await this.assetRepository.remove(asset);
  }

  async findByStatus(status: AssetStatus): Promise<Asset[]> {
    return this.assetRepository.find({
      where: { status },
      relations: ['currentAssignment', 'currentAssignment.assignedTo'],
      order: { name: 'ASC' },
    });
  }

  async findByCategory(category: string): Promise<Asset[]> {
    return this.assetRepository.find({
      where: { category },
      relations: ['currentAssignment', 'currentAssignment.assignedTo'],
      order: { name: 'ASC' },
    });
  }

  async findByLocation(location: string): Promise<Asset[]> {
    return this.assetRepository.find({
      where: { location },
      relations: ['currentAssignment', 'currentAssignment.assignedTo'],
      order: { name: 'ASC' },
    });
  }

  async findByAssetTag(assetTag: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({
      where: { assetTag },
      relations: ['currentAssignment', 'currentAssignment.assignedTo'],
    });

    if (!asset) {
      throw new NotFoundException(`Asset with tag ${assetTag} not found`);
    }

    return asset;
  }

  async assignAsset(assetId: string, assignedToId: string, assignedById: string, notes?: string): Promise<AssetAssignment> {
    const asset = await this.findOne(assetId);

    // Check if asset is available
    if (asset.status !== AssetStatus.AVAILABLE) {
      throw new Error('Asset is not available for assignment');
    }

    // End current assignment if exists
    if (asset.currentAssignment) {
      await this.unassignAsset(assetId, assignedById, 'Reassigned to new user');
    }

    // Create new assignment
    const assignment = this.assetAssignmentRepository.create({
      assetId,
      assignedToId,
      assignedById,
      assignedAt: new Date(),
      notes,
    });

    const savedAssignment = await this.assetAssignmentRepository.save(assignment);

    // Update asset status
    await this.assetRepository.update(assetId, {
      status: AssetStatus.ASSIGNED,
      currentAssignmentId: savedAssignment.id,
    });

    return savedAssignment;
  }

  async unassignAsset(assetId: string, unassignedById: string, notes?: string): Promise<void> {
    const asset = await this.findOne(assetId);

    if (!asset.currentAssignment) {
      throw new Error('Asset is not currently assigned');
    }

    // End current assignment
    await this.assetAssignmentRepository.update(asset.currentAssignment.id, {
      unassignedAt: new Date(),
      unassignedById,
      unassignmentNotes: notes,
    });

    // Update asset status
    await this.assetRepository.update(assetId, {
      status: AssetStatus.AVAILABLE,
      currentAssignmentId: null,
    });
  }

  async getAssetHistory(assetId: string): Promise<AssetAssignment[]> {
    return this.assetAssignmentRepository.find({
      where: { assetId },
      relations: ['assignedTo', 'assignedBy', 'unassignedBy'],
      order: { assignedAt: 'DESC' },
    });
  }

  async getUserAssets(userId: string): Promise<Asset[]> {
    const assignments = await this.assetAssignmentRepository.find({
      where: { assignedToId: userId, unassignedAt: null },
      relations: ['asset'],
    });

    return assignments.map(assignment => assignment.asset);
  }

  async searchAssets(searchTerm: string): Promise<Asset[]> {
    return this.assetRepository
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.currentAssignment', 'assignment')
      .leftJoinAndSelect('assignment.assignedTo', 'user')
      .where('asset.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('asset.assetTag ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('asset.serialNumber ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('asset.model ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('asset.name', 'ASC')
      .getMany();
  }

  async getAssetStatistics(): Promise<any> {
    const totalAssets = await this.assetRepository.count();
    const availableAssets = await this.assetRepository.count({ where: { status: AssetStatus.AVAILABLE } });
    const assignedAssets = await this.assetRepository.count({ where: { status: AssetStatus.ASSIGNED } });
    const maintenanceAssets = await this.assetRepository.count({ where: { status: AssetStatus.MAINTENANCE } });
    const retiredAssets = await this.assetRepository.count({ where: { status: AssetStatus.RETIRED } });

    const totalValue = await this.assetRepository
      .createQueryBuilder('asset')
      .select('SUM(asset.purchasePrice)', 'totalValue')
      .getRawOne();

    return {
      totalAssets,
      availableAssets,
      assignedAssets,
      maintenanceAssets,
      retiredAssets,
      totalValue: parseFloat(totalValue.totalValue) || 0,
      utilizationRate: totalAssets > 0 ? (assignedAssets / totalAssets) * 100 : 0,
    };
  }

  async getAssetsByCategory(): Promise<Array<{ category: string; count: number; value: number }>> {
    const result = await this.assetRepository
      .createQueryBuilder('asset')
      .select([
        'asset.category as category',
        'COUNT(asset.id) as count',
        'SUM(asset.purchasePrice) as value',
      ])
      .groupBy('asset.category')
      .orderBy('count', 'DESC')
      .getRawMany();

    return result.map(row => ({
      category: row.category,
      count: parseInt(row.count),
      value: parseFloat(row.value) || 0,
    }));
  }

  async getAssetsNearingWarrantyExpiry(days: number = 30): Promise<Asset[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.assetRepository
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.currentAssignment', 'assignment')
      .leftJoinAndSelect('assignment.assignedTo', 'user')
      .where('asset.warrantyExpiry <= :futureDate', { futureDate })
      .andWhere('asset.warrantyExpiry > :now', { now: new Date() })
      .orderBy('asset.warrantyExpiry', 'ASC')
      .getMany();
  }

  async updateAssetStatus(assetId: string, status: AssetStatus, notes?: string): Promise<Asset> {
    await this.assetRepository.update(assetId, {
      status,
      ...(notes && { notes }),
      updatedAt: new Date(),
    });

    return this.findOne(assetId);
  }

  async retireAsset(assetId: string, retiredById: string, reason: string): Promise<Asset> {
    const asset = await this.findOne(assetId);

    // Unassign if currently assigned
    if (asset.currentAssignment) {
      await this.unassignAsset(assetId, retiredById, 'Asset retired');
    }

    await this.assetRepository.update(assetId, {
      status: AssetStatus.RETIRED,
      retiredAt: new Date(),
      retirementReason: reason,
      updatedAt: new Date(),
    });

    return this.findOne(assetId);
  }

  private async generateAssetTag(): Promise<string> {
    const count = await this.assetRepository.count();
    const sequence = (count + 1).toString().padStart(6, '0');
    const year = new Date().getFullYear();
    return `AST-${year}-${sequence}`;
  }

  async getDashboardMetrics(): Promise<any> {
    const stats = await this.getAssetStatistics();
    const nearingExpiry = await this.getAssetsNearingWarrantyExpiry();
    
    return {
      ...stats,
      warrantyExpiringCount: nearingExpiry.length,
    };
  }
}
