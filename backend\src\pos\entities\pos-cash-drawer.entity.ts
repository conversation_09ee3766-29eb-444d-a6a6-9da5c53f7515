import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PosShift } from './pos-shift.entity';

export enum CashDrawerActivity {
  OPEN = 'open',
  CLOSE = 'close',
  CASH_IN = 'cash_in',
  CASH_OUT = 'cash_out',
  PAYOUT = 'payout',
  DROP = 'drop',
  COUNT = 'count',
  RECONCILE = 'reconcile',
}

@Entity('pos_cash_drawer')
export class PosCashDrawer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  shiftId: string;

  @ManyToOne(() => PosShift, shift => shift.cashDrawerActivities)
  @JoinColumn({ name: 'shiftId' })
  shift: PosShift;

  @Column({
    type: 'enum',
    enum: CashDrawerActivity,
  })
  activity: CashDrawerActivity;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  balanceBefore: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  balanceAfter: number;

  @Column({ type: 'text', nullable: true })
  reason: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column()
  performedBy: string;

  @Column({ type: 'timestamp' })
  timestamp: Date;

  @Column({ type: 'json', nullable: true })
  denominationBreakdown: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
