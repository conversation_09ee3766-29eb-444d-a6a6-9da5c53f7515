import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { WarehouseService } from '../services/warehouse.service';
import { Warehouse } from '../entities/warehouse.entity';
import { Location } from '../entities/location.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('warehouses')
@UseGuards(JwtAuthGuard)
export class WarehouseController {
  constructor(private readonly warehouseService: WarehouseService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createWarehouseDto: Partial<Warehouse>) {
    return this.warehouseService.create(createWarehouseDto);
  }

  @Get()
  async findAll() {
    return this.warehouseService.findAll();
  }

  @Get('active')
  async getActiveWarehouses() {
    return this.warehouseService.getActiveWarehouses();
  }

  @Get('statistics')
  async getStatistics() {
    return this.warehouseService.getWarehouseStatistics();
  }

  @Get('search')
  async searchWarehouses(@Query('q') searchTerm: string) {
    return this.warehouseService.searchWarehouses(searchTerm);
  }

  @Get('region/:region')
  async getWarehousesByRegion(@Param('region') region: string) {
    return this.warehouseService.getWarehousesByRegion(region);
  }

  @Get('code/:code')
  async findByCode(@Param('code') code: string) {
    return this.warehouseService.findByCode(code);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.warehouseService.findOne(id);
  }

  @Get(':id/locations')
  async getWarehouseLocations(@Param('id') id: string) {
    return this.warehouseService.getWarehouseLocations(id);
  }

  @Get(':id/stock')
  async getWarehouseStock(@Param('id') id: string) {
    return this.warehouseService.getWarehouseStock(id);
  }

  @Get(':id/stock-value')
  async getWarehouseStockValue(@Param('id') id: string) {
    const value = await this.warehouseService.getWarehouseStockValue(id);
    return { warehouseId: id, stockValue: value };
  }

  @Get(':id/capacity-utilization')
  async getCapacityUtilization(@Param('id') id: string) {
    return this.warehouseService.getWarehouseCapacityUtilization(id);
  }

  @Post(':id/locations')
  async createLocation(
    @Param('id') id: string,
    @Body() createLocationDto: Partial<Location>,
  ) {
    return this.warehouseService.createLocation(id, createLocationDto);
  }

  @Post('transfer-stock')
  async transferStock(
    @Body() transferData: {
      fromWarehouseId: string;
      toWarehouseId: string;
      productId: string;
      quantity: number;
    },
  ) {
    return this.warehouseService.transferStock(
      transferData.fromWarehouseId,
      transferData.toWarehouseId,
      transferData.productId,
      transferData.quantity,
    );
  }

  @Post('generate-code')
  async generateWarehouseCode(@Body() data: { name: string }) {
    const code = await this.warehouseService.generateWarehouseCode(data.name);
    return { code };
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateWarehouseDto: Partial<Warehouse>,
  ) {
    return this.warehouseService.update(id, updateWarehouseDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.warehouseService.remove(id);
  }
}
