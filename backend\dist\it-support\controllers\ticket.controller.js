"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TicketController = void 0;
const common_1 = require("@nestjs/common");
const ticket_service_1 = require("../services/ticket.service");
const ticket_entity_1 = require("../entities/ticket.entity");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let TicketController = class TicketController {
    ticketService;
    constructor(ticketService) {
        this.ticketService = ticketService;
    }
    async create(createTicketDto) {
        return this.ticketService.create(createTicketDto);
    }
    async findAll(status, priority, assigneeId, creatorId) {
        if (status) {
            return this.ticketService.findByStatus(status);
        }
        if (priority) {
            return this.ticketService.findByPriority(priority);
        }
        if (assigneeId) {
            return this.ticketService.findByAssignee(assigneeId);
        }
        if (creatorId) {
            return this.ticketService.findByCreator(creatorId);
        }
        return this.ticketService.findAll();
    }
    async getStatistics() {
        return this.ticketService.getTicketStatistics();
    }
    async getDashboardMetrics() {
        return this.ticketService.getDashboardMetrics();
    }
    async getOverdueTickets() {
        return this.ticketService.getOverdueTickets();
    }
    async searchTickets(searchTerm) {
        return this.ticketService.searchTickets(searchTerm);
    }
    async getTicketsByCategory(category) {
        return this.ticketService.getTicketsByCategory(category);
    }
    async findOne(id) {
        return this.ticketService.findOne(id);
    }
    async getTicketComments(id) {
        return this.ticketService.getTicketComments(id);
    }
    async addComment(id, commentData) {
        return this.ticketService.addComment(id, commentData.content, commentData.authorId);
    }
    async update(id, updateTicketDto) {
        return this.ticketService.update(id, updateTicketDto);
    }
    async assignTicket(id, assignmentData) {
        return this.ticketService.assignTicket(id, assignmentData.assigneeId);
    }
    async updateStatus(id, statusData) {
        return this.ticketService.updateStatus(id, statusData.status, statusData.userId);
    }
    async updatePriority(id, priorityData) {
        return this.ticketService.updatePriority(id, priorityData.priority, priorityData.userId);
    }
    async escalateTicket(id, escalationData) {
        return this.ticketService.escalateTicket(id, escalationData.reason, escalationData.userId);
    }
    async closeTicket(id, closeData) {
        return this.ticketService.closeTicket(id, closeData.resolution, closeData.userId);
    }
    async reopenTicket(id, reopenData) {
        return this.ticketService.reopenTicket(id, reopenData.reason, reopenData.userId);
    }
    async remove(id) {
        return this.ticketService.remove(id);
    }
};
exports.TicketController = TicketController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('priority')),
    __param(2, (0, common_1.Query)('assignee')),
    __param(3, (0, common_1.Query)('creator')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_a = typeof ticket_entity_1.TicketStatus !== "undefined" && ticket_entity_1.TicketStatus) === "function" ? _a : Object, typeof (_b = typeof ticket_entity_1.TicketPriority !== "undefined" && ticket_entity_1.TicketPriority) === "function" ? _b : Object, String, String]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Get)('overdue'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "getOverdueTickets", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "searchTickets", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "getTicketsByCategory", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/comments'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "getTicketComments", null);
__decorate([
    (0, common_1.Post)(':id/comments'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "addComment", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/assign'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "assignTicket", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/priority'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "updatePriority", null);
__decorate([
    (0, common_1.Post)(':id/escalate'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "escalateTicket", null);
__decorate([
    (0, common_1.Post)(':id/close'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "closeTicket", null);
__decorate([
    (0, common_1.Post)(':id/reopen'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "reopenTicket", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TicketController.prototype, "remove", null);
exports.TicketController = TicketController = __decorate([
    (0, common_1.Controller)('tickets'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [ticket_service_1.TicketService])
], TicketController);
//# sourceMappingURL=ticket.controller.js.map