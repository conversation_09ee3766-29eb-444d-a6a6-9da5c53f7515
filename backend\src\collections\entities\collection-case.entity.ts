import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CollectionActivity } from './collection-activity.entity';
import { CollectionStrategy } from './collection-strategy.entity';
import { CollectionAgent } from './collection-agent.entity';
import { PaymentPlan } from './payment-plan.entity';
import { CollectionNote } from './collection-note.entity';
import { CollectionDocument } from './collection-document.entity';
import { CollectionDispute } from './collection-dispute.entity';

export enum CaseStatus {
  NEW = 'new',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  PAYMENT_PLAN = 'payment_plan',
  DISPUTED = 'disputed',
  LEGAL = 'legal',
  CLOSED_PAID = 'closed_paid',
  CLOSED_WRITTEN_OFF = 'closed_written_off',
  CLOSED_SETTLED = 'closed_settled',
  CLOSED_UNCOLLECTABLE = 'closed_uncollectable',
}

export enum CasePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

export enum DebtType {
  INVOICE = 'invoice',
  LOAN = 'loan',
  CREDIT_CARD = 'credit_card',
  UTILITY = 'utility',
  MEDICAL = 'medical',
  STUDENT_LOAN = 'student_loan',
  MORTGAGE = 'mortgage',
  OTHER = 'other',
}

@Entity('collection_cases')
export class CollectionCase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  caseNumber: string;

  @Column()
  customerId: string;

  @Column({ length: 255 })
  customerName: string;

  @Column({ length: 200, nullable: true })
  customerEmail: string;

  @Column({ length: 20, nullable: true })
  customerPhone: string;

  @Column({
    type: 'enum',
    enum: DebtType,
    default: DebtType.INVOICE,
  })
  debtType: DebtType;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  originalAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  currentBalance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  interestAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  feesAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ type: 'date' })
  originalDueDate: Date;

  @Column({ type: 'int' })
  daysOverdue: number;

  @Column({
    type: 'enum',
    enum: CaseStatus,
    default: CaseStatus.NEW,
  })
  status: CaseStatus;

  @Column({
    type: 'enum',
    enum: CasePriority,
    default: CasePriority.MEDIUM,
  })
  priority: CasePriority;

  @Column({ nullable: true })
  assignedAgentId: string;

  @ManyToOne(() => CollectionAgent, { nullable: true })
  @JoinColumn({ name: 'assignedAgentId' })
  assignedAgent: CollectionAgent;

  @Column({ nullable: true })
  strategyId: string;

  @ManyToOne(() => CollectionStrategy, { nullable: true })
  @JoinColumn({ name: 'strategyId' })
  strategy: CollectionStrategy;

  @Column({ type: 'date', nullable: true })
  lastContactDate: Date;

  @Column({ type: 'date', nullable: true })
  nextActionDate: Date;

  @Column({ type: 'text', nullable: true })
  nextAction: string;

  @Column({ type: 'date', nullable: true })
  promiseToPayDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  promiseToPayAmount: number;

  @Column({ type: 'int', default: 0 })
  contactAttempts: number;

  @Column({ type: 'int', default: 0 })
  successfulContacts: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalPayments: number;

  @Column({ type: 'date', nullable: true })
  lastPaymentDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  lastPaymentAmount: number;

  @Column({ type: 'text', nullable: true })
  customerNotes: string;

  @Column({ type: 'text', nullable: true })
  internalNotes: string;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ default: false })
  isDisputed: boolean;

  @Column({ default: false })
  isLegal: boolean;

  @Column({ default: false })
  isBankrupt: boolean;

  @Column({ default: false })
  isDeceased: boolean;

  @Column({ default: false })
  doNotCall: boolean;

  @Column({ default: false })
  doNotEmail: boolean;

  @Column({ default: false })
  doNotMail: boolean;

  @OneToMany(() => CollectionActivity, activity => activity.case)
  activities: CollectionActivity[];

  @OneToMany(() => PaymentPlan, plan => plan.case)
  paymentPlans: PaymentPlan[];

  @OneToMany(() => CollectionNote, note => note.case)
  notes: CollectionNote[];

  @OneToMany(() => CollectionDocument, document => document.case)
  documents: CollectionDocument[];

  @OneToMany(() => CollectionDispute, dispute => dispute.case)
  disputes: CollectionDispute[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
