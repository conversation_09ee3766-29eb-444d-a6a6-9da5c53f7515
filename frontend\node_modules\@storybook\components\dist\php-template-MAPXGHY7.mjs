import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_php_template=__commonJS({"../../node_modules/highlight.js/lib/languages/php-template.js"(exports,module){function phpTemplate(hljs){return {name:"PHP template",subLanguage:"xml",contains:[{begin:/<\?(php|=)?/,end:/\?>/,subLanguage:"php",contains:[{begin:"/\\*",end:"\\*/",skip:!0},{begin:'b"',end:'"',skip:!0},{begin:"b'",end:"'",skip:!0},hljs.inherit(hljs.APOS_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0}),hljs.inherit(hljs.QUOTE_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0})]}]}}module.exports=phpTemplate;}});var phpTemplateMAPXGHY7 = require_php_template();

export { phpTemplateMAPXGHY7 as default };
