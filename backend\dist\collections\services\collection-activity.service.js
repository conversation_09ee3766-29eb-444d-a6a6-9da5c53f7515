"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionActivityService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const collection_activity_entity_1 = require("../entities/collection-activity.entity");
let CollectionActivityService = class CollectionActivityService {
    collectionActivityRepository;
    constructor(collectionActivityRepository) {
        this.collectionActivityRepository = collectionActivityRepository;
    }
    async create(activityData) {
        const activity = this.collectionActivityRepository.create({
            ...activityData,
            activityDate: activityData.activityDate || new Date(),
        });
        return this.collectionActivityRepository.save(activity);
    }
    async findAll() {
        return this.collectionActivityRepository.find({
            relations: ['case'],
            order: { activityDate: 'DESC' },
        });
    }
    async findOne(id) {
        const activity = await this.collectionActivityRepository.findOne({
            where: { id },
            relations: ['case'],
        });
        if (!activity) {
            throw new common_1.NotFoundException(`Activity with ID ${id} not found`);
        }
        return activity;
    }
    async findByCase(caseId) {
        return this.collectionActivityRepository.find({
            where: { caseId },
            order: { activityDate: 'DESC' },
        });
    }
    async findByAgent(agentId) {
        return this.collectionActivityRepository.find({
            where: { performedBy: agentId },
            relations: ['case'],
            order: { activityDate: 'DESC' },
        });
    }
    async update(id, updateData) {
        await this.collectionActivityRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const activity = await this.findOne(id);
        await this.collectionActivityRepository.remove(activity);
    }
    async logActivity(caseId, type, description, performedBy, outcome) {
        return this.create({
            caseId,
            type,
            description,
            performedBy,
            outcome,
            activityDate: new Date(),
        });
    }
    async logCall(caseId, duration, outcome, notes, performedBy) {
        return this.create({
            caseId,
            type: collection_activity_entity_1.ActivityType.CALL,
            description: `Phone call - Duration: ${duration} minutes`,
            outcome,
            notes,
            performedBy,
            activityDate: new Date(),
        });
    }
    async logEmail(caseId, subject, outcome, performedBy) {
        return this.create({
            caseId,
            type: collection_activity_entity_1.ActivityType.EMAIL,
            description: `Email sent - Subject: ${subject}`,
            outcome,
            performedBy,
            activityDate: new Date(),
        });
    }
    async logLetter(caseId, letterType, performedBy) {
        return this.create({
            caseId,
            type: collection_activity_entity_1.ActivityType.LETTER,
            description: `Collection letter sent - Type: ${letterType}`,
            performedBy,
            activityDate: new Date(),
        });
    }
    async logPayment(caseId, amount, paymentMethod, performedBy) {
        return this.create({
            caseId,
            type: collection_activity_entity_1.ActivityType.PAYMENT,
            description: `Payment received - Amount: $${amount} via ${paymentMethod}`,
            outcome: 'payment_received',
            performedBy,
            activityDate: new Date(),
        });
    }
    async getActivitySummary(caseId) {
        const activities = await this.findByCase(caseId);
        const summary = {
            totalActivities: activities.length,
            callsAttempted: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.CALL).length,
            emailsSent: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.EMAIL).length,
            lettersSent: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.LETTER).length,
            paymentsReceived: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.PAYMENT).length,
            lastActivity: activities.length > 0 ? activities[0].activityDate : null,
            lastActivityType: activities.length > 0 ? activities[0].type : null,
        };
        return summary;
    }
    async getAgentActivityReport(agentId, startDate, endDate) {
        const activities = await this.collectionActivityRepository
            .createQueryBuilder('activity')
            .where('activity.performedBy = :agentId', { agentId })
            .andWhere('activity.activityDate BETWEEN :startDate AND :endDate', { startDate, endDate })
            .getMany();
        const report = {
            totalActivities: activities.length,
            activitiesByType: {
                calls: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.CALL).length,
                emails: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.EMAIL).length,
                letters: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.LETTER).length,
                payments: activities.filter(a => a.type === collection_activity_entity_1.ActivityType.PAYMENT).length,
            },
            successfulOutcomes: activities.filter(a => a.outcome && ['payment_received', 'payment_arranged', 'resolved'].includes(a.outcome)).length,
        };
        return report;
    }
    async getRecentActivities(limit = 10) {
        return this.collectionActivityRepository.find({
            relations: ['case'],
            order: { activityDate: 'DESC' },
            take: limit,
        });
    }
    async scheduleFollowUp(caseId, followUpDate, notes, performedBy) {
        return this.create({
            caseId,
            type: collection_activity_entity_1.ActivityType.FOLLOW_UP,
            description: `Follow-up scheduled for ${followUpDate.toDateString()}`,
            notes,
            performedBy,
            scheduledDate: followUpDate,
            activityDate: new Date(),
        });
    }
    async getScheduledFollowUps() {
        const today = new Date();
        return this.collectionActivityRepository
            .createQueryBuilder('activity')
            .where('activity.type = :type', { type: collection_activity_entity_1.ActivityType.FOLLOW_UP })
            .andWhere('activity.scheduledDate <= :today', { today })
            .andWhere('activity.completed = false OR activity.completed IS NULL')
            .orderBy('activity.scheduledDate', 'ASC')
            .getMany();
    }
    async markFollowUpCompleted(id, outcome) {
        await this.collectionActivityRepository.update(id, {
            completed: true,
            outcome,
            completedDate: new Date(),
        });
        return this.findOne(id);
    }
};
exports.CollectionActivityService = CollectionActivityService;
exports.CollectionActivityService = CollectionActivityService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(collection_activity_entity_1.CollectionActivity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CollectionActivityService);
//# sourceMappingURL=collection-activity.service.js.map