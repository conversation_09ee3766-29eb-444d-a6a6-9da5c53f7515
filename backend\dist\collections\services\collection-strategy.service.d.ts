import { Repository } from 'typeorm';
import { CollectionStrategy } from '../entities/collection-strategy.entity';
export declare class CollectionStrategyService {
    private collectionStrategyRepository;
    constructor(collectionStrategyRepository: Repository<CollectionStrategy>);
    create(strategyData: Partial<CollectionStrategy>): Promise<CollectionStrategy>;
    findAll(): Promise<CollectionStrategy[]>;
    findOne(id: string): Promise<CollectionStrategy>;
    update(id: string, updateData: Partial<CollectionStrategy>): Promise<CollectionStrategy>;
    remove(id: string): Promise<void>;
    findByDebtRange(minAmount: number, maxAmount: number): Promise<CollectionStrategy[]>;
    findByDaysOverdue(daysOverdue: number): Promise<CollectionStrategy[]>;
    getRecommendedStrategy(debtAmount: number, daysOverdue: number): Promise<CollectionStrategy | null>;
    activateStrategy(id: string): Promise<CollectionStrategy>;
    deactivateStrategy(id: string): Promise<CollectionStrategy>;
    getActiveStrategies(): Promise<CollectionStrategy[]>;
    createDefaultStrategies(): Promise<CollectionStrategy[]>;
    getStrategyEffectiveness(): Promise<any[]>;
    assignStrategyToCase(caseId: string, strategyId: string): Promise<void>;
    getStrategyRecommendations(caseData: {
        debtAmount: number;
        daysOverdue: number;
        customerType?: string;
        previousPaymentHistory?: string;
    }): Promise<{
        primaryStrategy: CollectionStrategy | null;
        alternativeStrategies: CollectionStrategy[];
        recommendations: string[];
    }>;
}
