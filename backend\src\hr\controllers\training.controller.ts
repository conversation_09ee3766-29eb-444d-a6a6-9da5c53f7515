import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { TrainingService } from '../services/training.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/training')
@UseGuards(JwtAuthGuard)
export class TrainingController {
  constructor(private readonly trainingService: TrainingService) {}

  @Post()
  create(@Body() createTrainingDto: any) {
    return this.trainingService.create(createTrainingDto);
  }

  @Get()
  findAll(
    @Query('employeeId') employeeId?: string,
    @Query('type') type?: string,
    @Query('status') status?: string
  ) {
    const filters: any = {};
    if (employeeId) filters.employeeId = employeeId;
    if (type) filters.type = type;
    if (status) filters.status = status;

    return this.trainingService.findAll(filters);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.trainingService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTrainingDto: any) {
    return this.trainingService.update(id, updateTrainingDto);
  }

  @Post(':id/complete')
  completeTraining(
    @Param('id') id: string,
    @Body() completeDto: { score?: number; feedback?: string }
  ) {
    return this.trainingService.completeTraining(id, completeDto.score, completeDto.feedback);
  }
}
