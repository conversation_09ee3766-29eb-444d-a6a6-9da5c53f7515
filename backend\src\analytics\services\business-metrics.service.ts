import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Customer } from '../../sales/entities/customer.entity';
import { Invoice } from '../../sales/entities/invoice.entity';

@Injectable()
export class BusinessMetricsService {
  constructor(
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Invoice)
    private invoiceRepository: Repository<Invoice>,
  ) {}

  async getBusinessMetrics(companyId: string, period: string = '30d') {
    const days = this.parsePeriod(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get current period metrics
    const currentMetrics = await this.getMetricsForPeriod(companyId, startDate, new Date());

    // Get previous period metrics for comparison
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);
    const previousMetrics = await this.getMetricsForPeriod(companyId, previousStartDate, startDate);

    // Calculate growth percentages
    const revenueGrowth = this.calculateGrowth(currentMetrics.totalRevenue, previousMetrics.totalRevenue);
    const customerGrowth = this.calculateGrowth(currentMetrics.totalCustomers, previousMetrics.totalCustomers);
    const profitGrowth = this.calculateGrowth(currentMetrics.netProfit, previousMetrics.netProfit);

    return {
      totalRevenue: currentMetrics.totalRevenue,
      totalExpenses: currentMetrics.totalExpenses,
      netProfit: currentMetrics.netProfit,
      totalCustomers: currentMetrics.totalCustomers,
      totalProjects: currentMetrics.totalProjects,
      totalEmployees: currentMetrics.totalEmployees,
      revenueGrowth,
      customerGrowth,
      profitGrowth,
      projectGrowth: 15.7, // Mock data for now
      departmentMetrics: await this.getDepartmentMetrics(companyId, period),
    };
  }

  async getRevenueMetrics(companyId: string, period: string = '30d') {
    const days = this.parsePeriod(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const invoices = await this.invoiceRepository
      .createQueryBuilder('invoice')
      .where('invoice.companyId = :companyId', { companyId })
      .andWhere('invoice.createdAt >= :startDate', { startDate })
      .andWhere('invoice.status = :status', { status: 'paid' })
      .getMany();

    const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
    const totalInvoices = invoices.length;
    const averageInvoiceValue = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;

    return {
      totalRevenue,
      totalInvoices,
      averageInvoiceValue,
      revenueByMonth: await this.getRevenueByMonth(companyId, days),
    };
  }

  async getDepartmentMetrics(companyId: string, period: string = '30d') {
    // Mock department metrics for now
    // In a real implementation, you would query actual department data
    return [
      {
        department: 'Sales',
        revenue: 850000,
        expenses: 320000,
        profit: 530000,
        efficiency: 95,
        trend: 'up',
      },
      {
        department: 'Projects',
        revenue: 650000,
        expenses: 280000,
        profit: 370000,
        efficiency: 88,
        trend: 'up',
      },
      {
        department: 'Marketing',
        revenue: 450000,
        expenses: 180000,
        profit: 270000,
        efficiency: 92,
        trend: 'stable',
      },
    ];
  }

  async getGrowthMetrics(companyId: string, period: string = '30d') {
    const days = this.parsePeriod(period);
    const currentDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get growth data for the specified period
    const growthData = await this.calculateGrowthTrends(companyId, startDate, currentDate);

    return {
      revenueGrowthTrend: growthData.revenueGrowth,
      customerGrowthTrend: growthData.customerGrowth,
      profitGrowthTrend: growthData.profitGrowth,
      monthlyGrowthRates: growthData.monthlyRates,
    };
  }

  private async getMetricsForPeriod(companyId: string, startDate: Date, endDate: Date) {
    // Get revenue from paid invoices
    const invoices = await this.invoiceRepository
      .createQueryBuilder('invoice')
      .where('invoice.companyId = :companyId', { companyId })
      .andWhere('invoice.createdAt >= :startDate', { startDate })
      .andWhere('invoice.createdAt <= :endDate', { endDate })
      .andWhere('invoice.status = :status', { status: 'paid' })
      .getMany();

    const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);

    // Get customer count
    const totalCustomers = await this.customerRepository
      .createQueryBuilder('customer')
      .where('customer.companyId = :companyId', { companyId })
      .andWhere('customer.createdAt <= :endDate', { endDate })
      .getCount();

    // Mock data for expenses, projects, and employees
    const totalExpenses = totalRevenue * 0.65; // Assume 65% expense ratio
    const netProfit = totalRevenue - totalExpenses;

    return {
      totalRevenue,
      totalExpenses,
      netProfit,
      totalCustomers,
      totalProjects: 89, // Mock data
      totalEmployees: 156, // Mock data
    };
  }

  private async getRevenueByMonth(companyId: string, days: number): Promise<Array<{month: string, revenue: number}>> {
    // Mock monthly revenue data
    const months = Math.ceil(days / 30);
    const monthlyData: Array<{month: string, revenue: number}> = [];

    for (let i = 0; i < months; i++) {
      monthlyData.push({
        month: new Date(Date.now() - (i * 30 * 24 * 60 * 60 * 1000)).toISOString().slice(0, 7),
        revenue: Math.floor(Math.random() * 500000) + 200000,
      });
    }

    return monthlyData.reverse();
  }

  private async calculateGrowthTrends(companyId: string, startDate: Date, endDate: Date) {
    // Mock growth trend data
    return {
      revenueGrowth: 12.5,
      customerGrowth: 8.3,
      profitGrowth: 18.2,
      monthlyRates: [
        { month: '2024-01', growth: 10.2 },
        { month: '2024-02', growth: 15.8 },
        { month: '2024-03', growth: 12.5 },
        { month: '2024-04', growth: 18.2 },
        { month: '2024-05', growth: 14.7 },
      ],
    };
  }

  private parsePeriod(period: string): number {
    const match = period.match(/(\d+)([dmy])/);
    if (!match) return 30; // Default to 30 days

    const [, value, unit] = match;
    const num = parseInt(value, 10);

    switch (unit) {
      case 'd': return num;
      case 'm': return num * 30;
      case 'y': return num * 365;
      default: return 30;
    }
  }

  private calculateGrowth(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }
}
