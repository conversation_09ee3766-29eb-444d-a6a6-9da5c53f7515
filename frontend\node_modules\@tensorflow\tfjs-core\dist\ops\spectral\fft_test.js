/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('1D FFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (2 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2]);
        const t1Imag = tf.tensor1d([1, 1]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.fft(t1).data(), [3, 2, -1, 0]);
    });
    it('should calculate FFT from Tensor directly', async () => {
        const t1Real = tf.tensor1d([1, 2]);
        const t1Imag = tf.tensor1d([1, 1]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await t1.fft().data(), [3, 2, -1, 0]);
    });
    it('should return the same value as TensorFlow (3 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3]);
        const t1Imag = tf.tensor1d([0, 0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.fft(t1).data(), [6, 0, -1.5, 0.866025, -1.5, -0.866025]);
    });
    it('should return the same value as TensorFlow with imaginary (3 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3]);
        const t1Imag = tf.tensor1d([1, 2, 3]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.fft(t1).data(), [6, 6, -2.3660252, -0.63397473, -0.6339747, -2.3660254]);
    });
    it('should return the same value as TensorFlow (negative 3 elements)', async () => {
        const t1Real = tf.tensor1d([-1, -2, -3]);
        const t1Imag = tf.tensor1d([-1, -2, -3]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.fft(t1).data(), [-5.9999995, -6, 2.3660252, 0.63397473, 0.6339747, 2.3660254]);
    });
    it('should return the same value with TensorFlow (4 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3, 4]);
        const t1Imag = tf.tensor1d([0, 0, 0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.fft(t1).data(), [10, 0, -2, 2, -2, 0, -2, -2]);
    });
    it('should return the same value as TensorFlow with imaginary (4 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3, 4]);
        const t1Imag = tf.tensor1d([1, 2, 3, 4]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.fft(t1).data(), [10, 10, -4, 0, -2, -2, 0, -4]);
    });
});
describeWithFlags('2D FFT', ALL_ENVS, () => {
    it('2D: should return the same value as TensorFlow', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const t1Imag = tf.tensor2d([5, 6, 7, 8], [2, 2]);
        const t1 = tf.complex(t1Real, t1Imag);
        const y = tf.spectral.fft(t1);
        expectArraysClose(await y.data(), [3, 11, -1, -1, 7, 15, -1, -1]);
        expect(y.shape).toEqual(t1Real.shape);
    });
    it('3D: should return the same value as TensorFlow', async () => {
        const t1Real = tf.tensor3d([1, 2, 3, 4, -1, -2, -3, -4], [2, 2, 2]);
        const t1Imag = tf.tensor3d([5, 6, 7, 8, -5, -6, -7, -8], [2, 2, 2]);
        const t1 = tf.complex(t1Real, t1Imag);
        const y = tf.spectral.fft(t1);
        expectArraysClose(await y.data(), [3, 11, -1, -1, 7, 15, -1, -1, -3, -11, 1, 1, -7, -15, 1, 1]);
        expect(y.shape).toEqual(t1Real.shape);
    });
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZmZ0X3Rlc3QuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWNvcmUvc3JjL29wcy9zcGVjdHJhbC9mZnRfdGVzdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7Ozs7Ozs7O0dBZUc7QUFFSCxPQUFPLEtBQUssRUFBRSxNQUFNLGFBQWEsQ0FBQztBQUNsQyxPQUFPLEVBQUMsUUFBUSxFQUFFLGlCQUFpQixFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFDL0QsT0FBTyxFQUFDLGlCQUFpQixFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFFbEQsaUJBQWlCLENBQUMsUUFBUSxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUU7SUFDekMsRUFBRSxDQUFDLDJEQUEyRCxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3pFLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNuQyxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbkMsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDdEMsaUJBQWlCLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUNyRSxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQywyQ0FBMkMsRUFBRSxLQUFLLElBQUksRUFBRTtRQUN6RCxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbkMsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ25DLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3RDLGlCQUFpQixDQUFDLE1BQU0sRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQzFELENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHlEQUF5RCxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3ZFLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdEMsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN0QyxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztRQUN0QyxpQkFBaUIsQ0FDYixNQUFNLEVBQUUsQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDLElBQUksRUFBRSxFQUNoQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsUUFBUSxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUMvQyxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyx3RUFBd0UsRUFDeEUsS0FBSyxJQUFJLEVBQUU7UUFDVCxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3RDLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdEMsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDdEMsaUJBQWlCLENBQ2IsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFDaEMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsU0FBUyxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsU0FBUyxFQUFFLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQztJQUMvRCxDQUFDLENBQUMsQ0FBQztJQUVOLEVBQUUsQ0FBQyxrRUFBa0UsRUFDbEUsS0FBSyxJQUFJLEVBQUU7UUFDVCxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDekMsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDdEMsaUJBQWlCLENBQ2IsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFDaEMsQ0FBQyxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxTQUFTLENBQUMsQ0FBQyxDQUFDO0lBQ3JFLENBQUMsQ0FBQyxDQUFDO0lBRU4sRUFBRSxDQUFDLDJEQUEyRCxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3pFLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3RDLGlCQUFpQixDQUNiLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ3ZFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHdFQUF3RSxFQUN4RSxLQUFLLElBQUksRUFBRTtRQUNULE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3RDLGlCQUFpQixDQUNiLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ3hFLENBQUMsQ0FBQyxDQUFDO0FBQ1IsQ0FBQyxDQUFDLENBQUM7QUFFSCxpQkFBaUIsQ0FBQyxRQUFRLEVBQUUsUUFBUSxFQUFFLEdBQUcsRUFBRTtJQUN6QyxFQUFFLENBQUMsZ0RBQWdELEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDOUQsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDakQsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDakQsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDdEMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDOUIsaUJBQWlCLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ2xFLE1BQU0sQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUN4QyxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxnREFBZ0QsRUFBRSxLQUFLLElBQUksRUFBRTtRQUM5RCxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDcEUsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3BFLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3RDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQzlCLGlCQUFpQixDQUNiLE1BQU0sQ0FBQyxDQUFDLElBQUksRUFBRSxFQUNkLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbEUsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ3hDLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAyMCBHb29nbGUgTExDLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICovXG5cbmltcG9ydCAqIGFzIHRmIGZyb20gJy4uLy4uL2luZGV4JztcbmltcG9ydCB7QUxMX0VOVlMsIGRlc2NyaWJlV2l0aEZsYWdzfSBmcm9tICcuLi8uLi9qYXNtaW5lX3V0aWwnO1xuaW1wb3J0IHtleHBlY3RBcnJheXNDbG9zZX0gZnJvbSAnLi4vLi4vdGVzdF91dGlsJztcblxuZGVzY3JpYmVXaXRoRmxhZ3MoJzFEIEZGVCcsIEFMTF9FTlZTLCAoKSA9PiB7XG4gIGl0KCdzaG91bGQgcmV0dXJuIHRoZSBzYW1lIHZhbHVlIHdpdGggVGVuc29yRmxvdyAoMiBlbGVtZW50cyknLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdDFSZWFsID0gdGYudGVuc29yMWQoWzEsIDJdKTtcbiAgICBjb25zdCB0MUltYWcgPSB0Zi50ZW5zb3IxZChbMSwgMV0pO1xuICAgIGNvbnN0IHQxID0gdGYuY29tcGxleCh0MVJlYWwsIHQxSW1hZyk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgdGYuc3BlY3RyYWwuZmZ0KHQxKS5kYXRhKCksIFszLCAyLCAtMSwgMF0pO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIGNhbGN1bGF0ZSBGRlQgZnJvbSBUZW5zb3IgZGlyZWN0bHknLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdDFSZWFsID0gdGYudGVuc29yMWQoWzEsIDJdKTtcbiAgICBjb25zdCB0MUltYWcgPSB0Zi50ZW5zb3IxZChbMSwgMV0pO1xuICAgIGNvbnN0IHQxID0gdGYuY29tcGxleCh0MVJlYWwsIHQxSW1hZyk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgdDEuZmZ0KCkuZGF0YSgpLCBbMywgMiwgLTEsIDBdKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCByZXR1cm4gdGhlIHNhbWUgdmFsdWUgYXMgVGVuc29yRmxvdyAoMyBlbGVtZW50cyknLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdDFSZWFsID0gdGYudGVuc29yMWQoWzEsIDIsIDNdKTtcbiAgICBjb25zdCB0MUltYWcgPSB0Zi50ZW5zb3IxZChbMCwgMCwgMF0pO1xuICAgIGNvbnN0IHQxID0gdGYuY29tcGxleCh0MVJlYWwsIHQxSW1hZyk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoXG4gICAgICAgIGF3YWl0IHRmLnNwZWN0cmFsLmZmdCh0MSkuZGF0YSgpLFxuICAgICAgICBbNiwgMCwgLTEuNSwgMC44NjYwMjUsIC0xLjUsIC0wLjg2NjAyNV0pO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiB0aGUgc2FtZSB2YWx1ZSBhcyBUZW5zb3JGbG93IHdpdGggaW1hZ2luYXJ5ICgzIGVsZW1lbnRzKScsXG4gICAgIGFzeW5jICgpID0+IHtcbiAgICAgICBjb25zdCB0MVJlYWwgPSB0Zi50ZW5zb3IxZChbMSwgMiwgM10pO1xuICAgICAgIGNvbnN0IHQxSW1hZyA9IHRmLnRlbnNvcjFkKFsxLCAyLCAzXSk7XG4gICAgICAgY29uc3QgdDEgPSB0Zi5jb21wbGV4KHQxUmVhbCwgdDFJbWFnKTtcbiAgICAgICBleHBlY3RBcnJheXNDbG9zZShcbiAgICAgICAgICAgYXdhaXQgdGYuc3BlY3RyYWwuZmZ0KHQxKS5kYXRhKCksXG4gICAgICAgICAgIFs2LCA2LCAtMi4zNjYwMjUyLCAtMC42MzM5NzQ3MywgLTAuNjMzOTc0NywgLTIuMzY2MDI1NF0pO1xuICAgICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiB0aGUgc2FtZSB2YWx1ZSBhcyBUZW5zb3JGbG93IChuZWdhdGl2ZSAzIGVsZW1lbnRzKScsXG4gICAgIGFzeW5jICgpID0+IHtcbiAgICAgICBjb25zdCB0MVJlYWwgPSB0Zi50ZW5zb3IxZChbLTEsIC0yLCAtM10pO1xuICAgICAgIGNvbnN0IHQxSW1hZyA9IHRmLnRlbnNvcjFkKFstMSwgLTIsIC0zXSk7XG4gICAgICAgY29uc3QgdDEgPSB0Zi5jb21wbGV4KHQxUmVhbCwgdDFJbWFnKTtcbiAgICAgICBleHBlY3RBcnJheXNDbG9zZShcbiAgICAgICAgICAgYXdhaXQgdGYuc3BlY3RyYWwuZmZ0KHQxKS5kYXRhKCksXG4gICAgICAgICAgIFstNS45OTk5OTk1LCAtNiwgMi4zNjYwMjUyLCAwLjYzMzk3NDczLCAwLjYzMzk3NDcsIDIuMzY2MDI1NF0pO1xuICAgICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiB0aGUgc2FtZSB2YWx1ZSB3aXRoIFRlbnNvckZsb3cgKDQgZWxlbWVudHMpJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHQxUmVhbCA9IHRmLnRlbnNvcjFkKFsxLCAyLCAzLCA0XSk7XG4gICAgY29uc3QgdDFJbWFnID0gdGYudGVuc29yMWQoWzAsIDAsIDAsIDBdKTtcbiAgICBjb25zdCB0MSA9IHRmLmNvbXBsZXgodDFSZWFsLCB0MUltYWcpO1xuICAgIGV4cGVjdEFycmF5c0Nsb3NlKFxuICAgICAgICBhd2FpdCB0Zi5zcGVjdHJhbC5mZnQodDEpLmRhdGEoKSwgWzEwLCAwLCAtMiwgMiwgLTIsIDAsIC0yLCAtMl0pO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiB0aGUgc2FtZSB2YWx1ZSBhcyBUZW5zb3JGbG93IHdpdGggaW1hZ2luYXJ5ICg0IGVsZW1lbnRzKScsXG4gICAgIGFzeW5jICgpID0+IHtcbiAgICAgICBjb25zdCB0MVJlYWwgPSB0Zi50ZW5zb3IxZChbMSwgMiwgMywgNF0pO1xuICAgICAgIGNvbnN0IHQxSW1hZyA9IHRmLnRlbnNvcjFkKFsxLCAyLCAzLCA0XSk7XG4gICAgICAgY29uc3QgdDEgPSB0Zi5jb21wbGV4KHQxUmVhbCwgdDFJbWFnKTtcbiAgICAgICBleHBlY3RBcnJheXNDbG9zZShcbiAgICAgICAgICAgYXdhaXQgdGYuc3BlY3RyYWwuZmZ0KHQxKS5kYXRhKCksIFsxMCwgMTAsIC00LCAwLCAtMiwgLTIsIDAsIC00XSk7XG4gICAgIH0pO1xufSk7XG5cbmRlc2NyaWJlV2l0aEZsYWdzKCcyRCBGRlQnLCBBTExfRU5WUywgKCkgPT4ge1xuICBpdCgnMkQ6IHNob3VsZCByZXR1cm4gdGhlIHNhbWUgdmFsdWUgYXMgVGVuc29yRmxvdycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB0MVJlYWwgPSB0Zi50ZW5zb3IyZChbMSwgMiwgMywgNF0sIFsyLCAyXSk7XG4gICAgY29uc3QgdDFJbWFnID0gdGYudGVuc29yMmQoWzUsIDYsIDcsIDhdLCBbMiwgMl0pO1xuICAgIGNvbnN0IHQxID0gdGYuY29tcGxleCh0MVJlYWwsIHQxSW1hZyk7XG4gICAgY29uc3QgeSA9IHRmLnNwZWN0cmFsLmZmdCh0MSk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgeS5kYXRhKCksIFszLCAxMSwgLTEsIC0xLCA3LCAxNSwgLTEsIC0xXSk7XG4gICAgZXhwZWN0KHkuc2hhcGUpLnRvRXF1YWwodDFSZWFsLnNoYXBlKTtcbiAgfSk7XG5cbiAgaXQoJzNEOiBzaG91bGQgcmV0dXJuIHRoZSBzYW1lIHZhbHVlIGFzIFRlbnNvckZsb3cnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdDFSZWFsID0gdGYudGVuc29yM2QoWzEsIDIsIDMsIDQsIC0xLCAtMiwgLTMsIC00XSwgWzIsIDIsIDJdKTtcbiAgICBjb25zdCB0MUltYWcgPSB0Zi50ZW5zb3IzZChbNSwgNiwgNywgOCwgLTUsIC02LCAtNywgLThdLCBbMiwgMiwgMl0pO1xuICAgIGNvbnN0IHQxID0gdGYuY29tcGxleCh0MVJlYWwsIHQxSW1hZyk7XG4gICAgY29uc3QgeSA9IHRmLnNwZWN0cmFsLmZmdCh0MSk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoXG4gICAgICAgIGF3YWl0IHkuZGF0YSgpLFxuICAgICAgICBbMywgMTEsIC0xLCAtMSwgNywgMTUsIC0xLCAtMSwgLTMsIC0xMSwgMSwgMSwgLTcsIC0xNSwgMSwgMV0pO1xuICAgIGV4cGVjdCh5LnNoYXBlKS50b0VxdWFsKHQxUmVhbC5zaGFwZSk7XG4gIH0pO1xufSk7XG4iXX0=