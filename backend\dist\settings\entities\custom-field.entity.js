"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomField = exports.EntityType = exports.FieldType = void 0;
const typeorm_1 = require("typeorm");
const custom_field_value_entity_1 = require("./custom-field-value.entity");
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "text";
    FieldType["TEXTAREA"] = "textarea";
    FieldType["NUMBER"] = "number";
    FieldType["EMAIL"] = "email";
    FieldType["URL"] = "url";
    FieldType["PHONE"] = "phone";
    FieldType["DATE"] = "date";
    FieldType["DATETIME"] = "datetime";
    FieldType["BOOLEAN"] = "boolean";
    FieldType["SELECT"] = "select";
    FieldType["MULTISELECT"] = "multiselect";
    FieldType["RADIO"] = "radio";
    FieldType["CHECKBOX"] = "checkbox";
    FieldType["FILE"] = "file";
    FieldType["IMAGE"] = "image";
})(FieldType || (exports.FieldType = FieldType = {}));
var EntityType;
(function (EntityType) {
    EntityType["CUSTOMER"] = "customer";
    EntityType["PRODUCT"] = "product";
    EntityType["ORDER"] = "order";
    EntityType["INVOICE"] = "invoice";
    EntityType["PROJECT"] = "project";
    EntityType["TASK"] = "task";
    EntityType["EMPLOYEE"] = "employee";
    EntityType["VENDOR"] = "vendor";
    EntityType["ASSET"] = "asset";
    EntityType["TICKET"] = "ticket";
})(EntityType || (exports.EntityType = EntityType = {}));
let CustomField = class CustomField {
    id;
    name;
    label;
    description;
    type;
    entityType;
    isRequired;
    isActive;
    isSearchable;
    defaultValue;
    placeholder;
    helpText;
    options;
    validationRules;
    sortOrder;
    createdBy;
    values;
    metadata;
    createdAt;
    updatedAt;
};
exports.CustomField = CustomField;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CustomField.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CustomField.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CustomField.prototype, "label", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomField.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: FieldType,
    }),
    __metadata("design:type", String)
], CustomField.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EntityType,
    }),
    __metadata("design:type", String)
], CustomField.prototype, "entityType", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isSearchable", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomField.prototype, "defaultValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomField.prototype, "placeholder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomField.prototype, "helpText", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomField.prototype, "options", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomField.prototype, "validationRules", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CustomField.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CustomField.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => custom_field_value_entity_1.CustomFieldValue, value => value.customField),
    __metadata("design:type", Array)
], CustomField.prototype, "values", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomField.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomField.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomField.prototype, "updatedAt", void 0);
exports.CustomField = CustomField = __decorate([
    (0, typeorm_1.Entity)('custom_fields')
], CustomField);
//# sourceMappingURL=custom-field.entity.js.map