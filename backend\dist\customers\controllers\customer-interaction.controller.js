"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerInteractionController = void 0;
const common_1 = require("@nestjs/common");
const customer_interaction_service_1 = require("../services/customer-interaction.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerInteractionController = class CustomerInteractionController {
    customerInteractionService;
    constructor(customerInteractionService) {
        this.customerInteractionService = customerInteractionService;
    }
    async create(createInteractionDto) {
        return this.customerInteractionService.create(createInteractionDto);
    }
    async findAll(customerId, type, channel, startDate, endDate, page, limit) {
        const options = {
            customerId,
            type,
            channel,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            page: page ? parseInt(page) : 1,
            limit: limit ? parseInt(limit) : 20,
        };
        return this.customerInteractionService.findAll(options);
    }
    async getStatistics(customerId) {
        return this.customerInteractionService.getInteractionStats(customerId);
    }
    async getUpcomingFollowUps(days) {
        const daysAhead = days ? parseInt(days) : 7;
        return this.customerInteractionService.getUpcomingFollowUps(daysAhead);
    }
    async findByCustomer(customerId) {
        return this.customerInteractionService.findByCustomer(customerId);
    }
    async getCustomerTimeline(customerId, days) {
        const timelineDays = days ? parseInt(days) : 30;
        return this.customerInteractionService.getInteractionTimeline(customerId, timelineDays);
    }
    async getCustomerInteractionSummary(customerId) {
        return this.customerInteractionService.getInteractionSummary(customerId);
    }
    async findOne(id) {
        return this.customerInteractionService.findOne(id);
    }
    async logCall(callData) {
        return this.customerInteractionService.logCall(callData.customerId, {
            duration: callData.duration,
            outcome: callData.outcome,
            notes: callData.notes,
            followUpRequired: callData.followUpRequired,
            followUpDate: callData.followUpDate,
            contactedBy: callData.contactedBy,
        });
    }
    async logEmail(emailData) {
        return this.customerInteractionService.logEmail(emailData.customerId, {
            subject: emailData.subject,
            outcome: emailData.outcome,
            notes: emailData.notes,
            followUpRequired: emailData.followUpRequired,
            followUpDate: emailData.followUpDate,
            contactedBy: emailData.contactedBy,
        });
    }
    async logMeeting(meetingData) {
        return this.customerInteractionService.logMeeting(meetingData.customerId, {
            location: meetingData.location,
            duration: meetingData.duration,
            outcome: meetingData.outcome,
            notes: meetingData.notes,
            followUpRequired: meetingData.followUpRequired,
            followUpDate: meetingData.followUpDate,
            contactedBy: meetingData.contactedBy,
        });
    }
    async update(id, updateInteractionDto) {
        return this.customerInteractionService.update(id, updateInteractionDto);
    }
    async completeFollowUp(id, followUpData) {
        return this.customerInteractionService.markFollowUpCompleted(id, followUpData.notes);
    }
    async remove(id) {
        return this.customerInteractionService.remove(id);
    }
};
exports.CustomerInteractionController = CustomerInteractionController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('customerId')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('channel')),
    __param(3, (0, common_1.Query)('startDate')),
    __param(4, (0, common_1.Query)('endDate')),
    __param(5, (0, common_1.Query)('page')),
    __param(6, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __param(0, (0, common_1.Query)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('follow-ups'),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "getUpcomingFollowUps", null);
__decorate([
    (0, common_1.Get)('customer/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "findByCustomer", null);
__decorate([
    (0, common_1.Get)('customer/:customerId/timeline'),
    __param(0, (0, common_1.Param)('customerId')),
    __param(1, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "getCustomerTimeline", null);
__decorate([
    (0, common_1.Get)('customer/:customerId/summary'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "getCustomerInteractionSummary", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('log-call'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "logCall", null);
__decorate([
    (0, common_1.Post)('log-email'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "logEmail", null);
__decorate([
    (0, common_1.Post)('log-meeting'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "logMeeting", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/complete-follow-up'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "completeFollowUp", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerInteractionController.prototype, "remove", null);
exports.CustomerInteractionController = CustomerInteractionController = __decorate([
    (0, common_1.Controller)('customer-interactions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_interaction_service_1.CustomerInteractionService])
], CustomerInteractionController);
//# sourceMappingURL=customer-interaction.controller.js.map