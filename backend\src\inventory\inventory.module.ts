import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Product } from './entities/product.entity';
import { Category } from './entities/category.entity';
import { Warehouse } from './entities/warehouse.entity';
import { Location } from './entities/location.entity';
import { Stock } from './entities/stock.entity';
import { StockMovement } from './entities/stock-movement.entity';
import { Supplier } from './entities/supplier.entity';
import { PurchaseOrder } from './entities/purchase-order.entity';
import { PurchaseOrderItem } from './entities/purchase-order-item.entity';
import { StockAdjustment } from './entities/stock-adjustment.entity';
import { StockTransfer } from './entities/stock-transfer.entity';
import { InventoryCount } from './entities/inventory-count.entity';

// Services
import { ProductService } from './services/product.service';
import { CategoryService } from './services/category.service';
import { WarehouseService } from './services/warehouse.service';
import { StockService } from './services/stock.service';
import { SupplierService } from './services/supplier.service';
import { PurchaseOrderService } from './services/purchase-order.service';
import { StockMovementService } from './services/stock-movement.service';
import { InventoryReportService } from './services/inventory-report.service';

// Controllers
import { ProductController } from './controllers/product.controller';
import { CategoryController } from './controllers/category.controller';
import { WarehouseController } from './controllers/warehouse.controller';
import { StockController } from './controllers/stock.controller';
import { SupplierController } from './controllers/supplier.controller';
import { PurchaseOrderController } from './controllers/purchase-order.controller';
import { InventoryReportController } from './controllers/inventory-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      Category,
      Warehouse,
      Location,
      Stock,
      StockMovement,
      Supplier,
      PurchaseOrder,
      PurchaseOrderItem,
      StockAdjustment,
      StockTransfer,
      InventoryCount,
    ]),
  ],
  controllers: [
    ProductController,
    CategoryController,
    WarehouseController,
    StockController,
    SupplierController,
    PurchaseOrderController,
    InventoryReportController,
  ],
  providers: [
    ProductService,
    CategoryService,
    WarehouseService,
    StockService,
    SupplierService,
    PurchaseOrderService,
    StockMovementService,
    InventoryReportService,
  ],
  exports: [
    ProductService,
    CategoryService,
    WarehouseService,
    StockService,
    SupplierService,
    PurchaseOrderService,
    StockMovementService,
    InventoryReportService,
  ],
})
export class InventoryModule {}
