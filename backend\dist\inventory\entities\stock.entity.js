"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Stock = exports.StockStatus = void 0;
const typeorm_1 = require("typeorm");
const product_entity_1 = require("./product.entity");
const warehouse_entity_1 = require("./warehouse.entity");
const location_entity_1 = require("./location.entity");
const stock_movement_entity_1 = require("./stock-movement.entity");
var StockStatus;
(function (StockStatus) {
    StockStatus["AVAILABLE"] = "available";
    StockStatus["RESERVED"] = "reserved";
    StockStatus["DAMAGED"] = "damaged";
    StockStatus["EXPIRED"] = "expired";
    StockStatus["QUARANTINE"] = "quarantine";
    StockStatus["IN_TRANSIT"] = "in_transit";
})(StockStatus || (exports.StockStatus = StockStatus = {}));
let Stock = class Stock {
    id;
    productId;
    product;
    warehouseId;
    warehouse;
    locationId;
    location;
    quantityOnHand;
    quantityReserved;
    quantityAvailable;
    quantityInTransit;
    quantityDamaged;
    status;
    averageCost;
    lastCost;
    lastReceivedDate;
    lastIssuedDate;
    expiryDate;
    manufactureDate;
    batchNumber;
    serialNumber;
    lotNumbers;
    stockMovements;
    metadata;
    createdAt;
    updatedAt;
};
exports.Stock = Stock;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Stock.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Stock.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.Product, product => product.stocks),
    (0, typeorm_1.JoinColumn)({ name: 'productId' }),
    __metadata("design:type", product_entity_1.Product)
], Stock.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Stock.prototype, "warehouseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => warehouse_entity_1.Warehouse, warehouse => warehouse.stocks),
    (0, typeorm_1.JoinColumn)({ name: 'warehouseId' }),
    __metadata("design:type", warehouse_entity_1.Warehouse)
], Stock.prototype, "warehouse", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Stock.prototype, "locationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => location_entity_1.Location, location => location.stocks, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'locationId' }),
    __metadata("design:type", location_entity_1.Location)
], Stock.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Stock.prototype, "quantityOnHand", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Stock.prototype, "quantityReserved", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Stock.prototype, "quantityAvailable", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Stock.prototype, "quantityInTransit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Stock.prototype, "quantityDamaged", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: StockStatus,
        default: StockStatus.AVAILABLE,
    }),
    __metadata("design:type", String)
], Stock.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Stock.prototype, "averageCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Stock.prototype, "lastCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Stock.prototype, "lastReceivedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Stock.prototype, "lastIssuedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Stock.prototype, "expiryDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Stock.prototype, "manufactureDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Stock.prototype, "batchNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Stock.prototype, "serialNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Stock.prototype, "lotNumbers", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => stock_movement_entity_1.StockMovement, movement => movement.stock),
    __metadata("design:type", Array)
], Stock.prototype, "stockMovements", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Stock.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Stock.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Stock.prototype, "updatedAt", void 0);
exports.Stock = Stock = __decorate([
    (0, typeorm_1.Entity)('inventory_stock'),
    (0, typeorm_1.Index)(['productId', 'warehouseId', 'locationId'], { unique: true })
], Stock);
//# sourceMappingURL=stock.entity.js.map