import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CollectionStrategy } from '../entities/collection-strategy.entity';

@Injectable()
export class CollectionStrategyService {
  constructor(
    @InjectRepository(CollectionStrategy)
    private collectionStrategyRepository: Repository<CollectionStrategy>,
  ) {}

  async create(strategyData: Partial<CollectionStrategy>): Promise<CollectionStrategy> {
    const strategy = this.collectionStrategyRepository.create(strategyData);
    return this.collectionStrategyRepository.save(strategy);
  }

  async findAll(): Promise<CollectionStrategy[]> {
    return this.collectionStrategyRepository.find({
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<CollectionStrategy> {
    const strategy = await this.collectionStrategyRepository.findOne({
      where: { id },
    });

    if (!strategy) {
      throw new NotFoundException(`Collection strategy with ID ${id} not found`);
    }

    return strategy;
  }

  async update(id: string, updateData: Partial<CollectionStrategy>): Promise<CollectionStrategy> {
    await this.collectionStrategyRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const strategy = await this.findOne(id);
    await this.collectionStrategyRepository.remove(strategy);
  }

  async findByDebtRange(minAmount: number, maxAmount: number): Promise<CollectionStrategy[]> {
    return this.collectionStrategyRepository
      .createQueryBuilder('strategy')
      .where('strategy.minDebtAmount <= :maxAmount', { maxAmount })
      .andWhere('strategy.maxDebtAmount >= :minAmount', { minAmount })
      .orderBy('strategy.name', 'ASC')
      .getMany();
  }

  async findByDaysOverdue(daysOverdue: number): Promise<CollectionStrategy[]> {
    return this.collectionStrategyRepository
      .createQueryBuilder('strategy')
      .where('strategy.minDaysOverdue <= :daysOverdue', { daysOverdue })
      .andWhere('strategy.maxDaysOverdue >= :daysOverdue', { daysOverdue })
      .orderBy('strategy.name', 'ASC')
      .getMany();
  }

  async getRecommendedStrategy(debtAmount: number, daysOverdue: number): Promise<CollectionStrategy | null> {
    const strategies = await this.collectionStrategyRepository
      .createQueryBuilder('strategy')
      .where('strategy.minDebtAmount <= :debtAmount', { debtAmount })
      .andWhere('strategy.maxDebtAmount >= :debtAmount', { debtAmount })
      .andWhere('strategy.minDaysOverdue <= :daysOverdue', { daysOverdue })
      .andWhere('strategy.maxDaysOverdue >= :daysOverdue', { daysOverdue })
      .andWhere('strategy.isActive = :isActive', { isActive: true })
      .orderBy('strategy.name', 'ASC')
      .getMany();

    return strategies.length > 0 ? strategies[0] : null;
  }

  async activateStrategy(id: string): Promise<CollectionStrategy> {
    await this.collectionStrategyRepository.update(id, { isActive: true });
    return this.findOne(id);
  }

  async deactivateStrategy(id: string): Promise<CollectionStrategy> {
    await this.collectionStrategyRepository.update(id, { isActive: false });
    return this.findOne(id);
  }

  async getActiveStrategies(): Promise<CollectionStrategy[]> {
    return this.collectionStrategyRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  async createDefaultStrategies(): Promise<CollectionStrategy[]> {
    const defaultStrategies = [
      {
        name: 'Early Stage Collection',
        description: 'For debts 1-30 days overdue',
        minDebtAmount: 0,
        maxDebtAmount: 999999,
        minDaysOverdue: 1,
        maxDaysOverdue: 30,
        actions: [
          'Send friendly reminder email',
          'Make courtesy phone call',
          'Send first notice letter',
        ],
        escalationRules: 'Escalate if no response after 30 days',
        isActive: true,
      },
      {
        name: 'Mid Stage Collection',
        description: 'For debts 31-60 days overdue',
        minDebtAmount: 0,
        maxDebtAmount: 999999,
        minDaysOverdue: 31,
        maxDaysOverdue: 60,
        actions: [
          'Send formal demand letter',
          'Make multiple phone calls',
          'Offer payment plan',
        ],
        escalationRules: 'Escalate if no payment arrangement after 60 days',
        isActive: true,
      },
      {
        name: 'Late Stage Collection',
        description: 'For debts 61-90 days overdue',
        minDebtAmount: 0,
        maxDebtAmount: 999999,
        minDaysOverdue: 61,
        maxDaysOverdue: 90,
        actions: [
          'Send final demand letter',
          'Daily phone calls',
          'Consider legal action',
        ],
        escalationRules: 'Escalate to legal department after 90 days',
        isActive: true,
      },
      {
        name: 'High Value Collection',
        description: 'For high-value debts requiring special attention',
        minDebtAmount: 10000,
        maxDebtAmount: 999999,
        minDaysOverdue: 1,
        maxDaysOverdue: 999,
        actions: [
          'Immediate personal contact',
          'Senior agent assignment',
          'Flexible payment terms',
        ],
        escalationRules: 'Manager involvement from day 1',
        isActive: true,
      },
    ];

    const createdStrategies = [];
    for (const strategyData of defaultStrategies) {
      const existing = await this.collectionStrategyRepository.findOne({
        where: { name: strategyData.name },
      });
      
      if (!existing) {
        const strategy = await this.create(strategyData);
        createdStrategies.push(strategy);
      }
    }

    return createdStrategies;
  }

  async getStrategyEffectiveness(): Promise<any[]> {
    // This would require joining with collection cases to analyze effectiveness
    // For now, return basic strategy information
    const strategies = await this.findAll();
    
    return strategies.map(strategy => ({
      id: strategy.id,
      name: strategy.name,
      description: strategy.description,
      isActive: strategy.isActive,
      debtRange: `$${strategy.minDebtAmount} - $${strategy.maxDebtAmount}`,
      daysRange: `${strategy.minDaysOverdue} - ${strategy.maxDaysOverdue} days`,
      // TODO: Add effectiveness metrics when case data is available
      casesAssigned: 0,
      successRate: 0,
      averageResolutionTime: 0,
    }));
  }

  async assignStrategyToCase(caseId: string, strategyId: string): Promise<void> {
    // This would update the collection case with the assigned strategy
    // Implementation depends on the CollectionCase entity structure
    // For now, this is a placeholder
  }

  async getStrategyRecommendations(caseData: {
    debtAmount: number;
    daysOverdue: number;
    customerType?: string;
    previousPaymentHistory?: string;
  }): Promise<{
    primaryStrategy: CollectionStrategy | null;
    alternativeStrategies: CollectionStrategy[];
    recommendations: string[];
  }> {
    const primaryStrategy = await this.getRecommendedStrategy(
      caseData.debtAmount,
      caseData.daysOverdue
    );

    const alternativeStrategies = await this.collectionStrategyRepository
      .createQueryBuilder('strategy')
      .where('strategy.isActive = :isActive', { isActive: true })
      .andWhere('strategy.id != :primaryId', { 
        primaryId: primaryStrategy?.id || '00000000-0000-0000-0000-000000000000' 
      })
      .take(3)
      .getMany();

    const recommendations = [];
    
    if (caseData.debtAmount > 10000) {
      recommendations.push('Consider assigning to senior collection agent');
    }
    
    if (caseData.daysOverdue > 60) {
      recommendations.push('Escalate to legal review');
    }
    
    if (caseData.customerType === 'business') {
      recommendations.push('Contact during business hours');
    }

    return {
      primaryStrategy,
      alternativeStrategies,
      recommendations,
    };
  }
}
