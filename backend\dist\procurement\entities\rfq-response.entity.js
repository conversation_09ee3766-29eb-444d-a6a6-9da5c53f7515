"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RFQResponse = exports.ResponseStatus = void 0;
const typeorm_1 = require("typeorm");
const rfq_entity_1 = require("./rfq.entity");
var ResponseStatus;
(function (ResponseStatus) {
    ResponseStatus["DRAFT"] = "draft";
    ResponseStatus["SUBMITTED"] = "submitted";
    ResponseStatus["UNDER_REVIEW"] = "under_review";
    ResponseStatus["ACCEPTED"] = "accepted";
    ResponseStatus["REJECTED"] = "rejected";
    ResponseStatus["WITHDRAWN"] = "withdrawn";
})(ResponseStatus || (exports.ResponseStatus = ResponseStatus = {}));
let RFQResponse = class RFQResponse {
    id;
    rfqId;
    rfq;
    vendorId;
    status;
    submittedAt;
    totalPrice;
    currency;
    deliveryDays;
    proposal;
    lineItems;
    terms;
    attachments;
    evaluationScore;
    evaluationNotes;
    evaluatedBy;
    evaluatedAt;
    metadata;
    createdAt;
    updatedAt;
};
exports.RFQResponse = RFQResponse;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RFQResponse.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RFQResponse.prototype, "rfqId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => rfq_entity_1.RFQ, rfq => rfq.responses),
    (0, typeorm_1.JoinColumn)({ name: 'rfqId' }),
    __metadata("design:type", rfq_entity_1.RFQ)
], RFQResponse.prototype, "rfq", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RFQResponse.prototype, "vendorId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ResponseStatus,
        default: ResponseStatus.DRAFT,
    }),
    __metadata("design:type", String)
], RFQResponse.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], RFQResponse.prototype, "submittedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], RFQResponse.prototype, "totalPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'USD' }),
    __metadata("design:type", String)
], RFQResponse.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], RFQResponse.prototype, "deliveryDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RFQResponse.prototype, "proposal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], RFQResponse.prototype, "lineItems", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], RFQResponse.prototype, "terms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], RFQResponse.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], RFQResponse.prototype, "evaluationScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RFQResponse.prototype, "evaluationNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RFQResponse.prototype, "evaluatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], RFQResponse.prototype, "evaluatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], RFQResponse.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], RFQResponse.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], RFQResponse.prototype, "updatedAt", void 0);
exports.RFQResponse = RFQResponse = __decorate([
    (0, typeorm_1.Entity)('rfq_responses')
], RFQResponse);
//# sourceMappingURL=rfq-response.entity.js.map