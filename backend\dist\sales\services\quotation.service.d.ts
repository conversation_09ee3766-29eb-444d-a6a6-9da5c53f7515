import { Repository } from 'typeorm';
import { Quotation } from '../entities/quotation.entity';
import { QuotationItem } from '../entities/quotation-item.entity';
import { CreateQuotationDto } from '../dto/create-quotation.dto';
export declare class QuotationService {
    private quotationRepository;
    private quotationItemRepository;
    constructor(quotationRepository: Repository<Quotation>, quotationItemRepository: Repository<QuotationItem>);
    create(createQuotationDto: CreateQuotationDto, tenantId: string): Promise<Quotation>;
    findAll(tenantId: string): Promise<Quotation[]>;
    findOne(id: string, tenantId: string): Promise<Quotation>;
    update(id: string, updateQuotationDto: Partial<CreateQuotationDto>, tenantId: string): Promise<Quotation>;
    remove(id: string, tenantId: string): Promise<void>;
    updateStatus(id: string, status: string, tenantId: string): Promise<Quotation>;
    convertToInvoice(id: string, tenantId: string): Promise<{
        success: boolean;
        invoiceId?: string;
    }>;
    getQuotationStats(tenantId: string): Promise<{
        totalQuotations: number;
        acceptedQuotations: number;
        pendingQuotations: number;
        totalValue: number;
        conversionRate: number;
    }>;
    private calculateQuotationTotals;
    private calculateItemTax;
    private generateQuotationNumber;
}
