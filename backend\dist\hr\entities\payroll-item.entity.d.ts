import { Payroll } from './payroll.entity';
export declare enum PayrollItemType {
    EARNING = "earning",
    DEDUCTION = "deduction",
    BENEFIT = "benefit",
    TAX = "tax"
}
export declare enum PayrollItemCategory {
    BASIC_SALARY = "basic_salary",
    OVERTIME = "overtime",
    BONUS = "bonus",
    COMMISSION = "commission",
    ALLOWANCE = "allowance",
    INCOME_TAX = "income_tax",
    SOCIAL_SECURITY = "social_security",
    MEDICARE = "medicare",
    HEALTH_INSURANCE = "health_insurance",
    RETIREMENT = "retirement",
    LOAN_REPAYMENT = "loan_repayment",
    HEALTH_BENEFIT = "health_benefit",
    DENTAL_BENEFIT = "dental_benefit",
    VISION_BENEFIT = "vision_benefit",
    LIFE_INSURANCE = "life_insurance",
    OTHER = "other"
}
export declare class PayrollItem {
    id: string;
    payrollId: string;
    payroll: Payroll;
    name: string;
    code: string;
    type: PayrollItemType;
    category: PayrollItemCategory;
    amount: number;
    rate: number;
    hours: number;
    percentage: number;
    isTaxable: boolean;
    isStatutory: boolean;
    description: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
