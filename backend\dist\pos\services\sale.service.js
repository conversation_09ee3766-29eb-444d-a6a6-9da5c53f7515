"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sale_entity_1 = require("../entities/sale.entity");
const sale_item_entity_1 = require("../entities/sale-item.entity");
const payment_entity_1 = require("../entities/payment.entity");
let SaleService = class SaleService {
    saleRepository;
    saleItemRepository;
    paymentRepository;
    constructor(saleRepository, saleItemRepository, paymentRepository) {
        this.saleRepository = saleRepository;
        this.saleItemRepository = saleItemRepository;
        this.paymentRepository = paymentRepository;
    }
    async create(saleData) {
        const saleNumber = await this.generateSaleNumber();
        const sale = this.saleRepository.create({
            ...saleData,
            saleNumber,
            status: sale_entity_1.SaleStatus.PENDING,
            saleDate: new Date(),
        });
        return this.saleRepository.save(sale);
    }
    async findAll() {
        return this.saleRepository.find({
            relations: ['customer', 'cashier', 'items', 'items.product', 'payments'],
            order: { saleDate: 'DESC' },
        });
    }
    async findOne(id) {
        const sale = await this.saleRepository.findOne({
            where: { id },
            relations: ['customer', 'cashier', 'items', 'items.product', 'payments'],
        });
        if (!sale) {
            throw new common_1.NotFoundException(`Sale with ID ${id} not found`);
        }
        return sale;
    }
    async update(id, updateData) {
        await this.saleRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const sale = await this.findOne(id);
        if (sale.status === sale_entity_1.SaleStatus.COMPLETED) {
            throw new Error('Cannot delete completed sale');
        }
        await this.saleRepository.remove(sale);
    }
    async addItem(saleId, itemData) {
        const sale = await this.findOne(saleId);
        if (sale.status === sale_entity_1.SaleStatus.COMPLETED) {
            throw new Error('Cannot add items to completed sale');
        }
        const item = this.saleItemRepository.create({
            ...itemData,
            saleId: sale.id,
        });
        const savedItem = await this.saleItemRepository.save(item);
        await this.recalculateSaleTotals(saleId);
        return savedItem;
    }
    async updateItem(itemId, updateData) {
        const item = await this.saleItemRepository.findOne({
            where: { id: itemId },
            relations: ['sale'],
        });
        if (!item) {
            throw new common_1.NotFoundException(`Sale item with ID ${itemId} not found`);
        }
        if (item.sale.status === sale_entity_1.SaleStatus.COMPLETED) {
            throw new Error('Cannot update items in completed sale');
        }
        await this.saleItemRepository.update(itemId, updateData);
        await this.recalculateSaleTotals(item.sale.id);
        return this.saleItemRepository.findOne({
            where: { id: itemId },
            relations: ['product'],
        });
    }
    async removeItem(itemId) {
        const item = await this.saleItemRepository.findOne({
            where: { id: itemId },
            relations: ['sale'],
        });
        if (!item) {
            throw new common_1.NotFoundException(`Sale item with ID ${itemId} not found`);
        }
        if (item.sale.status === sale_entity_1.SaleStatus.COMPLETED) {
            throw new Error('Cannot remove items from completed sale');
        }
        const saleId = item.sale.id;
        await this.saleItemRepository.remove(item);
        await this.recalculateSaleTotals(saleId);
    }
    async recalculateSaleTotals(saleId) {
        const sale = await this.findOne(saleId);
        const subtotal = sale.items.reduce((sum, item) => {
            return sum + (item.quantity * item.unitPrice);
        }, 0);
        const discountAmount = subtotal * (sale.discountPercentage || 0) / 100;
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = taxableAmount * (sale.taxRate || 0) / 100;
        const total = taxableAmount + taxAmount;
        await this.saleRepository.update(saleId, {
            subtotal,
            discountAmount,
            taxAmount,
            total,
        });
    }
    async addPayment(saleId, paymentData) {
        const sale = await this.findOne(saleId);
        const payment = this.paymentRepository.create({
            ...paymentData,
            saleId: sale.id,
            paymentDate: new Date(),
        });
        const savedPayment = await this.paymentRepository.save(payment);
        await this.checkSalePaymentStatus(saleId);
        return savedPayment;
    }
    async checkSalePaymentStatus(saleId) {
        const sale = await this.findOne(saleId);
        const totalPaid = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
        const amountDue = sale.total - totalPaid;
        let status = sale.status;
        if (amountDue <= 0) {
            status = sale_entity_1.SaleStatus.COMPLETED;
        }
        else if (totalPaid > 0) {
            status = sale_entity_1.SaleStatus.PARTIALLY_PAID;
        }
        await this.saleRepository.update(saleId, {
            amountPaid: totalPaid,
            amountDue,
            status,
        });
    }
    async completeSale(saleId) {
        const sale = await this.findOne(saleId);
        if (sale.amountDue > 0) {
            throw new Error('Cannot complete sale with outstanding balance');
        }
        await this.saleRepository.update(saleId, {
            status: sale_entity_1.SaleStatus.COMPLETED,
            completedAt: new Date(),
        });
        return this.findOne(saleId);
    }
    async voidSale(saleId, reason) {
        const sale = await this.findOne(saleId);
        if (sale.status === sale_entity_1.SaleStatus.COMPLETED) {
            throw new Error('Cannot void completed sale. Use refund instead.');
        }
        await this.saleRepository.update(saleId, {
            status: sale_entity_1.SaleStatus.VOIDED,
            voidReason: reason,
            voidedAt: new Date(),
        });
        return this.findOne(saleId);
    }
    async refundSale(saleId, refundAmount, reason) {
        const sale = await this.findOne(saleId);
        if (sale.status !== sale_entity_1.SaleStatus.COMPLETED) {
            throw new Error('Can only refund completed sales');
        }
        if (refundAmount > sale.total) {
            throw new Error('Refund amount cannot exceed sale total');
        }
        await this.saleRepository.update(saleId, {
            status: sale_entity_1.SaleStatus.REFUNDED,
            refundAmount,
            refundReason: reason,
            refundedAt: new Date(),
        });
        return this.findOne(saleId);
    }
    async findByCustomer(customerId) {
        return this.saleRepository.find({
            where: { customerId },
            relations: ['items', 'payments'],
            order: { saleDate: 'DESC' },
        });
    }
    async findByCashier(cashierId) {
        return this.saleRepository.find({
            where: { cashierId },
            relations: ['customer', 'items'],
            order: { saleDate: 'DESC' },
        });
    }
    async findByDateRange(startDate, endDate) {
        return this.saleRepository
            .createQueryBuilder('sale')
            .leftJoinAndSelect('sale.customer', 'customer')
            .leftJoinAndSelect('sale.cashier', 'cashier')
            .leftJoinAndSelect('sale.items', 'items')
            .leftJoinAndSelect('sale.payments', 'payments')
            .where('sale.saleDate BETWEEN :startDate AND :endDate', { startDate, endDate })
            .orderBy('sale.saleDate', 'DESC')
            .getMany();
    }
    async getSalesStatistics(startDate, endDate) {
        const query = this.saleRepository.createQueryBuilder('sale');
        if (startDate && endDate) {
            query.where('sale.saleDate BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        const totalSales = await query.getCount();
        const completedSales = await query
            .clone()
            .andWhere('sale.status = :status', { status: sale_entity_1.SaleStatus.COMPLETED })
            .getCount();
        const salesData = await query
            .clone()
            .andWhere('sale.status = :status', { status: sale_entity_1.SaleStatus.COMPLETED })
            .select([
            'SUM(sale.total) as totalRevenue',
            'AVG(sale.total) as averageSaleValue',
            'COUNT(DISTINCT sale.customerId) as uniqueCustomers',
        ])
            .getRawOne();
        return {
            totalSales,
            completedSales,
            totalRevenue: parseFloat(salesData.totalRevenue) || 0,
            averageSaleValue: parseFloat(salesData.averageSaleValue) || 0,
            uniqueCustomers: parseInt(salesData.uniqueCustomers) || 0,
        };
    }
    async getTopSellingProducts(limit = 10) {
        const result = await this.saleItemRepository
            .createQueryBuilder('item')
            .leftJoin('item.product', 'product')
            .leftJoin('item.sale', 'sale')
            .where('sale.status = :status', { status: sale_entity_1.SaleStatus.COMPLETED })
            .select([
            'product.id as productId',
            'product.name as productName',
            'SUM(item.quantity) as totalQuantity',
            'SUM(item.quantity * item.unitPrice) as totalRevenue',
        ])
            .groupBy('product.id, product.name')
            .orderBy('totalQuantity', 'DESC')
            .limit(limit)
            .getRawMany();
        return result.map(row => ({
            productId: row.productId,
            productName: row.productName,
            totalQuantity: parseInt(row.totalQuantity),
            totalRevenue: parseFloat(row.totalRevenue),
        }));
    }
    async generateSaleNumber() {
        const count = await this.saleRepository.count();
        const sequence = (count + 1).toString().padStart(8, '0');
        const year = new Date().getFullYear();
        return `SAL-${year}-${sequence}`;
    }
    async getDashboardMetrics() {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);
        const todayStats = await this.getSalesStatistics(startOfDay, endOfDay);
        const overallStats = await this.getSalesStatistics();
        return {
            today: todayStats,
            overall: overallStats,
        };
    }
};
exports.SaleService = SaleService;
exports.SaleService = SaleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sale_entity_1.Sale)),
    __param(1, (0, typeorm_1.InjectRepository)(sale_item_entity_1.SaleItem)),
    __param(2, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], SaleService);
//# sourceMappingURL=sale.service.js.map