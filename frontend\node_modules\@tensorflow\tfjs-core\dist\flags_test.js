/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as device_util from './device_util';
import * as tf from './index';
describe('DEBUG', () => {
    beforeEach(() => {
        tf.env().reset();
        spyOn(console, 'warn').and.callFake((msg) => { });
    });
    afterAll(() => tf.env().reset());
    it('disabled by default', () => {
        expect(tf.env().getBool('DEBUG')).toBe(false);
    });
    it('warns when enabled', () => {
        const consoleWarnSpy = console.warn;
        tf.env().set('DEBUG', true);
        expect(consoleWarnSpy.calls.count()).toBe(1);
        expect(consoleWarnSpy.calls.first().args[0]
            .startsWith('Debugging mode is ON. '))
            .toBe(true);
        expect(tf.env().getBool('DEBUG')).toBe(true);
        expect(consoleWarnSpy.calls.count()).toBe(1);
    });
});
// TODO (yassogba) figure out why this spy is not working / fix this test.
describe('IS_BROWSER', () => {
    let isBrowser;
    beforeEach(() => {
        tf.env().reset();
        spyOn(device_util, 'isBrowser').and.callFake(() => isBrowser);
    });
    afterAll(() => tf.env().reset());
    // tslint:disable-next-line: ban
    xit('isBrowser: true', () => {
        isBrowser = true;
        expect(tf.env().getBool('IS_BROWSER')).toBe(true);
    });
    // tslint:disable-next-line: ban
    xit('isBrowser: false', () => {
        isBrowser = false;
        expect(tf.env().getBool('IS_BROWSER')).toBe(false);
    });
});
describe('PROD', () => {
    beforeEach(() => tf.env().reset());
    afterAll(() => tf.env().reset());
    it('disabled by default', () => {
        expect(tf.env().getBool('PROD')).toBe(false);
    });
});
describe('TENSORLIKE_CHECK_SHAPE_CONSISTENCY', () => {
    beforeEach(() => tf.env().reset());
    afterAll(() => tf.env().reset());
    it('disabled when debug is disabled', () => {
        tf.env().set('DEBUG', false);
        expect(tf.env().getBool('TENSORLIKE_CHECK_SHAPE_CONSISTENCY')).toBe(false);
    });
    it('enabled when debug is enabled', () => {
        // Silence debug warnings.
        spyOn(console, 'warn');
        tf.enableDebugMode();
        expect(tf.env().getBool('TENSORLIKE_CHECK_SHAPE_CONSISTENCY')).toBe(true);
    });
});
describe('DEPRECATION_WARNINGS_ENABLED', () => {
    beforeEach(() => tf.env().reset());
    afterAll(() => tf.env().reset());
    it('enabled by default', () => {
        expect(tf.env().getBool('DEPRECATION_WARNINGS_ENABLED')).toBe(true);
    });
});
describe('IS_TEST', () => {
    beforeEach(() => tf.env().reset());
    afterAll(() => tf.env().reset());
    it('disabled by default', () => {
        expect(tf.env().getBool('IS_TEST')).toBe(false);
    });
});
describe('async flags test', () => {
    const asyncFlagName = 'ASYNC_FLAG';
    beforeEach(() => tf.env().registerFlag(asyncFlagName, async () => true));
    afterEach(() => tf.env().reset());
    it('evaluating async flag works', async () => {
        const flagVal = await tf.env().getAsync(asyncFlagName);
        expect(flagVal).toBe(true);
    });
    it('evaluating async flag synchronously fails', async () => {
        expect(() => tf.env().get(asyncFlagName)).toThrow();
    });
});
//# sourceMappingURL=data:application/json;base64,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