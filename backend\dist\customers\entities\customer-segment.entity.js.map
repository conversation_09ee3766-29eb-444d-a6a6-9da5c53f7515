{"version": 3, "file": "customer-segment.entity.js", "sourceRoot": "", "sources": ["../../../src/customers/entities/customer-segment.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAMiB;AAEjB,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,0CAA2B,CAAA;IAC3B,wCAAyB,CAAA;IACzB,wCAAyB,CAAA;IACzB,8CAA+B,CAAA;IAC/B,8CAA+B,CAAA;IAC/B,gCAAiB,CAAA;AACnB,CAAC,EAPW,WAAW,2BAAX,WAAW,QAOtB;AAGM,IAAM,eAAe,GAArB,MAAM,eAAe;IAE1B,EAAE,CAAS;IAGX,IAAI,CAAS;IAGb,WAAW,CAAS;IAOpB,IAAI,CAAc;IAGlB,QAAQ,CAAM;IAGd,aAAa,CAAS;IAGtB,QAAQ,CAAU;IAGlB,YAAY,CAAU;IAGtB,WAAW,CAAO;IAGlB,SAAS,CAAS;IAGlB,IAAI,CAAW;IAGf,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AA9CY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;6CACX;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACrB;AAOpB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,WAAW,CAAC,MAAM;KAC5B,CAAC;;6CACgB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDACX;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sDACd;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iDACR;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;qDACL;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACjC,IAAI;oDAAC;AAGlB;IADC,IAAA,gBAAM,GAAE;;kDACS;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC1B;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;0BA7CL,eAAe;IAD3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;GACf,eAAe,CA8C3B"}