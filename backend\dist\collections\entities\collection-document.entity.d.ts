import { CollectionCase } from './collection-case.entity';
export declare enum DocumentType {
    DEMAND_LETTER = "demand_letter",
    PAYMENT_AGREEMENT = "payment_agreement",
    SETTLEMENT_AGREEMENT = "settlement_agreement",
    LEGAL_NOTICE = "legal_notice",
    COURT_DOCUMENT = "court_document",
    PAYMENT_RECEIPT = "payment_receipt",
    CORRESPONDENCE = "correspondence",
    DISPUTE_DOCUMENT = "dispute_document",
    IDENTIFICATION = "identification",
    FINANCIAL_STATEMENT = "financial_statement",
    OTHER = "other"
}
export declare class CollectionDocument {
    id: string;
    caseId: string;
    case: CollectionCase;
    name: string;
    description: string;
    type: DocumentType;
    fileName: string;
    originalName: string;
    filePath: string;
    mimeType: string;
    fileSize: number;
    uploadedBy: string;
    documentDate: Date;
    tags: string[];
    isActive: boolean;
    isConfidential: boolean;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
