export declare enum AgentStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    ON_BREAK = "on_break",
    TRAINING = "training",
    SUSPENDED = "suspended"
}
export declare class CollectionAgent {
    id: string;
    userId: string;
    agentNumber: string;
    name: string;
    email: string;
    phone: string;
    status: AgentStatus;
    hireDate: Date;
    maxCases: number;
    currentCases: number;
    totalCollected: number;
    collectionRate: number;
    totalCalls: number;
    successfulCalls: number;
    callSuccessRate: number;
    specializations: string[];
    permissions: string[];
    workingHours: any;
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
