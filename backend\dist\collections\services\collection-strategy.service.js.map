{"version": 3, "file": "collection-strategy.service.js", "sourceRoot": "", "sources": ["../../../src/collections/services/collection-strategy.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,uFAA4E;AAGrE,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAG1B;IAFV,YAEU,4BAA4D;QAA5D,iCAA4B,GAA5B,4BAA4B,CAAgC;IACnE,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,YAAyC;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;YAC5C,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;YAC/D,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,+BAA+B,EAAE,YAAY,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAuC;QAC9D,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACxC,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,SAAiB;QACxD,OAAO,IAAI,CAAC,4BAA4B;aACrC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,KAAK,CAAC,sCAAsC,EAAE,EAAE,SAAS,EAAE,CAAC;aAC5D,QAAQ,CAAC,sCAAsC,EAAE,EAAE,SAAS,EAAE,CAAC;aAC/D,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC;aAC/B,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,WAAmB;QACzC,OAAO,IAAI,CAAC,4BAA4B;aACrC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,KAAK,CAAC,yCAAyC,EAAE,EAAE,WAAW,EAAE,CAAC;aACjE,QAAQ,CAAC,yCAAyC,EAAE,EAAE,WAAW,EAAE,CAAC;aACpE,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC;aAC/B,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,UAAkB,EAAE,WAAmB;QAClE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,4BAA4B;aACvD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,KAAK,CAAC,uCAAuC,EAAE,EAAE,UAAU,EAAE,CAAC;aAC9D,QAAQ,CAAC,uCAAuC,EAAE,EAAE,UAAU,EAAE,CAAC;aACjE,QAAQ,CAAC,yCAAyC,EAAE,EAAE,WAAW,EAAE,CAAC;aACpE,QAAQ,CAAC,yCAAyC,EAAE,EAAE,WAAW,EAAE,CAAC;aACpE,QAAQ,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aAC7D,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC;aAC/B,OAAO,EAAE,CAAC;QAEb,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU;QAC/B,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACjC,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;YAC5C,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzB,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB;QAC3B,MAAM,iBAAiB,GAAG;YACxB;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,6BAA6B;gBAC1C,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,MAAM;gBACrB,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,EAAE;gBAClB,OAAO,EAAE;oBACP,8BAA8B;oBAC9B,0BAA0B;oBAC1B,0BAA0B;iBAC3B;gBACD,eAAe,EAAE,uCAAuC;gBACxD,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,8BAA8B;gBAC3C,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,MAAM;gBACrB,cAAc,EAAE,EAAE;gBAClB,cAAc,EAAE,EAAE;gBAClB,OAAO,EAAE;oBACP,2BAA2B;oBAC3B,2BAA2B;oBAC3B,oBAAoB;iBACrB;gBACD,eAAe,EAAE,kDAAkD;gBACnE,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,8BAA8B;gBAC3C,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,MAAM;gBACrB,cAAc,EAAE,EAAE;gBAClB,cAAc,EAAE,EAAE;gBAClB,OAAO,EAAE;oBACP,0BAA0B;oBAC1B,mBAAmB;oBACnB,uBAAuB;iBACxB;gBACD,eAAe,EAAE,4CAA4C;gBAC7D,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,kDAAkD;gBAC/D,aAAa,EAAE,KAAK;gBACpB,aAAa,EAAE,MAAM;gBACrB,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE;oBACP,4BAA4B;oBAC5B,yBAAyB;oBACzB,wBAAwB;iBACzB;gBACD,eAAe,EAAE,gCAAgC;gBACjD,QAAQ,EAAE,IAAI;aACf;SACF,CAAC;QAEF,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAC7B,KAAK,MAAM,YAAY,IAAI,iBAAiB,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE;aACnC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACjD,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,wBAAwB;QAG5B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QAExC,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACjC,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,SAAS,EAAE,IAAI,QAAQ,CAAC,aAAa,OAAO,QAAQ,CAAC,aAAa,EAAE;YACpE,SAAS,EAAE,GAAG,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,cAAc,OAAO;YAEzE,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;YACd,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,UAAkB;IAI7D,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,QAKhC;QAKC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACvD,QAAQ,CAAC,UAAU,EACnB,QAAQ,CAAC,WAAW,CACrB,CAAC;QAEF,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,4BAA4B;aAClE,kBAAkB,CAAC,UAAU,CAAC;aAC9B,KAAK,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aAC1D,QAAQ,CAAC,2BAA2B,EAAE;YACrC,SAAS,EAAE,eAAe,EAAE,EAAE,IAAI,sCAAsC;SACzE,CAAC;aACD,IAAI,CAAC,CAAC,CAAC;aACP,OAAO,EAAE,CAAC;QAEb,MAAM,eAAe,GAAG,EAAE,CAAC;QAE3B,IAAI,QAAQ,CAAC,UAAU,GAAG,KAAK,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,QAAQ,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YAC9B,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,QAAQ,CAAC,YAAY,KAAK,UAAU,EAAE,CAAC;YACzC,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACxD,CAAC;QAED,OAAO;YACL,eAAe;YACf,qBAAqB;YACrB,eAAe;SAChB,CAAC;IACJ,CAAC;CACF,CAAA;AA5OY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;qCACC,oBAAU;GAHvC,yBAAyB,CA4OrC"}