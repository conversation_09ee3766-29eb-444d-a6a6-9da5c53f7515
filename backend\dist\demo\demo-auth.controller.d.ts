import { JwtService } from '@nestjs/jwt';
export declare class DemoAuthController {
    private jwtService;
    constructor(jwtService: JwtService);
    registerCompany(registerDto: any): Promise<{
        user: {
            id: string;
            email: any;
            firstName: any;
            lastName: any;
            role: string;
            company: {
                id: string;
                name: any;
                slug: any;
                tenantId: string;
            };
        };
        token: string;
    }>;
    login(loginDto: any): Promise<{
        user: {
            id: any;
            email: any;
            firstName: any;
            lastName: any;
            role: any;
            company: {
                id: any;
                name: any;
                slug: any;
                tenantId: any;
            };
        };
        token: string;
    }>;
    getProfile(req: any): Promise<{
        user: {
            userId: string;
            email: string;
            role: string;
            companyId: string;
            tenantId: string;
        };
    }>;
    logout(): Promise<{
        message: string;
    }>;
}
