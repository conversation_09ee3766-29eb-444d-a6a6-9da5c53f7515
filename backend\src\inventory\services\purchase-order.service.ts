import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PurchaseOrder } from '../entities/purchase-order.entity';
import { PurchaseOrderItem } from '../entities/purchase-order-item.entity';
import { StockService } from './stock.service';

@Injectable()
export class PurchaseOrderService {
  constructor(
    @InjectRepository(PurchaseOrder)
    private purchaseOrderRepository: Repository<PurchaseOrder>,
    @InjectRepository(PurchaseOrderItem)
    private purchaseOrderItemRepository: Repository<PurchaseOrderItem>,
    private stockService: StockService,
  ) {}

  async create(orderData: Partial<PurchaseOrder>): Promise<PurchaseOrder> {
    const order = this.purchaseOrderRepository.create(orderData);
    return this.purchaseOrderRepository.save(order);
  }

  async findAll(): Promise<PurchaseOrder[]> {
    return this.purchaseOrderRepository.find({
      relations: ['supplier', 'items', 'items.product'],
      order: { orderDate: 'DESC' },
    });
  }

  async findOne(id: string): Promise<PurchaseOrder> {
    const order = await this.purchaseOrderRepository.findOne({
      where: { id },
      relations: ['supplier', 'items', 'items.product'],
    });

    if (!order) {
      throw new NotFoundException(`Purchase order with ID ${id} not found`);
    }

    return order;
  }

  async update(id: string, updateData: Partial<PurchaseOrder>): Promise<PurchaseOrder> {
    await this.purchaseOrderRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const order = await this.findOne(id);
    await this.purchaseOrderRepository.remove(order);
  }

  async addItem(orderId: string, itemData: Partial<PurchaseOrderItem>): Promise<PurchaseOrderItem> {
    const order = await this.findOne(orderId);
    
    const item = this.purchaseOrderItemRepository.create({
      ...itemData,
      purchaseOrderId: order.id,
    });

    const savedItem = await this.purchaseOrderItemRepository.save(item);
    
    // Recalculate order totals
    await this.recalculateOrderTotals(orderId);
    
    return savedItem;
  }

  async updateItem(itemId: string, updateData: Partial<PurchaseOrderItem>): Promise<PurchaseOrderItem> {
    const item = await this.purchaseOrderItemRepository.findOne({
      where: { id: itemId },
      relations: ['purchaseOrder'],
    });

    if (!item) {
      throw new NotFoundException(`Purchase order item with ID ${itemId} not found`);
    }

    await this.purchaseOrderItemRepository.update(itemId, updateData);
    
    // Recalculate order totals
    await this.recalculateOrderTotals(item.purchaseOrder.id);
    
    return this.purchaseOrderItemRepository.findOne({
      where: { id: itemId },
      relations: ['product'],
    });
  }

  async removeItem(itemId: string): Promise<void> {
    const item = await this.purchaseOrderItemRepository.findOne({
      where: { id: itemId },
      relations: ['purchaseOrder'],
    });

    if (!item) {
      throw new NotFoundException(`Purchase order item with ID ${itemId} not found`);
    }

    const orderId = item.purchaseOrder.id;
    await this.purchaseOrderItemRepository.remove(item);
    
    // Recalculate order totals
    await this.recalculateOrderTotals(orderId);
  }

  private async recalculateOrderTotals(orderId: string): Promise<void> {
    const order = await this.findOne(orderId);
    
    const subtotal = order.items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);

    const taxAmount = subtotal * (order.taxRate || 0) / 100;
    const total = subtotal + taxAmount + (order.shippingCost || 0);

    await this.purchaseOrderRepository.update(orderId, {
      subtotal,
      taxAmount,
      total,
    });
  }

  async receiveOrder(orderId: string, warehouseId: string): Promise<PurchaseOrder> {
    const order = await this.findOne(orderId);
    
    if (order.status !== 'PENDING') {
      throw new Error('Only pending orders can be received');
    }

    // Update stock for each item
    for (const item of order.items) {
      await this.stockService.adjustStock(
        item.productId,
        warehouseId,
        item.quantity,
        `Received from PO ${order.orderNumber}`,
      );
    }

    // Update order status
    await this.purchaseOrderRepository.update(orderId, {
      status: 'RECEIVED',
      receivedDate: new Date(),
    });

    return this.findOne(orderId);
  }

  async partialReceive(
    orderId: string,
    warehouseId: string,
    receivedItems: Array<{ itemId: string; receivedQuantity: number }>,
  ): Promise<PurchaseOrder> {
    const order = await this.findOne(orderId);
    
    for (const receivedItem of receivedItems) {
      const orderItem = order.items.find(item => item.id === receivedItem.itemId);
      if (orderItem && receivedItem.receivedQuantity > 0) {
        await this.stockService.adjustStock(
          orderItem.productId,
          warehouseId,
          receivedItem.receivedQuantity,
          `Partial receipt from PO ${order.orderNumber}`,
        );

        // Update received quantity on the item
        await this.purchaseOrderItemRepository.update(receivedItem.itemId, {
          receivedQuantity: (orderItem.receivedQuantity || 0) + receivedItem.receivedQuantity,
        });
      }
    }

    // Check if order is fully received
    const updatedOrder = await this.findOne(orderId);
    const isFullyReceived = updatedOrder.items.every(
      item => (item.receivedQuantity || 0) >= item.quantity
    );

    if (isFullyReceived) {
      await this.purchaseOrderRepository.update(orderId, {
        status: 'RECEIVED',
        receivedDate: new Date(),
      });
    } else {
      await this.purchaseOrderRepository.update(orderId, {
        status: 'PARTIALLY_RECEIVED',
      });
    }

    return this.findOne(orderId);
  }

  async cancelOrder(orderId: string, reason: string): Promise<PurchaseOrder> {
    await this.purchaseOrderRepository.update(orderId, {
      status: 'CANCELLED',
      notes: reason,
    });

    return this.findOne(orderId);
  }

  async findBySupplier(supplierId: string): Promise<PurchaseOrder[]> {
    return this.purchaseOrderRepository.find({
      where: { supplierId },
      relations: ['items', 'items.product'],
      order: { orderDate: 'DESC' },
    });
  }

  async findByStatus(status: string): Promise<PurchaseOrder[]> {
    return this.purchaseOrderRepository.find({
      where: { status },
      relations: ['supplier', 'items'],
      order: { orderDate: 'DESC' },
    });
  }

  async getPurchaseOrderStatistics(): Promise<any> {
    const totalOrders = await this.purchaseOrderRepository.count();
    const pendingOrders = await this.purchaseOrderRepository.count({ where: { status: 'PENDING' } });
    const receivedOrders = await this.purchaseOrderRepository.count({ where: { status: 'RECEIVED' } });
    const cancelledOrders = await this.purchaseOrderRepository.count({ where: { status: 'CANCELLED' } });

    const totalValue = await this.purchaseOrderRepository
      .createQueryBuilder('po')
      .select('SUM(po.total)', 'totalValue')
      .getRawOne();

    return {
      totalOrders,
      pendingOrders,
      receivedOrders,
      cancelledOrders,
      totalValue: parseFloat(totalValue.totalValue) || 0,
    };
  }

  async generateOrderNumber(): Promise<string> {
    const count = await this.purchaseOrderRepository.count();
    const sequence = (count + 1).toString().padStart(6, '0');
    const year = new Date().getFullYear();
    return `PO-${year}-${sequence}`;
  }
}
