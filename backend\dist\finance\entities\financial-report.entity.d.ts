export declare enum ReportType {
    BALANCE_SHEET = "balance_sheet",
    INCOME_STATEMENT = "income_statement",
    CASH_FLOW = "cash_flow",
    TRIAL_BALANCE = "trial_balance",
    BUDGET_VARIANCE = "budget_variance",
    EXPENSE_REPORT = "expense_report",
    REVENUE_REPORT = "revenue_report",
    TAX_REPORT = "tax_report",
    CUSTOM = "custom"
}
export declare enum ReportStatus {
    GENERATING = "generating",
    COMPLETED = "completed",
    FAILED = "failed",
    SCHEDULED = "scheduled"
}
export declare enum ReportFormat {
    PDF = "pdf",
    EXCEL = "excel",
    CSV = "csv",
    JSON = "json"
}
export declare class FinancialReport {
    id: string;
    name: string;
    description: string;
    type: ReportType;
    status: ReportStatus;
    format: ReportFormat;
    startDate: Date;
    endDate: Date;
    filters: any;
    parameters: any;
    reportData: string;
    filePath: string;
    fileSize: number;
    generatedBy: string;
    generatedAt: Date;
    expiresAt: Date;
    isScheduled: boolean;
    scheduleFrequency: string;
    scheduleConfig: any;
    emailRecipients: string[];
    downloadCount: number;
    lastDownloadedAt: Date;
    errorMessage: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
