// @flow
// This file is generated automatically by `scripts/build/typings.js`. Please, don't change it.

export type Locale = {
  code?: string,
  formatDistance?: (...args: Array<any>) => any,
  formatRelative?: (...args: Array<any>) => any,
  localize?: {
    ordinalNumber: (...args: Array<any>) => any,
    era: (...args: Array<any>) => any,
    quarter: (...args: Array<any>) => any,
    month: (...args: Array<any>) => any,
    day: (...args: Array<any>) => any,
    dayPeriod: (...args: Array<any>) => any,
  },
  formatLong?: {
    date: (...args: Array<any>) => any,
    time: (...args: Array<any>) => any,
    dateTime: (...args: Array<any>) => any,
  },
  match?: {
    ordinalNumber: (...args: Array<any>) => any,
    era: (...args: Array<any>) => any,
    quarter: (...args: Array<any>) => any,
    month: (...args: Array<any>) => any,
    day: (...args: Array<any>) => any,
    dayPeriod: (...args: Array<any>) => any,
  },
  options?: {
    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
  },
}

declare module.exports: {
  af: Locale,
  ar: Locale,
  arDZ: Locale,
  arEG: Locale,
  arMA: Locale,
  arSA: Locale,
  arTN: Locale,
  az: Locale,
  be: Locale,
  beTarask: Locale,
  bg: Locale,
  bn: Locale,
  bs: Locale,
  ca: Locale,
  cs: Locale,
  cy: Locale,
  da: Locale,
  de: Locale,
  deAT: Locale,
  el: Locale,
  enAU: Locale,
  enCA: Locale,
  enGB: Locale,
  enIE: Locale,
  enIN: Locale,
  enNZ: Locale,
  enUS: Locale,
  enZA: Locale,
  eo: Locale,
  es: Locale,
  et: Locale,
  eu: Locale,
  faIR: Locale,
  fi: Locale,
  fr: Locale,
  frCA: Locale,
  frCH: Locale,
  fy: Locale,
  gd: Locale,
  gl: Locale,
  gu: Locale,
  he: Locale,
  hi: Locale,
  hr: Locale,
  ht: Locale,
  hu: Locale,
  hy: Locale,
  id: Locale,
  is: Locale,
  it: Locale,
  itCH: Locale,
  ja: Locale,
  jaHira: Locale,
  ka: Locale,
  kk: Locale,
  km: Locale,
  kn: Locale,
  ko: Locale,
  lb: Locale,
  lt: Locale,
  lv: Locale,
  mk: Locale,
  mn: Locale,
  ms: Locale,
  mt: Locale,
  nb: Locale,
  nl: Locale,
  nlBE: Locale,
  nn: Locale,
  oc: Locale,
  pl: Locale,
  pt: Locale,
  ptBR: Locale,
  ro: Locale,
  ru: Locale,
  sk: Locale,
  sl: Locale,
  sq: Locale,
  sr: Locale,
  srLatn: Locale,
  sv: Locale,
  ta: Locale,
  te: Locale,
  th: Locale,
  tr: Locale,
  ug: Locale,
  uk: Locale,
  uz: Locale,
  uzCyrl: Locale,
  vi: Locale,
  zhCN: Locale,
  zhHK: Locale,
  zhTW: Locale,
}
