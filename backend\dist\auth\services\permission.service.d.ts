import { Repository } from 'typeorm';
import { Permission, PermissionModule } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';
export declare class PermissionService {
    private permissionRepository;
    private roleRepository;
    constructor(permissionRepository: Repository<Permission>, roleRepository: Repository<Role>);
    createDefaultPermissions(): Promise<Permission[]>;
    private getDefaultPermissions;
    findAll(): Promise<Permission[]>;
    findByModule(module: PermissionModule): Promise<Permission[]>;
    getPermissionsByRole(roleId: string): Promise<Permission[]>;
    getGroupedPermissions(): Promise<any>;
}
