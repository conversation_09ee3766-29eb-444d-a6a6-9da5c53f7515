"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const category_entity_1 = require("../entities/category.entity");
const stock_entity_1 = require("../entities/stock.entity");
let ProductService = class ProductService {
    productRepository;
    categoryRepository;
    stockRepository;
    constructor(productRepository, categoryRepository, stockRepository) {
        this.productRepository = productRepository;
        this.categoryRepository = categoryRepository;
        this.stockRepository = stockRepository;
    }
    async create(productData) {
        const product = this.productRepository.create(productData);
        const savedProduct = await this.productRepository.save(product);
        if (productData.initialStock !== undefined) {
            await this.stockRepository.save({
                productId: savedProduct.id,
                quantity: productData.initialStock,
                reservedQuantity: 0,
                availableQuantity: productData.initialStock,
            });
        }
        return savedProduct;
    }
    async findAll() {
        return this.productRepository.find({
            relations: ['category', 'stocks'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const product = await this.productRepository.findOne({
            where: { id },
            relations: ['category', 'stocks', 'stocks.warehouse'],
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with ID ${id} not found`);
        }
        return product;
    }
    async update(id, updateData) {
        await this.productRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const product = await this.findOne(id);
        await this.productRepository.remove(product);
    }
    async findByCategory(categoryId) {
        return this.productRepository.find({
            where: { categoryId },
            relations: ['category', 'stocks'],
            order: { name: 'ASC' },
        });
    }
    async findBySku(sku) {
        const product = await this.productRepository.findOne({
            where: { sku },
            relations: ['category', 'stocks'],
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with SKU ${sku} not found`);
        }
        return product;
    }
    async findByBarcode(barcode) {
        const product = await this.productRepository.findOne({
            where: { barcode },
            relations: ['category', 'stocks'],
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with barcode ${barcode} not found`);
        }
        return product;
    }
    async searchProducts(searchTerm) {
        return this.productRepository
            .createQueryBuilder('product')
            .leftJoinAndSelect('product.category', 'category')
            .leftJoinAndSelect('product.stocks', 'stocks')
            .where('product.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('product.sku ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('product.barcode ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('product.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('product.name', 'ASC')
            .getMany();
    }
    async getLowStockProducts(threshold = 10) {
        return this.productRepository
            .createQueryBuilder('product')
            .leftJoinAndSelect('product.stocks', 'stocks')
            .leftJoinAndSelect('product.category', 'category')
            .where('stocks.availableQuantity <= :threshold', { threshold })
            .orderBy('stocks.availableQuantity', 'ASC')
            .getMany();
    }
    async getOutOfStockProducts() {
        return this.productRepository
            .createQueryBuilder('product')
            .leftJoinAndSelect('product.stocks', 'stocks')
            .leftJoinAndSelect('product.category', 'category')
            .where('stocks.availableQuantity = 0')
            .orderBy('product.name', 'ASC')
            .getMany();
    }
    async updateStock(productId, warehouseId, quantity) {
        const stock = await this.stockRepository.findOne({
            where: { productId, warehouseId },
        });
        if (stock) {
            stock.quantity = quantity;
            stock.availableQuantity = quantity - stock.reservedQuantity;
            await this.stockRepository.save(stock);
        }
        else {
            await this.stockRepository.save({
                productId,
                warehouseId,
                quantity,
                reservedQuantity: 0,
                availableQuantity: quantity,
            });
        }
    }
    async reserveStock(productId, warehouseId, quantity) {
        const stock = await this.stockRepository.findOne({
            where: { productId, warehouseId },
        });
        if (!stock || stock.availableQuantity < quantity) {
            return false;
        }
        stock.reservedQuantity += quantity;
        stock.availableQuantity -= quantity;
        await this.stockRepository.save(stock);
        return true;
    }
    async releaseStock(productId, warehouseId, quantity) {
        const stock = await this.stockRepository.findOne({
            where: { productId, warehouseId },
        });
        if (stock) {
            stock.reservedQuantity = Math.max(0, stock.reservedQuantity - quantity);
            stock.availableQuantity = stock.quantity - stock.reservedQuantity;
            await this.stockRepository.save(stock);
        }
    }
    async getProductStatistics() {
        const totalProducts = await this.productRepository.count();
        const activeProducts = await this.productRepository.count({ where: { isActive: true } });
        const lowStockProducts = await this.getLowStockProducts();
        const outOfStockProducts = await this.getOutOfStockProducts();
        const totalValue = await this.productRepository
            .createQueryBuilder('product')
            .leftJoin('product.stocks', 'stocks')
            .select('SUM(product.costPrice * stocks.quantity)', 'totalValue')
            .getRawOne();
        return {
            totalProducts,
            activeProducts,
            inactiveProducts: totalProducts - activeProducts,
            lowStockCount: lowStockProducts.length,
            outOfStockCount: outOfStockProducts.length,
            totalInventoryValue: parseFloat(totalValue.totalValue) || 0,
        };
    }
    async getTopSellingProducts(limit = 10) {
        return this.productRepository.find({
            relations: ['category', 'stocks'],
            order: { name: 'ASC' },
            take: limit,
        });
    }
    async bulkUpdatePrices(updates) {
        for (const update of updates) {
            await this.productRepository.update(update.id, {
                salePrice: update.salePrice,
                ...(update.costPrice && { costPrice: update.costPrice }),
            });
        }
    }
    async generateSku(categoryCode) {
        const count = await this.productRepository.count();
        const sequence = (count + 1).toString().padStart(4, '0');
        return `${categoryCode}-${sequence}`;
    }
};
exports.ProductService = ProductService;
exports.ProductService = ProductService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __param(2, (0, typeorm_1.InjectRepository)(stock_entity_1.Stock)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ProductService);
//# sourceMappingURL=product.service.js.map