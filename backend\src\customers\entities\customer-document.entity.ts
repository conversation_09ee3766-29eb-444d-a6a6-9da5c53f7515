import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Customer } from './customer.entity';

export enum DocumentType {
  CONTRACT = 'contract',
  INVOICE = 'invoice',
  QUOTE = 'quote',
  PROPOSAL = 'proposal',
  AGREEMENT = 'agreement',
  CERTIFICATE = 'certificate',
  LICENSE = 'license',
  IDENTIFICATION = 'identification',
  FINANCIAL = 'financial',
  LEGAL = 'legal',
  CORRESPONDENCE = 'correspondence',
  OTHER = 'other',
}

@Entity('customer_documents')
export class CustomerDocument {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer.documents)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.OTHER,
  })
  type: DocumentType;

  @Column({ length: 255 })
  fileName: string;

  @Column({ length: 255 })
  originalName: string;

  @Column({ length: 500 })
  filePath: string;

  @Column({ length: 100 })
  mimeType: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column({ length: 10, nullable: true })
  version: string;

  @Column()
  uploadedBy: string;

  @Column({ type: 'date', nullable: true })
  expiryDate: Date;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isConfidential: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
