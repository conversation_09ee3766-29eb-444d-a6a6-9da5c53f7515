import { Invoice } from './invoice.entity';
import { Quotation } from './quotation.entity';
import { Payment } from './payment.entity';
export declare class Customer {
    id: string;
    customerNumber: string;
    name: string;
    type: 'individual' | 'commercial';
    email: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    taxNumber: string;
    commercialRegister: string;
    billingMethod: 'print' | 'email';
    displayLanguage: 'english' | 'arabic';
    status: 'active' | 'inactive';
    category: string;
    notes: string;
    currency: string;
    creditLimit: number;
    currentBalance: number;
    invoices: Invoice[];
    quotations: Quotation[];
    payments: Payment[];
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
}
