import { Repository } from 'typeorm';
import { Transaction } from '../entities/transaction.entity';
import { AccountService } from './account.service';
export declare class TransactionService {
    private transactionRepository;
    private accountService;
    constructor(transactionRepository: Repository<Transaction>, accountService: AccountService);
    create(createTransactionDto: any): Promise<Transaction>;
    findAll(filters?: any): Promise<Transaction[]>;
    findOne(id: string): Promise<Transaction>;
    update(id: string, updateTransactionDto: any): Promise<Transaction>;
    remove(id: string): Promise<void>;
    postTransaction(id: string, approvedBy?: string): Promise<Transaction>;
    reverseTransaction(id: string, reason: string, reversedBy?: string): Promise<Transaction>;
    getAccountLedger(accountId: string, startDate?: Date, endDate?: Date): Promise<any>;
    private updateAccountBalances;
    private generateTransactionNumber;
}
