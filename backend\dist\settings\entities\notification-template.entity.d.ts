export declare enum NotificationType {
    EMAIL = "email",
    SMS = "sms",
    PUSH = "push",
    IN_APP = "in_app",
    WEBHOOK = "webhook",
    SLACK = "slack",
    TEAMS = "teams"
}
export declare enum NotificationEvent {
    USER_REGISTERED = "user_registered",
    ORDER_CREATED = "order_created",
    PAYMENT_RECEIVED = "payment_received",
    TASK_ASSIGNED = "task_assigned",
    DEADLINE_APPROACHING = "deadline_approaching",
    SYSTEM_ALERT = "system_alert",
    CUSTOM = "custom"
}
export declare class NotificationTemplate {
    id: string;
    name: string;
    code: string;
    description: string;
    type: NotificationType;
    event: NotificationEvent;
    title: string;
    content: string;
    variables: string[];
    isActive: boolean;
    isSystem: boolean;
    createdBy: string;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
