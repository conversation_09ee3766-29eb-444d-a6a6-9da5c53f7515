import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { PosTerminal } from './pos-terminal.entity';
import { PosCashDrawer } from './pos-cash-drawer.entity';

export enum ShiftStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  SUSPENDED = 'suspended',
}

@Entity('pos_shifts')
export class PosShift {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  shiftNumber: string;

  @Column()
  terminalId: string;

  @ManyToOne(() => PosTerminal, terminal => terminal.shifts)
  @JoinColumn({ name: 'terminalId' })
  terminal: PosTerminal;

  @Column()
  cashierId: string;

  @Column({
    type: 'enum',
    enum: ShiftStatus,
    default: ShiftStatus.OPEN,
  })
  status: ShiftStatus;

  @Column({ type: 'timestamp' })
  startTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  endTime: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  openingCash: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  closingCash: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  expectedCash: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cashVariance: number;

  @Column({ type: 'int', default: 0 })
  totalTransactions: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalSales: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalReturns: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalDiscounts: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalTax: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cashSales: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cardSales: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  otherPayments: number;

  @Column({ type: 'int', default: 0 })
  voidedTransactions: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  voidedAmount: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  closedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  closedAt: Date;

  @OneToMany(() => PosCashDrawer, cashDrawer => cashDrawer.shift)
  cashDrawerActivities: PosCashDrawer[];

  @Column({ type: 'json', nullable: true })
  paymentSummary: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
