import { Repository } from 'typeorm';
import { Payment } from '../entities/payment.entity';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { CustomerService } from './customer.service';
export declare class PaymentService {
    private paymentRepository;
    private customerService;
    constructor(paymentRepository: Repository<Payment>, customerService: CustomerService);
    create(createPaymentDto: CreatePaymentDto, tenantId: string): Promise<Payment>;
    findAll(tenantId: string): Promise<Payment[]>;
    findOne(id: string, tenantId: string): Promise<Payment>;
    findByCustomer(customerId: string, tenantId: string): Promise<Payment[]>;
    findByInvoice(invoiceId: string, tenantId: string): Promise<Payment[]>;
    update(id: string, updatePaymentDto: Partial<CreatePaymentDto>, tenantId: string): Promise<Payment>;
    remove(id: string, tenantId: string): Promise<void>;
    updateStatus(id: string, status: string, tenantId: string): Promise<Payment>;
    getPaymentStats(tenantId: string): Promise<{
        totalPayments: number;
        receivedPayments: number;
        pendingPayments: number;
        totalAmount: number;
        averageAmount: number;
        methodStats: any[];
    }>;
    private generatePaymentNumber;
}
