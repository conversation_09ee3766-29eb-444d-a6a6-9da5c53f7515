import { KnowledgeBaseService } from '../services/knowledge-base.service';
import { KnowledgeBaseArticle } from '../entities/knowledge-base-article.entity';
export declare class KnowledgeBaseController {
    private readonly knowledgeBaseService;
    constructor(knowledgeBaseService: KnowledgeBaseService);
    create(createArticleDto: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle>;
    findAll(): Promise<KnowledgeBaseArticle[]>;
    getPublishedArticles(): Promise<KnowledgeBaseArticle[]>;
    getDraftArticles(): Promise<KnowledgeBaseArticle[]>;
    getPopularArticles(limit?: string): Promise<KnowledgeBaseArticle[]>;
    getRecentArticles(limit?: string): Promise<KnowledgeBaseArticle[]>;
    getStatistics(): Promise<any>;
    getCategories(): Promise<string[]>;
    getAllTags(): Promise<string[]>;
    searchArticles(searchTerm: string): Promise<KnowledgeBaseArticle[]>;
    findByCategory(category: string): Promise<KnowledgeBaseArticle[]>;
    getArticlesByAuthor(authorId: string): Promise<KnowledgeBaseArticle[]>;
    findByTags(tagsParam: string): Promise<KnowledgeBaseArticle[]>;
    findOne(id: string): Promise<KnowledgeBaseArticle>;
    getRelatedArticles(id: string, limit?: string): Promise<KnowledgeBaseArticle[]>;
    update(id: string, updateArticleDto: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle>;
    publishArticle(id: string): Promise<KnowledgeBaseArticle>;
    unpublishArticle(id: string): Promise<KnowledgeBaseArticle>;
    rateArticle(id: string, ratingData: {
        rating: number;
    }): Promise<KnowledgeBaseArticle>;
    remove(id: string): Promise<void>;
}
