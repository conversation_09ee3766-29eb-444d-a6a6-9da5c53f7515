import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
import { Location } from './location.entity';
import { StockMovement } from './stock-movement.entity';

export enum StockStatus {
  AVAILABLE = 'available',
  RESERVED = 'reserved',
  DAMAGED = 'damaged',
  EXPIRED = 'expired',
  QUARANTINE = 'quarantine',
  IN_TRANSIT = 'in_transit',
}

@Entity('inventory_stock')
@Index(['productId', 'warehouseId', 'locationId'], { unique: true })
export class Stock {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  productId: string;

  @ManyToOne(() => Product, product => product.stocks)
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column()
  warehouseId: string;

  @ManyToOne(() => Warehouse, warehouse => warehouse.stocks)
  @JoinColumn({ name: 'warehouseId' })
  warehouse: Warehouse;

  @Column({ nullable: true })
  locationId: string;

  @ManyToOne(() => Location, location => location.stocks, { nullable: true })
  @JoinColumn({ name: 'locationId' })
  location: Location;

  @Column({ type: 'int', default: 0 })
  quantityOnHand: number;

  @Column({ type: 'int', default: 0 })
  quantityReserved: number;

  @Column({ type: 'int', default: 0 })
  quantityAvailable: number;

  @Column({ type: 'int', default: 0 })
  quantityInTransit: number;

  @Column({ type: 'int', default: 0 })
  quantityDamaged: number;

  @Column({
    type: 'enum',
    enum: StockStatus,
    default: StockStatus.AVAILABLE,
  })
  status: StockStatus;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  averageCost: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  lastCost: number;

  @Column({ type: 'date', nullable: true })
  lastReceivedDate: Date;

  @Column({ type: 'date', nullable: true })
  lastIssuedDate: Date;

  @Column({ type: 'date', nullable: true })
  expiryDate: Date;

  @Column({ type: 'date', nullable: true })
  manufactureDate: Date;

  @Column({ length: 100, nullable: true })
  batchNumber: string;

  @Column({ length: 100, nullable: true })
  serialNumber: string;

  @Column({ type: 'json', nullable: true })
  lotNumbers: string[];

  @OneToMany(() => StockMovement, movement => movement.stock)
  stockMovements: StockMovement[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
