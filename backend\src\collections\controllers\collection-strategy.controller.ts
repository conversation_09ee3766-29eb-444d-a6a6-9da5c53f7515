import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { CollectionStrategyService } from '../services/collection-strategy.service';
import { CollectionStrategy } from '../entities/collection-strategy.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('collection-strategies')
@UseGuards(JwtAuthGuard)
export class CollectionStrategyController {
  constructor(private readonly collectionStrategyService: CollectionStrategyService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createStrategyDto: Partial<CollectionStrategy>) {
    return this.collectionStrategyService.create(createStrategyDto);
  }

  @Get()
  async findAll() {
    return this.collectionStrategyService.findAll();
  }

  @Get('active')
  async getActiveStrategies() {
    return this.collectionStrategyService.getActiveStrategies();
  }

  @Get('effectiveness')
  async getStrategyEffectiveness() {
    return this.collectionStrategyService.getStrategyEffectiveness();
  }

  @Get('recommend')
  async getRecommendedStrategy(
    @Query('debtAmount') debtAmount: number,
    @Query('daysOverdue') daysOverdue: number,
  ) {
    return this.collectionStrategyService.getRecommendedStrategy(
      Number(debtAmount),
      Number(daysOverdue),
    );
  }

  @Get('recommendations')
  async getStrategyRecommendations(
    @Query('debtAmount') debtAmount: number,
    @Query('daysOverdue') daysOverdue: number,
    @Query('customerType') customerType?: string,
    @Query('paymentHistory') paymentHistory?: string,
  ) {
    return this.collectionStrategyService.getStrategyRecommendations({
      debtAmount: Number(debtAmount),
      daysOverdue: Number(daysOverdue),
      customerType,
      previousPaymentHistory: paymentHistory,
    });
  }

  @Get('debt-range')
  async findByDebtRange(
    @Query('minAmount') minAmount: number,
    @Query('maxAmount') maxAmount: number,
  ) {
    return this.collectionStrategyService.findByDebtRange(
      Number(minAmount),
      Number(maxAmount),
    );
  }

  @Get('days-overdue/:days')
  async findByDaysOverdue(@Param('days') days: number) {
    return this.collectionStrategyService.findByDaysOverdue(Number(days));
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.collectionStrategyService.findOne(id);
  }

  @Post('create-defaults')
  async createDefaultStrategies() {
    return this.collectionStrategyService.createDefaultStrategies();
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateStrategyDto: Partial<CollectionStrategy>,
  ) {
    return this.collectionStrategyService.update(id, updateStrategyDto);
  }

  @Patch(':id/activate')
  async activate(@Param('id') id: string) {
    return this.collectionStrategyService.activateStrategy(id);
  }

  @Patch(':id/deactivate')
  async deactivate(@Param('id') id: string) {
    return this.collectionStrategyService.deactivateStrategy(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.collectionStrategyService.remove(id);
  }
}
