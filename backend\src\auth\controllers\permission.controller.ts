import {
  <PERSON>,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { PermissionService } from '../services/permission.service';
import { PermissionModule } from '../entities/permission.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('permissions')
@UseGuards(JwtAuthGuard)
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Get()
  async findAll(@Query('module') module?: PermissionModule) {
    if (module) {
      return this.permissionService.findByModule(module);
    }
    return this.permissionService.findAll();
  }

  @Get('grouped')
  async getGroupedPermissions() {
    return this.permissionService.getGroupedPermissions();
  }

  @Get('role/:roleId')
  async getPermissionsByRole(@Param('roleId') roleId: string) {
    return this.permissionService.getPermissionsByRole(roleId);
  }

  @Post('create-defaults')
  @HttpCode(HttpStatus.CREATED)
  async createDefaultPermissions() {
    return this.permissionService.createDefaultPermissions();
  }
}
