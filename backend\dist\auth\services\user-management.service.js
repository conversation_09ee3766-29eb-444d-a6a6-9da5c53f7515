"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManagementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
const role_entity_1 = require("../entities/role.entity");
const permission_entity_1 = require("../entities/permission.entity");
const bcrypt = require("bcrypt");
let UserManagementService = class UserManagementService {
    userRepository;
    roleRepository;
    permissionRepository;
    constructor(userRepository, roleRepository, permissionRepository) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
    }
    async createUser(createUserDto) {
        const existingUser = await this.userRepository.findOne({
            where: { email: createUserDto.email },
        });
        if (existingUser) {
            throw new common_1.BadRequestException('Email already exists');
        }
        const role = await this.roleRepository.findOne({
            where: { id: createUserDto.roleId },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
        const user = this.userRepository.create({
            ...createUserDto,
            password: hashedPassword,
            status: user_entity_1.UserStatus.ACTIVE,
        });
        const savedUser = await this.userRepository.save(user);
        if (createUserDto.additionalPermissionIds && createUserDto.additionalPermissionIds.length > 0) {
            await this.assignAdditionalPermissions(savedUser.id, createUserDto.additionalPermissionIds);
        }
        return this.findUserById(savedUser.id);
    }
    async findAllUsers() {
        return this.userRepository.find({
            relations: ['role', 'role.permissions', 'additionalPermissions'],
            order: { firstName: 'ASC', lastName: 'ASC' },
        });
    }
    async findUserById(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['role', 'role.permissions', 'additionalPermissions'],
        });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return user;
    }
    async findUserByEmail(email) {
        const user = await this.userRepository.findOne({
            where: { email },
            relations: ['role', 'role.permissions', 'additionalPermissions'],
        });
        if (!user) {
            throw new common_1.NotFoundException(`User with email ${email} not found`);
        }
        return user;
    }
    async updateUser(id, updateUserDto) {
        const user = await this.findUserById(id);
        if (updateUserDto.roleId) {
            const role = await this.roleRepository.findOne({
                where: { id: updateUserDto.roleId },
            });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
        }
        await this.userRepository.update(id, updateUserDto);
        if (updateUserDto.additionalPermissionIds !== undefined) {
            await this.assignAdditionalPermissions(id, updateUserDto.additionalPermissionIds);
        }
        return this.findUserById(id);
    }
    async deleteUser(id) {
        const user = await this.findUserById(id);
        await this.userRepository.remove(user);
    }
    async changeUserPassword(id, newPassword) {
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await this.userRepository.update(id, { password: hashedPassword });
    }
    async activateUser(id) {
        await this.userRepository.update(id, {
            status: user_entity_1.UserStatus.ACTIVE,
            isActive: true,
        });
        return this.findUserById(id);
    }
    async deactivateUser(id) {
        await this.userRepository.update(id, {
            status: user_entity_1.UserStatus.INACTIVE,
            isActive: false,
        });
        return this.findUserById(id);
    }
    async suspendUser(id) {
        await this.userRepository.update(id, {
            status: user_entity_1.UserStatus.SUSPENDED,
            isActive: false,
        });
        return this.findUserById(id);
    }
    async assignRole(userId, roleId) {
        const role = await this.roleRepository.findOne({
            where: { id: roleId },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        await this.userRepository.update(userId, { roleId });
        return this.findUserById(userId);
    }
    async assignAdditionalPermissions(userId, permissionIds) {
        const user = await this.findUserById(userId);
        if (permissionIds.length > 0) {
            const permissions = await this.permissionRepository.findByIds(permissionIds);
            user.additionalPermissions = permissions;
        }
        else {
            user.additionalPermissions = [];
        }
        await this.userRepository.save(user);
        return this.findUserById(userId);
    }
    async removeAdditionalPermissions(userId, permissionIds) {
        const user = await this.findUserById(userId);
        user.additionalPermissions = user.additionalPermissions.filter(permission => !permissionIds.includes(permission.id));
        await this.userRepository.save(user);
        return this.findUserById(userId);
    }
    async getUserPermissions(userId) {
        const user = await this.findUserById(userId);
        const rolePermissions = user.role?.permissions || [];
        const additionalPermissions = user.additionalPermissions || [];
        const allPermissions = [...rolePermissions, ...additionalPermissions];
        const uniquePermissions = allPermissions.filter((permission, index, self) => index === self.findIndex(p => p.id === permission.id));
        return uniquePermissions;
    }
    async checkUserPermission(userId, module, action, resource) {
        const user = await this.findUserById(userId);
        if (!user.isActive || user.status !== user_entity_1.UserStatus.ACTIVE) {
            return { hasPermission: false, source: 'none' };
        }
        const rolePermissions = user.role?.permissions || [];
        const rolePermission = rolePermissions.find(p => p.module === module && p.action === action && p.resource === resource);
        if (rolePermission) {
            return {
                hasPermission: true,
                source: 'role',
                permission: rolePermission,
            };
        }
        const additionalPermissions = user.additionalPermissions || [];
        const additionalPermission = additionalPermissions.find(p => p.module === module && p.action === action && p.resource === resource);
        if (additionalPermission) {
            return {
                hasPermission: true,
                source: 'additional',
                permission: additionalPermission,
            };
        }
        return { hasPermission: false, source: 'none' };
    }
    async getUsersByRole(roleId) {
        return this.userRepository.find({
            where: { roleId },
            relations: ['role'],
            order: { firstName: 'ASC', lastName: 'ASC' },
        });
    }
    async getUsersByDepartment(department) {
        return this.userRepository.find({
            where: { department },
            relations: ['role'],
            order: { firstName: 'ASC', lastName: 'ASC' },
        });
    }
    async getUsersWithDepartmentAccess(department) {
        return this.userRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.role', 'role')
            .where('user.departmentAccess @> :department', { department: [department] })
            .orWhere('role.departmentAccess @> :department', { department: [department] })
            .orderBy('user.firstName', 'ASC')
            .addOrderBy('user.lastName', 'ASC')
            .getMany();
    }
    async searchUsers(searchTerm) {
        return this.userRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.role', 'role')
            .where('user.firstName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('user.lastName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('user.email ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('user.employeeId ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('user.firstName', 'ASC')
            .addOrderBy('user.lastName', 'ASC')
            .getMany();
    }
    async getUserStatistics() {
        const totalUsers = await this.userRepository.count();
        const activeUsers = await this.userRepository.count({
            where: { status: user_entity_1.UserStatus.ACTIVE },
        });
        const inactiveUsers = await this.userRepository.count({
            where: { status: user_entity_1.UserStatus.INACTIVE },
        });
        const suspendedUsers = await this.userRepository.count({
            where: { status: user_entity_1.UserStatus.SUSPENDED },
        });
        const pendingUsers = await this.userRepository.count({
            where: { status: user_entity_1.UserStatus.PENDING },
        });
        const departmentStats = await this.userRepository
            .createQueryBuilder('user')
            .select('user.department', 'department')
            .addSelect('COUNT(user.id)', 'count')
            .where('user.department IS NOT NULL')
            .groupBy('user.department')
            .orderBy('count', 'DESC')
            .getRawMany();
        const roleStats = await this.userRepository
            .createQueryBuilder('user')
            .leftJoin('user.role', 'role')
            .select('role.name', 'roleName')
            .addSelect('COUNT(user.id)', 'count')
            .where('role.name IS NOT NULL')
            .groupBy('role.name')
            .orderBy('count', 'DESC')
            .getRawMany();
        return {
            totalUsers,
            activeUsers,
            inactiveUsers,
            suspendedUsers,
            pendingUsers,
            departmentDistribution: departmentStats.map(stat => ({
                department: stat.department,
                count: parseInt(stat.count),
            })),
            roleDistribution: roleStats.map(stat => ({
                role: stat.roleName,
                count: parseInt(stat.count),
            })),
        };
    }
    async updateLastLogin(userId) {
        await this.userRepository.update(userId, {
            lastLoginAt: new Date(),
        });
    }
    async bulkUpdateUsers(userIds, updateData) {
        await this.userRepository.update(userIds, updateData);
    }
    async exportUsers() {
        const users = await this.findAllUsers();
        return users.map(user => ({
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            department: user.department,
            position: user.position,
            employeeId: user.employeeId,
            role: user.role?.name,
            status: user.status,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            departmentAccess: user.departmentAccess,
            additionalPermissions: user.additionalPermissions?.map(p => p.name),
        }));
    }
};
exports.UserManagementService = UserManagementService;
exports.UserManagementService = UserManagementService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(2, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], UserManagementService);
//# sourceMappingURL=user-management.service.js.map