import { PayrollService } from '../services/payroll.service';
export declare class PayrollController {
    private readonly payrollService;
    constructor(payrollService: PayrollService);
    create(createPayrollDto: any): Promise<import("../entities/payroll.entity").Payroll>;
    findAll(employeeId?: string, status?: string, payPeriod?: string): Promise<import("../entities/payroll.entity").Payroll[]>;
    findOne(id: string): Promise<import("../entities/payroll.entity").Payroll>;
    calculatePayroll(id: string): Promise<import("../entities/payroll.entity").Payroll>;
    approvePayroll(id: string, approveDto: {
        approvedBy: string;
    }): Promise<import("../entities/payroll.entity").Payroll>;
    processPayment(id: string, paymentDto: {
        paidBy: string;
    }): Promise<import("../entities/payroll.entity").Payroll>;
    addPayrollItem(id: string, createPayrollItemDto: any): Promise<import("../entities/payroll-item.entity").PayrollItem>;
}
