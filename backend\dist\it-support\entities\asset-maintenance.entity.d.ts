import { Asset } from './asset.entity';
export declare enum MaintenanceType {
    PREVENTIVE = "preventive",
    CORRECTIVE = "corrective",
    EMERGENCY = "emergency",
    UPGRADE = "upgrade",
    INSPECTION = "inspection"
}
export declare enum MaintenanceStatus {
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    OVERDUE = "overdue"
}
export declare class AssetMaintenance {
    id: string;
    assetId: string;
    asset: Asset;
    type: MaintenanceType;
    status: MaintenanceStatus;
    title: string;
    description: string;
    scheduledDate: Date;
    completedDate: Date;
    performedBy: string;
    vendor: string;
    cost: number;
    workPerformed: string;
    partsUsed: string;
    notes: string;
    nextMaintenanceDate: Date;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
