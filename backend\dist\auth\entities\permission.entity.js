"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permission = exports.PermissionAction = exports.PermissionModule = void 0;
const typeorm_1 = require("typeorm");
const role_entity_1 = require("./role.entity");
var PermissionModule;
(function (PermissionModule) {
    PermissionModule["ANALYTICS"] = "analytics";
    PermissionModule["CUSTOMERS"] = "customers";
    PermissionModule["COLLECTIONS"] = "collections";
    PermissionModule["FINANCE"] = "finance";
    PermissionModule["HR"] = "hr";
    PermissionModule["INVENTORY"] = "inventory";
    PermissionModule["IT_SUPPORT"] = "it_support";
    PermissionModule["POS"] = "pos";
    PermissionModule["PROCUREMENT"] = "procurement";
    PermissionModule["PROJECTS"] = "projects";
    PermissionModule["SALES"] = "sales";
    PermissionModule["SETTINGS"] = "settings";
    PermissionModule["SYSTEM_GUIDE"] = "system_guide";
    PermissionModule["USER_MANAGEMENT"] = "user_management";
})(PermissionModule || (exports.PermissionModule = PermissionModule = {}));
var PermissionAction;
(function (PermissionAction) {
    PermissionAction["CREATE"] = "create";
    PermissionAction["READ"] = "read";
    PermissionAction["UPDATE"] = "update";
    PermissionAction["DELETE"] = "delete";
    PermissionAction["APPROVE"] = "approve";
    PermissionAction["REJECT"] = "reject";
    PermissionAction["ASSIGN"] = "assign";
    PermissionAction["UNASSIGN"] = "unassign";
    PermissionAction["ESCALATE"] = "escalate";
    PermissionAction["CLOSE"] = "close";
    PermissionAction["REOPEN"] = "reopen";
    PermissionAction["EXPORT"] = "export";
    PermissionAction["IMPORT"] = "import";
    PermissionAction["PRINT"] = "print";
    PermissionAction["PROCESS_PAYMENT"] = "process_payment";
    PermissionAction["REFUND"] = "refund";
    PermissionAction["VOID"] = "void";
    PermissionAction["ADJUST"] = "adjust";
    PermissionAction["VIEW_REPORTS"] = "view_reports";
    PermissionAction["GENERATE_REPORTS"] = "generate_reports";
    PermissionAction["VIEW_ANALYTICS"] = "view_analytics";
    PermissionAction["MANAGE_SETTINGS"] = "manage_settings";
    PermissionAction["MANAGE_USERS"] = "manage_users";
    PermissionAction["MANAGE_ROLES"] = "manage_roles";
    PermissionAction["MANAGE_PERMISSIONS"] = "manage_permissions";
    PermissionAction["BACKUP"] = "backup";
    PermissionAction["RESTORE"] = "restore";
    PermissionAction["AUDIT"] = "audit";
    PermissionAction["CONFIGURE"] = "configure";
})(PermissionAction || (exports.PermissionAction = PermissionAction = {}));
let Permission = class Permission {
    id;
    module;
    action;
    resource;
    name;
    description;
    isActive;
    conditions;
    roles;
    createdAt;
    updatedAt;
};
exports.Permission = Permission;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Permission.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
    }),
    __metadata("design:type", String)
], Permission.prototype, "module", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
    }),
    __metadata("design:type", String)
], Permission.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], Permission.prototype, "resource", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Permission.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Permission.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Permission.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, role => role.permissions),
    __metadata("design:type", Array)
], Permission.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Permission.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Permission.prototype, "updatedAt", void 0);
exports.Permission = Permission = __decorate([
    (0, typeorm_1.Entity)('permissions')
], Permission);
//# sourceMappingURL=permission.entity.js.map