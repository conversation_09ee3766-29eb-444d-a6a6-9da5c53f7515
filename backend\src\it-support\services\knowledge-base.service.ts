import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KnowledgeBaseArticle } from '../entities/knowledge-base-article.entity';

@Injectable()
export class KnowledgeBaseService {
  constructor(
    @InjectRepository(KnowledgeBaseArticle)
    private articleRepository: Repository<KnowledgeBaseArticle>,
  ) {}

  async create(articleData: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle> {
    const article = this.articleRepository.create({
      ...articleData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return this.articleRepository.save(article);
  }

  async findAll(): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      relations: ['author'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<KnowledgeBaseArticle> {
    const article = await this.articleRepository.findOne({
      where: { id },
      relations: ['author'],
    });

    if (!article) {
      throw new NotFoundException(`Article with ID ${id} not found`);
    }

    // Increment view count
    await this.articleRepository.update(id, {
      viewCount: article.viewCount + 1,
    });

    return article;
  }

  async update(id: string, updateData: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle> {
    await this.articleRepository.update(id, {
      ...updateData,
      updatedAt: new Date(),
    });
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const article = await this.findOne(id);
    await this.articleRepository.remove(article);
  }

  async findByCategory(category: string): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      where: { category },
      relations: ['author'],
      order: { title: 'ASC' },
    });
  }

  async findByTags(tags: string[]): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository
      .createQueryBuilder('article')
      .leftJoinAndSelect('article.author', 'author')
      .where('article.tags && :tags', { tags })
      .orderBy('article.title', 'ASC')
      .getMany();
  }

  async searchArticles(searchTerm: string): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository
      .createQueryBuilder('article')
      .leftJoinAndSelect('article.author', 'author')
      .where('article.title ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('article.content ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('article.summary ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('article.title', 'ASC')
      .getMany();
  }

  async getPublishedArticles(): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      where: { isPublished: true },
      relations: ['author'],
      order: { createdAt: 'DESC' },
    });
  }

  async getDraftArticles(): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      where: { isPublished: false },
      relations: ['author'],
      order: { updatedAt: 'DESC' },
    });
  }

  async publishArticle(id: string): Promise<KnowledgeBaseArticle> {
    await this.articleRepository.update(id, {
      isPublished: true,
      publishedAt: new Date(),
    });
    return this.findOne(id);
  }

  async unpublishArticle(id: string): Promise<KnowledgeBaseArticle> {
    await this.articleRepository.update(id, {
      isPublished: false,
      publishedAt: null,
    });
    return this.findOne(id);
  }

  async getPopularArticles(limit: number = 10): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      where: { isPublished: true },
      relations: ['author'],
      order: { viewCount: 'DESC' },
      take: limit,
    });
  }

  async getRecentArticles(limit: number = 10): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      where: { isPublished: true },
      relations: ['author'],
      order: { publishedAt: 'DESC' },
      take: limit,
    });
  }

  async getArticlesByAuthor(authorId: string): Promise<KnowledgeBaseArticle[]> {
    return this.articleRepository.find({
      where: { authorId },
      order: { createdAt: 'DESC' },
    });
  }

  async getCategories(): Promise<string[]> {
    const result = await this.articleRepository
      .createQueryBuilder('article')
      .select('DISTINCT article.category', 'category')
      .where('article.category IS NOT NULL')
      .andWhere('article.isPublished = true')
      .orderBy('article.category', 'ASC')
      .getRawMany();

    return result.map(row => row.category);
  }

  async getAllTags(): Promise<string[]> {
    const articles = await this.articleRepository.find({
      where: { isPublished: true },
      select: ['tags'],
    });

    const allTags = new Set<string>();
    articles.forEach(article => {
      if (article.tags) {
        article.tags.forEach(tag => allTags.add(tag));
      }
    });

    return Array.from(allTags).sort();
  }

  async getArticleStatistics(): Promise<any> {
    const totalArticles = await this.articleRepository.count();
    const publishedArticles = await this.articleRepository.count({ where: { isPublished: true } });
    const draftArticles = await this.articleRepository.count({ where: { isPublished: false } });

    const totalViews = await this.articleRepository
      .createQueryBuilder('article')
      .select('SUM(article.viewCount)', 'totalViews')
      .getRawOne();

    const categories = await this.getCategories();

    return {
      totalArticles,
      publishedArticles,
      draftArticles,
      totalViews: parseInt(totalViews.totalViews) || 0,
      categoriesCount: categories.length,
    };
  }

  async rateArticle(articleId: string, rating: number): Promise<KnowledgeBaseArticle> {
    const article = await this.findOne(articleId);
    
    // Simple rating calculation (in a real app, you'd store individual ratings)
    const newRatingCount = article.ratingCount + 1;
    const newAverageRating = ((article.averageRating * article.ratingCount) + rating) / newRatingCount;

    await this.articleRepository.update(articleId, {
      averageRating: Math.round(newAverageRating * 100) / 100,
      ratingCount: newRatingCount,
    });

    return this.findOne(articleId);
  }

  async getRelatedArticles(articleId: string, limit: number = 5): Promise<KnowledgeBaseArticle[]> {
    const article = await this.findOne(articleId);
    
    if (!article.tags || article.tags.length === 0) {
      return [];
    }

    return this.articleRepository
      .createQueryBuilder('article')
      .leftJoinAndSelect('article.author', 'author')
      .where('article.id != :articleId', { articleId })
      .andWhere('article.isPublished = true')
      .andWhere('article.tags && :tags', { tags: article.tags })
      .orderBy('article.viewCount', 'DESC')
      .take(limit)
      .getMany();
  }
}
