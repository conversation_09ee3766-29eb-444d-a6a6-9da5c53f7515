import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BankAccount } from './bank-account.entity';

export enum BankTransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  TRANSFER = 'transfer',
  FEE = 'fee',
  INTEREST = 'interest',
  CHECK = 'check',
  ACH = 'ach',
  WIRE = 'wire',
  ATM = 'atm',
  DEBIT_CARD = 'debit_card',
  CREDIT_CARD = 'credit_card',
  OTHER = 'other',
}

export enum BankTransactionStatus {
  PENDING = 'pending',
  CLEARED = 'cleared',
  CANCELLED = 'cancelled',
  RETURNED = 'returned',
  RECONCILED = 'reconciled',
}

@Entity('finance_bank_transactions')
export class BankTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  bankAccountId: string;

  @ManyToOne(() => BankAccount, account => account.transactions)
  @JoinColumn({ name: 'bankAccountId' })
  bankAccount: BankAccount;

  @Column({ length: 50, nullable: true })
  transactionId: string; // Bank's transaction ID

  @Column({
    type: 'enum',
    enum: BankTransactionType,
  })
  type: BankTransactionType;

  @Column({
    type: 'enum',
    enum: BankTransactionStatus,
    default: BankTransactionStatus.PENDING,
  })
  status: BankTransactionStatus;

  @Column({ type: 'date' })
  transactionDate: Date;

  @Column({ type: 'date', nullable: true })
  valueDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  runningBalance: number;

  @Column({ type: 'text' })
  description: string;

  @Column({ length: 255, nullable: true })
  reference: string;

  @Column({ length: 255, nullable: true })
  checkNumber: string;

  @Column({ length: 255, nullable: true })
  payee: string;

  @Column({ length: 255, nullable: true })
  category: string;

  @Column({ nullable: true })
  relatedTransactionId: string; // Link to finance transaction

  @Column({ default: false })
  isReconciled: boolean;

  @Column({ type: 'date', nullable: true })
  reconciledDate: Date;

  @Column({ nullable: true })
  reconciledBy: string;

  @Column({ type: 'json', nullable: true })
  bankData: any; // Raw data from bank import

  @Column({ type: 'text', nullable: true })
  memo: string;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
