import { PosSale } from './pos-sale.entity';
export declare enum ReceiptType {
    SALE = "sale",
    RETURN = "return",
    EXCHANGE = "exchange",
    VOID = "void",
    REPRINT = "reprint",
    GIFT_RECEIPT = "gift_receipt"
}
export declare enum ReceiptFormat {
    THERMAL = "thermal",
    LASER = "laser",
    EMAIL = "email",
    SMS = "sms",
    DIGITAL = "digital"
}
export declare class PosReceipt {
    id: string;
    saleId: string;
    sale: PosSale;
    receiptNumber: string;
    type: ReceiptType;
    format: ReceiptFormat;
    content: string;
    template: any;
    isPrinted: boolean;
    printedAt: Date;
    isEmailed: boolean;
    emailedAt: Date;
    emailAddress: string;
    isSms: boolean;
    smsAt: Date;
    phoneNumber: string;
    customFields: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
