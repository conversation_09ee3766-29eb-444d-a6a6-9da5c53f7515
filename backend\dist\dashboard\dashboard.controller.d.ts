export declare class DashboardController {
    getDashboard(req: any): Promise<{
        message: string;
        user: any;
        stats: {
            totalUsers: number;
            totalProjects: number;
            totalSales: number;
            totalRevenue: number;
        };
        recentActivities: never[];
    }>;
    getStats(req: any): Promise<{
        overview: {
            totalUsers: number;
            totalProjects: number;
            totalSales: number;
            totalRevenue: number;
        };
        departments: {
            sales: {
                active: number;
                pending: number;
            };
            projects: {
                active: number;
                completed: number;
            };
            hr: {
                employees: number;
                departments: number;
            };
            finance: {
                income: number;
                expenses: number;
            };
        };
    }>;
}
