"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_entity_1 = require("../entities/payment.entity");
const customer_service_1 = require("./customer.service");
let PaymentService = class PaymentService {
    paymentRepository;
    customerService;
    constructor(paymentRepository, customerService) {
        this.paymentRepository = paymentRepository;
        this.customerService = customerService;
    }
    async create(createPaymentDto, tenantId) {
        const paymentNumber = await this.generatePaymentNumber();
        const payment = this.paymentRepository.create({
            ...createPaymentDto,
            paymentNumber,
            tenantId,
        });
        const savedPayment = await this.paymentRepository.save(payment);
        await this.customerService.updateBalance(createPaymentDto.customerId, -createPaymentDto.amount, tenantId);
        return this.findOne(savedPayment.id, tenantId);
    }
    async findAll(tenantId) {
        return this.paymentRepository.find({
            where: { tenantId },
            relations: ['customer', 'invoice'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id, tenantId) {
        const payment = await this.paymentRepository.findOne({
            where: { id, tenantId },
            relations: ['customer', 'invoice'],
        });
        if (!payment) {
            throw new common_1.NotFoundException('Payment not found');
        }
        return payment;
    }
    async findByCustomer(customerId, tenantId) {
        return this.paymentRepository.find({
            where: { customerId, tenantId },
            relations: ['invoice'],
            order: { createdAt: 'DESC' },
        });
    }
    async findByInvoice(invoiceId, tenantId) {
        return this.paymentRepository.find({
            where: { invoiceId, tenantId },
            relations: ['customer'],
            order: { createdAt: 'DESC' },
        });
    }
    async update(id, updatePaymentDto, tenantId) {
        const payment = await this.findOne(id, tenantId);
        if (updatePaymentDto.amount && updatePaymentDto.amount !== payment.amount) {
            const difference = updatePaymentDto.amount - payment.amount;
            await this.customerService.updateBalance(payment.customerId, -difference, tenantId);
        }
        Object.assign(payment, updatePaymentDto);
        return this.paymentRepository.save(payment);
    }
    async remove(id, tenantId) {
        const payment = await this.findOne(id, tenantId);
        await this.customerService.updateBalance(payment.customerId, payment.amount, tenantId);
        await this.paymentRepository.remove(payment);
    }
    async updateStatus(id, status, tenantId) {
        const payment = await this.findOne(id, tenantId);
        payment.status = status;
        return this.paymentRepository.save(payment);
    }
    async getPaymentStats(tenantId) {
        const totalPayments = await this.paymentRepository.count({ where: { tenantId } });
        const receivedPayments = await this.paymentRepository.count({
            where: { tenantId, status: 'received' }
        });
        const pendingPayments = await this.paymentRepository.count({
            where: { tenantId, status: 'pending' }
        });
        const result = await this.paymentRepository
            .createQueryBuilder('payment')
            .select('SUM(payment.amount)', 'totalAmount')
            .addSelect('AVG(payment.amount)', 'averageAmount')
            .where('payment.tenantId = :tenantId', { tenantId })
            .andWhere('payment.status = :status', { status: 'received' })
            .getRawOne();
        const methodStats = await this.paymentRepository
            .createQueryBuilder('payment')
            .select('payment.paymentMethod', 'method')
            .addSelect('COUNT(*)', 'count')
            .addSelect('SUM(payment.amount)', 'amount')
            .where('payment.tenantId = :tenantId', { tenantId })
            .groupBy('payment.paymentMethod')
            .getRawMany();
        return {
            totalPayments,
            receivedPayments,
            pendingPayments,
            totalAmount: parseFloat(result.totalAmount) || 0,
            averageAmount: parseFloat(result.averageAmount) || 0,
            methodStats,
        };
    }
    async generatePaymentNumber() {
        const count = await this.paymentRepository.count();
        const year = new Date().getFullYear();
        return `PAY-${year}-${(count + 1).toString().padStart(4, '0')}`;
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        customer_service_1.CustomerService])
], PaymentService);
//# sourceMappingURL=payment.service.js.map