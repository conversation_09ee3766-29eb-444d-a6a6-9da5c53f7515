import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Benefit } from '../entities/benefit.entity';
import { EmployeeBenefit, EnrollmentStatus } from '../entities/employee-benefit.entity';

@Injectable()
export class BenefitService {
  constructor(
    @InjectRepository(Benefit)
    private benefitRepository: Repository<Benefit>,
    @InjectRepository(EmployeeBenefit)
    private employeeBenefitRepository: Repository<EmployeeBenefit>,
  ) {}

  async create(createBenefitDto: any): Promise<Benefit> {
    const benefit = this.benefitRepository.create(createBenefitDto);
    return this.benefitRepository.save(benefit);
  }

  async findAll(): Promise<Benefit[]> {
    return this.benefitRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Benefit> {
    const benefit = await this.benefitRepository.findOne({
      where: { id },
      relations: ['employeeBenefits'],
    });

    if (!benefit) {
      throw new NotFoundException(`Benefit with ID ${id} not found`);
    }

    return benefit;
  }

  async enrollEmployee(employeeId: string, benefitId: string, enrollmentData: any): Promise<EmployeeBenefit> {
    const employeeBenefit = this.employeeBenefitRepository.create({
      employeeId,
      benefitId,
      ...enrollmentData,
      status: EnrollmentStatus.ENROLLED,
      enrollmentDate: new Date(),
    });

    return this.employeeBenefitRepository.save(employeeBenefit);
  }

  async getEmployeeBenefits(employeeId: string): Promise<EmployeeBenefit[]> {
    return this.employeeBenefitRepository.find({
      where: { employeeId },
      relations: ['benefit'],
      order: { enrollmentDate: 'DESC' },
    });
  }
}
