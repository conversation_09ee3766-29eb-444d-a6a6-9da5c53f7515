import { Repository } from 'typeorm';
import { Employee } from '../entities/employee.entity';
export declare class EmployeeService {
    private employeeRepository;
    constructor(employeeRepository: Repository<Employee>);
    create(createEmployeeDto: any): Promise<Employee>;
    findAll(filters?: any): Promise<Employee[]>;
    findOne(id: string): Promise<Employee>;
    findByEmployeeNumber(employeeNumber: string): Promise<Employee>;
    update(id: string, updateEmployeeDto: any): Promise<Employee>;
    remove(id: string): Promise<void>;
    terminate(id: string, terminationDate: Date, reason?: string): Promise<Employee>;
    reactivate(id: string): Promise<Employee>;
    getEmployeeHierarchy(managerId?: string): Promise<Employee[]>;
    getEmployeeStats(): Promise<any>;
    searchEmployees(searchTerm: string): Promise<Employee[]>;
    private generateEmployeeNumber;
}
