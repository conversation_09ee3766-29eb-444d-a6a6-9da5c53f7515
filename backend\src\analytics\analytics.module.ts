import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditReportsController } from './controllers/audit-reports.controller';
import { BusinessMetricsController } from './controllers/business-metrics.controller';
import { AuditReportsService } from './services/audit-reports.service';
import { BusinessMetricsService } from './services/business-metrics.service';
import { AuditReport } from './entities/audit-report.entity';
import { AuditFinding } from './entities/audit-finding.entity';
import { Customer } from '../sales/entities/customer.entity';
import { Invoice } from '../sales/entities/invoice.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([AuditReport, AuditFinding, Customer, Invoice]),
  ],
  controllers: [AuditReportsController, BusinessMetricsController],
  providers: [AuditReportsService, BusinessMetricsService],
  exports: [AuditReportsService, BusinessMetricsService],
})
export class AnalyticsModule {}
