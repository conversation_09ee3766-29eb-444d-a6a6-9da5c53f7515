import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { BudgetService } from '../services/budget.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('finance/budgets')
@UseGuards(JwtAuthGuard)
export class BudgetController {
  constructor(private readonly budgetService: BudgetService) {}

  @Post()
  create(@Body() createBudgetDto: any) {
    return this.budgetService.create(createBudgetDto);
  }

  @Get()
  findAll(
    @Query('type') type?: string,
    @Query('status') status?: string,
    @Query('departmentId') departmentId?: string
  ) {
    const filters: any = {};
    if (type) filters.type = type;
    if (status) filters.status = status;
    if (departmentId) filters.departmentId = departmentId;

    return this.budgetService.findAll(filters);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.budgetService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateBudgetDto: any) {
    return this.budgetService.update(id, updateBudgetDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.budgetService.remove(id);
  }

  @Post(':id/items')
  addBudgetItem(@Param('id') id: string, @Body() createBudgetItemDto: any) {
    return this.budgetService.addBudgetItem(id, createBudgetItemDto);
  }

  @Patch('items/:itemId')
  updateBudgetItem(@Param('itemId') itemId: string, @Body() updateBudgetItemDto: any) {
    return this.budgetService.updateBudgetItem(itemId, updateBudgetItemDto);
  }

  @Delete('items/:itemId')
  removeBudgetItem(@Param('itemId') itemId: string) {
    return this.budgetService.removeBudgetItem(itemId);
  }

  @Post(':id/approve')
  approveBudget(@Param('id') id: string, @Body() approveDto: { approvedBy: string }) {
    return this.budgetService.approveBudget(id, approveDto.approvedBy);
  }

  @Post(':id/activate')
  activateBudget(@Param('id') id: string) {
    return this.budgetService.activateBudget(id);
  }

  @Get(':id/variance-report')
  getBudgetVarianceReport(@Param('id') id: string) {
    return this.budgetService.getBudgetVarianceReport(id);
  }
}
