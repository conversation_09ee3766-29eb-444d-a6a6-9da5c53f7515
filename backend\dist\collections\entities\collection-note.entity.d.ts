import { CollectionCase } from './collection-case.entity';
export declare enum NoteType {
    GENERAL = "general",
    CONTACT = "contact",
    PAYMENT = "payment",
    DISPUTE = "dispute",
    LEGAL = "legal",
    INTERNAL = "internal",
    CUSTOMER = "customer",
    SYSTEM = "system"
}
export declare class CollectionNote {
    id: string;
    caseId: string;
    case: CollectionCase;
    type: NoteType;
    title: string;
    content: string;
    createdBy: string;
    isPrivate: boolean;
    isPinned: boolean;
    tags: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
