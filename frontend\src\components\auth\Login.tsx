import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Space, Divider } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useAuth } from '../../hooks/useAuth';
import { LoginRequest } from '../../types/auth';
import LanguageSwitcher from '../common/LanguageSwitcher';

const { Title, Text } = Typography;

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;

  .ant-card-body {
    padding: 40px;
  }
`;

const StyledTitle = styled(Title)`
  text-align: center;
  margin-bottom: 8px !important;
  color: #1f2937;
`;

const StyledSubtitle = styled(Text)`
  display: block;
  text-align: center;
  margin-bottom: 32px;
  color: #6b7280;
`;

const StyledButton = styled(Button)`
  width: 100%;
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
`;

const HeaderActions = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
`;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const onFinish = async (values: LoginRequest) => {
    setLoading(true);
    try {
      await login(values);
      message.success(t('common.success'));
      navigate('/dashboard');
    } catch (error: any) {
      message.error(error.response?.data?.message || t('common.error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <LoginContainer>
      <HeaderActions>
        <LanguageSwitcher />
      </HeaderActions>

      <LoginCard>
        <StyledTitle level={2}>{t('auth.loginTitle')}</StyledTitle>
        <StyledSubtitle>{t('auth.loginSubtitle')}</StyledSubtitle>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            name="email"
            label={t('auth.email')}
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('auth.email')}
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label={t('auth.password')}
            rules={[{ required: true, message: 'Please input your password!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('auth.password')}
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <StyledButton
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              {t('auth.signIn')}
            </StyledButton>
          </Form.Item>
        </Form>

        <Divider />

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text>
            {t('auth.dontHaveAccount')}{' '}
            <Link to="/register">{t('auth.signUp')}</Link>
          </Text>
        </Space>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
