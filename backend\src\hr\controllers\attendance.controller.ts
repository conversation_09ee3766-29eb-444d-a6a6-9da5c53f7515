import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AttendanceService } from '../services/attendance.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('hr/attendance')
@UseGuards(JwtAuthGuard)
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  @Post()
  create(@Body() createAttendanceDto: any) {
    return this.attendanceService.create(createAttendanceDto);
  }

  @Get()
  findAll(
    @Query('employeeId') employeeId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('status') status?: string
  ) {
    const filters: any = {};
    if (employeeId) filters.employeeId = employeeId;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (status) filters.status = status;

    return this.attendanceService.findAll(filters);
  }

  @Get('report/:employeeId')
  getAttendanceReport(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    return this.attendanceService.getAttendanceReport(
      employeeId,
      new Date(startDate),
      new Date(endDate)
    );
  }

  @Post('check-in')
  checkIn(@Body() checkInDto: { employeeId: string; location?: string; gpsCoordinates?: any }) {
    return this.attendanceService.checkIn(checkInDto.employeeId, checkInDto);
  }

  @Post('check-out')
  checkOut(@Body() checkOutDto: { employeeId: string; notes?: string }) {
    return this.attendanceService.checkOut(checkOutDto.employeeId, checkOutDto);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.attendanceService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateAttendanceDto: any) {
    return this.attendanceService.update(id, updateAttendanceDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.attendanceService.remove(id);
  }
}
