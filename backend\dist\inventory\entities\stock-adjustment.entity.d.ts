import { Product } from './product.entity';
import { Warehouse } from './warehouse.entity';
export declare enum AdjustmentType {
    INCREASE = "increase",
    DECREASE = "decrease"
}
export declare enum AdjustmentReason {
    PHYSICAL_COUNT = "physical_count",
    DAMAGE = "damage",
    EXPIRY = "expiry",
    THEFT = "theft",
    LOSS = "loss",
    FOUND = "found",
    CORRECTION = "correction",
    WRITE_OFF = "write_off",
    OTHER = "other"
}
export declare class StockAdjustment {
    id: string;
    adjustmentNumber: string;
    productId: string;
    product: Product;
    warehouseId: string;
    warehouse: Warehouse;
    type: AdjustmentType;
    reason: AdjustmentReason;
    quantityBefore: number;
    quantityAdjusted: number;
    quantityAfter: number;
    unitCost: number;
    totalCost: number;
    adjustmentDate: Date;
    notes: string;
    performedBy: string;
    approvedBy: string;
    approvedAt: Date;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
