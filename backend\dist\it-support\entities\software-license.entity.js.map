{"version": 3, "file": "software-license.entity.js", "sourceRoot": "", "sources": ["../../../src/it-support/entities/software-license.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAMiB;AAEjB,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,sCAAuB,CAAA;IACvB,4CAA6B,CAAA;IAC7B,gCAAiB,CAAA;IACjB,0BAAW,CAAA;IACX,8BAAe,CAAA;IACf,oCAAqB,CAAA;IACrB,0CAA2B,CAAA;AAC7B,CAAC,EARW,WAAW,2BAAX,WAAW,QAQtB;AAED,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,kCAAiB,CAAA;IACjB,oCAAmB,CAAA;IACnB,wCAAuB,CAAA;IACvB,wCAAuB,CAAA;IACvB,gCAAe,CAAA;AACjB,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB;AAGM,IAAM,eAAe,GAArB,MAAM,eAAe;IAE1B,EAAE,CAAS;IAGX,YAAY,CAAS;IAGrB,MAAM,CAAS;IAGf,OAAO,CAAS;IAMhB,IAAI,CAAc;IAOlB,MAAM,CAAgB;IAGtB,UAAU,CAAS;IAGnB,aAAa,CAAS;IAGtB,YAAY,CAAS;IAGrB,iBAAiB,CAAS;IAG1B,YAAY,CAAO;IAGnB,UAAU,CAAO;IAGjB,IAAI,CAAS;IAGb,UAAU,CAAS;IAGnB,QAAQ,CAAS;IAGjB,aAAa,CAAS;IAGtB,WAAW,CAAS;IAGpB,aAAa,CAAW;IAGxB,cAAc,CAAW;IAGzB,KAAK,CAAS;IAGd,WAAW,CAAW;IAGtB,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AA/EY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;qDACH;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACzB;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACxB;AAMhB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,WAAW;KAClB,CAAC;;6CACgB;AAOlB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,aAAa,CAAC,MAAM;KAC9B,CAAC;;+CACoB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACrB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;sDACF;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDACf;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;0DACV;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAC3B,IAAI;qDAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAC7B,IAAI;mDAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CACxD;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAClD;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;iDACtB;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAClB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACrB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACjB;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAChB;AAGzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC3B;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACnB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;0BA9EL,eAAe;IAD3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;GACf,eAAe,CA+E3B"}