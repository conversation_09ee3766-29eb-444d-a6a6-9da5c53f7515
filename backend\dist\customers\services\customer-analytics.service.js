"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("../entities/customer.entity");
const customer_interaction_entity_1 = require("../entities/customer-interaction.entity");
const customer_segment_entity_1 = require("../entities/customer-segment.entity");
let CustomerAnalyticsService = class CustomerAnalyticsService {
    customerRepository;
    interactionRepository;
    segmentRepository;
    constructor(customerRepository, interactionRepository, segmentRepository) {
        this.customerRepository = customerRepository;
        this.interactionRepository = interactionRepository;
        this.segmentRepository = segmentRepository;
    }
    async getCustomerOverview() {
        const totalCustomers = await this.customerRepository.count();
        const activeCustomers = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.ACTIVE } });
        const prospects = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.PROSPECT } });
        const leads = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.LEAD } });
        const inactiveCustomers = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.INACTIVE } });
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const newCustomersThisMonth = await this.customerRepository.count({
            where: { createdAt: (0, typeorm_2.Between)(thirtyDaysAgo, new Date()) },
        });
        const conversionRate = prospects > 0 ? (activeCustomers / prospects) * 100 : 0;
        return {
            totalCustomers,
            activeCustomers,
            prospects,
            leads,
            inactiveCustomers,
            newCustomersThisMonth,
            conversionRate: Math.round(conversionRate * 100) / 100,
            customerGrowthRate: totalCustomers > 0 ? (newCustomersThisMonth / totalCustomers) * 100 : 0,
        };
    }
    async getCustomerDistribution() {
        const statusDistribution = await this.customerRepository
            .createQueryBuilder('customer')
            .select('customer.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('customer.status')
            .getRawMany();
        const typeDistribution = await this.customerRepository
            .createQueryBuilder('customer')
            .select('customer.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('customer.type')
            .getRawMany();
        const tierDistribution = await this.customerRepository
            .createQueryBuilder('customer')
            .select('customer.tier', 'tier')
            .addSelect('COUNT(*)', 'count')
            .groupBy('customer.tier')
            .getRawMany();
        return {
            statusDistribution: statusDistribution.map(item => ({
                status: item.status,
                count: parseInt(item.count),
            })),
            typeDistribution: typeDistribution.map(item => ({
                type: item.type,
                count: parseInt(item.count),
            })),
            tierDistribution: tierDistribution.map(item => ({
                tier: item.tier,
                count: parseInt(item.count),
            })),
        };
    }
    async getFinancialMetrics() {
        const financialData = await this.customerRepository
            .createQueryBuilder('customer')
            .select([
            'SUM(customer.totalSpent) as totalRevenue',
            'AVG(customer.totalSpent) as averageCustomerValue',
            'SUM(customer.currentBalance) as totalOutstanding',
            'AVG(customer.averageOrderValue) as averageOrderValue',
            'SUM(customer.loyaltyPoints) as totalLoyaltyPoints',
        ])
            .where('customer.status = :status', { status: customer_entity_1.CustomerStatus.ACTIVE })
            .getRawOne();
        const topCustomers = await this.customerRepository.find({
            where: { status: customer_entity_1.CustomerStatus.ACTIVE },
            order: { totalSpent: 'DESC' },
            take: 10,
            select: ['id', 'firstName', 'lastName', 'companyName', 'totalSpent', 'tier'],
        });
        const revenueByTier = await this.customerRepository
            .createQueryBuilder('customer')
            .select('customer.tier', 'tier')
            .addSelect('SUM(customer.totalSpent)', 'revenue')
            .addSelect('COUNT(*)', 'customerCount')
            .where('customer.status = :status', { status: customer_entity_1.CustomerStatus.ACTIVE })
            .groupBy('customer.tier')
            .getRawMany();
        return {
            totalRevenue: parseFloat(financialData.totalRevenue) || 0,
            averageCustomerValue: parseFloat(financialData.averageCustomerValue) || 0,
            totalOutstanding: parseFloat(financialData.totalOutstanding) || 0,
            averageOrderValue: parseFloat(financialData.averageOrderValue) || 0,
            totalLoyaltyPoints: parseInt(financialData.totalLoyaltyPoints) || 0,
            topCustomers,
            revenueByTier: revenueByTier.map(item => ({
                tier: item.tier,
                revenue: parseFloat(item.revenue) || 0,
                customerCount: parseInt(item.customerCount),
            })),
        };
    }
    async getCustomerLifecycleMetrics() {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
        const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        const acquisitionData = await this.customerRepository
            .createQueryBuilder('customer')
            .select('DATE_FORMAT(customer.createdAt, "%Y-%m") as month')
            .addSelect('COUNT(*) as count')
            .where('customer.createdAt >= :startDate', { startDate: ninetyDaysAgo })
            .groupBy('month')
            .orderBy('month')
            .getRawMany();
        const activeCustomers = await this.customerRepository.count({
            where: { status: customer_entity_1.CustomerStatus.ACTIVE },
        });
        const customersWithRecentActivity = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastContactDate: (0, typeorm_2.Between)(thirtyDaysAgo, now),
            },
        });
        const customersWithRecentPurchase = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastPurchaseDate: (0, typeorm_2.Between)(thirtyDaysAgo, now),
            },
        });
        const churnRiskCustomers = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastContactDate: (0, typeorm_2.Between)(sixtyDaysAgo, thirtyDaysAgo),
            },
        });
        const retentionRate = activeCustomers > 0 ? (customersWithRecentActivity / activeCustomers) * 100 : 0;
        const churnRisk = activeCustomers > 0 ? (churnRiskCustomers / activeCustomers) * 100 : 0;
        return {
            acquisitionTrend: acquisitionData.map(item => ({
                month: item.month,
                count: parseInt(item.count),
            })),
            retentionMetrics: {
                totalActiveCustomers: activeCustomers,
                customersWithRecentActivity,
                customersWithRecentPurchase,
                retentionRate: Math.round(retentionRate * 100) / 100,
                churnRiskCustomers,
                churnRisk: Math.round(churnRisk * 100) / 100,
            },
        };
    }
    async getInteractionAnalytics() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const totalInteractions = await this.interactionRepository.count({
            where: { interactionDate: (0, typeorm_2.Between)(thirtyDaysAgo, new Date()) },
        });
        const interactionTypes = await this.interactionRepository
            .createQueryBuilder('interaction')
            .select('interaction.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .where('interaction.interactionDate >= :startDate', { startDate: thirtyDaysAgo })
            .groupBy('interaction.type')
            .getRawMany();
        const channelDistribution = await this.interactionRepository
            .createQueryBuilder('interaction')
            .select('interaction.channel', 'channel')
            .addSelect('COUNT(*)', 'count')
            .where('interaction.interactionDate >= :startDate', { startDate: thirtyDaysAgo })
            .groupBy('interaction.channel')
            .getRawMany();
        const outcomeAnalysis = await this.interactionRepository
            .createQueryBuilder('interaction')
            .select('interaction.outcome', 'outcome')
            .addSelect('COUNT(*)', 'count')
            .where('interaction.interactionDate >= :startDate', { startDate: thirtyDaysAgo })
            .andWhere('interaction.outcome IS NOT NULL')
            .groupBy('interaction.outcome')
            .getRawMany();
        const followUpRequired = await this.interactionRepository.count({
            where: {
                followUpRequired: true,
                followUpCompleted: false,
            },
        });
        const overdueFollowUps = await this.interactionRepository.count({
            where: {
                followUpRequired: true,
                followUpCompleted: false,
                followUpDate: (0, typeorm_2.Between)(new Date('1900-01-01'), new Date()),
            },
        });
        return {
            totalInteractions,
            interactionTypes: interactionTypes.map(item => ({
                type: item.type,
                count: parseInt(item.count),
            })),
            channelDistribution: channelDistribution.map(item => ({
                channel: item.channel,
                count: parseInt(item.count),
            })),
            outcomeAnalysis: outcomeAnalysis.map(item => ({
                outcome: item.outcome,
                count: parseInt(item.count),
            })),
            followUpMetrics: {
                followUpRequired,
                overdueFollowUps,
                completionRate: followUpRequired > 0 ? ((followUpRequired - overdueFollowUps) / followUpRequired) * 100 : 0,
            },
        };
    }
    async getSegmentAnalytics() {
        const totalSegments = await this.segmentRepository.count();
        const activeSegments = await this.segmentRepository.count({ where: { isActive: true } });
        const segments = await this.segmentRepository.find({
            where: { isActive: true },
            order: { customerCount: 'DESC' },
            take: 10,
        });
        return {
            totalSegments,
            activeSegments,
            topSegments: segments.map(segment => ({
                id: segment.id,
                name: segment.name,
                customerCount: segment.customerCount,
                lastUpdated: segment.lastUpdated,
            })),
        };
    }
    async getCustomerHealthScore(customerId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
            relations: ['interactions'],
        });
        if (!customer) {
            return null;
        }
        let score = 0;
        const factors = [];
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentInteractions = customer.interactions?.filter(i => i.interactionDate >= thirtyDaysAgo).length || 0;
        const activityScore = Math.min(recentInteractions * 10, 30);
        score += activityScore;
        factors.push({ factor: 'Recent Activity', score: activityScore, maxScore: 30 });
        const purchaseScore = customer.totalSpent > 0 ? Math.min((customer.totalSpent / 1000) * 5, 25) : 0;
        score += purchaseScore;
        factors.push({ factor: 'Purchase History', score: purchaseScore, maxScore: 25 });
        const loyaltyScore = Math.min((customer.loyaltyPoints / 100) * 2, 20);
        score += loyaltyScore;
        factors.push({ factor: 'Loyalty Engagement', score: loyaltyScore, maxScore: 20 });
        const paymentScore = customer.currentBalance <= 0 ? 15 : Math.max(0, 15 - (customer.currentBalance / 1000));
        score += paymentScore;
        factors.push({ factor: 'Payment Behavior', score: paymentScore, maxScore: 15 });
        const responseScore = customer.allowMarketing ? 10 : 5;
        score += responseScore;
        factors.push({ factor: 'Communication Responsiveness', score: responseScore, maxScore: 10 });
        const healthScore = Math.round(score);
        let healthStatus = 'Poor';
        if (healthScore >= 80)
            healthStatus = 'Excellent';
        else if (healthScore >= 60)
            healthStatus = 'Good';
        else if (healthScore >= 40)
            healthStatus = 'Fair';
        return {
            customerId,
            healthScore,
            healthStatus,
            maxScore: 100,
            factors,
            recommendations: this.generateHealthRecommendations(healthScore, factors),
        };
    }
    generateHealthRecommendations(score, factors) {
        const recommendations = [];
        if (score < 60) {
            const lowFactors = factors.filter(f => (f.score / f.maxScore) < 0.5);
            lowFactors.forEach(factor => {
                switch (factor.factor) {
                    case 'Recent Activity':
                        recommendations.push('Schedule a follow-up call or meeting to re-engage');
                        break;
                    case 'Purchase History':
                        recommendations.push('Offer personalized product recommendations or discounts');
                        break;
                    case 'Loyalty Engagement':
                        recommendations.push('Promote loyalty program benefits and rewards');
                        break;
                    case 'Payment Behavior':
                        recommendations.push('Review payment terms and offer payment plan options');
                        break;
                    case 'Communication Responsiveness':
                        recommendations.push('Update communication preferences and channels');
                        break;
                }
            });
        }
        if (recommendations.length === 0) {
            recommendations.push('Customer is performing well - maintain current engagement level');
        }
        return recommendations;
    }
    async getDashboardMetrics() {
        const overview = await this.getCustomerOverview();
        const financial = await this.getFinancialMetrics();
        const lifecycle = await this.getCustomerLifecycleMetrics();
        const interactions = await this.getInteractionAnalytics();
        return {
            overview,
            financial: {
                totalRevenue: financial.totalRevenue,
                averageCustomerValue: financial.averageCustomerValue,
                totalOutstanding: financial.totalOutstanding,
            },
            lifecycle: {
                retentionRate: lifecycle.retentionMetrics.retentionRate,
                churnRisk: lifecycle.retentionMetrics.churnRisk,
            },
            interactions: {
                totalInteractions: interactions.totalInteractions,
                followUpRequired: interactions.followUpMetrics.followUpRequired,
            },
        };
    }
};
exports.CustomerAnalyticsService = CustomerAnalyticsService;
exports.CustomerAnalyticsService = CustomerAnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_interaction_entity_1.CustomerInteraction)),
    __param(2, (0, typeorm_1.InjectRepository)(customer_segment_entity_1.CustomerSegment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerAnalyticsService);
//# sourceMappingURL=customer-analytics.service.js.map