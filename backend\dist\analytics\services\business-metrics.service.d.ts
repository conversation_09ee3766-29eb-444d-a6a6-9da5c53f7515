import { Repository } from 'typeorm';
import { Customer } from '../../sales/entities/customer.entity';
import { Invoice } from '../../sales/entities/invoice.entity';
export declare class BusinessMetricsService {
    private customerRepository;
    private invoiceRepository;
    constructor(customerRepository: Repository<Customer>, invoiceRepository: Repository<Invoice>);
    getBusinessMetrics(companyId: string, period?: string): Promise<{
        totalRevenue: number;
        totalExpenses: number;
        netProfit: number;
        totalCustomers: number;
        totalProjects: number;
        totalEmployees: number;
        revenueGrowth: number;
        customerGrowth: number;
        profitGrowth: number;
        projectGrowth: number;
        departmentMetrics: {
            department: string;
            revenue: number;
            expenses: number;
            profit: number;
            efficiency: number;
            trend: string;
        }[];
    }>;
    getRevenueMetrics(companyId: string, period?: string): Promise<{
        totalRevenue: number;
        totalInvoices: number;
        averageInvoiceValue: number;
        revenueByMonth: {
            month: string;
            revenue: number;
        }[];
    }>;
    getDepartmentMetrics(companyId: string, period?: string): Promise<{
        department: string;
        revenue: number;
        expenses: number;
        profit: number;
        efficiency: number;
        trend: string;
    }[]>;
    getGrowthMetrics(companyId: string, period?: string): Promise<{
        revenueGrowthTrend: number;
        customerGrowthTrend: number;
        profitGrowthTrend: number;
        monthlyGrowthRates: {
            month: string;
            growth: number;
        }[];
    }>;
    private getMetricsForPeriod;
    private getRevenueByMonth;
    private calculateGrowthTrends;
    private parsePeriod;
    private calculateGrowth;
}
