{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../src/config/database.config.ts"], "names": [], "mappings": ";;;AAGO,MAAM,iBAAiB,GAAG,CAAC,aAA4B,EAAwB,EAAE;IACtF,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC;IAE1E,IAAI,WAAW,EAAE,CAAC;QAEhB,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;YAClC,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;YAClC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;YAC1C,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;YAC1C,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAC7C,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;YAClD,WAAW,EAAE,OAAO,KAAK,aAAa;YACtC,OAAO,EAAE,OAAO,KAAK,aAAa;SACnC,CAAC;IACJ,CAAC;SAAM,CAAC;QAEN,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,qBAAqB;YAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;YAClD,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AA3BW,QAAA,iBAAiB,qBA2B5B;AAEK,MAAM,uBAAuB,GAAG,CACrC,aAA4B,EAC5B,QAAgB,EACM,EAAE;IACxB,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC;IAE1E,IAAI,WAAW,EAAE,CAAC;QAEhB,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;YAClC,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;YAClC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;YAC1C,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;YAC1C,QAAQ,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,QAAQ,EAAE;YAC/D,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;YAClD,WAAW,EAAE,OAAO,KAAK,aAAa;YACtC,OAAO,EAAE,OAAO,KAAK,aAAa;SACnC,CAAC;IACJ,CAAC;SAAM,CAAC;QAEN,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,oBAAoB,QAAQ,KAAK;YAC3C,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;YAClD,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AA9BW,QAAA,uBAAuB,2BA8BlC"}