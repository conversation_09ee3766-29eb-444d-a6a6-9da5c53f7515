"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TenantConnectionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantConnectionService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("typeorm");
const database_config_1 = require("../config/database.config");
let TenantConnectionService = TenantConnectionService_1 = class TenantConnectionService {
    configService;
    logger = new common_1.Logger(TenantConnectionService_1.name);
    connections = new Map();
    constructor(configService) {
        this.configService = configService;
    }
    async getTenantConnection(tenantId) {
        if (this.connections.has(tenantId)) {
            const connection = this.connections.get(tenantId);
            if (connection && connection.isInitialized) {
                return connection;
            }
        }
        const config = (0, database_config_1.getTenantDatabaseConfig)(this.configService, tenantId);
        const dataSource = new typeorm_1.DataSource(config);
        try {
            await dataSource.initialize();
            this.connections.set(tenantId, dataSource);
            this.logger.log(`Connected to tenant database: ${tenantId}`);
            return dataSource;
        }
        catch (error) {
            this.logger.error(`Failed to connect to tenant database: ${tenantId}`, error);
            throw error;
        }
    }
    async createTenantDatabase(tenantId) {
        const usePostgres = this.configService.get('USE_POSTGRES', 'false') === 'true';
        if (!usePostgres) {
            try {
                const tenantConnection = await this.getTenantConnection(tenantId);
                await tenantConnection.synchronize();
                this.logger.log(`Created and synchronized SQLite tenant database: ${tenantId}`);
                return;
            }
            catch (error) {
                this.logger.error(`Failed to create SQLite tenant database: ${tenantId}`, error);
                throw error;
            }
        }
        const masterConfig = {
            type: 'postgres',
            host: this.configService.get('DB_HOST'),
            port: this.configService.get('DB_PORT'),
            username: this.configService.get('DB_USERNAME'),
            password: this.configService.get('DB_PASSWORD'),
            database: 'postgres',
        };
        const masterDataSource = new typeorm_1.DataSource(masterConfig);
        try {
            await masterDataSource.initialize();
            const databaseName = `${this.configService.get('TENANT_DB_PREFIX')}${tenantId}`;
            const result = await masterDataSource.query('SELECT 1 FROM pg_database WHERE datname = $1', [databaseName]);
            if (result.length === 0) {
                await masterDataSource.query(`CREATE DATABASE "${databaseName}"`);
                this.logger.log(`Created tenant database: ${databaseName}`);
            }
            else {
                this.logger.log(`Tenant database already exists: ${databaseName}`);
            }
            await masterDataSource.destroy();
            const tenantConnection = await this.getTenantConnection(tenantId);
            await tenantConnection.synchronize();
            this.logger.log(`Synchronized tenant database schema: ${databaseName}`);
        }
        catch (error) {
            this.logger.error(`Failed to create tenant database: ${tenantId}`, error);
            if (masterDataSource.isInitialized) {
                await masterDataSource.destroy();
            }
            throw error;
        }
    }
    async closeTenantConnection(tenantId) {
        const connection = this.connections.get(tenantId);
        if (connection && connection.isInitialized) {
            await connection.destroy();
            this.connections.delete(tenantId);
            this.logger.log(`Closed tenant database connection: ${tenantId}`);
        }
    }
    async closeAllConnections() {
        const promises = Array.from(this.connections.keys()).map(tenantId => this.closeTenantConnection(tenantId));
        await Promise.all(promises);
    }
};
exports.TenantConnectionService = TenantConnectionService;
exports.TenantConnectionService = TenantConnectionService = TenantConnectionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], TenantConnectionService);
//# sourceMappingURL=tenant-connection.service.js.map