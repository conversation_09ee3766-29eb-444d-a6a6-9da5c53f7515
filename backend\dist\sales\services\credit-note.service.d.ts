import { Repository } from 'typeorm';
import { CreditNote } from '../entities/credit-note.entity';
import { CreditNoteItem } from '../entities/credit-note-item.entity';
import { CreateCreditNoteDto } from '../dto/create-credit-note.dto';
export declare class CreditNoteService {
    private creditNoteRepository;
    private creditNoteItemRepository;
    constructor(creditNoteRepository: Repository<CreditNote>, creditNoteItemRepository: Repository<CreditNoteItem>);
    create(createCreditNoteDto: CreateCreditNoteDto, tenantId: string): Promise<CreditNote>;
    findAll(tenantId: string): Promise<CreditNote[]>;
    findOne(id: string, tenantId: string): Promise<CreditNote>;
    update(id: string, updateCreditNoteDto: Partial<CreateCreditNoteDto>, tenantId: string): Promise<CreditNote>;
    remove(id: string, tenantId: string): Promise<void>;
    updateStatus(id: string, status: string, tenantId: string): Promise<CreditNote>;
    getCreditNoteStats(tenantId: string): Promise<{
        totalCreditNotes: number;
        issuedCreditNotes: number;
        appliedCreditNotes: number;
        totalAmount: number;
        typeStats: any[];
    }>;
    private calculateCreditNoteTotals;
    private calculateItemTax;
    private generateCreditNoteNumber;
}
