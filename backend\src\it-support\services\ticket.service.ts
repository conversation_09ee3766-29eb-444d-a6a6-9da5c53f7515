import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ticket, TicketStatus, TicketPriority } from '../entities/ticket.entity';
import { TicketComment } from '../entities/ticket-comment.entity';

@Injectable()
export class TicketService {
  constructor(
    @InjectRepository(Ticket)
    private ticketRepository: Repository<Ticket>,
    @InjectRepository(TicketComment)
    private ticketCommentRepository: Repository<TicketComment>,
  ) {}

  async create(ticketData: Partial<Ticket>): Promise<Ticket> {
    const ticketNumber = await this.generateTicketNumber();
    const ticket = this.ticketRepository.create({
      ...ticketData,
      ticketNumber,
      status: TicketStatus.OPEN,
      createdAt: new Date(),
    });
    return this.ticketRepository.save(ticket);
  }

  async findAll(): Promise<Ticket[]> {
    return this.ticketRepository.find({
      relations: ['assignedTo', 'createdBy', 'comments'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Ticket> {
    const ticket = await this.ticketRepository.findOne({
      where: { id },
      relations: ['assignedTo', 'createdBy', 'comments', 'comments.author'],
    });

    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }

    return ticket;
  }

  async update(id: string, updateData: Partial<Ticket>): Promise<Ticket> {
    await this.ticketRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const ticket = await this.findOne(id);
    await this.ticketRepository.remove(ticket);
  }

  async findByStatus(status: TicketStatus): Promise<Ticket[]> {
    return this.ticketRepository.find({
      where: { status },
      relations: ['assignedTo', 'createdBy'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByPriority(priority: TicketPriority): Promise<Ticket[]> {
    return this.ticketRepository.find({
      where: { priority },
      relations: ['assignedTo', 'createdBy'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByAssignee(assigneeId: string): Promise<Ticket[]> {
    return this.ticketRepository.find({
      where: { assignedToId: assigneeId },
      relations: ['createdBy'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByCreator(creatorId: string): Promise<Ticket[]> {
    return this.ticketRepository.find({
      where: { createdById: creatorId },
      relations: ['assignedTo'],
      order: { createdAt: 'DESC' },
    });
  }

  async assignTicket(ticketId: string, assigneeId: string): Promise<Ticket> {
    await this.ticketRepository.update(ticketId, {
      assignedToId: assigneeId,
      status: TicketStatus.IN_PROGRESS,
    });

    // Add comment about assignment
    await this.addComment(ticketId, `Ticket assigned to user ${assigneeId}`, 'system');

    return this.findOne(ticketId);
  }

  async updateStatus(ticketId: string, status: TicketStatus, userId?: string): Promise<Ticket> {
    await this.ticketRepository.update(ticketId, { status });

    // Add comment about status change
    await this.addComment(ticketId, `Status changed to ${status}`, userId || 'system');

    return this.findOne(ticketId);
  }

  async updatePriority(ticketId: string, priority: TicketPriority, userId?: string): Promise<Ticket> {
    await this.ticketRepository.update(ticketId, { priority });

    // Add comment about priority change
    await this.addComment(ticketId, `Priority changed to ${priority}`, userId || 'system');

    return this.findOne(ticketId);
  }

  async addComment(ticketId: string, content: string, authorId: string): Promise<TicketComment> {
    const comment = this.ticketCommentRepository.create({
      ticketId,
      content,
      authorId,
      createdAt: new Date(),
    });

    return this.ticketCommentRepository.save(comment);
  }

  async getTicketComments(ticketId: string): Promise<TicketComment[]> {
    return this.ticketCommentRepository.find({
      where: { ticketId },
      relations: ['author'],
      order: { createdAt: 'ASC' },
    });
  }

  async searchTickets(searchTerm: string): Promise<Ticket[]> {
    return this.ticketRepository
      .createQueryBuilder('ticket')
      .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
      .leftJoinAndSelect('ticket.createdBy', 'createdBy')
      .where('ticket.title ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('ticket.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('ticket.ticketNumber ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('ticket.createdAt', 'DESC')
      .getMany();
  }

  async getTicketStatistics(): Promise<any> {
    const totalTickets = await this.ticketRepository.count();
    const openTickets = await this.ticketRepository.count({ where: { status: TicketStatus.OPEN } });
    const inProgressTickets = await this.ticketRepository.count({ where: { status: TicketStatus.IN_PROGRESS } });
    const resolvedTickets = await this.ticketRepository.count({ where: { status: TicketStatus.RESOLVED } });
    const closedTickets = await this.ticketRepository.count({ where: { status: TicketStatus.CLOSED } });

    const highPriorityTickets = await this.ticketRepository.count({ where: { priority: TicketPriority.HIGH } });
    const criticalTickets = await this.ticketRepository.count({ where: { priority: TicketPriority.CRITICAL } });

    return {
      totalTickets,
      openTickets,
      inProgressTickets,
      resolvedTickets,
      closedTickets,
      highPriorityTickets,
      criticalTickets,
      resolutionRate: totalTickets > 0 ? ((resolvedTickets + closedTickets) / totalTickets) * 100 : 0,
    };
  }

  async getOverdueTickets(): Promise<Ticket[]> {
    const now = new Date();
    return this.ticketRepository
      .createQueryBuilder('ticket')
      .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
      .leftJoinAndSelect('ticket.createdBy', 'createdBy')
      .where('ticket.dueDate < :now', { now })
      .andWhere('ticket.status NOT IN (:...statuses)', { 
        statuses: [TicketStatus.RESOLVED, TicketStatus.CLOSED] 
      })
      .orderBy('ticket.dueDate', 'ASC')
      .getMany();
  }

  async getTicketsByCategory(category: string): Promise<Ticket[]> {
    return this.ticketRepository.find({
      where: { category },
      relations: ['assignedTo', 'createdBy'],
      order: { createdAt: 'DESC' },
    });
  }

  async escalateTicket(ticketId: string, reason: string, userId?: string): Promise<Ticket> {
    const ticket = await this.findOne(ticketId);
    
    let newPriority = ticket.priority;
    if (ticket.priority === TicketPriority.LOW) {
      newPriority = TicketPriority.MEDIUM;
    } else if (ticket.priority === TicketPriority.MEDIUM) {
      newPriority = TicketPriority.HIGH;
    } else if (ticket.priority === TicketPriority.HIGH) {
      newPriority = TicketPriority.CRITICAL;
    }

    await this.ticketRepository.update(ticketId, { priority: newPriority });

    // Add comment about escalation
    await this.addComment(ticketId, `Ticket escalated to ${newPriority}: ${reason}`, userId || 'system');

    return this.findOne(ticketId);
  }

  async closeTicket(ticketId: string, resolution: string, userId?: string): Promise<Ticket> {
    await this.ticketRepository.update(ticketId, {
      status: TicketStatus.CLOSED,
      resolution,
      resolvedAt: new Date(),
    });

    // Add comment about closure
    await this.addComment(ticketId, `Ticket closed: ${resolution}`, userId || 'system');

    return this.findOne(ticketId);
  }

  async reopenTicket(ticketId: string, reason: string, userId?: string): Promise<Ticket> {
    await this.ticketRepository.update(ticketId, {
      status: TicketStatus.OPEN,
      resolvedAt: null,
      resolution: null,
    });

    // Add comment about reopening
    await this.addComment(ticketId, `Ticket reopened: ${reason}`, userId || 'system');

    return this.findOne(ticketId);
  }

  private async generateTicketNumber(): Promise<string> {
    const count = await this.ticketRepository.count();
    const sequence = (count + 1).toString().padStart(6, '0');
    const year = new Date().getFullYear();
    return `TKT-${year}-${sequence}`;
  }

  async getDashboardMetrics(): Promise<any> {
    const stats = await this.getTicketStatistics();
    const overdueTickets = await this.getOverdueTickets();
    
    return {
      ...stats,
      overdueCount: overdueTickets.length,
      urgentTickets: stats.highPriorityTickets + stats.criticalTickets,
    };
  }
}
