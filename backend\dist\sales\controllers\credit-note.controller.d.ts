import { CreditNoteService } from '../services/credit-note.service';
import { CreateCreditNoteDto } from '../dto/create-credit-note.dto';
export declare class CreditNoteController {
    private readonly creditNoteService;
    constructor(creditNoteService: CreditNoteService);
    create(createCreditNoteDto: CreateCreditNoteDto, req: any): Promise<import("../entities/credit-note.entity").CreditNote>;
    findAll(req: any): Promise<import("../entities/credit-note.entity").CreditNote[]>;
    getStats(req: any): Promise<{
        totalCreditNotes: number;
        issuedCreditNotes: number;
        appliedCreditNotes: number;
        totalAmount: number;
        typeStats: any[];
    }>;
    findOne(id: string, req: any): Promise<import("../entities/credit-note.entity").CreditNote>;
    update(id: string, updateCreditNoteDto: Partial<CreateCreditNoteDto>, req: any): Promise<import("../entities/credit-note.entity").CreditNote>;
    remove(id: string, req: any): Promise<void>;
    updateStatus(id: string, body: {
        status: string;
    }, req: any): Promise<import("../entities/credit-note.entity").CreditNote>;
}
