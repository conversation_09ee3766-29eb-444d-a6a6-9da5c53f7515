import { PurchaseOrder } from './purchase-order.entity';
import { Product } from './product.entity';
export declare class PurchaseOrderItem {
    id: string;
    purchaseOrderId: string;
    purchaseOrder: PurchaseOrder;
    productId: string;
    product: Product;
    quantityOrdered: number;
    quantityReceived: number;
    quantityPending: number;
    unitPrice: number;
    totalPrice: number;
    discountPercentage: number;
    discountAmount: number;
    taxPercentage: number;
    taxAmount: number;
    lineTotal: number;
    expectedDeliveryDate: Date;
    actualDeliveryDate: Date;
    description: string;
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
