"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockMovementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const stock_movement_entity_1 = require("../entities/stock-movement.entity");
let StockMovementService = class StockMovementService {
    stockMovementRepository;
    constructor(stockMovementRepository) {
        this.stockMovementRepository = stockMovementRepository;
    }
    async findAll() {
        return this.stockMovementRepository.find({
            relations: ['product', 'warehouse'],
            order: { movementDate: 'DESC' },
        });
    }
    async findByProduct(productId) {
        return this.stockMovementRepository.find({
            where: { productId },
            relations: ['warehouse'],
            order: { movementDate: 'DESC' },
        });
    }
    async findByWarehouse(warehouseId) {
        return this.stockMovementRepository.find({
            where: { warehouseId },
            relations: ['product'],
            order: { movementDate: 'DESC' },
        });
    }
    async findByDateRange(startDate, endDate) {
        return this.stockMovementRepository.find({
            where: {
                movementDate: (0, typeorm_2.Between)(startDate, endDate),
            },
            relations: ['product', 'warehouse'],
            order: { movementDate: 'DESC' },
        });
    }
    async getMovementStatistics(startDate, endDate) {
        const query = this.stockMovementRepository.createQueryBuilder('movement');
        if (startDate && endDate) {
            query.where('movement.movementDate BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        const totalMovements = await query.getCount();
        const inMovements = await query
            .clone()
            .andWhere('movement.type = :type', { type: 'IN' })
            .getCount();
        const outMovements = await query
            .clone()
            .andWhere('movement.type = :type', { type: 'OUT' })
            .getCount();
        const totalInQuantity = await query
            .clone()
            .andWhere('movement.type = :type', { type: 'IN' })
            .select('SUM(movement.quantity)', 'total')
            .getRawOne();
        const totalOutQuantity = await query
            .clone()
            .andWhere('movement.type = :type', { type: 'OUT' })
            .select('SUM(movement.quantity)', 'total')
            .getRawOne();
        return {
            totalMovements,
            inMovements,
            outMovements,
            totalInQuantity: parseInt(totalInQuantity.total) || 0,
            totalOutQuantity: parseInt(totalOutQuantity.total) || 0,
            netMovement: (parseInt(totalInQuantity.total) || 0) - (parseInt(totalOutQuantity.total) || 0),
        };
    }
    async getMovementsByType(movementType) {
        return this.stockMovementRepository.find({
            where: { movementType },
            relations: ['product', 'warehouse'],
            order: { movementDate: 'DESC' },
        });
    }
    async getRecentMovements(limit = 50) {
        return this.stockMovementRepository.find({
            relations: ['product', 'warehouse'],
            order: { movementDate: 'DESC' },
            take: limit,
        });
    }
};
exports.StockMovementService = StockMovementService;
exports.StockMovementService = StockMovementService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(stock_movement_entity_1.StockMovement)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], StockMovementService);
//# sourceMappingURL=stock-movement.service.js.map