import { TicketService } from '../services/ticket.service';
import { Ticket, TicketStatus, TicketPriority } from '../entities/ticket.entity';
export declare class TicketController {
    private readonly ticketService;
    constructor(ticketService: TicketService);
    create(createTicketDto: Partial<Ticket>): Promise<Ticket>;
    findAll(status?: TicketStatus, priority?: TicketPriority, assigneeId?: string, creatorId?: string): Promise<Ticket[]>;
    getStatistics(): Promise<any>;
    getDashboardMetrics(): Promise<any>;
    getOverdueTickets(): Promise<Ticket[]>;
    searchTickets(searchTerm: string): Promise<Ticket[]>;
    getTicketsByCategory(category: string): Promise<Ticket[]>;
    findOne(id: string): Promise<Ticket>;
    getTicketComments(id: string): Promise<import("../entities/ticket-comment.entity").TicketComment[]>;
    addComment(id: string, commentData: {
        content: string;
        authorId: string;
    }): Promise<import("../entities/ticket-comment.entity").TicketComment>;
    update(id: string, updateTicketDto: Partial<Ticket>): Promise<Ticket>;
    assignTicket(id: string, assignmentData: {
        assigneeId: string;
    }): Promise<Ticket>;
    updateStatus(id: string, statusData: {
        status: TicketStatus;
        userId?: string;
    }): Promise<Ticket>;
    updatePriority(id: string, priorityData: {
        priority: TicketPriority;
        userId?: string;
    }): Promise<Ticket>;
    escalateTicket(id: string, escalationData: {
        reason: string;
        userId?: string;
    }): Promise<Ticket>;
    closeTicket(id: string, closeData: {
        resolution: string;
        userId?: string;
    }): Promise<Ticket>;
    reopenTicket(id: string, reopenData: {
        reason: string;
        userId?: string;
    }): Promise<Ticket>;
    remove(id: string): Promise<void>;
}
