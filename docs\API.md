# ZaidanOne Management System - API Documentation

## 📡 API Overview

The ZaidanOne Management System provides a comprehensive RESTful API built with NestJS, offering endpoints for all business operations across departments.

**Base URL**: `http://localhost:3001/api` (Development)
**API Version**: v1
**Authentication**: JWT Bearer Token
**Content-Type**: `application/json`

## 🔐 Authentication

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "admin",
      "tenantId": "company-uuid"
    }
  }
}
```

### Register
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "companyName": "Acme Corp"
}
```

### Refresh Token
```http
POST /api/auth/refresh
Authorization: Bearer <refresh_token>
```

## 📊 Dashboard API

### Get Dashboard Overview
```http
GET /api/dashboard/overview
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalRevenue": 2450000,
    "totalExpenses": 1680000,
    "netProfit": 770000,
    "totalCustomers": 1247,
    "totalProjects": 89,
    "totalEmployees": 156,
    "recentActivities": [...]
  }
}
```

### Get KPIs
```http
GET /api/dashboard/kpis?period=30d
Authorization: Bearer <token>
```

## 🏢 Sales API

### Customers

#### List Customers
```http
GET /api/sales/customers?page=1&limit=10&search=john
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "uuid",
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone": "******-0123",
        "type": "Individual",
        "status": "Active",
        "totalOrders": 15,
        "totalValue": 45000,
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

#### Create Customer
```http
POST /api/sales/customers
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "John Smith",
  "email": "<EMAIL>",
  "phone": "******-0123",
  "type": "Individual",
  "address": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "country": "USA"
  },
  "billingMethod": "Email",
  "currency": "USD",
  "language": "English"
}
```

#### Update Customer
```http
PATCH /api/sales/customers/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "John Smith Updated",
  "phone": "******-0124"
}
```

#### Delete Customer
```http
DELETE /api/sales/customers/:id
Authorization: Bearer <token>
```

### Invoices

#### List Invoices
```http
GET /api/sales/invoices?page=1&limit=10&status=pending
Authorization: Bearer <token>
```

#### Create Invoice
```http
POST /api/sales/invoices
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerId": "customer-uuid",
  "invoiceNumber": "INV-2024-001",
  "dueDate": "2024-06-30",
  "items": [
    {
      "description": "Web Development",
      "quantity": 1,
      "rate": 5000,
      "amount": 5000
    }
  ],
  "subtotal": 5000,
  "taxRate": 15,
  "taxAmount": 750,
  "total": 5750,
  "notes": "Payment due within 30 days"
}
```

#### Send Invoice
```http
POST /api/sales/invoices/:id/send
Authorization: Bearer <token>
```

## 📋 Projects API

### List Projects
```http
GET /api/projects?page=1&limit=10&status=active
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "uuid",
        "name": "Website Redesign",
        "description": "Complete website redesign",
        "status": "In Progress",
        "priority": "High",
        "startDate": "2024-05-01",
        "endDate": "2024-07-01",
        "progress": 65,
        "budget": 50000,
        "spent": 32500,
        "teamMembers": ["John Doe", "Jane Smith"],
        "tasks": 24,
        "completedTasks": 16
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10
  }
}
```

### Create Project
```http
POST /api/projects
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Mobile App Development",
  "description": "iOS and Android app development",
  "startDate": "2024-06-01",
  "endDate": "2024-09-01",
  "budget": 75000,
  "priority": "High",
  "status": "Planning",
  "teamMembers": ["user-uuid-1", "user-uuid-2"]
}
```

### Project Tasks
```http
GET /api/projects/:id/tasks
Authorization: Bearer <token>

POST /api/projects/:id/tasks
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Design Database Schema",
  "description": "Create database schema for the application",
  "assigneeId": "user-uuid",
  "dueDate": "2024-06-15",
  "priority": "High",
  "estimatedHours": 8
}
```

## 👥 HR API

### Employees

#### List Employees
```http
GET /api/hr/employees?page=1&limit=10&department=Engineering
Authorization: Bearer <token>
```

#### Create Employee
```http
POST /api/hr/employees
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "******-0101",
  "department": "Engineering",
  "position": "Senior Developer",
  "salary": 85000,
  "hireDate": "2024-01-15",
  "managerId": "manager-uuid"
}
```

### Attendance
```http
GET /api/hr/attendance?employeeId=uuid&date=2024-05-20
Authorization: Bearer <token>

POST /api/hr/attendance
Authorization: Bearer <token>
Content-Type: application/json

{
  "employeeId": "employee-uuid",
  "date": "2024-05-20",
  "checkIn": "09:00:00",
  "checkOut": "17:30:00",
  "breakTime": 60,
  "notes": "Regular workday"
}
```

## 💰 Finance API

### Financial Overview
```http
GET /api/finance/overview
Authorization: Bearer <token>
```

### Transactions
```http
GET /api/finance/transactions?page=1&limit=10&type=income
Authorization: Bearer <token>

POST /api/finance/transactions
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "Income",
  "description": "Payment from ABC Corporation",
  "amount": 15000,
  "date": "2024-05-20",
  "category": "Sales",
  "accountId": "account-uuid"
}
```

## 📦 Inventory API

### Inventory Items
```http
GET /api/inventory/items?page=1&limit=10&category=Electronics
Authorization: Bearer <token>

POST /api/inventory/items
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Wireless Mouse",
  "sku": "WM-001",
  "category": "Electronics",
  "currentStock": 45,
  "minStock": 10,
  "maxStock": 100,
  "unitPrice": 25.99,
  "location": "A1-B2",
  "supplierId": "supplier-uuid"
}
```

### Stock Movements
```http
GET /api/inventory/stock-movements?itemId=uuid
Authorization: Bearer <token>

POST /api/inventory/stock-movements
Authorization: Bearer <token>
Content-Type: application/json

{
  "itemId": "item-uuid",
  "type": "IN",
  "quantity": 50,
  "reason": "Purchase Order",
  "referenceNumber": "PO-2024-001",
  "notes": "Received from supplier"
}
```

## 📈 Analytics API

### Business Metrics
```http
GET /api/analytics/business-metrics?period=30d
Authorization: Bearer <token>
```

### Generate Report
```http
POST /api/analytics/generate-report
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "sales",
  "period": {
    "start": "2024-01-01",
    "end": "2024-05-31"
  },
  "format": "pdf",
  "filters": {
    "department": "Sales",
    "status": "completed"
  }
}
```

### Export Data
```http
POST /api/analytics/export/customers?format=excel
Authorization: Bearer <token>
Content-Type: application/json

{
  "filters": {
    "status": "active",
    "createdAfter": "2024-01-01"
  }
}
```

## ⚙️ Settings API

### Company Settings
```http
GET /api/settings/company
Authorization: Bearer <token>

PATCH /api/settings/company
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "ZaidanOne Corporation",
  "email": "<EMAIL>",
  "phone": "+****************",
  "address": "123 Business Street, City, State 12345",
  "currency": "USD",
  "timezone": "America/New_York",
  "language": "English"
}
```

### User Management
```http
GET /api/settings/users
Authorization: Bearer <token>

POST /api/settings/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "manager",
  "department": "Sales",
  "permissions": ["read", "write", "delete"]
}
```

## 📁 File Upload API

### Upload File
```http
POST /api/files/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary_data>
type: "avatar" | "document" | "invoice"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "/uploads/documents/file-uuid.pdf",
    "filename": "document.pdf",
    "size": 1024000,
    "mimeType": "application/pdf"
  }
}
```

## 🚨 Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  },
  "timestamp": "2024-05-20T10:00:00Z",
  "path": "/api/sales/customers"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## 🔄 Pagination

All list endpoints support pagination:

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 100)
- `search` - Search term
- `sort` - Sort field
- `order` - Sort order (asc/desc)

**Response Format:**
```json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 10,
  "totalPages": 10,
  "hasNext": true,
  "hasPrev": false
}
```

## 🔍 Filtering & Search

Most endpoints support filtering and search:

```http
GET /api/sales/customers?search=john&status=active&type=commercial&sort=createdAt&order=desc
```

## 📚 Additional Resources

- **Swagger UI**: http://localhost:3001/api/docs
- **Postman Collection**: Available in `/docs/postman/`
- **API Testing**: Use the provided test suite
- **Rate Limiting**: 100 requests per minute per user

---

For more detailed information, visit the interactive Swagger documentation at `/api/docs` when running the development server.
