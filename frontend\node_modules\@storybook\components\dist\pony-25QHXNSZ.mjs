import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_pony=__commonJS({"../../node_modules/highlight.js/lib/languages/pony.js"(exports,module){function pony(hljs){let KEYWORDS={keyword:"actor addressof and as be break class compile_error compile_intrinsic consume continue delegate digestof do else elseif embed end error for fun if ifdef in interface is isnt lambda let match new not object or primitive recover repeat return struct then trait try type until use var where while with xor",meta:"iso val tag trn box ref",literal:"this false true"},TRIPLE_QUOTE_STRING_MODE={className:"string",begin:'"""',end:'"""',relevance:10},QUOTE_STRING_MODE={className:"string",begin:'"',end:'"',contains:[hljs.BACKSLASH_ESCAPE]},SINGLE_QUOTE_CHAR_MODE={className:"string",begin:"'",end:"'",contains:[hljs.BACKSLASH_ESCAPE],relevance:0},TYPE_NAME={className:"type",begin:"\\b_?[A-Z][\\w]*",relevance:0},PRIMED_NAME={begin:hljs.IDENT_RE+"'",relevance:0};return {name:"Pony",keywords:KEYWORDS,contains:[TYPE_NAME,TRIPLE_QUOTE_STRING_MODE,QUOTE_STRING_MODE,SINGLE_QUOTE_CHAR_MODE,PRIMED_NAME,{className:"number",begin:"(-?)(\\b0[xX][a-fA-F0-9]+|\\b0[bB][01]+|(\\b\\d+(_\\d+)?(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",relevance:0},hljs.C_LINE_COMMENT_MODE,hljs.C_BLOCK_COMMENT_MODE]}}module.exports=pony;}});var pony25QHXNSZ = require_pony();

export { pony25QHXNSZ as default };
