import { __commonJS } from './chunk-JRLSWQMA.mjs';

var require_dsconfig=__commonJS({"../../node_modules/highlight.js/lib/languages/dsconfig.js"(exports,module){function dsconfig(hljs){return {keywords:"dsconfig",contains:[{className:"keyword",begin:"^dsconfig",end:/\s/,excludeEnd:!0,relevance:10},{className:"built_in",begin:/(list|create|get|set|delete)-(\w+)/,end:/\s/,excludeEnd:!0,illegal:"!@#$%^&*()",relevance:10},{className:"built_in",begin:/--(\w+)/,end:/\s/,excludeEnd:!0},{className:"string",begin:/"/,end:/"/},{className:"string",begin:/'/,end:/'/},{className:"string",begin:/[\w\-?]+:\w+/,end:/\W/,relevance:0},{className:"string",begin:/\w+(\-\w+)*/,end:/(?=\W)/,relevance:0},hljs.HASH_COMMENT_MODE]}}module.exports=dsconfig;}});var dsconfigPN4OSR4X = require_dsconfig();

export { dsconfigPN4OSR4X as default };
