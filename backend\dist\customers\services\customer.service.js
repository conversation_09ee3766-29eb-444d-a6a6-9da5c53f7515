"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("../entities/customer.entity");
const customer_contact_entity_1 = require("../entities/customer-contact.entity");
const customer_address_entity_1 = require("../entities/customer-address.entity");
const customer_note_entity_1 = require("../entities/customer-note.entity");
let CustomerService = class CustomerService {
    customerRepository;
    contactRepository;
    addressRepository;
    noteRepository;
    constructor(customerRepository, contactRepository, addressRepository, noteRepository) {
        this.customerRepository = customerRepository;
        this.contactRepository = contactRepository;
        this.addressRepository = addressRepository;
        this.noteRepository = noteRepository;
    }
    async create(customerData) {
        if (!customerData.customerNumber) {
            customerData.customerNumber = await this.generateCustomerNumber();
        }
        this.validateCustomerData(customerData);
        const customer = this.customerRepository.create(customerData);
        return this.customerRepository.save(customer);
    }
    async findAll(options) {
        const { page = 1, limit = 20, search, status, type, tier, groupId } = options || {};
        const queryBuilder = this.customerRepository.createQueryBuilder('customer')
            .leftJoinAndSelect('customer.group', 'group')
            .leftJoinAndSelect('customer.addresses', 'addresses')
            .leftJoinAndSelect('customer.contacts', 'contacts');
        if (search) {
            queryBuilder.andWhere('(customer.firstName LIKE :search OR customer.lastName LIKE :search OR customer.companyName LIKE :search OR customer.primaryEmail LIKE :search OR customer.customerNumber LIKE :search)', { search: `%${search}%` });
        }
        if (status) {
            queryBuilder.andWhere('customer.status = :status', { status });
        }
        if (type) {
            queryBuilder.andWhere('customer.type = :type', { type });
        }
        if (tier) {
            queryBuilder.andWhere('customer.tier = :tier', { tier });
        }
        if (groupId) {
            queryBuilder.andWhere('customer.groupId = :groupId', { groupId });
        }
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        queryBuilder.orderBy('customer.createdAt', 'DESC');
        const [customers, total] = await queryBuilder.getManyAndCount();
        const totalPages = Math.ceil(total / limit);
        return {
            customers,
            total,
            page,
            totalPages,
        };
    }
    async findOne(id) {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: [
                'group',
                'contacts',
                'addresses',
                'notes',
                'documents',
                'interactions',
                'loyaltyHistory',
                'creditHistory',
                'preferences',
            ],
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${id} not found`);
        }
        return customer;
    }
    async findByCustomerNumber(customerNumber) {
        const customer = await this.customerRepository.findOne({
            where: { customerNumber },
            relations: ['group', 'contacts', 'addresses'],
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with number ${customerNumber} not found`);
        }
        return customer;
    }
    async update(id, updateData) {
        const customer = await this.findOne(id);
        this.validateCustomerData({ ...customer, ...updateData });
        await this.customerRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const customer = await this.findOne(id);
        await this.customerRepository.remove(customer);
    }
    async updateStatus(id, status, reason) {
        const customer = await this.findOne(id);
        if (reason) {
            await this.addNote(id, `Status changed to ${status}: ${reason}`, 'system');
        }
        await this.customerRepository.update(id, { status });
        return this.findOne(id);
    }
    async updateTier(id, tier, reason) {
        const customer = await this.findOne(id);
        if (reason) {
            await this.addNote(id, `Tier changed to ${tier}: ${reason}`, 'system');
        }
        await this.customerRepository.update(id, { tier });
        return this.findOne(id);
    }
    async addContact(customerId, contactData) {
        const customer = await this.findOne(customerId);
        const contact = this.contactRepository.create({
            ...contactData,
            customerId,
        });
        return this.contactRepository.save(contact);
    }
    async addAddress(customerId, addressData) {
        const customer = await this.findOne(customerId);
        const address = this.addressRepository.create({
            ...addressData,
            customerId,
        });
        return this.addressRepository.save(address);
    }
    async addNote(customerId, content, type = 'general', createdBy) {
        const customer = await this.findOne(customerId);
        const note = this.noteRepository.create({
            customerId,
            content,
            type,
            createdBy,
        });
        return this.noteRepository.save(note);
    }
    async updateFinancials(id, financials) {
        await this.customerRepository.update(id, financials);
        return this.findOne(id);
    }
    async updateLoyaltyPoints(id, points, operation = 'add') {
        const customer = await this.findOne(id);
        let newPoints;
        switch (operation) {
            case 'add':
                newPoints = customer.loyaltyPoints + points;
                break;
            case 'subtract':
                newPoints = Math.max(0, customer.loyaltyPoints - points);
                break;
            case 'set':
                newPoints = points;
                break;
        }
        await this.customerRepository.update(id, { loyaltyPoints: newPoints });
        await this.addNote(id, `Loyalty points ${operation}: ${points}. New balance: ${newPoints}`, 'loyalty');
        return this.findOne(id);
    }
    async getCustomerStats() {
        const totalCustomers = await this.customerRepository.count();
        const activeCustomers = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.ACTIVE } });
        const prospects = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.PROSPECT } });
        const leads = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.LEAD } });
        const tierStats = await this.customerRepository
            .createQueryBuilder('customer')
            .select('customer.tier', 'tier')
            .addSelect('COUNT(*)', 'count')
            .groupBy('customer.tier')
            .getRawMany();
        const typeStats = await this.customerRepository
            .createQueryBuilder('customer')
            .select('customer.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('customer.type')
            .getRawMany();
        return {
            totalCustomers,
            activeCustomers,
            prospects,
            leads,
            conversionRate: prospects > 0 ? (activeCustomers / prospects) * 100 : 0,
            tierDistribution: tierStats,
            typeDistribution: typeStats,
        };
    }
    async generateCustomerNumber() {
        const year = new Date().getFullYear();
        const count = await this.customerRepository.count();
        return `CUST-${year}-${String(count + 1).padStart(6, '0')}`;
    }
    validateCustomerData(customerData) {
        if (customerData.type === customer_entity_1.CustomerType.INDIVIDUAL) {
            if (!customerData.firstName || !customerData.lastName) {
                throw new common_1.BadRequestException('First name and last name are required for individual customers');
            }
        }
        else if (customerData.type === customer_entity_1.CustomerType.BUSINESS) {
            if (!customerData.companyName) {
                throw new common_1.BadRequestException('Company name is required for business customers');
            }
        }
        if (customerData.primaryEmail && !this.isValidEmail(customerData.primaryEmail)) {
            throw new common_1.BadRequestException('Invalid email format');
        }
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
};
exports.CustomerService = CustomerService;
exports.CustomerService = CustomerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_contact_entity_1.CustomerContact)),
    __param(2, (0, typeorm_1.InjectRepository)(customer_address_entity_1.CustomerAddress)),
    __param(3, (0, typeorm_1.InjectRepository)(customer_note_entity_1.CustomerNote)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerService);
//# sourceMappingURL=customer.service.js.map