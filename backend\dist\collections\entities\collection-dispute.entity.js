"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionDispute = exports.DisputeStatus = exports.DisputeReason = void 0;
const typeorm_1 = require("typeorm");
const collection_case_entity_1 = require("./collection-case.entity");
var DisputeReason;
(function (DisputeReason) {
    DisputeReason["AMOUNT_INCORRECT"] = "amount_incorrect";
    DisputeReason["ALREADY_PAID"] = "already_paid";
    DisputeReason["NOT_MY_DEBT"] = "not_my_debt";
    DisputeReason["IDENTITY_THEFT"] = "identity_theft";
    DisputeReason["STATUTE_OF_LIMITATIONS"] = "statute_of_limitations";
    DisputeReason["BANKRUPTCY"] = "bankruptcy";
    DisputeReason["DECEASED"] = "deceased";
    DisputeReason["QUALITY_ISSUE"] = "quality_issue";
    DisputeReason["SERVICE_NOT_RECEIVED"] = "service_not_received";
    DisputeReason["BILLING_ERROR"] = "billing_error";
    DisputeReason["OTHER"] = "other";
})(DisputeReason || (exports.DisputeReason = DisputeReason = {}));
var DisputeStatus;
(function (DisputeStatus) {
    DisputeStatus["OPEN"] = "open";
    DisputeStatus["INVESTIGATING"] = "investigating";
    DisputeStatus["RESOLVED"] = "resolved";
    DisputeStatus["REJECTED"] = "rejected";
    DisputeStatus["ESCALATED"] = "escalated";
    DisputeStatus["CLOSED"] = "closed";
})(DisputeStatus || (exports.DisputeStatus = DisputeStatus = {}));
let CollectionDispute = class CollectionDispute {
    id;
    caseId;
    case;
    disputeNumber;
    reason;
    status;
    description;
    disputedAmount;
    disputeDate;
    responseDate;
    resolutionDate;
    customerEvidence;
    companyResponse;
    resolution;
    assignedTo;
    attachments;
    metadata;
    createdAt;
    updatedAt;
};
exports.CollectionDispute = CollectionDispute;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CollectionDispute.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CollectionDispute.prototype, "caseId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => collection_case_entity_1.CollectionCase, collectionCase => collectionCase.disputes),
    (0, typeorm_1.JoinColumn)({ name: 'caseId' }),
    __metadata("design:type", collection_case_entity_1.CollectionCase)
], CollectionDispute.prototype, "case", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "disputeNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DisputeReason,
    }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DisputeStatus,
        default: DisputeStatus.OPEN,
    }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CollectionDispute.prototype, "disputedAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], CollectionDispute.prototype, "disputeDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionDispute.prototype, "responseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CollectionDispute.prototype, "resolutionDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "customerEvidence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "companyResponse", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "resolution", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CollectionDispute.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CollectionDispute.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CollectionDispute.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CollectionDispute.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CollectionDispute.prototype, "updatedAt", void 0);
exports.CollectionDispute = CollectionDispute = __decorate([
    (0, typeorm_1.Entity)('collection_disputes')
], CollectionDispute);
//# sourceMappingURL=collection-dispute.entity.js.map