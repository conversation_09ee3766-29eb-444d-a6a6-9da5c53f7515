/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('logicalNot', ALL_ENVS, () => {
    it('Tensor1D.', async () => {
        let a = tf.tensor1d([1, 0, 0], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 1, 1]);
        a = tf.tensor1d([0, 0, 0], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [1, 1, 1]);
        a = tf.tensor1d([1, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 0]);
    });
    it('Tests chaining in Tensor1D', async () => {
        let a = tf.tensor1d([1, 0, 0], 'bool');
        expectArraysClose(await a.logicalNot().data(), [0, 1, 1]);
        a = tf.tensor1d([0, 0, 0], 'bool');
        expectArraysClose(await a.logicalNot().data(), [1, 1, 1]);
        a = tf.tensor1d([1, 1], 'bool');
        expectArraysClose(await a.logicalNot().data(), [0, 0]);
    });
    it('Tensor2D', async () => {
        let a = tf.tensor2d([[1, 0, 1], [0, 0, 0]], [2, 3], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 1, 0, 1, 1, 1]);
        a = tf.tensor2d([[0, 0, 0], [1, 1, 1]], [2, 3], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [1, 1, 1, 0, 0, 0]);
    });
    it('Tensor3D', async () => {
        let a = tf.tensor3d([[[1], [0], [1]], [[0], [0], [0]]], [2, 3, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 1, 0, 1, 1, 1]);
        a = tf.tensor3d([[[0], [0], [0]], [[1], [1], [1]]], [2, 3, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [1, 1, 1, 0, 0, 0]);
    });
    it('Tensor4D', async () => {
        let a = tf.tensor4d([1, 0, 1, 0], [2, 2, 1, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 1, 0, 1]);
        a = tf.tensor4d([0, 0, 0, 0], [2, 2, 1, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [1, 1, 1, 1]);
        a = tf.tensor4d([1, 1, 1, 1], [2, 2, 1, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 0, 0, 0]);
    });
    it('Tensor6D', async () => {
        let a = tf.tensor6d([1, 0, 1, 0], [2, 2, 1, 1, 1, 1], 'bool');
        expectArraysClose(await tf.logicalNot(a).data(), [0, 1, 0, 1]);
        a = tf.zeros([2, 2, 2, 2, 2, 2]).cast('bool');
        let expectedResult = new Uint8Array(64).fill(1);
        expectedResult = expectedResult.fill(1);
        expectArraysClose(await tf.logicalNot(a).data(), expectedResult);
        a = tf.ones([2, 2, 2, 2, 2, 2]).cast('bool');
        expectedResult = expectedResult.fill(0);
        expectArraysClose(await tf.logicalNot(a).data(), expectedResult);
    });
    it('throws when passed a non-tensor', () => {
        expect(() => tf.logicalNot({}))
            .toThrowError(/Argument 'x' passed to 'logicalNot' must be a Tensor/);
    });
    it('accepts a tensor-like object', async () => {
        const a = [1, 0, 0];
        expectArraysClose(await tf.logicalNot(a).data(), [0, 1, 1]);
    });
});
//# sourceMappingURL=data:application/json;base64,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