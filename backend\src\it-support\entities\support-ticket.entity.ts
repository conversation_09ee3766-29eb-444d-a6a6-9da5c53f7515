import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { TicketComment } from './ticket-comment.entity';
import { TicketAttachment } from './ticket-attachment.entity';

export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  PENDING_USER = 'pending_user',
  PENDING_VENDOR = 'pending_vendor',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  CANCELLED = 'cancelled',
}

export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

export enum TicketType {
  INCIDENT = 'incident',
  SERVICE_REQUEST = 'service_request',
  CHANGE_REQUEST = 'change_request',
  PROBLEM = 'problem',
  QUESTION = 'question',
  COMPLAINT = 'complaint',
}

export enum TicketCategory {
  HARDWARE = 'hardware',
  SOFTWARE = 'software',
  NETWORK = 'network',
  EMAIL = 'email',
  PHONE = 'phone',
  PRINTER = 'printer',
  ACCESS = 'access',
  ACCOUNT = 'account',
  TRAINING = 'training',
  OTHER = 'other',
}

@Entity('support_tickets')
export class SupportTicket {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  ticketNumber: string;

  @Column({ length: 255 })
  subject: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: TicketType,
    default: TicketType.INCIDENT,
  })
  type: TicketType;

  @Column({
    type: 'enum',
    enum: TicketCategory,
    default: TicketCategory.OTHER,
  })
  category: TicketCategory;

  @Column({
    type: 'enum',
    enum: TicketStatus,
    default: TicketStatus.OPEN,
  })
  status: TicketStatus;

  @Column({
    type: 'enum',
    enum: TicketPriority,
    default: TicketPriority.MEDIUM,
  })
  priority: TicketPriority;

  @Column()
  requesterId: string;

  @Column({ length: 255 })
  requesterName: string;

  @Column({ length: 200 })
  requesterEmail: string;

  @Column({ length: 20, nullable: true })
  requesterPhone: string;

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ nullable: true })
  assignedGroup: string;

  @Column({ type: 'timestamp', nullable: true })
  assignedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  firstResponseAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  resolvedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  closedAt: Date;

  @Column({ type: 'date', nullable: true })
  dueDate: Date;

  @Column({ type: 'text', nullable: true })
  resolution: string;

  @Column({ type: 'text', nullable: true })
  workaround: string;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'decimal', precision: 2, scale: 1, nullable: true })
  satisfactionRating: number; // 1-5

  @Column({ type: 'text', nullable: true })
  satisfactionFeedback: string;

  @Column({ type: 'int', default: 0 })
  escalationLevel: number;

  @Column({ type: 'timestamp', nullable: true })
  lastEscalatedAt: Date;

  @Column({ type: 'int', default: 0 })
  reopenCount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastReopenedAt: Date;

  @OneToMany(() => TicketComment, comment => comment.ticket)
  comments: TicketComment[];

  @OneToMany(() => TicketAttachment, attachment => attachment.ticket)
  attachments: TicketAttachment[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
