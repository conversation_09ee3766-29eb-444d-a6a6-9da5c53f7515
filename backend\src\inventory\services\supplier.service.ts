import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Supplier } from '../entities/supplier.entity';

@Injectable()
export class SupplierService {
  constructor(
    @InjectRepository(Supplier)
    private supplierRepository: Repository<Supplier>,
  ) {}

  async create(supplierData: Partial<Supplier>): Promise<Supplier> {
    const supplier = this.supplierRepository.create(supplierData);
    return this.supplierRepository.save(supplier);
  }

  async findAll(): Promise<Supplier[]> {
    return this.supplierRepository.find({
      relations: ['purchaseOrders'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Supplier> {
    const supplier = await this.supplierRepository.findOne({
      where: { id },
      relations: ['purchaseOrders'],
    });

    if (!supplier) {
      throw new NotFoundException(`Supplier with ID ${id} not found`);
    }

    return supplier;
  }

  async update(id: string, updateData: Partial<Supplier>): Promise<Supplier> {
    await this.supplierRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const supplier = await this.findOne(id);
    await this.supplierRepository.remove(supplier);
  }

  async findByCode(code: string): Promise<Supplier> {
    const supplier = await this.supplierRepository.findOne({
      where: { code },
    });

    if (!supplier) {
      throw new NotFoundException(`Supplier with code ${code} not found`);
    }

    return supplier;
  }

  async getActiveSuppliers(): Promise<Supplier[]> {
    return this.supplierRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  async searchSuppliers(searchTerm: string): Promise<Supplier[]> {
    return this.supplierRepository
      .createQueryBuilder('supplier')
      .where('supplier.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('supplier.code ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('supplier.email ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('supplier.name', 'ASC')
      .getMany();
  }

  async getSupplierStatistics(): Promise<any> {
    const totalSuppliers = await this.supplierRepository.count();
    const activeSuppliers = await this.supplierRepository.count({ where: { isActive: true } });

    return {
      totalSuppliers,
      activeSuppliers,
      inactiveSuppliers: totalSuppliers - activeSuppliers,
    };
  }

  async generateSupplierCode(name: string): Promise<string> {
    const baseCode = name.substring(0, 3).toUpperCase();
    const count = await this.supplierRepository.count();
    const sequence = (count + 1).toString().padStart(4, '0');
    return `SUP-${baseCode}${sequence}`;
  }
}
