{"version": 3, "file": "collection-case.service.js", "sourceRoot": "", "sources": ["../../../src/collections/services/collection-case.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,+EAAgF;AAChF,uFAA4E;AAGrE,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAGtB;IAEA;IAJV,YAEU,wBAAoD,EAEpD,4BAA4D;QAF5D,6BAAwB,GAAxB,wBAAwB,CAA4B;QAEpD,iCAA4B,GAA5B,4BAA4B,CAAgC;IACnE,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,QAAiC;QAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;YACxC,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;YACrC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;SACtC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,EAAE,YAAY,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAmC;QAC1D,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9C,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAkB;QACnC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,SAAS,EAAE,CAAC,UAAU,CAAC;SACxB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,UAAU,EAAE;YACrB,SAAS,EAAE,CAAC,YAAY,CAAC;YACzB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAkB,EAAE,KAAc;QAC/D,MAAM,UAAU,GAAQ,EAAE,MAAM,EAAE,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YAEV,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;gBAC3C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qBAAqB,MAAM,KAAK,KAAK,EAAE;gBACpD,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,OAAe;QAC/C,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;QAG5E,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;YAC3C,MAAM;YACN,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,0BAA0B,OAAO,EAAE;YAChD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QAC1D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;YACvD,KAAK,EAAE,EAAE,MAAM,EAAE,mCAAU,CAAC,MAAM,EAAE;SACrC,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;YACzD,KAAK,EAAE,EAAE,MAAM,EAAE,mCAAU,CAAC,QAAQ,EAAE;SACvC,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;YACxD,KAAK,EAAE,EAAE,MAAM,EAAE,mCAAU,CAAC,OAAO,EAAE;SACtC,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,wBAAwB;aACtD,kBAAkB,CAAC,MAAM,CAAC;aAC1B,MAAM,CAAC;YACN,uCAAuC;YACvC,6CAA6C;YAC7C,4EAA4E;SAC7E,CAAC;aACD,SAAS,EAAE,CAAC;QAEf,OAAO;YACL,KAAK;YACL,MAAM;YACN,QAAQ;YACR,OAAO;YACP,SAAS,EAAE,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC;YACnD,cAAc,EAAE,UAAU,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC;YAC7D,cAAc,EAAE,UAAU,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC;YAChE,cAAc,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SACzD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,QAAQ,EAAE;YACnB,SAAS,EAAE,CAAC,UAAU,CAAC;YACvB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,wBAAwB;aACjC,kBAAkB,CAAC,MAAM,CAAC;aAC1B,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,CAAC;aACzC,QAAQ,CAAC,0BAA0B,EAAE,EAAE,QAAQ,EAAE,mCAAU,CAAC,QAAQ,EAAE,CAAC;aACvE,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;QAC3C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAG9C,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE,EAAE;YAC7C,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE,CAAC,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC;SAC3D,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;YAC3C,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,mBAAmB,MAAM,EAAE;YACxC,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,MAAc,EAAE,WAAiB,EAAE,KAAc;QAC/E,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9C,MAAM,kBAAkB,GAAG,CAAC,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QAE1E,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE,EAAE;YAC7C,eAAe,EAAE,kBAAkB;YACnC,eAAe,EAAE,WAAW;SAC7B,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;YAC3C,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,sBAAsB,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACxE,YAAY,EAAE,WAAW;SAC1B,CAAC,CAAC;QAGH,IAAI,kBAAkB,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;YACxD,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,mCAAU,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QACzC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAElD,OAAO;YACL,GAAG,KAAK;YACR,YAAY,EAAE,YAAY,CAAC,MAAM;YACjC,WAAW,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;SACpE,CAAC;IACJ,CAAC;CACF,CAAA;AAlMY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,uCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;qCADH,oBAAU;QAEN,oBAAU;GALvC,qBAAqB,CAkMjC"}