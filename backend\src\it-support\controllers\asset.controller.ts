import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { AssetService } from '../services/asset.service';
import { Asset, AssetStatus } from '../entities/asset.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('assets')
@UseGuards(JwtAuthGuard)
export class AssetController {
  constructor(private readonly assetService: AssetService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createAssetDto: Partial<Asset>) {
    return this.assetService.create(createAssetDto);
  }

  @Get()
  async findAll(
    @Query('status') status?: AssetStatus,
    @Query('category') category?: string,
    @Query('location') location?: string,
  ) {
    if (status) {
      return this.assetService.findByStatus(status);
    }
    if (category) {
      return this.assetService.findByCategory(category);
    }
    if (location) {
      return this.assetService.findByLocation(location);
    }
    return this.assetService.findAll();
  }

  @Get('statistics')
  async getStatistics() {
    return this.assetService.getAssetStatistics();
  }

  @Get('dashboard')
  async getDashboardMetrics() {
    return this.assetService.getDashboardMetrics();
  }

  @Get('by-category')
  async getAssetsByCategory() {
    return this.assetService.getAssetsByCategory();
  }

  @Get('warranty-expiring')
  async getAssetsNearingWarrantyExpiry(@Query('days') days?: string) {
    const daysNum = days ? parseInt(days) : 30;
    return this.assetService.getAssetsNearingWarrantyExpiry(daysNum);
  }

  @Get('search')
  async searchAssets(@Query('q') searchTerm: string) {
    return this.assetService.searchAssets(searchTerm);
  }

  @Get('user/:userId')
  async getUserAssets(@Param('userId') userId: string) {
    return this.assetService.getUserAssets(userId);
  }

  @Get('tag/:assetTag')
  async findByAssetTag(@Param('assetTag') assetTag: string) {
    return this.assetService.findByAssetTag(assetTag);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.assetService.findOne(id);
  }

  @Get(':id/history')
  async getAssetHistory(@Param('id') id: string) {
    return this.assetService.getAssetHistory(id);
  }

  @Post(':id/assign')
  async assignAsset(
    @Param('id') id: string,
    @Body() assignmentData: {
      assignedToId: string;
      assignedById: string;
      notes?: string;
    },
  ) {
    return this.assetService.assignAsset(
      id,
      assignmentData.assignedToId,
      assignmentData.assignedById,
      assignmentData.notes,
    );
  }

  @Post(':id/unassign')
  async unassignAsset(
    @Param('id') id: string,
    @Body() unassignmentData: {
      unassignedById: string;
      notes?: string;
    },
  ) {
    await this.assetService.unassignAsset(
      id,
      unassignmentData.unassignedById,
      unassignmentData.notes,
    );
    return { message: 'Asset unassigned successfully' };
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAssetDto: Partial<Asset>,
  ) {
    return this.assetService.update(id, updateAssetDto);
  }

  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body() statusData: { status: AssetStatus; notes?: string },
  ) {
    return this.assetService.updateAssetStatus(id, statusData.status, statusData.notes);
  }

  @Post(':id/retire')
  async retireAsset(
    @Param('id') id: string,
    @Body() retirementData: {
      retiredById: string;
      reason: string;
    },
  ) {
    return this.assetService.retireAsset(
      id,
      retirementData.retiredById,
      retirementData.reason,
    );
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    return this.assetService.remove(id);
  }
}
