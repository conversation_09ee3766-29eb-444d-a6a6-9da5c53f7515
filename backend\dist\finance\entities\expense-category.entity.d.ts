import { Expense } from './expense.entity';
import { Account } from './account.entity';
export declare class ExpenseCategory {
    id: string;
    name: string;
    description: string;
    code: string;
    parentCategoryId: string;
    parentCategory: ExpenseCategory;
    childCategories: ExpenseCategory[];
    defaultAccountId: string;
    defaultAccount: Account;
    isActive: boolean;
    requiresApproval: boolean;
    approvalLimit: number;
    isTaxDeductible: boolean;
    defaultTaxRate: number;
    allowedFileTypes: string[];
    requiresReceipt: boolean;
    expenses: Expense[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
