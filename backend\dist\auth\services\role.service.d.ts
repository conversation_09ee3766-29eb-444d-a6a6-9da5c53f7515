import { Repository } from 'typeorm';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
export declare class RoleService {
    private roleRepository;
    private permissionRepository;
    constructor(roleRepository: Repository<Role>, permissionRepository: Repository<Permission>);
    create(roleData: Partial<Role>): Promise<Role>;
    findAll(): Promise<Role[]>;
    findOne(id: string): Promise<Role>;
    update(id: string, updateData: Partial<Role>): Promise<Role>;
    remove(id: string): Promise<void>;
    assignPermissions(roleId: string, permissionIds: string[]): Promise<Role>;
    removePermissions(roleId: string, permissionIds: string[]): Promise<Role>;
    createDefaultRoles(): Promise<Role[]>;
    assignDefaultPermissions(): Promise<void>;
    private assignDepartmentPermissions;
    getRolesByDepartment(department: string): Promise<Role[]>;
    cloneRole(sourceRoleId: string, newRoleName: string, description?: string): Promise<Role>;
}
