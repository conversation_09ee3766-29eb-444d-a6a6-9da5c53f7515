import { EmployeeService } from '../services/employee.service';
export declare class EmployeeController {
    private readonly employeeService;
    constructor(employeeService: EmployeeService);
    create(createEmployeeDto: any): Promise<import("../entities/employee.entity").Employee>;
    findAll(status?: string, departmentId?: string, positionId?: string, managerId?: string, search?: string): Promise<import("../entities/employee.entity").Employee[]>;
    getEmployeeStats(): Promise<any>;
    getEmployeeHierarchy(managerId?: string): Promise<import("../entities/employee.entity").Employee[]>;
    searchEmployees(searchTerm: string): Promise<import("../entities/employee.entity").Employee[]>;
    findByEmployeeNumber(employeeNumber: string): Promise<import("../entities/employee.entity").Employee>;
    findOne(id: string): Promise<import("../entities/employee.entity").Employee>;
    update(id: string, updateEmployeeDto: any): Promise<import("../entities/employee.entity").Employee>;
    remove(id: string): Promise<void>;
    terminate(id: string, terminateDto: {
        terminationDate: string;
        reason?: string;
    }): Promise<import("../entities/employee.entity").Employee>;
    reactivate(id: string): Promise<import("../entities/employee.entity").Employee>;
}
