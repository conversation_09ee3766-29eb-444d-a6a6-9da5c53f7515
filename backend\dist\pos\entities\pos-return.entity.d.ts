import { PosSale } from './pos-sale.entity';
export declare enum ReturnReason {
    DEFECTIVE = "defective",
    WRONG_ITEM = "wrong_item",
    CUSTOMER_CHANGED_MIND = "customer_changed_mind",
    SIZE_ISSUE = "size_issue",
    COLOR_ISSUE = "color_issue",
    DAMAGED_IN_SHIPPING = "damaged_in_shipping",
    NOT_AS_DESCRIBED = "not_as_described",
    DUPLICATE_ORDER = "duplicate_order",
    GIFT_RETURN = "gift_return",
    OTHER = "other"
}
export declare enum ReturnStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    PROCESSED = "processed",
    REFUNDED = "refunded"
}
export declare class PosReturn {
    id: string;
    returnNumber: string;
    originalSaleId: string;
    originalSale: PosSale;
    newSaleId: string;
    newSale: PosSale;
    reason: ReturnReason;
    status: ReturnStatus;
    returnDate: Date;
    returnAmount: number;
    refundAmount: number;
    storeCreditAmount: number;
    exchangeAmount: number;
    returnedItems: any[];
    notes: string;
    processedBy: string;
    approvedBy: string;
    approvedAt: Date;
    refundDetails: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
