"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerLoyaltyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_loyalty_entity_1 = require("../entities/customer-loyalty.entity");
const customer_entity_1 = require("../entities/customer.entity");
let CustomerLoyaltyService = class CustomerLoyaltyService {
    loyaltyRepository;
    customerRepository;
    constructor(loyaltyRepository, customerRepository) {
        this.loyaltyRepository = loyaltyRepository;
        this.customerRepository = customerRepository;
    }
    async create(loyaltyData) {
        const loyalty = this.loyaltyRepository.create(loyaltyData);
        return this.loyaltyRepository.save(loyalty);
    }
    async findAll() {
        return this.loyaltyRepository.find({
            relations: ['customer'],
            order: { transactionDate: 'DESC' },
        });
    }
    async findOne(id) {
        const loyalty = await this.loyaltyRepository.findOne({
            where: { id },
            relations: ['customer'],
        });
        if (!loyalty) {
            throw new common_1.NotFoundException(`Loyalty record with ID ${id} not found`);
        }
        return loyalty;
    }
    async findByCustomer(customerId) {
        return this.loyaltyRepository.find({
            where: { customerId },
            order: { transactionDate: 'DESC' },
        });
    }
    async addPoints(customerId, points, reason, orderId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        const loyaltyTransaction = await this.create({
            customerId,
            pointsEarned: points,
            pointsRedeemed: 0,
            transactionType: 'earned',
            reason,
            orderId,
            balanceAfter: (customer.loyaltyPoints || 0) + points,
        });
        await this.customerRepository.update(customerId, {
            loyaltyPoints: (customer.loyaltyPoints || 0) + points,
        });
        return loyaltyTransaction;
    }
    async redeemPoints(customerId, points, reason, orderId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        if ((customer.loyaltyPoints || 0) < points) {
            throw new Error('Insufficient loyalty points');
        }
        const loyaltyTransaction = await this.create({
            customerId,
            pointsEarned: 0,
            pointsRedeemed: points,
            transactionType: 'redeemed',
            reason,
            orderId,
            balanceAfter: (customer.loyaltyPoints || 0) - points,
        });
        await this.customerRepository.update(customerId, {
            loyaltyPoints: (customer.loyaltyPoints || 0) - points,
        });
        return loyaltyTransaction;
    }
    async getCustomerLoyaltySummary(customerId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${customerId} not found`);
        }
        const transactions = await this.findByCustomer(customerId);
        const totalEarned = transactions.reduce((sum, t) => sum + (t.pointsEarned || 0), 0);
        const totalRedeemed = transactions.reduce((sum, t) => sum + (t.pointsRedeemed || 0), 0);
        const currentBalance = customer.loyaltyPoints || 0;
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentTransactions = transactions.filter(t => t.transactionDate >= thirtyDaysAgo);
        const recentEarned = recentTransactions.reduce((sum, t) => sum + (t.pointsEarned || 0), 0);
        const recentRedeemed = recentTransactions.reduce((sum, t) => sum + (t.pointsRedeemed || 0), 0);
        return {
            customerId,
            currentBalance,
            totalEarned,
            totalRedeemed,
            recentEarned,
            recentRedeemed,
            transactionCount: transactions.length,
            lastTransaction: transactions[0]?.transactionDate,
            tier: this.calculateLoyaltyTier(currentBalance),
        };
    }
    async getLoyaltyStatistics() {
        const totalCustomers = await this.customerRepository.count();
        const loyaltyMembers = await this.customerRepository.count({
            where: { loyaltyPoints: (0, typeorm_2.MoreThan)(0) },
        });
        const loyaltyData = await this.customerRepository
            .createQueryBuilder('customer')
            .select([
            'SUM(customer.loyaltyPoints) as totalPoints',
            'AVG(customer.loyaltyPoints) as averagePoints',
            'MAX(customer.loyaltyPoints) as maxPoints',
        ])
            .where('customer.loyaltyPoints > 0')
            .getRawOne();
        const tierDistribution = {
            bronze: await this.customerRepository.count({
                where: { loyaltyPoints: (0, typeorm_2.Between)(1, 999) },
            }),
            silver: await this.customerRepository.count({
                where: { loyaltyPoints: (0, typeorm_2.Between)(1000, 4999) },
            }),
            gold: await this.customerRepository.count({
                where: { loyaltyPoints: (0, typeorm_2.Between)(5000, 9999) },
            }),
            platinum: await this.customerRepository.count({
                where: { loyaltyPoints: (0, typeorm_2.MoreThan)(10000) },
            }),
        };
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentActivity = await this.loyaltyRepository
            .createQueryBuilder('loyalty')
            .select([
            'SUM(loyalty.pointsEarned) as pointsEarned',
            'SUM(loyalty.pointsRedeemed) as pointsRedeemed',
            'COUNT(*) as transactionCount',
        ])
            .where('loyalty.transactionDate >= :startDate', { startDate: thirtyDaysAgo })
            .getRawOne();
        return {
            totalCustomers,
            loyaltyMembers,
            participationRate: totalCustomers > 0 ? (loyaltyMembers / totalCustomers) * 100 : 0,
            totalPoints: parseInt(loyaltyData.totalPoints) || 0,
            averagePoints: parseFloat(loyaltyData.averagePoints) || 0,
            maxPoints: parseInt(loyaltyData.maxPoints) || 0,
            tierDistribution,
            recentActivity: {
                pointsEarned: parseInt(recentActivity.pointsEarned) || 0,
                pointsRedeemed: parseInt(recentActivity.pointsRedeemed) || 0,
                transactionCount: parseInt(recentActivity.transactionCount) || 0,
            },
        };
    }
    calculateLoyaltyTier(points) {
        if (points >= 10000)
            return 'platinum';
        if (points >= 5000)
            return 'gold';
        if (points >= 1000)
            return 'silver';
        if (points > 0)
            return 'bronze';
        return 'none';
    }
    async adjustPoints(customerId, adjustment, reason) {
        if (adjustment > 0) {
            return this.addPoints(customerId, adjustment, reason);
        }
        else {
            return this.redeemPoints(customerId, Math.abs(adjustment), reason);
        }
    }
    async expirePoints(customerId, points) {
        return this.create({
            customerId,
            pointsEarned: 0,
            pointsRedeemed: points,
            transactionType: 'expired',
            reason: 'Points expired',
            balanceAfter: 0,
        });
    }
    async getTopLoyaltyCustomers(limit = 10) {
        return this.customerRepository.find({
            where: { loyaltyPoints: (0, typeorm_2.MoreThan)(0) },
            order: { loyaltyPoints: 'DESC' },
            take: limit,
            select: ['id', 'firstName', 'lastName', 'companyName', 'loyaltyPoints', 'tier'],
        });
    }
};
exports.CustomerLoyaltyService = CustomerLoyaltyService;
exports.CustomerLoyaltyService = CustomerLoyaltyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_loyalty_entity_1.CustomerLoyalty)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerLoyaltyService);
//# sourceMappingURL=customer-loyalty.service.js.map