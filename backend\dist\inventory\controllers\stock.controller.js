"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockController = void 0;
const common_1 = require("@nestjs/common");
const stock_service_1 = require("../services/stock.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let StockController = class StockController {
    stockService;
    constructor(stockService) {
        this.stockService = stockService;
    }
    async findAll() {
        return this.stockService.findAll();
    }
    async getStatistics() {
        return this.stockService.getStockStatistics();
    }
    async getLowStockItems(threshold) {
        const thresholdNum = threshold ? parseInt(threshold) : 10;
        return this.stockService.getLowStockItems(thresholdNum);
    }
    async getOutOfStockItems() {
        return this.stockService.getOutOfStockItems();
    }
    async getOverstockItems(threshold) {
        const thresholdNum = threshold ? parseInt(threshold) : 1000;
        return this.stockService.getOverstockItems(thresholdNum);
    }
    async getStockMovements(productId, warehouseId, startDate, endDate) {
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        return this.stockService.getStockMovements(productId, warehouseId, start, end);
    }
    async getStockAdjustments(productId, warehouseId, startDate, endDate) {
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        return this.stockService.getStockAdjustments(productId, warehouseId, start, end);
    }
    async findByProduct(productId) {
        return this.stockService.findByProduct(productId);
    }
    async findByWarehouse(warehouseId) {
        return this.stockService.findByWarehouse(warehouseId);
    }
    async findByProductAndWarehouse(productId, warehouseId) {
        return this.stockService.findByProductAndWarehouse(productId, warehouseId);
    }
    async findOne(id) {
        return this.stockService.findOne(id);
    }
    async adjustStock(adjustmentData) {
        return this.stockService.adjustStock(adjustmentData.productId, adjustmentData.warehouseId, adjustmentData.adjustment, adjustmentData.reason, adjustmentData.adjustedBy);
    }
    async reserveStock(reservationData) {
        const success = await this.stockService.reserveStock(reservationData.productId, reservationData.warehouseId, reservationData.quantity);
        return { success, message: success ? 'Stock reserved' : 'Insufficient stock' };
    }
    async releaseReservation(releaseData) {
        await this.stockService.releaseReservation(releaseData.productId, releaseData.warehouseId, releaseData.quantity);
        return { message: 'Reservation released successfully' };
    }
    async fulfillReservation(fulfillmentData) {
        const success = await this.stockService.fulfillReservation(fulfillmentData.productId, fulfillmentData.warehouseId, fulfillmentData.quantity);
        return { success, message: success ? 'Reservation fulfilled' : 'Insufficient reserved stock' };
    }
    async bulkStockUpdate(updates) {
        await this.stockService.bulkStockUpdate(updates);
        return { message: 'Bulk stock update completed successfully' };
    }
};
exports.StockController = StockController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StockController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StockController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('low-stock'),
    __param(0, (0, common_1.Query)('threshold')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "getLowStockItems", null);
__decorate([
    (0, common_1.Get)('out-of-stock'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StockController.prototype, "getOutOfStockItems", null);
__decorate([
    (0, common_1.Get)('overstock'),
    __param(0, (0, common_1.Query)('threshold')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "getOverstockItems", null);
__decorate([
    (0, common_1.Get)('movements'),
    __param(0, (0, common_1.Query)('productId')),
    __param(1, (0, common_1.Query)('warehouseId')),
    __param(2, (0, common_1.Query)('startDate')),
    __param(3, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "getStockMovements", null);
__decorate([
    (0, common_1.Get)('adjustments'),
    __param(0, (0, common_1.Query)('productId')),
    __param(1, (0, common_1.Query)('warehouseId')),
    __param(2, (0, common_1.Query)('startDate')),
    __param(3, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "getStockAdjustments", null);
__decorate([
    (0, common_1.Get)('product/:productId'),
    __param(0, (0, common_1.Param)('productId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "findByProduct", null);
__decorate([
    (0, common_1.Get)('warehouse/:warehouseId'),
    __param(0, (0, common_1.Param)('warehouseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "findByWarehouse", null);
__decorate([
    (0, common_1.Get)('product/:productId/warehouse/:warehouseId'),
    __param(0, (0, common_1.Param)('productId')),
    __param(1, (0, common_1.Param)('warehouseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "findByProductAndWarehouse", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('adjust'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "adjustStock", null);
__decorate([
    (0, common_1.Post)('reserve'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "reserveStock", null);
__decorate([
    (0, common_1.Post)('release-reservation'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "releaseReservation", null);
__decorate([
    (0, common_1.Post)('fulfill-reservation'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "fulfillReservation", null);
__decorate([
    (0, common_1.Post)('bulk-update'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], StockController.prototype, "bulkStockUpdate", null);
exports.StockController = StockController = __decorate([
    (0, common_1.Controller)('stock'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [stock_service_1.StockService])
], StockController);
//# sourceMappingURL=stock.controller.js.map