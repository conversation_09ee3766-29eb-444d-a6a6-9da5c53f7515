"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerReportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("../entities/customer.entity");
const customer_interaction_entity_1 = require("../entities/customer-interaction.entity");
const customer_segment_entity_1 = require("../entities/customer-segment.entity");
let CustomerReportService = class CustomerReportService {
    customerRepository;
    interactionRepository;
    segmentRepository;
    constructor(customerRepository, interactionRepository, segmentRepository) {
        this.customerRepository = customerRepository;
        this.interactionRepository = interactionRepository;
        this.segmentRepository = segmentRepository;
    }
    async generateCustomerSummaryReport() {
        const totalCustomers = await this.customerRepository.count();
        const activeCustomers = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.ACTIVE } });
        const prospects = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.PROSPECT } });
        const leads = await this.customerRepository.count({ where: { status: customer_entity_1.CustomerStatus.LEAD } });
        const financialData = await this.customerRepository
            .createQueryBuilder('customer')
            .select([
            'SUM(customer.totalSpent) as totalRevenue',
            'AVG(customer.totalSpent) as averageCustomerValue',
            'SUM(customer.currentBalance) as totalOutstanding',
            'SUM(customer.loyaltyPoints) as totalLoyaltyPoints',
        ])
            .getRawOne();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const newCustomers = await this.customerRepository.count({
            where: { createdAt: (0, typeorm_2.Between)(thirtyDaysAgo, new Date()) },
        });
        return {
            summary: {
                totalCustomers,
                activeCustomers,
                prospects,
                leads,
                newCustomersThisMonth: newCustomers,
                conversionRate: prospects > 0 ? (activeCustomers / prospects) * 100 : 0,
            },
            financial: {
                totalRevenue: parseFloat(financialData.totalRevenue) || 0,
                averageCustomerValue: parseFloat(financialData.averageCustomerValue) || 0,
                totalOutstanding: parseFloat(financialData.totalOutstanding) || 0,
                totalLoyaltyPoints: parseInt(financialData.totalLoyaltyPoints) || 0,
            },
            growth: {
                newCustomersThisMonth: newCustomers,
                growthRate: totalCustomers > 0 ? (newCustomers / totalCustomers) * 100 : 0,
            },
        };
    }
    async generateCustomerSegmentReport() {
        const segments = await this.segmentRepository.find({
            where: { isActive: true },
        });
        const segmentData = [];
        for (const segment of segments) {
            const analytics = await this.getSegmentAnalytics(segment.id);
            segmentData.push({
                segmentName: segment.name,
                description: segment.description,
                customerCount: segment.customerCount,
                ...analytics,
            });
        }
        return {
            totalSegments: segments.length,
            segments: segmentData,
        };
    }
    async generateInteractionReport(startDate, endDate) {
        const interactions = await this.interactionRepository.find({
            where: { interactionDate: (0, typeorm_2.Between)(startDate, endDate) },
            relations: ['customer'],
        });
        const typeAnalysis = interactions.reduce((acc, interaction) => {
            acc[interaction.type] = (acc[interaction.type] || 0) + 1;
            return acc;
        }, {});
        const channelAnalysis = interactions.reduce((acc, interaction) => {
            acc[interaction.channel] = (acc[interaction.channel] || 0) + 1;
            return acc;
        }, {});
        const outcomeAnalysis = interactions.reduce((acc, interaction) => {
            if (interaction.outcome) {
                acc[interaction.outcome] = (acc[interaction.outcome] || 0) + 1;
            }
            return acc;
        }, {});
        const dailyVolume = interactions.reduce((acc, interaction) => {
            const date = interaction.interactionDate.toISOString().split('T')[0];
            acc[date] = (acc[date] || 0) + 1;
            return acc;
        }, {});
        return {
            period: { startDate, endDate },
            totalInteractions: interactions.length,
            typeAnalysis,
            channelAnalysis,
            outcomeAnalysis,
            dailyVolume,
            averageInteractionsPerDay: interactions.length / this.getDaysBetween(startDate, endDate),
        };
    }
    async generateCustomerLifecycleReport() {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
        const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        const acquisitionData = await this.customerRepository
            .createQueryBuilder('customer')
            .select('DATE_FORMAT(customer.createdAt, "%Y-%m") as month')
            .addSelect('COUNT(*) as count')
            .where('customer.createdAt >= :startDate', { startDate: ninetyDaysAgo })
            .groupBy('month')
            .orderBy('month')
            .getRawMany();
        const totalActiveCustomers = await this.customerRepository.count({
            where: { status: customer_entity_1.CustomerStatus.ACTIVE },
        });
        const customersWithRecentActivity = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastContactDate: (0, typeorm_2.Between)(thirtyDaysAgo, now),
            },
        });
        const customersWithRecentPurchase = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastPurchaseDate: (0, typeorm_2.Between)(thirtyDaysAgo, now),
            },
        });
        const churnRiskCustomers = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastContactDate: (0, typeorm_2.Between)(sixtyDaysAgo, thirtyDaysAgo),
            },
        });
        return {
            acquisition: {
                trend: acquisitionData.map(item => ({
                    month: item.month,
                    count: parseInt(item.count),
                })),
            },
            retention: {
                totalActiveCustomers,
                customersWithRecentActivity,
                customersWithRecentPurchase,
                retentionRate: totalActiveCustomers > 0 ? (customersWithRecentActivity / totalActiveCustomers) * 100 : 0,
            },
            churnRisk: {
                customersAtRisk: churnRiskCustomers,
                churnRiskPercentage: totalActiveCustomers > 0 ? (churnRiskCustomers / totalActiveCustomers) * 100 : 0,
            },
        };
    }
    async generateTopCustomersReport(limit = 20) {
        const topByRevenue = await this.customerRepository.find({
            where: { status: customer_entity_1.CustomerStatus.ACTIVE },
            order: { totalSpent: 'DESC' },
            take: limit,
            select: ['id', 'firstName', 'lastName', 'companyName', 'totalSpent', 'tier'],
        });
        const topByLoyalty = await this.customerRepository.find({
            where: { status: customer_entity_1.CustomerStatus.ACTIVE },
            order: { loyaltyPoints: 'DESC' },
            take: limit,
            select: ['id', 'firstName', 'lastName', 'companyName', 'loyaltyPoints', 'tier'],
        });
        const mostActive = await this.customerRepository
            .createQueryBuilder('customer')
            .leftJoin('customer.interactions', 'interaction')
            .select([
            'customer.id',
            'customer.firstName',
            'customer.lastName',
            'customer.companyName',
            'COUNT(interaction.id) as interactionCount',
        ])
            .where('customer.status = :status', { status: customer_entity_1.CustomerStatus.ACTIVE })
            .groupBy('customer.id')
            .orderBy('interactionCount', 'DESC')
            .limit(limit)
            .getRawMany();
        return {
            topByRevenue,
            topByLoyalty,
            mostActive: mostActive.map(customer => ({
                ...customer,
                interactionCount: parseInt(customer.interactionCount),
            })),
        };
    }
    async generateCustomerHealthReport() {
        const healthyCustomers = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastContactDate: (0, typeorm_2.Between)(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()),
            },
        });
        const atRiskCustomers = await this.customerRepository.count({
            where: {
                status: customer_entity_1.CustomerStatus.ACTIVE,
                lastContactDate: (0, typeorm_2.Between)(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
            },
        });
        const inactiveCustomers = await this.customerRepository.count({
            where: { status: customer_entity_1.CustomerStatus.INACTIVE },
        });
        const totalCustomers = await this.customerRepository.count();
        return {
            healthyCustomers,
            atRiskCustomers,
            inactiveCustomers,
            healthScore: totalCustomers > 0 ? (healthyCustomers / totalCustomers) * 100 : 0,
            riskScore: totalCustomers > 0 ? (atRiskCustomers / totalCustomers) * 100 : 0,
        };
    }
    async getSegmentAnalytics(segmentId) {
        const segment = await this.segmentRepository.findOne({
            where: { id: segmentId },
        });
        if (!segment || !segment.criteria) {
            return {};
        }
        return {
            totalRevenue: 0,
            averageCustomerValue: 0,
            retentionRate: 0,
        };
    }
    getDaysBetween(startDate, endDate) {
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    async exportCustomerData(format = 'csv') {
        const customers = await this.customerRepository.find({
            relations: ['group', 'contacts', 'addresses'],
        });
        const exportData = customers.map(customer => ({
            customerNumber: customer.customerNumber,
            firstName: customer.firstName,
            lastName: customer.lastName,
            companyName: customer.companyName,
            email: customer.primaryEmail,
            phone: customer.primaryPhone,
            status: customer.status,
            type: customer.type,
            tier: customer.tier,
            totalSpent: customer.totalSpent,
            loyaltyPoints: customer.loyaltyPoints,
            createdAt: customer.createdAt,
            lastContactDate: customer.lastContactDate,
            lastPurchaseDate: customer.lastPurchaseDate,
        }));
        return {
            data: exportData,
            format,
            filename: `customers_export_${new Date().toISOString().split('T')[0]}.${format}`,
        };
    }
};
exports.CustomerReportService = CustomerReportService;
exports.CustomerReportService = CustomerReportService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __param(1, (0, typeorm_1.InjectRepository)(customer_interaction_entity_1.CustomerInteraction)),
    __param(2, (0, typeorm_1.InjectRepository)(customer_segment_entity_1.CustomerSegment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CustomerReportService);
//# sourceMappingURL=customer-report.service.js.map