import { AccountService } from '../services/account.service';
export declare class AccountController {
    private readonly accountService;
    constructor(accountService: AccountService);
    create(createAccountDto: any): Promise<import("../entities/account.entity").Account>;
    findAll(type?: string, subType?: string): Promise<import("../entities/account.entity").Account[]>;
    getAccountHierarchy(): Promise<import("../entities/account.entity").Account[]>;
    getTrialBalance(asOfDate?: string): Promise<any>;
    findOne(id: string): Promise<import("../entities/account.entity").Account>;
    update(id: string, updateAccountDto: any): Promise<import("../entities/account.entity").Account>;
    remove(id: string): Promise<void>;
    updateBalance(id: string, updateBalanceDto: {
        amount: number;
        isDebit: boolean;
    }): Promise<import("../entities/account.entity").Account>;
}
