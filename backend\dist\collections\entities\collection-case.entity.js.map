{"version": 3, "file": "collection-case.entity.js", "sourceRoot": "", "sources": ["../../../src/collections/entities/collection-case.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCASiB;AACjB,6EAAkE;AAClE,6EAAkE;AAClE,uEAA4D;AAC5D,+DAAoD;AACpD,qEAA0D;AAC1D,6EAAkE;AAClE,2EAAgE;AAEhE,IAAY,UAWX;AAXD,WAAY,UAAU;IACpB,yBAAW,CAAA;IACX,+BAAiB,CAAA;IACjB,iCAAmB,CAAA;IACnB,2CAA6B,CAAA;IAC7B,mCAAqB,CAAA;IACrB,6BAAe,CAAA;IACf,yCAA2B,CAAA;IAC3B,uDAAyC,CAAA;IACzC,+CAAiC,CAAA;IACjC,2DAA6C,CAAA;AAC/C,CAAC,EAXW,UAAU,0BAAV,UAAU,QAWrB;AAED,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,2BAAW,CAAA;IACX,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,iCAAiB,CAAA;IACjB,qCAAqB,CAAA;AACvB,CAAC,EANW,YAAY,4BAAZ,YAAY,QAMvB;AAED,IAAY,QASX;AATD,WAAY,QAAQ;IAClB,+BAAmB,CAAA;IACnB,yBAAa,CAAA;IACb,uCAA2B,CAAA;IAC3B,+BAAmB,CAAA;IACnB,+BAAmB,CAAA;IACnB,yCAA6B,CAAA;IAC7B,iCAAqB,CAAA;IACrB,2BAAe,CAAA;AACjB,CAAC,EATW,QAAQ,wBAAR,QAAQ,QASnB;AAGM,IAAM,cAAc,GAApB,MAAM,cAAc;IAEzB,EAAE,CAAS;IAGX,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,YAAY,CAAS;IAGrB,aAAa,CAAS;IAGtB,aAAa,CAAS;IAOtB,QAAQ,CAAW;IAGnB,cAAc,CAAS;IAGvB,cAAc,CAAS;IAGvB,cAAc,CAAS;IAGvB,UAAU,CAAS;IAGnB,WAAW,CAAS;IAGpB,QAAQ,CAAS;IAGjB,eAAe,CAAO;IAGtB,WAAW,CAAS;IAOpB,MAAM,CAAa;IAOnB,QAAQ,CAAe;IAGvB,eAAe,CAAS;IAIxB,aAAa,CAAkB;IAG/B,UAAU,CAAS;IAInB,QAAQ,CAAqB;IAG7B,eAAe,CAAO;IAGtB,cAAc,CAAO;IAGrB,UAAU,CAAS;IAGnB,gBAAgB,CAAO;IAGvB,kBAAkB,CAAS;IAG3B,eAAe,CAAS;IAGxB,kBAAkB,CAAS;IAG3B,aAAa,CAAS;IAGtB,eAAe,CAAO;IAGtB,iBAAiB,CAAS;IAG1B,aAAa,CAAS;IAGtB,aAAa,CAAS;IAGtB,IAAI,CAAW;IAGf,UAAU,CAAU;IAGpB,OAAO,CAAU;IAGjB,UAAU,CAAU;IAGpB,UAAU,CAAU;IAGpB,SAAS,CAAU;IAGnB,UAAU,CAAU;IAGpB,SAAS,CAAU;IAGnB,UAAU,CAAuB;IAGjC,YAAY,CAAgB;IAG5B,KAAK,CAAmB;IAGxB,SAAS,CAAuB;IAGhC,QAAQ,CAAsB;IAG9B,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AAjKY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;kDAClB;AAGnB;IADC,IAAA,gBAAM,GAAE;;kDACU;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;oDACH;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAClB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACjB;AAOtB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ,CAAC,OAAO;KAC1B,CAAC;;gDACiB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;sDAC9B;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;sDAC9B;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sDAC1C;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;kDAC9C;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;mDAC7C;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;gDACtB;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACR,IAAI;uDAAC;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;mDACJ;AAOpB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,UAAU,CAAC,GAAG;KACxB,CAAC;;8CACiB;AAOnB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,YAAY,CAAC,MAAM;KAC7B,CAAC;;gDACqB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDACH;AAIxB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yCAAe,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACpD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;8BACzB,yCAAe;qDAAC;AAG/B;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDACR;AAInB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACvD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,+CAAkB;gDAAC;AAG7B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACxB,IAAI;uDAAC;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACzB,IAAI;sDAAC;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDACtB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACvB,IAAI;wDAAC;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC1C;AAG3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;uDACZ;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;0DACT;AAG3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDAC3C;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACxB,IAAI;uDAAC;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAC3C;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACnB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACnB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC1B;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;+CACV;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;iDACR;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;iDACR;AAGnB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;;kDAC9B;AAGjC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iCAAW,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;;oDACpB;AAG5B;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,uCAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;;6CAC3B;AAGxB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;;iDAC/B;AAGhC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;;gDAC9B;AAG9B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC3B;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;yBAhKL,cAAc;IAD1B,IAAA,gBAAM,EAAC,kBAAkB,CAAC;GACd,cAAc,CAiK1B"}