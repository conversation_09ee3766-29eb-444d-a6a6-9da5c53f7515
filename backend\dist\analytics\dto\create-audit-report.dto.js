"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAuditReportDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class AuditTeamDto {
    leadAuditor;
    members;
    externalAuditors;
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AuditTeamDto.prototype, "leadAuditor", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AuditTeamDto.prototype, "members", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AuditTeamDto.prototype, "externalAuditors", void 0);
class AuditCriteriaDto {
    standards;
    regulations;
    policies;
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AuditCriteriaDto.prototype, "standards", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AuditCriteriaDto.prototype, "regulations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AuditCriteriaDto.prototype, "policies", void 0);
class RiskAssessmentDto {
    inherentRisk;
    controlRisk;
    detectionRisk;
    overallRisk;
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RiskAssessmentDto.prototype, "inherentRisk", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RiskAssessmentDto.prototype, "controlRisk", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RiskAssessmentDto.prototype, "detectionRisk", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RiskAssessmentDto.prototype, "overallRisk", void 0);
class SamplingMethodDto {
    type;
    size;
    criteria;
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SamplingMethodDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SamplingMethodDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SamplingMethodDto.prototype, "criteria", void 0);
class CreateAuditReportDto {
    year;
    quarter;
    reportType;
    department;
    scope;
    auditCategory;
    regulatoryFramework;
    riskLevel;
    auditor;
    objectives;
    methodology;
    executiveSummary;
    complianceScore;
    plannedStartDate;
    plannedEndDate;
    actualStartDate;
    actualEndDate;
    auditTeam;
    auditCriteria;
    riskAssessment;
    samplingMethod;
    limitations;
    conclusion;
    managementResponse;
    nextAuditDate;
}
exports.CreateAuditReportDto = CreateAuditReportDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateAuditReportDto.prototype, "year", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateAuditReportDto.prototype, "quarter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: [
            'Annual', 'Quarterly', 'Special', 'Financial', 'Comprehensive',
            'BSA_AML', 'Operations_Compliance', 'Branch_Operations', 'ACH_Cash_Management',
            'ALM', 'IT_Security', 'Network_Penetration', 'Credit_Review', 'SBA_Lending',
            'Trust_Operations', 'SOX_FDICIA', 'Enterprise_Risk'
        ]
    }),
    (0, class_validator_1.IsEnum)([
        'Annual', 'Quarterly', 'Special', 'Financial', 'Comprehensive',
        'BSA_AML', 'Operations_Compliance', 'Branch_Operations', 'ACH_Cash_Management',
        'ALM', 'IT_Security', 'Network_Penetration', 'Credit_Review', 'SBA_Lending',
        'Trust_Operations', 'SOX_FDICIA', 'Enterprise_Risk'
    ]),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "reportType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "department", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['Single Department', 'All Departments', 'Financial Only', 'Comprehensive', 'Institution_Wide', 'Regulatory_Focus'] }),
    (0, class_validator_1.IsEnum)(['Single Department', 'All Departments', 'Financial Only', 'Comprehensive', 'Institution_Wide', 'Regulatory_Focus']),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "scope", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: ['General', 'Financial_Institution', 'Regulatory_Compliance', 'Risk_Management', 'Technology', 'Operations'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['General', 'Financial_Institution', 'Regulatory_Compliance', 'Risk_Management', 'Technology', 'Operations']),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "auditCategory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateAuditReportDto.prototype, "regulatoryFramework", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: ['Low', 'Medium', 'High', 'Critical'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['Low', 'Medium', 'High', 'Critical']),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "riskLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "auditor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "objectives", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "methodology", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "executiveSummary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateAuditReportDto.prototype, "complianceScore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "plannedStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "plannedEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "actualStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "actualEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: AuditTeamDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AuditTeamDto),
    __metadata("design:type", AuditTeamDto)
], CreateAuditReportDto.prototype, "auditTeam", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: AuditCriteriaDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AuditCriteriaDto),
    __metadata("design:type", AuditCriteriaDto)
], CreateAuditReportDto.prototype, "auditCriteria", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: RiskAssessmentDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => RiskAssessmentDto),
    __metadata("design:type", RiskAssessmentDto)
], CreateAuditReportDto.prototype, "riskAssessment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: SamplingMethodDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SamplingMethodDto),
    __metadata("design:type", SamplingMethodDto)
], CreateAuditReportDto.prototype, "samplingMethod", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "limitations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "conclusion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "managementResponse", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateAuditReportDto.prototype, "nextAuditDate", void 0);
//# sourceMappingURL=create-audit-report.dto.js.map