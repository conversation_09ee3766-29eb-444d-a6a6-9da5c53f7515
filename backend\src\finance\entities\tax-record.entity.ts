import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum TaxType {
  INCOME_TAX = 'income_tax',
  SALES_TAX = 'sales_tax',
  VAT = 'vat',
  PAYROLL_TAX = 'payroll_tax',
  PROPERTY_TAX = 'property_tax',
  EXCISE_TAX = 'excise_tax',
  CUSTOMS_DUTY = 'customs_duty',
  OTHER = 'other',
}

export enum TaxStatus {
  CALCULATED = 'calculated',
  FILED = 'filed',
  PAID = 'paid',
  OVERDUE = 'overdue',
  AMENDED = 'amended',
}

@Entity('finance_tax_records')
export class TaxRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  taxRecordNumber: string;

  @Column({
    type: 'enum',
    enum: TaxType,
  })
  taxType: TaxType;

  @Column({
    type: 'enum',
    enum: TaxStatus,
    default: TaxStatus.CALCULATED,
  })
  status: TaxStatus;

  @Column({ type: 'date' })
  taxPeriodStart: Date;

  @Column({ type: 'date' })
  taxPeriodEnd: Date;

  @Column({ type: 'date' })
  dueDate: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  taxableAmount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  taxRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  penaltyAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  interestAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  totalAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  paidAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  outstandingAmount: number;

  @Column({ length: 10, default: 'USD' })
  currency: string;

  @Column({ length: 255, nullable: true })
  taxAuthority: string;

  @Column({ length: 255, nullable: true })
  referenceNumber: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'date', nullable: true })
  filedDate: Date;

  @Column({ type: 'date', nullable: true })
  paidDate: Date;

  @Column({ nullable: true })
  filedBy: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  calculations: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
