"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerInteraction = exports.InteractionStatus = exports.InteractionDirection = exports.InteractionType = void 0;
const typeorm_1 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
var InteractionType;
(function (InteractionType) {
    InteractionType["CALL"] = "call";
    InteractionType["EMAIL"] = "email";
    InteractionType["MEETING"] = "meeting";
    InteractionType["CHAT"] = "chat";
    InteractionType["SMS"] = "sms";
    InteractionType["SOCIAL_MEDIA"] = "social_media";
    InteractionType["SUPPORT_TICKET"] = "support_ticket";
    InteractionType["COMPLAINT"] = "complaint";
    InteractionType["FEEDBACK"] = "feedback";
    InteractionType["SURVEY"] = "survey";
    InteractionType["VISIT"] = "visit";
    InteractionType["OTHER"] = "other";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
var InteractionDirection;
(function (InteractionDirection) {
    InteractionDirection["INBOUND"] = "inbound";
    InteractionDirection["OUTBOUND"] = "outbound";
})(InteractionDirection || (exports.InteractionDirection = InteractionDirection = {}));
var InteractionStatus;
(function (InteractionStatus) {
    InteractionStatus["SCHEDULED"] = "scheduled";
    InteractionStatus["COMPLETED"] = "completed";
    InteractionStatus["CANCELLED"] = "cancelled";
    InteractionStatus["NO_SHOW"] = "no_show";
    InteractionStatus["FOLLOW_UP_REQUIRED"] = "follow_up_required";
})(InteractionStatus || (exports.InteractionStatus = InteractionStatus = {}));
let CustomerInteraction = class CustomerInteraction {
    id;
    customerId;
    customer;
    type;
    direction;
    status;
    subject;
    description;
    interactionDate;
    duration;
    performedBy;
    channel;
    participants;
    tags;
    outcome;
    nextSteps;
    followUpDate;
    followUpBy;
    attachments;
    rating;
    metadata;
    createdAt;
    updatedAt;
};
exports.CustomerInteraction = CustomerInteraction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => customer_entity_1.Customer, customer => customer.interactions),
    (0, typeorm_1.JoinColumn)({ name: 'customerId' }),
    __metadata("design:type", customer_entity_1.Customer)
], CustomerInteraction.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: InteractionType,
    }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: InteractionDirection,
    }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "direction", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: InteractionStatus,
        default: InteractionStatus.COMPLETED,
    }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "subject", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], CustomerInteraction.prototype, "interactionDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], CustomerInteraction.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "performedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "channel", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CustomerInteraction.prototype, "participants", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CustomerInteraction.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "outcome", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "nextSteps", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], CustomerInteraction.prototype, "followUpDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CustomerInteraction.prototype, "followUpBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], CustomerInteraction.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 2, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], CustomerInteraction.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CustomerInteraction.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomerInteraction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomerInteraction.prototype, "updatedAt", void 0);
exports.CustomerInteraction = CustomerInteraction = __decorate([
    (0, typeorm_1.Entity)('customer_interactions')
], CustomerInteraction);
//# sourceMappingURL=customer-interaction.entity.js.map