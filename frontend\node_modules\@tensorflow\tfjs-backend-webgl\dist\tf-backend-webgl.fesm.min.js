/**
 * @license
 * Copyright 2024 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import*as e from"@tensorflow/tfjs-core";import{env as t,util as n,device_util as a,backend_util as r,buffer as o,upcastType as s,tidy as i,reshape as l,broadcastTo as u,Tensor<PERSON>uffer as c,slice_util as d,kernel_impls as p,KernelBackend as h,DataStorage as f,engine as x,scalar as m,nextFrame as g,registerBackend as b,Identity as v,Complex as C,LeakyRelu as $,Prelu as y,Multiply as I,Reshape as w,sumOutType as S,Sum as R,Transpose as T,broadcast_util as k,_FusedMatMul as N,Abs as E,Acos as A,Acosh as O,Add as F,AddN as _,All as D,Any as P,ArgMax as L,ArgMin as B,Asin as V,Asinh as W,Atan as U,Atan2 as M,Atanh as G,AvgPool as z,AvgPool3D as X,AvgPool3DGrad as H,AvgPoolGrad as j,BatchMatMul as K,FusedBatchNorm as q,Slice as Y,BatchToSpaceND as Q,Bincount as Z,BitwiseAnd as J,BroadcastArgs as ee,NotEqual as te,Real as ne,Cast as ae,Ceil as re,ClipByValue as oe,ComplexAbs as se,Imag as ie,Concat as le,Conv2D as ue,Conv2DBackpropFilter as ce,Conv2DBackpropInput as de,Conv3D as pe,Conv3DBackpropFilterV2 as he,Conv3DBackpropInputV2 as fe,Cos as xe,Cosh as me,CropAndResize as ge,Cumprod as be,Cumsum as ve,DenseBincount as Ce,DepthToSpace as $e,DepthwiseConv2dNative as ye,DepthwiseConv2dNativeBackpropFilter as Ie,DepthwiseConv2dNativeBackpropInput as we,Diag as Se,Dilation2D as Re,Einsum as Te,Elu as ke,EluGrad as Ne,Equal as Ee,Erf as Ae,Exp as Oe,ExpandDims as Fe,Expm1 as _e,FFT as De,Fill as Pe,FlipLeftRight as Le,Floor as Be,FloorDiv as Ve,FromPixels as We,FusedConv2D as Ue,FusedDepthwiseConv2D as Me,GatherNd as Ge,GatherV2 as ze,Greater as Xe,GreaterEqual as He,IFFT as je,IsFinite as Ke,IsInf as qe,IsNan as Ye,Less as Qe,LessEqual as Ze,LinSpace as Je,Log as et,Log1p as tt,LogicalAnd as nt,LogicalNot as at,LogicalOr as rt,LRN as ot,LRNGrad as st,Max as it,Maximum as lt,MaxPool as ut,MaxPool3D as ct,MaxPool3DGrad as dt,MaxPoolGrad as pt,MaxPoolWithArgmax as ht,Mean as ft,Min as xt,Minimum as mt,MirrorPad as gt,Mod as bt,RealDiv as vt,Sub as Ct,Softmax as $t,Multinomial as yt,Neg as It,NonMaxSuppressionV3 as wt,NonMaxSuppressionV4 as St,NonMaxSuppressionV5 as Rt,OneHot as Tt,ZerosLike as kt,OnesLike as Nt,Pack as Et,PadV2 as At,Pow as Ot,Prod as Ft,RaggedGather as _t,RaggedRange as Dt,RaggedTensorToTensor as Pt,Range as Lt,Reciprocal as Bt,Relu as Vt,Relu6 as Wt,ResizeBilinear as Ut,ResizeBilinearGrad as Mt,ResizeNearestNeighbor as Gt,ResizeNearestNeighborGrad as zt,Reverse as Xt,RotateWithOffset as Ht,Round as jt,Rsqrt as Kt,ScatterNd as qt,SearchSorted as Yt,Select as Qt,Selu as Zt,Sigmoid as Jt,Sign as en,Sin as tn,Sinh as nn,Softplus as an,SpaceToBatchND as rn,SparseFillEmptyRows as on,SparseReshape as sn,SparseSegmentMean as ln,SparseSegmentSum as un,SparseToDense as cn,SplitV as dn,Sqrt as pn,Square as hn,SquaredDifference as fn,StaticRegexReplace as xn,Step as mn,StridedSlice as gn,StringNGrams as bn,StringSplit as vn,StringToHashBucketFast as Cn,Tan as $n,Tanh as yn,TensorScatterUpdate as In,Tile as wn,TopK as Sn,Transform as Rn,Unique as Tn,Unpack as kn,UnsortedSegmentSum as Nn,registerKernel as En}from"@tensorflow/tfjs-core";const An={},On={alpha:!1,antialias:!1,premultipliedAlpha:!1,preserveDrawingBuffer:!1,depth:!1,stencil:!1,failIfMajorPerformanceCaveat:!0};function Fn(e,t){An[e]=t}function _n(e,n){if(!(e in An)||null!=n){const a=function(e,n){if(1!==e&&2!==e)throw new Error("Cannot get WebGL rendering context, WebGL is disabled.");const a=null==n?function(e){if(t().getBool("IS_SAFARI")||"undefined"==typeof OffscreenCanvas||2!==e){if("undefined"!=typeof document)return document.createElement("canvas");throw new Error("Cannot create a canvas in this context")}return new OffscreenCanvas(300,150)}(e):n;a.addEventListener("webglcontextlost",(t=>{t.preventDefault(),delete An[e]}),!1),t().getBool("SOFTWARE_WEBGL_ENABLED")&&(On.failIfMajorPerformanceCaveat=!1);if(1===e)return a.getContext("webgl",On)||a.getContext("experimental-webgl",On);return a.getContext("webgl2",On)}(e,n);if(null===a)return console.log("Could not get context for WebGL version",e),null;An[e]=a}const a=An[e];return null==a||a.isContextLost()?(delete An[e],_n(e)):(a.disable(a.DEPTH_TEST),a.disable(a.STENCIL_TEST),a.disable(a.BLEND),a.disable(a.DITHER),a.disable(a.POLYGON_OFFSET_FILL),a.disable(a.SAMPLE_COVERAGE),a.enable(a.SCISSOR_TEST),a.enable(a.CULL_FACE),a.cullFace(a.BACK),An[e])}var Dn,Pn,Ln;function Bn(e,t){return[t,e]}function Vn(e){const t=n.sizeFromShape(e),a=Math.ceil(t/4);return n.sizeToSquarishShape(a)}function Wn(e,t){return[Math.max(1,Math.ceil(t/2)),Math.max(1,Math.ceil(e/2))]}function Un(e,n){const a=e;let r,o,s,i,l,u,c,d,p,h;return 2===t().getNumber("WEBGL_VERSION")?(r=a.R32F,o=a.R16F,s=a.RGBA16F,i=a.RGBA32F,l=a.RED,c=4,d=1,p=a.HALF_FLOAT,h=a.FLOAT,u=a.RGBA8):(r=e.RGBA,o=e.RGBA,s=e.RGBA,i=a.RGBA,l=e.RGBA,c=4,d=4,p=null!=n?n.HALF_FLOAT_OES:null,h=e.FLOAT,u=e.RGBA),{internalFormatFloat:r,internalFormatHalfFloat:o,internalFormatPackedHalfFloat:s,internalFormatPackedFloat:i,textureFormatFloat:l,downloadTextureFormat:u,downloadUnpackNumChannels:c,defaultNumChannels:d,textureTypeHalfFloat:p,textureTypeFloat:h}}function Mn(e,n){const a=n();return t().getBool("DEBUG")&&function(e){const t=e.getError();if(t!==e.NO_ERROR)throw new Error("WebGL Error: "+zn(e,t))}(e),a}!function(e){e[e.DENSE=0]="DENSE",e[e.SHARED_BATCH=1]="SHARED_BATCH"}(Dn||(Dn={})),function(e){e[e.RENDER=0]="RENDER",e[e.UPLOAD=1]="UPLOAD",e[e.PIXELS=2]="PIXELS",e[e.DOWNLOAD=3]="DOWNLOAD"}(Pn||(Pn={})),function(e){e[e.UNPACKED_FLOAT16=0]="UNPACKED_FLOAT16",e[e.UNPACKED_FLOAT32=1]="UNPACKED_FLOAT32",e[e.PACKED_4X1_UNSIGNED_BYTE=2]="PACKED_4X1_UNSIGNED_BYTE",e[e.PACKED_2X2_FLOAT32=3]="PACKED_2X2_FLOAT32",e[e.PACKED_2X2_FLOAT16=4]="PACKED_2X2_FLOAT16"}(Ln||(Ln={}));function Gn(e){return!!(t().getBool("WEBGL_RENDER_FLOAT32_ENABLED")||0===e||5.96e-8<Math.abs(e)&&Math.abs(e)<65504)}function zn(e,t){switch(t){case e.NO_ERROR:return"NO_ERROR";case e.INVALID_ENUM:return"INVALID_ENUM";case e.INVALID_VALUE:return"INVALID_VALUE";case e.INVALID_OPERATION:return"INVALID_OPERATION";case e.INVALID_FRAMEBUFFER_OPERATION:return"INVALID_FRAMEBUFFER_OPERATION";case e.OUT_OF_MEMORY:return"OUT_OF_MEMORY";case e.CONTEXT_LOST_WEBGL:return"CONTEXT_LOST_WEBGL";default:return`Unknown error code ${t}`}}function Xn(e,t){return ha(e,(()=>e.getExtension(t)),'Extension "'+t+'" not supported on this browser.')}function Hn(e,t){const n=ha(e,(()=>e.createShader(e.VERTEX_SHADER)),"Unable to create vertex WebGLShader.");if(Mn(e,(()=>e.shaderSource(n,t))),Mn(e,(()=>e.compileShader(n))),!1===e.getShaderParameter(n,e.COMPILE_STATUS))throw console.log(e.getShaderInfoLog(n)),new Error("Failed to compile vertex shader.");return n}function jn(e,n){const a=ha(e,(()=>e.createShader(e.FRAGMENT_SHADER)),"Unable to create fragment WebGLShader.");if(Mn(e,(()=>e.shaderSource(a,n))),Mn(e,(()=>e.compileShader(a))),t().get("ENGINE_COMPILE_ONLY"))return a;if(!1===e.getShaderParameter(a,e.COMPILE_STATUS))throw qn(n,e.getShaderInfoLog(a)),new Error("Failed to compile fragment shader.");return a}const Kn=/ERROR: [0-9]+:([0-9]+):/g;function qn(e,t){const a=Kn.exec(t);if(null==a)return console.log(`Couldn't parse line number in error: ${t}`),void console.log(e);const r=+a[1],o=e.split("\n"),s=o.length.toString().length+2,i=o.map(((e,t)=>n.rightPad((t+1).toString(),s)+e));let l=0;for(let e=0;e<i.length;e++)l=Math.max(i[e].length,l);const u=i.slice(0,r-1),c=i.slice(r-1,r),d=i.slice(r);console.log(u.join("\n")),console.log(t.split("\n")[0]),console.log(`%c ${n.rightPad(c[0],l)}`,"border:1px solid red; background-color:#e3d2d2; color:#a61717"),console.log(d.join("\n"))}function Yn(e){return ha(e,(()=>e.createProgram()),"Unable to create WebGLProgram.")}function Qn(e,n){if(Mn(e,(()=>e.linkProgram(n))),!t().get("ENGINE_COMPILE_ONLY")&&!1===e.getProgramParameter(n,e.LINK_STATUS))throw console.log(e.getProgramInfoLog(n)),new Error("Failed to link vertex and fragment shaders.")}function Zn(e,t){if(Mn(e,(()=>e.validateProgram(t))),!1===e.getProgramParameter(t,e.VALIDATE_STATUS))throw console.log(e.getProgramInfoLog(t)),new Error("Shader program validation failed.")}function Jn(e,t){const n=ha(e,(()=>e.createBuffer()),"Unable to create WebGLBuffer");return Mn(e,(()=>e.bindBuffer(e.ARRAY_BUFFER,n))),Mn(e,(()=>e.bufferData(e.ARRAY_BUFFER,t,e.STATIC_DRAW))),n}function ea(e,t){const n=ha(e,(()=>e.createBuffer()),"Unable to create WebGLBuffer");return Mn(e,(()=>e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,n))),Mn(e,(()=>e.bufferData(e.ELEMENT_ARRAY_BUFFER,t,e.STATIC_DRAW))),n}function ta(e){return ha(e,(()=>e.createTexture()),"Unable to create WebGLTexture.")}function na(e,n){const a=t().getNumber("WEBGL_MAX_TEXTURE_SIZE");if(e<=0||n<=0){throw new Error("Requested texture size "+`[${e}x${n}]`+" is invalid.")}if(e>a||n>a){throw new Error("Requested texture size "+`[${e}x${n}]`+" greater than WebGL maximum on this browser / GPU "+`[${a}x${a}]`+".")}}function aa(e){return ha(e,(()=>e.createFramebuffer()),"Unable to create WebGLFramebuffer.")}function ra(e,t,n,a,r,o,s){const i=e.getAttribLocation(t,n);return-1!==i&&(Mn(e,(()=>e.bindBuffer(e.ARRAY_BUFFER,a))),Mn(e,(()=>e.vertexAttribPointer(i,r,e.FLOAT,!1,o,s))),Mn(e,(()=>e.enableVertexAttribArray(i))),!0)}function oa(e,t,n){fa(e,n),Mn(e,(()=>e.activeTexture(e.TEXTURE0+n))),Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,t)))}function sa(e,t,n){return ha(e,(()=>e.getUniformLocation(t,n)),'uniform "'+n+'" not present in program.')}function ia(e,t,n){return e.getUniformLocation(t,n)}function la(e,t,n,a){Mn(e,(()=>oa(e,t,a))),Mn(e,(()=>e.uniform1i(n,a)))}function ua(e,t,n){Mn(e,(()=>e.bindFramebuffer(e.FRAMEBUFFER,n))),Mn(e,(()=>e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t,0)))}function ca(e,t){Mn(e,(()=>e.bindFramebuffer(e.FRAMEBUFFER,t))),Mn(e,(()=>e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,null,0)))}function da(e){const t=e.checkFramebufferStatus(e.FRAMEBUFFER);if(t!==e.FRAMEBUFFER_COMPLETE)throw new Error("Error binding framebuffer: "+pa(e,t))}function pa(e,t){switch(t){case e.FRAMEBUFFER_INCOMPLETE_ATTACHMENT:return"FRAMEBUFFER_INCOMPLETE_ATTACHMENT";case e.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT:return"FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT";case e.FRAMEBUFFER_INCOMPLETE_DIMENSIONS:return"FRAMEBUFFER_INCOMPLETE_DIMENSIONS";case e.FRAMEBUFFER_UNSUPPORTED:return"FRAMEBUFFER_UNSUPPORTED";default:return`unknown error ${t}`}}function ha(e,t,n){const a=Mn(e,(()=>t()));if(null==a)throw new Error(n);return a}function fa(e,t){const n=e.MAX_COMBINED_TEXTURE_IMAGE_UNITS-1,a=t+e.TEXTURE0;if(a<e.TEXTURE0||a>n){throw new Error(`textureUnit must be in ${`[gl.TEXTURE0, gl.TEXTURE${n}]`}.`)}}function xa(e,t=2){return n.sizeFromShape(e.slice(0,e.length-t))}function ma(e){if(0===e.length)throw Error("Cannot get rows and columns of an empty shape array.");return[e.length>1?e[e.length-2]:1,e[e.length-1]]}function ga(e){let t=[1,1,1];return 0===e.length||1===e.length&&1===e[0]||(t=[xa(e),...ma(e)]),t}function ba(e,a=!1){let r=t().getNumber("WEBGL_MAX_TEXTURE_SIZE"),o=t().getNumber("WEBGL_MAX_SIZE_FOR_NARROW_TEXTURE");if(o===1/0&&t().getBool("WEBGL_AUTO_SQUARIFY_NARROW_TEXTURE_SHAPE")&&(o=r/2),a&&(r*=2,o*=2,1===(e=e.map(((t,a)=>a>=e.length-2?n.nearestLargerEven(e[a]):e[a]))).length&&(e=[2,e[0]])),2!==e.length){const t=n.squeezeShape(e);e=t.newShape}let s=n.sizeFromShape(e),i=null;e.length<=1&&s<=r?i=[1,s]:2===e.length&&e[0]<=r&&e[1]<=r?i=e:3===e.length&&e[0]*e[1]<=r&&e[2]<=r?i=[e[0]*e[1],e[2]]:3===e.length&&e[0]<=r&&e[1]*e[2]<=r?i=[e[0],e[1]*e[2]]:4===e.length&&e[0]*e[1]*e[2]<=r&&e[3]<=r?i=[e[0]*e[1]*e[2],e[3]]:4===e.length&&e[0]<=r&&e[1]*e[2]*e[3]<=r&&(i=[e[0],e[1]*e[2]*e[3]]);const l=null!=i&&Math.max(...i)>o&&Math.min(...i)<=(a?2:1)&&Math.min(...i)>0;if(null==i||l)if(a){const t=xa(e);let a=2,r=2;e.length&&([a,r]=ma(e)),s=t*(a/2)*(r/2),i=n.sizeToSquarishShape(s).map((e=>2*e))}else i=n.sizeToSquarishShape(s);return i}function va(e){return e%2==0}function Ca(e,t){if(e=e.slice(-2),t=t.slice(-2),n.arraysEqual(e,t))return!0;if(!e.length||!t.length)return!0;if(0===e[0]||0===e[1]||0===t[0]||0===t[1])return!0;if(e.length!==t.length){const n=e[e.length-1],a=t[t.length-1];if(n===a)return!0;if(va(n)&&va(a)&&(1===e[0]||1===t[0]))return!0}return e[1]===t[1]&&va(e[0])&&va(t[0])}let $a,ya;function Ia(e){if(null==$a){const t=_n(e);$a=t.getParameter(t.MAX_TEXTURE_SIZE)}return $a}function wa(e){if(null==ya){const t=_n(e);ya=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS)}return Math.min(16,ya)}function Sa(e){if(0===e)return 0;let t;const n=_n(e);return t=Ra(n,"EXT_disjoint_timer_query_webgl2")&&2===e?2:Ra(n,"EXT_disjoint_timer_query")?1:0,t}function Ra(e,t){return null!=e.getExtension(t)}function Ta(e){try{if(null!=_n(e))return!0}catch(e){return console.log("Error when getting WebGL context: ",e),!1}return!1}function ka(e){if(0===e)return!1;const t=_n(e);if(1===e){if(!Ra(t,"OES_texture_float"))return!1}else if(!Ra(t,"EXT_color_buffer_float"))return!1;return Ea(t)}function Na(e){if(0===e)return!1;const t=_n(e);if(1!==e){if(Ra(t,"EXT_color_buffer_float"))return Ea(t);const e="EXT_color_buffer_half_float";if(Ra(t,e)){const n=t.getExtension(e);return function(e,t){const n=Un(e,t),a=e.createTexture();e.bindTexture(e.TEXTURE_2D,a);const r=1,o=1;e.texImage2D(e.TEXTURE_2D,0,n.internalFormatHalfFloat,r,o,0,n.textureFormatFloat,n.textureTypeHalfFloat,null);const s=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,s),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,a,0);const i=e.checkFramebufferStatus(e.FRAMEBUFFER)===e.FRAMEBUFFER_COMPLETE;return e.bindTexture(e.TEXTURE_2D,null),e.bindFramebuffer(e.FRAMEBUFFER,null),e.deleteTexture(a),e.deleteFramebuffer(s),i}(t,n)}return!1}if(!Ra(t,"OES_texture_float"))return!1;if(!Ra(t,"WEBGL_color_buffer_float"))return!1;return Ea(t)}function Ea(e){const t=Un(e),n=e.createTexture();e.bindTexture(e.TEXTURE_2D,n);e.texImage2D(e.TEXTURE_2D,0,t.internalFormatFloat,1,1,0,t.textureFormatFloat,t.textureTypeFloat,null);const a=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,a),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,n,0);const r=e.checkFramebufferStatus(e.FRAMEBUFFER)===e.FRAMEBUFFER_COMPLETE;return e.bindTexture(e.TEXTURE_2D,null),e.bindFramebuffer(e.FRAMEBUFFER,null),e.deleteTexture(n),e.deleteFramebuffer(a),r}function Aa(e){if(2!==e)return!1;return null!=_n(e).fenceSync}function Oa(e,t){Array.isArray(e)||(e=[e]),e.forEach((e=>{null!=e&&n.assert("complex64"!==e.dtype,(()=>`${t} does not support complex64 tensors in the WebGL backend.`))}))}var Fa={__proto__:null,assertNotComplex:Oa,bindCanvasToFramebuffer:function(e){Mn(e,(()=>e.bindFramebuffer(e.FRAMEBUFFER,null))),Mn(e,(()=>e.viewport(0,0,e.canvas.width,e.canvas.height))),Mn(e,(()=>e.scissor(0,0,e.canvas.width,e.canvas.height)))},bindColorTextureToFramebuffer:ua,bindTextureToProgramUniformSampler:la,bindTextureUnit:oa,bindVertexBufferToProgramAttribute:ra,callAndCheck:Mn,canBeRepresented:Gn,createFragmentShader:jn,createFramebuffer:aa,createProgram:Yn,createStaticIndexBuffer:ea,createStaticVertexBuffer:Jn,createTexture:ta,createVertexShader:Hn,getBatchDim:xa,getExtensionOrThrow:Xn,getFramebufferErrorMessage:pa,getMaxTexturesInShader:wa,getNumChannels:function(){return 2===t().getNumber("WEBGL_VERSION")?1:4},getProgramUniformLocation:ia,getProgramUniformLocationOrThrow:sa,getRowsCols:ma,getShapeAs3D:ga,getTextureShapeFromLogicalShape:ba,getWebGLDisjointQueryTimerVersion:Sa,getWebGLErrorMessage:zn,getWebGLMaxTextureSize:Ia,hasExtension:Ra,isCapableOfRenderingToFloatTexture:ka,isDownloadFloatTextureEnabled:Na,isReshapeFree:Ca,isWebGLFenceEnabled:Aa,isWebGLVersionEnabled:Ta,linkProgram:Qn,logShaderSourceAndInfoLog:qn,resetMaxTextureSize:function(){$a=null},resetMaxTexturesInShader:function(){ya=null},unbindColorTextureFromFramebuffer:ca,unbindTextureUnit:function(e,t){fa(e,t),Mn(e,(()=>e.activeTexture(e.TEXTURE0+t))),Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,null)))},validateFramebuffer:da,validateProgram:Zn,validateTextureSize:na};const _a=t();function Da(){let e,n,a,r,o,s,i,l,u,c;return 2===t().getNumber("WEBGL_VERSION")?(e="#version 300 es",n="in",a="out",r="in",o="texture",s="outputColor",i="out vec4 outputColor;",l=t().getBool("WEBGL2_ISNAN_CUSTOM")?"\n      bool isnan_custom(float val) {\n        uint floatToUint = floatBitsToUint(val);\n        return (floatToUint & 0x7fffffffu) > 0x7f800000u;\n      }\n\n      bvec4 isnan_custom(vec4 val) {\n        return bvec4(isnan_custom(val.x),\n          isnan_custom(val.y), isnan_custom(val.z), isnan_custom(val.w));\n      }\n\n      #define isnan(value) isnan_custom(value)\n    ":"",u="",c="\n      #define round(value) newRound(value)\n      int newRound(float value) {\n        return int(floor(value + 0.5));\n      }\n\n      ivec4 newRound(vec4 value) {\n        return ivec4(floor(value + vec4(0.5)));\n      }\n    "):(e="",n="attribute",a="varying",r="varying",o="texture2D",s="gl_FragColor",i="",l="\n      #define isnan(value) isnan_custom(value)\n      bool isnan_custom(float val) {\n        return (val > 0. || val < 1. || val == 0.) ? false : true;\n      }\n      bvec4 isnan_custom(vec4 val) {\n        return bvec4(isnan(val.x), isnan(val.y), isnan(val.z), isnan(val.w));\n      }\n    ",u="\n      uniform float INFINITY;\n\n      bool isinf(float val) {\n        return abs(val) == INFINITY;\n      }\n      bvec4 isinf(vec4 val) {\n        return equal(abs(val), vec4(INFINITY));\n      }\n    ",c="\n      int round(float value) {\n        return int(floor(value + 0.5));\n      }\n\n      ivec4 round(vec4 value) {\n        return ivec4(floor(value + vec4(0.5)));\n      }\n    "),{version:e,attribute:n,varyingVs:a,varyingFs:r,texture2D:o,output:s,defineOutput:i,defineSpecialNaN:l,defineSpecialInf:u,defineRound:c}}function Pa(e,t,a="index"){const r=n.computeStrides(t);return r.map(((t,n)=>`${`int ${e[n]} = ${a} / ${t}`}; ${n===r.length-1?`int ${e[n+1]} = ${a} - ${e[n]} * ${t}`:`index -= ${e[n]} * ${t}`};`)).join("")}function La(e,t,a="index"){const r=n.computeStrides(t);return r.map(((t,n)=>`${`int ${e[n]} = ${a} / outShapeStrides[${n}]`}; ${n===r.length-1?`int ${e[n+1]} = ${a} - ${e[n]} * outShapeStrides[${n}]`:`index -= ${e[n]} * outShapeStrides[${n}]`};`)).join("")}function Ba(e,t,n="index"){const a=function(e,t){const n=e.length,a=e.map((e=>`${t}[${e}]`)),r=new Array(n-1);r[n-2]=a[n-1];for(let e=n-3;e>=0;--e)r[e]=`(${r[e+1]} * ${a[e+1]})`;return r}(e.map(((e,t)=>t)),t);return a.map(((t,r)=>`${`int ${e[r]} = ${n} / ${a[r]}`}; ${r===a.length-1?`int ${e[r+1]} = ${n} - ${e[r]} * ${a[r]}`:`index -= ${e[r]} * ${a[r]}`};`)).join("")}function Va(e){const t=n.computeStrides(e).map((e=>e.toString()));return`\n  int getFlatIndex(ivec3 coords) {\n    return coords.x * ${t[0]} + coords.y * ${t[1]} + coords.z;\n  }\n`}_a.registerFlag("HAS_WEBGL",(()=>_a.getNumber("WEBGL_VERSION")>0)),_a.registerFlag("WEBGL_VERSION",(()=>Ta(2)?2:Ta(1)?1:0)),_a.registerFlag("WEBGL_CHECK_NUMERICAL_PROBLEMS",(()=>!1)),_a.registerFlag("WEBGL_BUFFER_SUPPORTED",(()=>2===_a.get("WEBGL_VERSION"))),_a.registerFlag("WEBGL_CPU_FORWARD",(()=>!0)),_a.registerFlag("WEBGL_FORCE_F16_TEXTURES",(()=>!1)),_a.registerFlag("WEBGL_PACK",(()=>_a.getBool("HAS_WEBGL"))),_a.registerFlag("WEBGL_PACK_NORMALIZATION",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_CLIP",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_DEPTHWISECONV",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_BINARY_OPERATIONS",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_UNARY_OPERATIONS",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_ARRAY_OPERATIONS",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_IMAGE_OPERATIONS",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_REDUCE",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_LAZILY_UNPACK",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_CONV_IM2COL",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_PACK_CONV2DTRANSPOSE",(()=>_a.getBool("WEBGL_PACK"))),_a.registerFlag("WEBGL_MAX_TEXTURE_SIZE",(()=>Ia(_a.getNumber("WEBGL_VERSION")))),_a.registerFlag("WEBGL_MAX_TEXTURES_IN_SHADER",(()=>wa(_a.getNumber("WEBGL_VERSION")))),_a.registerFlag("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION",(()=>{const e=_a.getNumber("WEBGL_VERSION");return 0===e?0:Sa(e)})),_a.registerFlag("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE",(()=>_a.getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION")>0&&!a.isMobile())),_a.registerFlag("WEBGL_RENDER_FLOAT32_CAPABLE",(()=>ka(_a.getNumber("WEBGL_VERSION")))),_a.registerFlag("WEBGL_RENDER_FLOAT32_ENABLED",(()=>!_a.getBool("WEBGL_FORCE_F16_TEXTURES")&&_a.getBool("WEBGL_RENDER_FLOAT32_CAPABLE"))),_a.registerFlag("WEBGL_DOWNLOAD_FLOAT_ENABLED",(()=>Na(_a.getNumber("WEBGL_VERSION")))),_a.registerFlag("WEBGL_FENCE_API_ENABLED",(()=>Aa(_a.getNumber("WEBGL_VERSION")))),_a.registerFlag("WEBGL_SIZE_UPLOAD_UNIFORM",(()=>_a.getBool("WEBGL_RENDER_FLOAT32_ENABLED")?4:0)),_a.registerFlag("WEBGL_DELETE_TEXTURE_THRESHOLD",(()=>-1),(e=>{if("number"!=typeof e)throw new Error(`WEBGL_DELETE_TEXTURE_THRESHOLD must be a number but got ${e}.`);if(e<0&&-1!==e)throw new Error(`WEBGL_DELETE_TEXTURE_THRESHOLD must be -1 (indicating never delete) or at least 0, but got ${e}.`)})),_a.registerFlag("WEBGL_FLUSH_THRESHOLD",(()=>a.isMobile()?1:-1),(e=>{if("number"!=typeof e)throw new Error(`WEBGL_FLUSH_THRESHOLD must be a number but got ${e}.`);if(e<0&&-1!==e)throw new Error(`WEBGL_FLUSH_THRESHOLD must be -1 (indicating never manual flush) or at least 0, but got ${e}.`)})),_a.registerFlag("CPU_HANDOFF_SIZE_THRESHOLD",(()=>128)),_a.registerFlag("WEBGL_USE_SHAPES_UNIFORMS",(()=>!1)),_a.registerFlag("TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD",(()=>1e5)),_a.registerFlag("TOPK_K_CPU_HANDOFF_THRESHOLD",(()=>128)),_a.registerFlag("WEBGL_EXP_CONV",(()=>!1)),_a.registerFlag("SOFTWARE_WEBGL_ENABLED",(()=>_a.getBool("IS_TEST"))),_a.registerFlag("WEBGL_MAX_SIZE_FOR_NARROW_TEXTURE",(()=>1/0)),_a.registerFlag("WEBGL_AUTO_SQUARIFY_NARROW_TEXTURE_SHAPE",(()=>!1)),_a.registerFlag("WEBGL2_ISNAN_CUSTOM",(()=>!1)),_a.registerFlag("ENGINE_COMPILE_ONLY",(()=>!1));const Wa="\n  const float FLOAT_MAX = 1.70141184e38;\n  const float FLOAT_MIN = 1.17549435e-38;\n\n  lowp vec4 encode_float(highp float v) {\n    if (isnan(v)) {\n      return vec4(255, 255, 255, 255);\n    }\n\n    highp float av = abs(v);\n\n    if(av < FLOAT_MIN) {\n      return vec4(0.0, 0.0, 0.0, 0.0);\n    } else if(v > FLOAT_MAX) {\n      return vec4(0.0, 0.0, 128.0, 127.0) / 255.0;\n    } else if(v < -FLOAT_MAX) {\n      return vec4(0.0, 0.0,  128.0, 255.0) / 255.0;\n    }\n\n    highp vec4 c = vec4(0,0,0,0);\n\n    highp float e = floor(log2(av));\n    highp float m = exp2(fract(log2(av))) - 1.0;\n\n    c[2] = floor(128.0 * m);\n    m -= c[2] / 128.0;\n    c[1] = floor(32768.0 * m);\n    m -= c[1] / 32768.0;\n    c[0] = floor(8388608.0 * m);\n\n    highp float ebias = e + 127.0;\n    c[3] = floor(ebias / 2.0);\n    ebias -= c[3] * 2.0;\n    c[2] += floor(ebias) * 128.0;\n\n    c[3] += 128.0 * step(0.0, -v);\n\n    return c / 255.0;\n  }\n",{getBroadcastDims:Ua}=r;function Ma(e,t,a){const r=[];if(e.forEach((e=>{const t=n.sizeFromShape(e.shapeInfo.logicalShape);if(e.shapeInfo.isUniform?r.push(`uniform float ${e.name}${t>1?`[${t}]`:""};`):(r.push(`uniform sampler2D ${e.name};`),r.push(`uniform int offset${e.name};`)),a.enableShapeUniforms){const{uniformShape:t}=Ja(a.packedInputs,e.shapeInfo.logicalShape,e.shapeInfo.texShape);switch(t.length){case 1:r.push(`uniform int ${e.name}Shape;`);break;case 2:r.push(`uniform ivec2 ${e.name}Shape;`);break;case 3:r.push(`uniform ivec3 ${e.name}Shape;`);break;case 4:r.push(`uniform ivec4 ${e.name}Shape;`)}r.push(`uniform ivec2 ${e.name}TexShape;`)}})),a.enableShapeUniforms){switch(t.logicalShape.length){case 1:r.push("uniform int outShape;");break;case 2:r.push("uniform ivec2 outShape;"),r.push("uniform int outShapeStrides;");break;case 3:r.push("uniform ivec3 outShape;"),r.push("uniform ivec2 outShapeStrides;");break;case 4:r.push("uniform ivec4 outShape;"),r.push("uniform ivec3 outShapeStrides;")}r.push("uniform ivec2 outTexShape;")}a.customUniforms&&a.customUniforms.forEach((e=>{r.push(`uniform ${e.type} ${e.name}${e.arrayIndex?`[${e.arrayIndex}]`:""};`)}));const o=r.join("\n"),s=e.map((e=>function(e,t,a=!1,r){let o="";o+=a?za(e,r):Ga(e,r);const s=e.shapeInfo.logicalShape,i=t.logicalShape;s.length<=i.length&&(o+=a?function(e,t){const a=e.name,r=a.charAt(0).toUpperCase()+a.slice(1),o="get"+r+"AtOutCoords",s=e.shapeInfo.logicalShape.length,i=t.logicalShape.length,l=Ua(e.shapeInfo.logicalShape,t.logicalShape),u=Za(i),c=i-s;let d;const p=["x","y","z","w","u","v"];d=0===s?"":i<2&&l.length>=1?"coords = 0;":l.map((e=>`coords.${p[e+c]} = 0;`)).join("\n");let h="";h=i<2&&s>0?"coords":e.shapeInfo.logicalShape.map(((e,t)=>`coords.${p[t+c]}`)).join(", ");let f="return outputValue;";const x=1===n.sizeFromShape(e.shapeInfo.logicalShape),m=1===n.sizeFromShape(t.logicalShape);if(1!==s||x||m){if(x&&!m)f=1===i?"\n        return vec4(outputValue.x, outputValue.x, 0., 0.);\n      ":"\n        return vec4(outputValue.x);\n      ";else if(l.length){const e=s-2,t=s-1;l.indexOf(e)>-1&&l.indexOf(t)>-1?f="return vec4(outputValue.x);":l.indexOf(e)>-1?f="return vec4(outputValue.x, outputValue.y, outputValue.x, outputValue.y);":l.indexOf(t)>-1&&(f="return vec4(outputValue.xx, outputValue.zz);")}}else f="\n      return vec4(outputValue.xy, outputValue.xy);\n    ";return`\n    vec4 ${o}() {\n      ${u} coords = getOutputCoords();\n      ${d}\n      vec4 outputValue = get${r}(${h});\n      ${f}\n    }\n  `}(e,t):function(e,t){const a=e.name,r=a.charAt(0).toUpperCase()+a.slice(1),o="get"+r+"AtOutCoords",s=t.texShape,i=e.shapeInfo.texShape,l=e.shapeInfo.logicalShape.length,u=t.logicalShape.length;if(!e.shapeInfo.isUniform&&l===u&&null==e.shapeInfo.flatOffset&&n.arraysEqual(i,s))return`\n      float ${o}() {\n        return sampleTexture(${a}, resultUV);\n      }\n    `;const c=Za(u),d=Ua(e.shapeInfo.logicalShape,t.logicalShape),p=u-l;let h;const f=["x","y","z","w","u","v"];h=0===l?"":u<2&&d.length>=1?"coords = 0;":d.map((e=>`coords.${f[e+p]} = 0;`)).join("\n");let x="";x=u<2&&l>0?"coords":e.shapeInfo.logicalShape.map(((e,t)=>`coords.${f[t+p]}`)).join(", ");return`\n    float ${o}() {\n      ${c} coords = getOutputCoords();\n      ${h}\n      return get${r}(${x});\n    }\n  `}(e,t));return o}(e,t,a.packedInputs,a.enableShapeUniforms))).join("\n"),i=t.texShape,l=Da(),u=function(e){return`\n    float sampleTexture(sampler2D textureSampler, vec2 uv) {\n      return ${e.texture2D}(textureSampler, uv).r;\n    }\n  `}(l);let c,d,p=function(e){return`${e.version}\n    precision highp float;\n    precision highp int;\n    precision highp sampler2D;\n    ${e.varyingFs} vec2 resultUV;\n    ${e.defineOutput}\n    const vec2 halfCR = vec2(0.5, 0.5);\n\n    struct ivec5\n    {\n      int x;\n      int y;\n      int z;\n      int w;\n      int u;\n    };\n\n    struct ivec6\n    {\n      int x;\n      int y;\n      int z;\n      int w;\n      int u;\n      int v;\n    };\n\n    uniform float NAN;\n    ${e.defineSpecialNaN}\n    ${e.defineSpecialInf}\n    ${e.defineRound}\n\n    int imod(int x, int y) {\n      return x - y * (x / y);\n    }\n\n    int idiv(int a, int b, float sign) {\n      int res = a / b;\n      int mod = imod(a, b);\n      if (sign < 0. && mod != 0) {\n        res -= 1;\n      }\n      return res;\n    }\n\n    //Based on the work of Dave Hoskins\n    //https://www.shadertoy.com/view/4djSRW\n    #define HASHSCALE1 443.8975\n    float random(float seed){\n      vec2 p = resultUV * seed;\n      vec3 p3  = fract(vec3(p.xyx) * HASHSCALE1);\n      p3 += dot(p3, p3.yzx + 19.19);\n      return fract((p3.x + p3.y) * p3.z);\n    }\n\n    ${Xa}\n    ${Ha}\n    ${ja}\n  `}(l);t.isPacked?(c=function(e,t,a){switch(e.length){case 0:return qa();case 1:return function(e,t,n){const a=[Math.ceil(t[0]/2),Math.ceil(t[1]/2)];if(1===a[0])return n?"\n      int getOutputCoords() {\n        return 2 * int(resultUV.x * ceil(float(outTexShape[1]) / 2.0));\n      }\n    ":`\n      int getOutputCoords() {\n        return 2 * int(resultUV.x * ${a[1]}.0);\n      }\n    `;if(1===a[1])return n?"\n      int getOutputCoords() {\n        return 2 * int(resultUV.y * ceil(float(outTexShape[0]) / 2.0));\n      }\n    ":`\n      int getOutputCoords() {\n        return 2 * int(resultUV.y * ${a[0]}.0);\n      }\n    `;if(n)return"\n    int getOutputCoords() {\n      ivec2 packedTexShape = ivec2(ceil(float(outTexShape[0]) / 2.0), ceil(float(outTexShape[1]) / 2.0));\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(packedTexShape[0], packedTexShape[1]));\n      return 2 * (resTexRC.x * packedTexShape[1] + resTexRC.y);\n    }\n  ";return`\n    int getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${a[0]}, ${a[1]}));\n      return 2 * (resTexRC.x * ${a[1]} + resTexRC.y);\n    }\n  `}(0,t,a);case 2:return function(e,t,a){const r=[Math.ceil(t[0]/2),Math.ceil(t[1]/2)];if(n.arraysEqual(e,t))return a?"\n      ivec2 getOutputCoords() {\n        ivec2 packedTexShape = ivec2(ceil(float(outTexShape[0]) / 2.0), ceil(float(outTexShape[1]) / 2.0));\n        return 2 * ivec2(resultUV.yx * vec2(packedTexShape[0], packedTexShape[1]));\n      }\n    ":`\n      ivec2 getOutputCoords() {\n        return 2 * ivec2(resultUV.yx * vec2(${r[0]}, ${r[1]}));\n      }\n    `;const o=Math.ceil(e[1]/2);if(a)return"\n    ivec2 getOutputCoords() {\n      ivec2 packedTexShape = ivec2(ceil(float(outTexShape[0]) / 2.0), ceil(float(outTexShape[1]) / 2.0));\n      int texelsInLogicalRow = int(ceil(float(outShape[1]) / 2.0));\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(packedTexShape[0], packedTexShape[1]));\n\n      int index = resTexRC.x * packedTexShape[1] + resTexRC.y;\n      int r = 2 * (index / texelsInLogicalRow);\n      int c = imod(index, texelsInLogicalRow) * 2;\n\n      return ivec2(r, c);\n    }\n  ";return`\n    ivec2 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${r[0]}, ${r[1]}));\n\n      int index = resTexRC.x * ${r[1]} + resTexRC.y;\n      int r = 2 * (index / ${o});\n      int c = imod(index, ${o}) * 2;\n\n      return ivec2(r, c);\n    }\n  `}(e,t,a);case 3:return function(e,t,n){if(n)return"\n    ivec3 getOutputCoords() {\n      ivec2 packedTexShape = ivec2(ceil(float(outTexShape[0]) / 2.0), ceil(float(outTexShape[1]) / 2.0));\n      int texelsInLogicalRow = int(ceil(float(outShape[2]) / 2.0));\n      int texelsInBatch = texelsInLogicalRow * int(ceil(float(outShape[1]) / 2.0));\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(packedTexShape[0], packedTexShape[1]));\n      int index = resTexRC.x * packedTexShape[1] + resTexRC.y;\n\n      int b = index / texelsInBatch;\n      index -= b * texelsInBatch;\n\n      int r = 2 * (index / texelsInLogicalRow);\n      int c = imod(index, texelsInLogicalRow) * 2;\n\n      return ivec3(b, r, c);\n    }\n  ";const a=[Math.ceil(t[0]/2),Math.ceil(t[1]/2)],r=Math.ceil(e[2]/2),o=r*Math.ceil(e[1]/2);return`\n    ivec3 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${a[0]}, ${a[1]}));\n      int index = resTexRC.x * ${a[1]} + resTexRC.y;\n\n      int b = index / ${o};\n      index -= b * ${o};\n\n      int r = 2 * (index / ${r});\n      int c = imod(index, ${r}) * 2;\n\n      return ivec3(b, r, c);\n    }\n  `}(e,t,a);default:return function(e,t,n){if(n)return"\n    ivec4 getOutputCoords() {\n      ivec2 packedTexShape = ivec2(ceil(float(outTexShape[0]) / 2.0), ceil(float(outTexShape[1]) / 2.0));\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(packedTexShape[0], packedTexShape[1]));\n      int index = resTexRC.x * packedTexShape[1] + resTexRC.y;\n\n      int texelsInLogicalRow = int(ceil(float(outShape[3]) / 2.0));\n      int texelsInBatch = texelsInLogicalRow * int(ceil(float(outShape[2]) / 2.0));\n      int texelsInBatchN = texelsInBatch * outShape[1];\n\n      int b2 = index / texelsInBatchN;\n      index -= b2 * texelsInBatchN;\n\n      int b = index / texelsInBatch;\n      index -= b * texelsInBatch;\n\n      int r = 2 * (index / texelsInLogicalRow);\n      int c = imod(index, texelsInLogicalRow) * 2;\n\n      return ivec4(b2, b, r, c);\n    }\n  ";const a=[Math.ceil(t[0]/2),Math.ceil(t[1]/2)],r=Math.ceil(e[e.length-1]/2),o=r*Math.ceil(e[e.length-2]/2);let s=o,i="",l="b, r, c";for(let t=2;t<e.length-1;t++)s*=e[e.length-t-1],i=`\n      int b${t} = index / ${s};\n      index -= b${t} * ${s};\n    `+i,l=`b${t}, `+l;return`\n    ivec${e.length} getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${a[0]}, ${a[1]}));\n      int index = resTexRC.x * ${a[1]} + resTexRC.y;\n\n      ${i}\n\n      int b = index / ${o};\n      index -= b * ${o};\n\n      int r = 2 * (index / ${r});\n      int c = imod(index, ${r}) * 2;\n\n      return ivec${e.length}(${l});\n    }\n  `}(e,t,a)}}(t.logicalShape,i,a.enableShapeUniforms),d=function(e){return`\n    void setOutput(vec4 val) {\n      ${e.output} = val;\n    }\n  `}(l)):(c=function(e,t,a){switch(e.length){case 0:return qa();case 1:return function(e,t,n){if(1===t[0])return n?"\n      int getOutputCoords() {\n        return int(resultUV.x * float(outTexShape[1]));\n      }\n    ":`\n      int getOutputCoords() {\n        return int(resultUV.x * ${t[1]}.0);\n      }\n    `;if(1===t[1])return n?"\n      int getOutputCoords() {\n        return int(resultUV.y * float(outTexShape[0]));\n      }\n    ":`\n      int getOutputCoords() {\n        return int(resultUV.y * ${t[0]}.0);\n      }\n    `;if(n)return"\n    int getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(outTexShape[0], outTexShape[1]));\n      return resTexRC.x * outTexShape[1] + resTexRC.y;\n    }\n  ";return`\n    int getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${t[0]}, ${t[1]}));\n      return resTexRC.x * ${t[1]} + resTexRC.y;\n    }\n  `}(0,t,a);case 2:return function(e,t,a){if(n.arraysEqual(e,t))return a?"\n      ivec2 getOutputCoords() {\n        return ivec2(resultUV.yx * vec2(outTexShape[0], outTexShape[1]));\n      }\n    ":`\n      ivec2 getOutputCoords() {\n        return ivec2(resultUV.yx * vec2(${t[0]}, ${t[1]}));\n      }\n    `;if(1===e[1])return a?"\n      ivec2 getOutputCoords() {\n        ivec2 resTexRC = ivec2(resultUV.yx *\n                               vec2(outTexShape[0], outTexShape[1]));\n        int index = resTexRC.x * outTexShape[1] + resTexRC.y;\n        return ivec2(index, 0);\n      }\n    ":`\n      ivec2 getOutputCoords() {\n        ivec2 resTexRC = ivec2(resultUV.yx *\n                               vec2(${t[0]}, ${t[1]}));\n        int index = resTexRC.x * ${t[1]} + resTexRC.y;\n        return ivec2(index, 0);\n      }\n    `;if(1===e[0])return a?"\n      ivec2 getOutputCoords() {\n        ivec2 resTexRC = ivec2(resultUV.yx *\n                               vec2(outTexShape[0], outTexShape[1]));\n        int index = resTexRC.x * outTexShape[1] + resTexRC.y;\n        return ivec2(0, index);\n      }\n    ":`\n      ivec2 getOutputCoords() {\n        ivec2 resTexRC = ivec2(resultUV.yx *\n                               vec2(${t[0]}, ${t[1]}));\n        int index = resTexRC.x * ${t[1]} + resTexRC.y;\n        return ivec2(0, index);\n      }\n    `;if(a)return"\n    ivec2 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(outTexShape[0], outTexShape[1]));\n      int index = resTexRC.x * outTexShape[1] + resTexRC.y;\n      int r = index / outShape[1];\n      int c = index - r * outShape[1];\n      return ivec2(r, c);\n    }\n  ";return`\n    ivec2 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${t[0]}, ${t[1]}));\n      int index = resTexRC.x * ${t[1]} + resTexRC.y;\n      int r = index / ${e[1]};\n      int c = index - r * ${e[1]};\n      return ivec2(r, c);\n    }\n  `}(e,t,a);case 3:return function(e,t,n){if(n){return`\n  ivec3 getOutputCoords() {\n    ivec2 resTexRC = ivec2(resultUV.yx *\n                           vec2(outTexShape[0], outTexShape[1]));\n    int index = resTexRC.x * outTexShape[1] + resTexRC.y;\n    ${La(["r","c","d"],e)}\n    return ivec3(r, c, d);\n  }\n`}const a=Pa(["r","c","d"],e);return`\n    ivec3 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n                             vec2(${t[0]}, ${t[1]}));\n      int index = resTexRC.x * ${t[1]} + resTexRC.y;\n      ${a}\n      return ivec3(r, c, d);\n    }\n  `}(e,t,a);case 4:return function(e,t,n){if(n){return`\n    ivec4 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n        vec2(outTexShape[0], outTexShape[1]));\n      int index = resTexRC.x * outTexShape[1] + resTexRC.y;\n      ${La(["r","c","d","d2"],e)}\n      return ivec4(r, c, d, d2);\n    }\n  `}const a=Pa(["r","c","d","d2"],e);return`\n    ivec4 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n        vec2(${t[0]}, ${t[1]}));\n      int index = resTexRC.x * ${t[1]} + resTexRC.y;\n      ${a}\n      return ivec4(r, c, d, d2);\n    }\n  `}(e,t,a);case 5:return function(e,t){const n=Pa(["r","c","d","d2","d3"],e);return`\n    ivec5 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx * vec2(${t[0]},\n                             ${t[1]}));\n\n      int index = resTexRC.x * ${t[1]} + resTexRC.y;\n\n      ${n}\n\n      ivec5 outShape = ivec5(r, c, d, d2, d3);\n      return outShape;\n    }\n  `}(e,t);case 6:return function(e,t){const n=Pa(["r","c","d","d2","d3","d4"],e);return`\n    ivec6 getOutputCoords() {\n      ivec2 resTexRC = ivec2(resultUV.yx *\n        vec2(${t[0]}, ${t[1]}));\n      int index = resTexRC.x * ${t[1]} + resTexRC.y;\n\n      ${n}\n\n      ivec6 result = ivec6(r, c, d, d2, d3, d4);\n      return result;\n    }\n  `}(e,t);default:throw new Error(`${e.length}-D output sampling is not yet supported`)}}(t.logicalShape,i,a.enableShapeUniforms),d=function(e){return`\n    void setOutput(float val) {\n      ${e.output} = vec4(val, 0, 0, 0);\n    }\n  `}(l)),a.packedInputs&&(p+=Ka);return[p,u,d,o,c,s,a.userCode].join("\n")}function Ga(e,t=!1){const a=e.shapeInfo.logicalShape;switch(a.length){case 0:return function(e,t){const n=e.name,a="get"+n.charAt(0).toUpperCase()+n.slice(1);if(e.shapeInfo.isUniform)return`float ${a}() {return ${n};}`;const[r,o]=e.shapeInfo.texShape;if(1===r&&1===o)return`\n      float ${a}() {\n        return sampleTexture(${n}, halfCR);\n      }\n    `;const s=Ya(n);if(t)return`\n    float ${a}() {\n      vec2 uv = uvFromFlat(${n}TexShape[0], ${n}TexShape[1], ${s});\n      return sampleTexture(${n}, uv);\n    }\n  `;const[i,l]=e.shapeInfo.texShape;return`\n    float ${a}() {\n      vec2 uv = uvFromFlat(${i}, ${l}, ${s});\n      return sampleTexture(${n}, uv);\n    }\n  `}(e,t);case 1:return function(e,t){const n=e.name,a="get"+n.charAt(0).toUpperCase()+n.slice(1);if(e.shapeInfo.isUniform)return`\n      float ${a}(int index) {\n        ${Qa(e)}\n      }\n    `;const r=e.shapeInfo.texShape,o=r[0],s=r[1];if(1===s&&1===o)return`\n      float ${a}(int index) {\n        return sampleTexture(${n}, halfCR);\n      }\n    `;const i=Ya(n);if(1===s)return t?`\n      float ${a}(int index) {\n        vec2 uv = vec2(0.5, (float(index + ${i}) + 0.5) / float(${n}TexShape[0]));\n        return sampleTexture(${n}, uv);\n      }\n    `:`\n      float ${a}(int index) {\n        vec2 uv = vec2(0.5, (float(index + ${i}) + 0.5) / ${o}.0);\n        return sampleTexture(${n}, uv);\n      }\n    `;if(1===o)return t?`\n      float ${a}(int index) {\n        vec2 uv = vec2((float(index + ${i}) + 0.5) / float(${n}TexShape[1]), 0.5);\n        return sampleTexture(${n}, uv);\n      }\n    `:`\n      float ${a}(int index) {\n        vec2 uv = vec2((float(index + ${i}) + 0.5) / ${s}.0, 0.5);\n        return sampleTexture(${n}, uv);\n      }\n    `;if(t)return`\n    float ${a}(int index) {\n      vec2 uv = uvFromFlat(${n}TexShape[0], ${n}TexShape[1], index + ${i});\n      return sampleTexture(${n}, uv);\n    }\n  `;return`\n    float ${a}(int index) {\n      vec2 uv = uvFromFlat(${o}, ${s}, index + ${i});\n      return sampleTexture(${n}, uv);\n    }\n  `}(e,t);case 2:return function(e,t){const a=e.shapeInfo.logicalShape,r=e.name,o="get"+r.charAt(0).toUpperCase()+r.slice(1),s=e.shapeInfo.texShape;if(null!=s&&n.arraysEqual(a,s)){if(t)return`\n      float ${o}(int row, int col) {\n        vec2 uv = (vec2(col, row) + halfCR) / vec2(${r}TexShape[1], ${r}TexShape[0]);\n        return sampleTexture(${r}, uv);\n      }\n    `;const e=s[0];return`\n    float ${o}(int row, int col) {\n      vec2 uv = (vec2(col, row) + halfCR) / vec2(${s[1]}.0, ${e}.0);\n      return sampleTexture(${r}, uv);\n    }\n  `}const{newShape:i,keptDims:l}=n.squeezeShape(a),u=i;if(u.length<a.length){const n=["row","col"];return`\n      ${Ga(er(e,u),t)}\n      float ${o}(int row, int col) {\n        return ${o}(${tr(n,l)});\n      }\n    `}if(e.shapeInfo.isUniform)return`\n      float ${o}(int row, int col) {\n        int index = round(dot(vec2(row, col), vec2(${a[1]}, 1)));\n        ${Qa(e)}\n      }\n    `;const c=s[0],d=s[1],p=Ya(r);if(1===d)return t?`\n      float ${o}(int row, int col) {\n        float index = dot(vec3(row, col, ${p}), vec3(${r}Shape[1], 1, 1));\n        vec2 uv = vec2(0.5, (index + 0.5) / float(${r}TexShape[0]));\n        return sampleTexture(${r}, uv);\n      }\n    `:`\n    float ${o}(int row, int col) {\n      float index = dot(vec3(row, col, ${p}), vec3(${a[1]}, 1, 1));\n      vec2 uv = vec2(0.5, (index + 0.5) / ${c}.0);\n      return sampleTexture(${r}, uv);\n    }\n  `;if(1===c)return t?`\n      float ${o}(int row, int col) {\n        float index = dot(vec3(row, col, ${p}), vec3(${r}Shape[1], 1, 1));\n        vec2 uv = vec2((index + 0.5) / float(${r}TexShape[1]), 0.5);\n        return sampleTexture(${r}, uv);\n      }\n    `:`\n    float ${o}(int row, int col) {\n      float index = dot(vec3(row, col, ${p}), vec3(${a[1]}, 1, 1));\n      vec2 uv = vec2((index + 0.5) / ${d}.0, 0.5);\n      return sampleTexture(${r}, uv);\n    }\n  `;if(t)return`\n      float ${o}(int row, int col) {\n        // Explicitly use integer operations as dot() only works on floats.\n        int index = row * ${r}Shape[1] + col + ${p};\n        vec2 uv = uvFromFlat(${r}TexShape[0], ${r}TexShape[1], index);\n        return sampleTexture(${r}, uv);\n      }\n    `;return`\n  float ${o}(int row, int col) {\n    // Explicitly use integer operations as dot() only works on floats.\n    int index = row * ${a[1]} + col + ${p};\n    vec2 uv = uvFromFlat(${c}, ${d}, index);\n    return sampleTexture(${r}, uv);\n  }\n`}(e,t);case 3:return function(e,t){const a=e.shapeInfo.logicalShape,r=e.name,o="get"+r.charAt(0).toUpperCase()+r.slice(1),s=a[1]*a[2],i=a[2],{newShape:l,keptDims:u}=n.squeezeShape(a),c=l;if(c.length<a.length){const n=["row","col","depth"];return`\n        ${Ga(er(e,c),t)}\n        float ${o}(int row, int col, int depth) {\n          return ${o}(${tr(n,u)});\n        }\n      `}if(e.shapeInfo.isUniform)return`\n      float ${o}(int row, int col, int depth) {\n        int index = round(dot(vec3(row, col, depth),\n                          vec3(${s}, ${i}, 1)));\n        ${Qa(e)}\n      }\n    `;const d=e.shapeInfo.texShape,p=d[0],h=d[1],f=e.shapeInfo.flatOffset;if(h===s&&null==f)return t?`\n      float ${o}(int row, int col, int depth) {\n        int stride1 = ${r}Shape[2];\n        float texR = float(row);\n        float texC = dot(vec2(col, depth), vec2(stride1, 1));\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                   vec2(${r}TexShape[1], ${r}TexShape[0]);\n        return sampleTexture(${r}, uv);\n      }\n    `:`\n        float ${o}(int row, int col, int depth) {\n          float texR = float(row);\n          float texC = dot(vec2(col, depth), vec2(${i}, 1));\n          vec2 uv = (vec2(texC, texR) + halfCR) /\n                     vec2(${h}.0, ${p}.0);\n          return sampleTexture(${r}, uv);\n        }\n      `;if(h===i&&null==f)return t?`\n      float ${o}(int row, int col, int depth) {\n        float texR = dot(vec2(row, col), vec2(${r}Shape[1], 1));\n        float texC = float(depth);\n        vec2 uv = (vec2(texC, texR) + halfCR) / vec2(${r}TexShape[1], ${r}TexShape[0]);\n        return sampleTexture(${r}, uv);\n      }\n    `:`\n    float ${o}(int row, int col, int depth) {\n      float texR = dot(vec2(row, col), vec2(${a[1]}, 1));\n      float texC = float(depth);\n      vec2 uv = (vec2(texC, texR) + halfCR) / vec2(${h}.0, ${p}.0);\n      return sampleTexture(${r}, uv);\n    }\n  `;const x=Ya(r);if(t)return`\n    float ${o}(int row, int col, int depth) {\n      // Explicitly use integer operations as dot() only works on floats.\n      int stride0 = ${r}Shape[1] * ${r}Shape[2];\n      int stride1 = ${r}Shape[2];\n      int index = row * stride0 + col * stride1 + depth + ${x};\n      vec2 uv = uvFromFlat(${r}TexShape[0], ${r}TexShape[1], index);\n      return sampleTexture(${r}, uv);\n    }\n    `;return`\n      float ${o}(int row, int col, int depth) {\n        // Explicitly use integer operations as dot() only works on floats.\n        int index = row * ${s} + col * ${i} + depth + ${x};\n        vec2 uv = uvFromFlat(${p}, ${h}, index);\n        return sampleTexture(${r}, uv);\n      }\n  `}(e,t);case 4:return function(e,t){const a=e.shapeInfo.logicalShape,r=e.name,o="get"+r.charAt(0).toUpperCase()+r.slice(1),s=a[3],i=a[2]*s,l=a[1]*i,{newShape:u,keptDims:c}=n.squeezeShape(a);if(u.length<a.length){const n=["row","col","depth","depth2"];return`\n      ${Ga(er(e,u),t)}\n      float ${o}(int row, int col, int depth, int depth2) {\n        return ${o}(${tr(n,c)});\n      }\n    `}if(e.shapeInfo.isUniform)return`\n      float ${o}(int row, int col, int depth, int depth2) {\n        int index = round(dot(vec4(row, col, depth, depth2),\n                          vec4(${l}, ${i}, ${s}, 1)));\n        ${Qa(e)}\n      }\n    `;const d=e.shapeInfo.flatOffset,p=e.shapeInfo.texShape,h=p[0],f=p[1],x=`int stride2 = ${r}Shape[3];`,m=`int stride1 = ${r}Shape[2] * stride2;`,g=`int stride0 = ${r}Shape[1] * stride1;`;if(f===l&&null==d)return t?`\n      float ${o}(int row, int col, int depth, int depth2) {\n        ${x}\n        ${m}\n        float texR = float(row);\n        float texC =\n            dot(vec3(col, depth, depth2),\n                vec3(stride1, stride2, 1));\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                   vec2(${r}TexShape[1], ${r}TexShape[0]);\n        return sampleTexture(${r}, uv);\n      }\n    `:`\n      float ${o}(int row, int col, int depth, int depth2) {\n        float texR = float(row);\n        float texC =\n            dot(vec3(col, depth, depth2),\n                vec3(${i}, ${s}, 1));\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                   vec2(${f}.0, ${h}.0);\n        return sampleTexture(${r}, uv);\n      }\n    `;if(f===s&&null==d)return t?`\n      float ${o}(int row, int col, int depth, int depth2) {\n        float texR = dot(vec3(row, col, depth),\n                         vec3(${r}Shape[1] * ${r}Shape[2], ${r}Shape[2], 1));\n        float texC = float(depth2);\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                  vec2(${r}TexShape[1], ${r}TexShape[0]);\n        return sampleTexture(${r}, uv);\n      }\n    `:`\n      float ${o}(int row, int col, int depth, int depth2) {\n        float texR = dot(vec3(row, col, depth),\n                         vec3(${a[1]*a[2]}, ${a[2]}, 1));\n        float texC = float(depth2);\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                  vec2(${f}.0, ${h}.0);\n        return sampleTexture(${r}, uv);\n      }\n    `;const b=Ya(r);if(t)return`\n    float ${o}(int row, int col, int depth, int depth2) {\n      // Explicitly use integer operations as dot() only works on floats.\n      ${x}\n      ${m}\n      ${g}\n      int index = row * stride0 + col * stride1 +\n          depth * stride2 + depth2;\n      vec2 uv = uvFromFlat(${r}TexShape[0], ${r}TexShape[1], index + ${b});\n      return sampleTexture(${r}, uv);\n    }\n  `;return`\n    float ${o}(int row, int col, int depth, int depth2) {\n      // Explicitly use integer operations as dot() only works on floats.\n      int index = row * ${l} + col * ${i} +\n          depth * ${s} + depth2;\n      vec2 uv = uvFromFlat(${h}, ${f}, index + ${b});\n      return sampleTexture(${r}, uv);\n    }\n  `}(e,t);case 5:return function(e){const t=e.shapeInfo.logicalShape,a=e.name,r="get"+a.charAt(0).toUpperCase()+a.slice(1),o=t[4],s=t[3]*o,i=t[2]*s,l=t[1]*i,{newShape:u,keptDims:c}=n.squeezeShape(t);if(u.length<t.length){const t=["row","col","depth","depth2","depth3"];return`\n      ${Ga(er(e,u))}\n      float ${r}(int row, int col, int depth, int depth2, int depth3) {\n        return ${r}(${tr(t,c)});\n      }\n    `}if(e.shapeInfo.isUniform)return`\n      float ${r}(int row, int col, int depth, int depth2, int depth3) {\n        float index = dot(\n          vec4(row, col, depth, depth2),\n          vec4(${l}, ${i}, ${s}, ${o})) +\n          depth3;\n        ${Qa(e)}\n      }\n    `;const d=e.shapeInfo.flatOffset,p=e.shapeInfo.texShape,h=p[0],f=p[1];if(f===l&&null==d)return`\n      float ${r}(int row, int col, int depth, int depth2, int depth3) {\n        int texR = row;\n        float texC = dot(vec4(col, depth, depth2, depth3),\n                         vec4(${i}, ${s}, ${o}, 1));\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                   vec2(${f}.0, ${h}.0);\n        return sampleTexture(${a}, uv);\n      }\n    `;if(f===o&&null==d)return`\n      float ${r}(int row, int col, int depth, int depth2, int depth3) {\n        float texR = dot(\n          vec4(row, col, depth, depth2),\n          vec4(${t[1]*t[2]*t[3]},\n               ${t[2]*t[3]}, ${t[3]}, 1));\n        int texC = depth3;\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                  vec2(${f}.0, ${h}.0);\n        return sampleTexture(${a}, uv);\n      }\n    `;const x=Ya(a);return`\n    float ${r}(int row, int col, int depth, int depth2, int depth3) {\n      // Explicitly use integer operations as dot() only works on floats.\n      int index = row * ${l} + col * ${i} + depth * ${s} +\n          depth2 * ${o} + depth3 + ${x};\n      vec2 uv = uvFromFlat(${h}, ${f}, index);\n      return sampleTexture(${a}, uv);\n    }\n  `}(e);case 6:return function(e){const t=e.shapeInfo.logicalShape,a=e.name,r="get"+a.charAt(0).toUpperCase()+a.slice(1),{newShape:o,keptDims:s}=n.squeezeShape(t);if(o.length<t.length){const t=["row","col","depth","depth2","depth3","depth4"];return`\n      ${Ga(er(e,o))}\n      float ${r}(int row, int col, int depth,\n                    int depth2, int depth3, int depth4) {\n        return ${r}(${tr(t,s)});\n      }\n    `}const i=t[5],l=t[4]*i,u=t[3]*l,c=t[2]*u,d=t[1]*c;if(e.shapeInfo.isUniform)return`\n      float ${r}(int row, int col, int depth,\n                  int depth2, int depth3, int depth4) {\n        int index = round(dot(\n          vec4(row, col, depth, depth2),\n          vec4(${d}, ${c}, ${u}, ${l})) +\n          dot(\n            vec2(depth3, depth4),\n            vec2(${i}, 1)));\n        ${Qa(e)}\n      }\n    `;const p=e.shapeInfo.flatOffset,h=e.shapeInfo.texShape,f=h[0],x=h[1];if(x===d&&null==p)return`\n      float ${r}(int row, int col, int depth,\n                    int depth2, int depth3, int depth4) {\n        int texR = row;\n        float texC = dot(vec4(col, depth, depth2, depth3),\n          vec4(${c}, ${u}, ${l}, ${i})) +\n               float(depth4);\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                   vec2(${x}.0, ${f}.0);\n        return sampleTexture(${a}, uv);\n      }\n    `;if(x===i&&null==p)return`\n      float ${r}(int row, int col, int depth,\n                    int depth2, int depth3, int depth4) {\n        float texR = dot(vec4(row, col, depth, depth2),\n          vec4(${t[1]*t[2]*t[3]*t[4]},\n               ${t[2]*t[3]*t[4]},\n               ${t[3]*t[4]},\n               ${t[4]})) + float(depth3);\n        int texC = depth4;\n        vec2 uv = (vec2(texC, texR) + halfCR) /\n                  vec2(${x}.0, ${f}.0);\n        return sampleTexture(${a}, uv);\n      }\n    `;const m=Ya(a);return`\n    float ${r}(int row, int col, int depth,\n                  int depth2, int depth3, int depth4) {\n      // Explicitly use integer operations as dot() only works on floats.\n      int index = row * ${d} + col * ${c} + depth * ${u} +\n          depth2 * ${l} + depth3 * ${i} + depth4 + ${m};\n      vec2 uv = uvFromFlat(${f}, ${x}, index);\n      return sampleTexture(${a}, uv);\n    }\n  `}(e);default:throw new Error(`${a.length}-D input sampling is not yet supported`)}}function za(e,t){switch(e.shapeInfo.logicalShape.length){case 0:return function(e){const t=e.name,n="get"+t.charAt(0).toUpperCase()+t.slice(1),a=Da();return`\n    vec4 ${n}() {\n      return ${a.texture2D}(${t}, halfCR);\n    }\n  `}(e);case 1:return function(e,t){const n=e.name,a="get"+n.charAt(0).toUpperCase()+n.slice(1),r=e.shapeInfo.texShape,o=Da();if(t)return`\n    vec4 ${a}(int index) {\n      ivec2 packedTexShape = ivec2(ceil(float(${n}TexShape[0]) / 2.0), ceil(float(${n}TexShape[1]) / 2.0));\n      vec2 uv = packedUVfrom1D(\n        packedTexShape[0], packedTexShape[1], index);\n      return ${o.texture2D}(${n}, uv);\n    }\n  `;const s=[Math.ceil(r[0]/2),Math.ceil(r[1]/2)];return`\n    vec4 ${a}(int index) {\n      vec2 uv = packedUVfrom1D(\n        ${s[0]}, ${s[1]}, index);\n      return ${o.texture2D}(${n}, uv);\n    }\n  `}(e,t);case 2:return function(e,t){const a=e.shapeInfo.logicalShape,r=e.name,o="get"+r.charAt(0).toUpperCase()+r.slice(1),s=e.shapeInfo.texShape,i=s[0],l=s[1],u=Da();if(null!=s&&n.arraysEqual(a,s))return t?`\n      vec4 ${o}(int row, int col) {\n        vec2 uv = (vec2(col, row) + halfCR) / vec2(${r}TexShape[1], ${r}TexShape[0]);\n\n        return ${u.texture2D}(${r}, uv);\n      }\n    `:`\n      vec4 ${o}(int row, int col) {\n        vec2 uv = (vec2(col, row) + halfCR) / vec2(${l}.0, ${i}.0);\n\n        return ${u.texture2D}(${r}, uv);\n      }\n    `;if(t)return`\n    vec4 ${o}(int row, int col) {\n      ivec2 packedTexShape = ivec2(ceil(float(${r}TexShape[0]) / 2.0), ceil(float(${r}TexShape[1]) / 2.0));\n      int valuesPerRow = int(ceil(float(${r}Shape[1]) / 2.0));\n      vec2 uv = packedUVfrom2D(valuesPerRow, packedTexShape[0], packedTexShape[1], row, col);\n      return ${u.texture2D}(${r}, uv);\n    }\n  `;const c=[Math.ceil(s[0]/2),Math.ceil(s[1]/2)],d=Math.ceil(a[1]/2);return`\n    vec4 ${o}(int row, int col) {\n      vec2 uv = packedUVfrom2D(${d}, ${c[0]}, ${c[1]}, row, col);\n      return ${u.texture2D}(${r}, uv);\n    }\n  `}(e,t);case 3:return function(e,t){const n=e.shapeInfo.logicalShape,a=e.name,r="get"+a.charAt(0).toUpperCase()+a.slice(1),o=e.shapeInfo.texShape,s=[Math.ceil(o[0]/2),Math.ceil(o[1]/2)];if(1===n[0]){const a=[1,2],o=["b","row","col"];return`\n        ${za(er(e,n.slice(1)),t)}\n        vec4 ${r}(int b, int row, int col) {\n          return ${r}(${tr(o,a)});\n        }\n      `}const i=Da();if(t)return`\n    vec4 ${r}(int b, int row, int col) {\n      ivec2 packedTexShape = ivec2(ceil(float(${a}TexShape[0]) / 2.0), ceil(float(${a}TexShape[1]) / 2.0));\n      int valuesPerRow = int(ceil(float(${a}Shape[2]) / 2.0));\n      int texelsInBatch = valuesPerRow * int(ceil(float(${a}Shape[1]) / 2.0));\n      vec2 uv = packedUVfrom3D(\n        packedTexShape[0], packedTexShape[1], texelsInBatch, valuesPerRow, b, row, col);\n      return ${i.texture2D}(${a}, uv);\n    }\n  `;const l=s[0],u=s[1],c=Math.ceil(n[2]/2),d=c*Math.ceil(n[1]/2);return`\n    vec4 ${r}(int b, int row, int col) {\n      vec2 uv = packedUVfrom3D(\n        ${l}, ${u}, ${d}, ${c}, b, row, col);\n      return ${i.texture2D}(${a}, uv);\n    }\n  `}(e,t);default:return function(e,t){const n=e.name,a="get"+n.charAt(0).toUpperCase()+n.slice(1),r=Da();if(t)return`\n    vec4 ${a}(int b2, int b, int row, int col) {\n      int valuesPerRow = int(ceil(float(${n}Shape[3]) / 2.0));\n      int texelsInBatch = valuesPerRow * int(ceil(float(${n}Shape[2]) / 2.0));\n      int index = b * texelsInBatch + (row / 2) * valuesPerRow + (col / 2);\n      texelsInBatch *= ${n}Shape[1];\n      index = b2 * texelsInBatch + index;\n      ivec2 packedTexShape = ivec2(ceil(float(${n}TexShape[0]) / 2.0), ceil(float(${n}TexShape[1]) / 2.0));\n      int texR = index / packedTexShape[1];\n      int texC = index - texR * packedTexShape[1];\n      vec2 uv = (vec2(texC, texR) + halfCR) / vec2(packedTexShape[1], packedTexShape[0]); return ${r.texture2D}(${n}, uv);\n    }\n  `;const o=e.shapeInfo.logicalShape,s=o.length,i=e.shapeInfo.texShape,l=[Math.ceil(i[0]/2),Math.ceil(i[1]/2)],u=l[0],c=l[1],d=Math.ceil(o[s-1]/2);let p=d*Math.ceil(o[s-2]/2),h="int b, int row, int col",f=`b * ${p} + (row / 2) * ${d} + (col / 2)`;for(let e=2;e<s-1;e++)h=`int b${e}, `+h,p*=o[s-e-1],f=`b${e} * ${p} + `+f;return`\n    vec4 ${a}(${h}) {\n      int index = ${f};\n      int texR = index / ${c};\n      int texC = index - texR * ${c};\n      vec2 uv = (vec2(texC, texR) + halfCR) / vec2(${c}, ${u});\n      return ${r.texture2D}(${n}, uv);\n    }\n  `}(e,t)}}const Xa="\nvec2 uvFromFlat(int texNumR, int texNumC, int index) {\n  int texR = index / texNumC;\n  int texC = index - texR * texNumC;\n  return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n}\nvec2 packedUVfrom1D(int texNumR, int texNumC, int index) {\n  int texelIndex = index / 2;\n  int texR = texelIndex / texNumC;\n  int texC = texelIndex - texR * texNumC;\n  return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n}\n",Ha="\nvec2 packedUVfrom2D(int texelsInLogicalRow, int texNumR,\n  int texNumC, int row, int col) {\n  int texelIndex = (row / 2) * texelsInLogicalRow + (col / 2);\n  int texR = texelIndex / texNumC;\n  int texC = texelIndex - texR * texNumC;\n  return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n}\n",ja="\nvec2 packedUVfrom3D(int texNumR, int texNumC,\n    int texelsInBatch, int texelsInLogicalRow, int b,\n    int row, int col) {\n  int index = b * texelsInBatch + (row / 2) * texelsInLogicalRow + (col / 2);\n  int texR = index / texNumC;\n  int texC = index - texR * texNumC;\n  return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n}\n",Ka="\n  float getChannel(vec4 frag, vec2 innerDims) {\n    vec2 modCoord = mod(innerDims, 2.);\n    return modCoord.x == 0. ?\n      (modCoord.y == 0. ? frag.r : frag.g) :\n      (modCoord.y == 0. ? frag.b : frag.a);\n  }\n  float getChannel(vec4 frag, int dim) {\n    float modCoord = mod(float(dim), 2.);\n    return modCoord == 0. ? frag.r : frag.g;\n  }\n";function qa(){return"\n    int getOutputCoords() {\n      return 0;\n    }\n  "}function Ya(e){return`offset${e}`}function Qa(e){const t=e.name,a=n.sizeFromShape(e.shapeInfo.logicalShape);return a<2?`return ${t};`:`\n    for (int i = 0; i < ${a}; i++) {\n      if (i == index) {\n        return ${t}[i];\n      }\n    }\n  `}function Za(e){if(e<=1)return"int";if(2===e)return"ivec2";if(3===e)return"ivec3";if(4===e)return"ivec4";if(5===e)return"ivec5";if(6===e)return"ivec6";throw Error(`GPU for rank ${e} is not yet supported`)}function Ja(e,t,a){const{newShape:r,keptDims:o}=n.squeezeShape(t),s=t.length,i=e&&3===s&&1===t[0],l=i?t.slice(1):r,u=!e&&s>1&&!n.arraysEqual(t,a)&&r.length<s||i;return{useSqueezeShape:u,uniformShape:u?l:t,keptDims:o}}function er(e,t){const n=JSON.parse(JSON.stringify(e));return n.shapeInfo.logicalShape=t,n}function tr(e,t){return t.map((t=>e[t])).join(", ")}function nr(e,n,a){const r=[],o=[];let s,i,l,u=null,c=null;c=e.getUniformLocation(a,"NAN",!1),1===t().getNumber("WEBGL_VERSION")&&(u=e.getUniformLocation(a,"INFINITY",!1));const d=!1;for(const t of n.variableNames){const o={name:t,uniform:e.getUniformLocation(a,t,d),offset:e.getUniformLocation(a,`offset${t}`,d)};n.enableShapeUniforms&&(o.shape=e.getUniformLocation(a,`${t}Shape`,d),o.texShape=e.getUniformLocation(a,`${t}TexShape`,d)),r.push(o)}if(n.enableShapeUniforms&&(s=e.getUniformLocation(a,"outShape",d),l=e.getUniformLocation(a,"outShapeStrides",d),i=e.getUniformLocation(a,"outTexShape",d)),n.customUniforms)for(const t of n.customUniforms)o.push(e.getUniformLocation(a,t.name,d));return{variablesLocations:r,customUniformLocations:o,infLoc:u,nanLoc:c,outShapeLocation:s,outShapeStridesLocation:l,outTexShapeLocation:i}}function ar(e,t){if(e.length!==t.length)throw Error(`Binary was compiled with ${e.length} inputs, but was executed with ${t.length} inputs`);e.forEach(((e,a)=>{const r=e.logicalShape,o=t[a],s=o.shape;if(!n.arraysEqual(r,s))throw Error(`Binary was compiled with different shapes than the current args. Shapes ${r} and ${s} must match`);if(e.isUniform&&o.isUniform)return;const i=e.texShape,l=o.isUniform?null:o.texData.texShape;if(!n.arraysEqual(i,l))throw Error(`Binary was compiled with different texture shapes than the current args. Shape ${i} and ${l} must match`)}))}function rr(e){return t().getBool("WEBGL_USE_SHAPES_UNIFORMS")&&e<=4}class or{constructor(e){this.variableNames=["A"],this.packedInputs=!1,this.packedOutput=!0,this.outPackingScheme=Dn.DENSE,this.customUniforms=[{name:"texShape",type:"ivec2"}];const t=Da();this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length),this.userCode=`\n      ivec3 outCoordsFromFlatIndex(int index) {\n        ${this.enableShapeUniforms?La(["r","c","d"],e):Pa(["r","c","d"],e)}\n        return ivec3(r, c, d);\n      }\n\n      void main() {\n        ivec2 resTexRC = ivec2(resultUV.yx * vec2(texShape[0], texShape[1]));\n        int index = 4 * (resTexRC.x * texShape[1] + resTexRC.y);\n\n        vec4 result = vec4(0.);\n\n        for (int i=0; i<4; i++) {\n          int flatIndex = index + i;\n          ivec3 rc = outCoordsFromFlatIndex(flatIndex);\n          result[i] = getA(rc.x, rc.y, rc.z);\n        }\n\n        ${t.output} = result;\n      }\n    `}}class sr{constructor(e){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.outPackingScheme=Dn.DENSE,this.customUniforms=[{name:"texShape",type:"ivec2"}];const t=Da();this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length),this.userCode=`\n      ivec3 outCoordsFromFlatIndex(int index) {\n        ${this.enableShapeUniforms?La(["r","c","d"],e):Pa(["r","c","d"],e)}\n        return ivec3(r, c, d);\n      }\n\n      void main() {\n        ivec2 resTexRC = ivec2(resultUV.yx * vec2(texShape[0], texShape[1]));\n        int index = 4 * (resTexRC.x * texShape[1] + resTexRC.y);\n\n        vec4 result = vec4(0.);\n\n        for (int i=0; i<4; i++) {\n          int flatIndex = index + i;\n          ivec3 rc = outCoordsFromFlatIndex(flatIndex);\n          result[i] = getChannel(getA(rc.x, rc.y, rc.z), vec2(rc.y, rc.z));\n        }\n\n        ${t.output} = result;\n      }\n    `}}class ir{constructor(e){this.variableNames=["A"],this.outTexUsage=Pn.DOWNLOAD;const t=Da();this.outputShape=e,this.userCode=`\n      ${Wa}\n\n      void main() {\n        float x = getAAtOutCoords();\n        ${t.output} = encode_float(x);\n      }\n    `}}class lr{constructor(e){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!1,this.outTexUsage=Pn.DOWNLOAD;const t=Da();this.outputShape=e,this.userCode=`\n      ${Wa}\n\n      void main() {\n        ivec3 coords = getOutputCoords();\n        float x = getChannel(getAAtOutCoords(), vec2(coords.y, coords.z));\n        ${t.output} = encode_float(x);\n      }\n    `}}const ur={R:0,G:1,B:2,A:3};class cr{constructor(e,t=!1,n="RGBA"){this.variableNames=["A"],this.customUniforms=[{name:"texShape",type:"ivec2"}];const a=Da();this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length);let r="result";t&&(r="floor(result * 255. + 0.5)");let o="";for(let e=0;e<n.length;e++){const t=n[e];o+=`\n          if(offset == ${e}) {\n            result = values[${ur[t]}];\n          }`}this.userCode=`\n      ${this.enableShapeUniforms?"\n  int getFlatIndex(ivec3 coords) {\n    return coords.x * outShapeStrides[0] + coords.y * outShapeStrides[1] + coords.z;\n  }\n":Va(e)}\n\n      void main() {\n        ivec3 coords = getOutputCoords();\n        int flatIndex = getFlatIndex(coords);\n        float result = 0.;\n        int offset = imod(flatIndex, ${n.length});\n\n        flatIndex = idiv(flatIndex, ${n.length}, 1.);\n\n        int r = flatIndex / texShape[1];\n        if (r < texShape[0]) {\n          int c = imod(flatIndex, texShape[1]);\n          vec2 uv = (vec2(c, r) + halfCR) / vec2(texShape[1], texShape[0]);\n          vec4 values = ${a.texture2D}(A, uv);\n          ${o}\n        }\n        ${a.output} = vec4(${r}, 0., 0., 0.);\n      }\n    `}}class dr{constructor(e,t=!1){this.variableNames=["A"],this.packedInputs=!1,this.packedOutput=!0,this.customUniforms=[{name:"texShape",type:"ivec2"}];const n=Da();this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length);let a="",r="result";t&&(r="floor(result * 255. + 0.5)");for(let t=0;t<=1;t++)for(let r=0;r<=1;r++){const o=2*t+r;a+=`\n          localCoords = coords;\n          if(localCoords[2] + ${r} < ${this.enableShapeUniforms?"outShape[2]":`${e[2]}`}) {\n          localCoords[2] += ${r};\n          if (localCoords[1] + ${t} < ${this.enableShapeUniforms?"outShape[1]":`${e[1]}`}) {\n            localCoords[1] += ${t};\n\n            flatIndex = getFlatIndex(localCoords);\n            offset = imod(flatIndex, 4);\n\n            flatIndex = idiv(flatIndex, 4, 1.);\n\n            int r = flatIndex / texShape[1];\n            int c = imod(flatIndex, texShape[1]);\n            vec2 uv = (vec2(c, r) + halfCR) / vec2(texShape[1], texShape[0]);\n            values = ${n.texture2D}(A, uv);\n\n            if (offset == 0) {\n              result[${o}] = values[0];\n            } else if (offset == 1) {\n              result[${o}] = values[1];\n            } else if (offset == 2) {\n              result[${o}] = values[2];\n            } else {\n              result[${o}] = values[3];\n            }\n          }\n        }\n        `}this.userCode=`\n        ${this.enableShapeUniforms?"\n  int getFlatIndex(ivec3 coords) {\n    return coords.x * outShapeStrides[0] + coords.y * outShapeStrides[1] + coords.z;\n  }\n":Va(e)}\n\n        void main() {\n          ivec3 coords = getOutputCoords();\n\n          vec4 result = vec4(0.);\n          int flatIndex, r, c, offset;\n          ivec3 localCoords;\n          vec2 uv;\n          vec4 values;\n\n          ${a}\n\n          ${n.output} = ${r};\n        }\n    `}}function pr(e){const t=Da();return Hn(e,`${t.version}\n    precision highp float;\n    ${t.attribute} vec3 clipSpacePos;\n    ${t.attribute} vec2 uv;\n    ${t.varyingVs} vec2 resultUV;\n\n    void main() {\n      gl_Position = vec4(clipSpacePos, 1);\n      resultUV = uv;\n    }`)}function hr(e){return Jn(e,new Float32Array([-1,1,0,0,1,-1,-1,0,0,0,1,1,0,1,1,1,-1,0,1,0]))}function fr(e){return ea(e,new Uint16Array([0,1,2,2,1,3]))}function xr(e,n,a,r,o,s){na(n,a);const i=ta(e),l=e.TEXTURE_2D;return Mn(e,(()=>e.bindTexture(l,i))),Mn(e,(()=>e.texParameteri(l,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE))),Mn(e,(()=>e.texParameteri(l,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE))),Mn(e,(()=>e.texParameteri(l,e.TEXTURE_MIN_FILTER,e.NEAREST))),Mn(e,(()=>e.texParameteri(l,e.TEXTURE_MAG_FILTER,e.NEAREST))),1===t().getNumber("WEBGL_VERSION")?Mn(e,(()=>e.texImage2D(l,0,r,n,a,0,o,s,null))):Mn(e,(()=>e.texStorage2D(l,1,r,n,a))),Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,null))),{texture:i,texShape:[a,n]}}function mr(e){return e.internalFormatFloat}function gr(e,t,n,a){const[r,o]=Bn(t,n);return xr(e,r,o,mr(a),a.textureFormatFloat,e.FLOAT)}function br(e){return e.internalFormatHalfFloat}function vr(e,t,n,a){const[r,o]=Bn(t,n);return xr(e,r,o,br(a),a.textureFormatFloat,a.textureTypeHalfFloat)}function Cr(e){return e.downloadTextureFormat}function $r(e,t,n,a){const[r,o]=Bn(t,n);return xr(e,r,o,Cr(a),e.RGBA,e.UNSIGNED_BYTE)}function yr(e){return e.internalFormatPackedFloat}function Ir(e,t,n,a){const[r,o]=Wn(t,n);return xr(e,r,o,yr(a),e.RGBA,e.FLOAT)}function wr(e){return e.internalFormatPackedHalfFloat}function Sr(e,t,n,a){const[r,o]=Wn(t,n);return xr(e,r,o,wr(a),e.RGBA,a.textureTypeHalfFloat)}function Rr(e,t,n){Mn(e,(()=>e.bindBuffer(e.ARRAY_BUFFER,n)));return ra(e,t,"clipSpacePos",n,3,20,0)&&ra(e,t,"uv",n,2,20,12)}function Tr(e,n,a,r,o,s){let i,l,u;Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,n))),o instanceof Uint8Array?(i=new Uint8Array(a*r*4),l=e.UNSIGNED_BYTE,u=e.RGBA):(i=new Float32Array(a*r*4),l=e.FLOAT,u=s.internalFormatPackedFloat),i.set(o),2===t().getNumber("WEBGL_VERSION")?Mn(e,(()=>e.texSubImage2D(e.TEXTURE_2D,0,0,0,a,r,e.RGBA,l,i))):Mn(e,(()=>e.texImage2D(e.TEXTURE_2D,0,u,a,r,0,e.RGBA,l,i))),Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,null)))}function kr(e,n,a){Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,n))),a.data instanceof Uint8Array?2===t().getNumber("WEBGL_VERSION")?Mn(e,(()=>e.texSubImage2D(e.TEXTURE_2D,0,0,0,a.width,a.height,e.RGBA,e.UNSIGNED_BYTE,a.data))):Mn(e,(()=>e.texImage2D(e.TEXTURE_2D,0,e.RGBA,a.width,a.height,0,e.RGBA,e.UNSIGNED_BYTE,a.data))):2===t().getNumber("WEBGL_VERSION")?Mn(e,(()=>e.texSubImage2D(e.TEXTURE_2D,0,0,0,e.RGBA,e.UNSIGNED_BYTE,a))):Mn(e,(()=>e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,a))),Mn(e,(()=>e.bindTexture(e.TEXTURE_2D,null)))}function Nr(e,t,n,a){const r=e.createBuffer();Mn(e,(()=>e.bindBuffer(e.PIXEL_PACK_BUFFER,r)));const o=16*t*n;return Mn(e,(()=>e.bufferData(e.PIXEL_PACK_BUFFER,o,e.STREAM_READ))),Mn(e,(()=>e.readPixels(0,0,n,t,e.RGBA,e.FLOAT,0))),Mn(e,(()=>e.bindBuffer(e.PIXEL_PACK_BUFFER,null))),r}function Er(e,t,n){const a=e,r=new Float32Array(n);return a.bindBuffer(a.PIXEL_PACK_BUFFER,t),a.getBufferSubData(a.PIXEL_PACK_BUFFER,0,r),a.bindBuffer(a.PIXEL_PACK_BUFFER,null),r}function Ar(e,t,n,a){const[r,o]=Bn(t,n),s=new Uint8Array(t*n*4);return Mn(e,(()=>e.readPixels(0,0,r,o,a.downloadTextureFormat,e.UNSIGNED_BYTE,s))),new Float32Array(s.buffer)}function Or(e,t,n,a,r,o,s,i){const l=e,u=new Float32Array(function(e,t){const[n,a]=Wn(e,t);return n*a*4}(o,s));return l.bindBuffer(l.PIXEL_PACK_BUFFER,t),l.getBufferSubData(l.PIXEL_PACK_BUFFER,0,u),l.bindBuffer(l.PIXEL_PACK_BUFFER,null),u}function Fr(e,t,n){const a=new Float32Array(t*n*4);return Mn(e,(()=>e.readPixels(0,0,n,t,e.RGBA,e.FLOAT,a))),a}var _r={__proto__:null,bindVertexProgramAttributeStreams:Rr,createBufferFromOutputTexture:Nr,createFloat16MatrixTexture:vr,createFloat16PackedMatrixTexture:Sr,createFloat32MatrixTexture:gr,createIndexBuffer:fr,createPackedMatrixTexture:Ir,createUnsignedBytesMatrixTexture:$r,createVertexBuffer:hr,createVertexShader:pr,downloadByteEncodedFloatMatrixFromOutputTexture:Ar,downloadFloat32MatrixFromBuffer:Er,downloadMatrixFromPackedOutputTexture:Fr,downloadPackedMatrixFromBuffer:Or,getInternalFormatForFloat16MatrixTexture:br,getInternalFormatForFloat16PackedMatrixTexture:wr,getInternalFormatForFloat32MatrixTexture:mr,getInternalFormatForPackedMatrixTexture:yr,getInternalFormatForUnsignedBytesMatrixTexture:Cr,uploadDenseMatrixToTexture:Tr,uploadPixelDataToTexture:kr};class Dr{constructor(e){this.outputTexture=null,this.program=null,this.disposed=!1,this.itemsToPoll=[];const n=t().getNumber("WEBGL_VERSION");if(null!=e?(this.gl=e,Fn(n,e)):this.gl=_n(n),e=this.gl,2===t().getNumber("WEBGL_VERSION")){const t=e;this.createVertexArray=()=>Mn(t,(()=>t.createVertexArray())),this.bindVertexArray=e=>Mn(t,(()=>t.bindVertexArray(e))),this.deleteVertexArray=e=>Mn(t,(()=>t.deleteVertexArray(e))),this.getVertexArray=()=>Mn(t,(()=>t.getParameter(t.VERTEX_ARRAY_BINDING)))}else if(null!=e){const t=e.getExtension("OES_vertex_array_object");if(null==t)throw new Error("All WebGL1 implementations are expected to offer OES_vertex_array_object.");this.createVertexArray=()=>Mn(e,(()=>t.createVertexArrayOES())),this.bindVertexArray=n=>Mn(e,(()=>t.bindVertexArrayOES(n))),this.deleteVertexArray=n=>Mn(e,(()=>t.deleteVertexArrayOES(n))),this.getVertexArray=()=>Mn(e,(()=>e.getParameter(t.VERTEX_ARRAY_BINDING_OES)))}let a="WEBGL_color_buffer_float";const r="EXT_color_buffer_half_float";if(this.parallelCompilationExtension=this.gl.getExtension("KHR_parallel_shader_compile"),1===t().getNumber("WEBGL_VERSION")){const e="OES_texture_float",n="OES_texture_half_float";if(this.textureFloatExtension=Xn(this.gl,e),Ra(this.gl,n))this.textureHalfFloatExtension=Xn(this.gl,n);else if(t().get("WEBGL_FORCE_F16_TEXTURES"))throw new Error("GL context does not support half float textures, yet the environment flag WEBGL_FORCE_F16_TEXTURES is set to true.");if(this.colorBufferFloatExtension=this.gl.getExtension(a),Ra(this.gl,r))this.colorBufferHalfFloatExtension=Xn(this.gl,r);else if(t().get("WEBGL_FORCE_F16_TEXTURES"))throw new Error("GL context does not support color renderable half floats, yet the environment flag WEBGL_FORCE_F16_TEXTURES is set to true.")}else if(a="EXT_color_buffer_float",Ra(this.gl,a))this.colorBufferFloatExtension=this.gl.getExtension(a);else{if(!Ra(this.gl,r))throw new Error("GL context does not support color renderable floats");this.colorBufferHalfFloatExtension=this.gl.getExtension(r)}this.vertexBuffer=hr(this.gl),this.indexBuffer=fr(this.gl),this.framebuffer=aa(this.gl),this.textureConfig=Un(this.gl,this.textureHalfFloatExtension)}get debug(){return t().getBool("DEBUG")}dispose(){if(this.disposed)return;null!=this.program&&console.warn("Disposing a GPGPUContext that still has a bound WebGLProgram. This is probably a resource leak, delete the program with GPGPUContext.deleteProgram before disposing."),null!=this.outputTexture&&console.warn("Disposing a GPGPUContext that still has a bound output matrix texture.  This is probably a resource leak, delete the output matrix texture with GPGPUContext.deleteMatrixTexture before disposing.");const e=this.gl;Mn(e,(()=>e.finish())),Mn(e,(()=>e.bindFramebuffer(e.FRAMEBUFFER,null))),Mn(e,(()=>e.deleteFramebuffer(this.framebuffer))),Mn(e,(()=>e.bindBuffer(e.ARRAY_BUFFER,null))),Mn(e,(()=>e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,null))),Mn(e,(()=>e.deleteBuffer(this.indexBuffer))),this.disposed=!0}createFloat32MatrixTexture(e,t){return this.throwIfDisposed(),gr(this.gl,e,t,this.textureConfig)}createFloat16MatrixTexture(e,t){return this.throwIfDisposed(),vr(this.gl,e,t,this.textureConfig)}createUnsignedBytesMatrixTexture(e,t){return this.throwIfDisposed(),$r(this.gl,e,t,this.textureConfig)}uploadPixelDataToTexture(e,t){this.throwIfDisposed(),kr(this.gl,e,t)}uploadDenseMatrixToTexture(e,t,n,a){this.throwIfDisposed(),Tr(this.gl,e,t,n,a,this.textureConfig)}createFloat16PackedMatrixTexture(e,t){return this.throwIfDisposed(),Sr(this.gl,e,t,this.textureConfig)}createPackedMatrixTexture(e,t){return this.throwIfDisposed(),Ir(this.gl,e,t,this.textureConfig)}deleteMatrixTexture(e){this.throwIfDisposed(),this.outputTexture===e&&(ca(this.gl,this.framebuffer),this.outputTexture=null),Mn(this.gl,(()=>this.gl.deleteTexture(e)))}downloadByteEncodedFloatMatrixFromOutputTexture(e,t,n){return this.downloadMatrixDriver(e,(()=>Ar(this.gl,t,n,this.textureConfig)))}downloadPackedMatrixFromBuffer(e,t,n,a,r,o){return Or(this.gl,e,0,0,0,r,o,this.textureConfig)}downloadFloat32MatrixFromBuffer(e,t){return Er(this.gl,e,t)}createBufferFromTexture(e,t,n){this.bindTextureToFrameBuffer(e);const a=Nr(this.gl,t,n,this.textureConfig);return this.unbindTextureToFrameBuffer(),a}createAndWaitForFence(){const e=this.createFence(this.gl);return this.pollFence(e)}createFence(e){let n,a;if(t().getBool("WEBGL_FENCE_API_ENABLED")){const t=e,r=t.fenceSync(t.SYNC_GPU_COMMANDS_COMPLETE,0);e.flush(),a=()=>{const e=t.clientWaitSync(r,0,0);return e===t.ALREADY_SIGNALED||e===t.CONDITION_SATISFIED},n=r}else t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION")>0?(n=this.beginQuery(),this.endQuery(),a=()=>this.isQueryAvailable(n,t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION"))):a=()=>!0;return{query:n,isFencePassed:a}}downloadMatrixFromPackedTexture(e,t,n){return this.downloadMatrixDriver(e,(()=>Fr(this.gl,t,n)))}createProgram(e){this.throwIfDisposed();const t=this.gl;null==this.vertexShader&&(this.vertexShader=pr(t));const n=Yn(t);Mn(t,(()=>t.attachShader(n,this.vertexShader))),Mn(t,(()=>t.attachShader(n,e))),Qn(t,n);const a=Object.assign(n,{vao:this.createVertexArray()});return this.debug&&Zn(t,a),a}buildVao(e){this.setProgram(e),this.bindVertexArray(e.vao);const t=this.gl;Mn(t,(()=>t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,this.indexBuffer))),Rr(t,e,this.vertexBuffer)}deleteProgram(e){this.throwIfDisposed(),e===this.program&&(this.program=null),null!=e&&(Mn(this.gl,(()=>this.gl.deleteProgram(e))),this.deleteVertexArray(e.vao))}setProgram(e){this.throwIfDisposed(),this.program=e,null!=this.program&&this.debug&&Zn(this.gl,this.program),Mn(this.gl,(()=>this.gl.useProgram(e)))}getUniformLocation(e,t,n=!0){return this.throwIfDisposed(),n?sa(this.gl,e,t):ia(this.gl,e,t)}getAttributeLocation(e,t){return this.throwIfDisposed(),Mn(this.gl,(()=>this.gl.getAttribLocation(e,t)))}getUniformLocationNoThrow(e,t){return this.throwIfDisposed(),this.gl.getUniformLocation(e,t)}setInputMatrixTexture(e,t,n){this.throwIfDisposed(),this.throwIfNoProgram(),la(this.gl,e,t,n)}setOutputMatrixTexture(e,t,n){this.setOutputMatrixTextureDriver(e,n,t)}setOutputPackedMatrixTexture(e,t,n){this.throwIfDisposed();const[a,r]=Wn(t,n);this.setOutputMatrixTextureDriver(e,a,r)}setOutputMatrixWriteRegion(e,t,n,a){this.setOutputMatrixWriteRegionDriver(n,e,a,t)}setOutputPackedMatrixWriteRegion(e,t,n,a){throw new Error("setOutputPackedMatrixWriteRegion not implemented.")}debugValidate(){null!=this.program&&Zn(this.gl,this.program),da(this.gl)}executeProgram(){this.throwIfDisposed(),this.throwIfNoProgram();const e=this.gl;if(this.debug){const e=this.getVertexArray();console.assert(e===this.program.vao,"VAO changed between setProgram and executeProgram!"),this.debugValidate()}Mn(e,(()=>e.drawElements(e.TRIANGLES,6,e.UNSIGNED_SHORT,0)))}blockUntilAllProgramsCompleted(){this.throwIfDisposed(),Mn(this.gl,(()=>this.gl.finish()))}getQueryTimerExtension(){return null==this.disjointQueryTimerExtension&&(this.disjointQueryTimerExtension=Xn(this.gl,2===t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION")?"EXT_disjoint_timer_query_webgl2":"EXT_disjoint_timer_query")),this.disjointQueryTimerExtension}getQueryTimerExtensionWebGL2(){return this.getQueryTimerExtension()}getQueryTimerExtensionWebGL1(){return this.getQueryTimerExtension()}beginQuery(){if(2===t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION")){const e=this.gl,t=this.getQueryTimerExtensionWebGL2(),n=e.createQuery();return e.beginQuery(t.TIME_ELAPSED_EXT,n),n}const e=this.getQueryTimerExtensionWebGL1(),n=e.createQueryEXT();return e.beginQueryEXT(e.TIME_ELAPSED_EXT,n),n}endQuery(){if(2===t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION")){const e=this.gl,t=this.getQueryTimerExtensionWebGL2();return void e.endQuery(t.TIME_ELAPSED_EXT)}const e=this.getQueryTimerExtensionWebGL1();e.endQueryEXT(e.TIME_ELAPSED_EXT)}async waitForQueryAndGetTime(e){return await n.repeatedTry((()=>this.disposed||this.isQueryAvailable(e,t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION")))),this.getQueryTime(e,t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_VERSION"))}getQueryTime(e,t){if(0===t)return null;if(2===t){const t=this.gl;return t.getQueryParameter(e,t.QUERY_RESULT)/1e6}{const t=this.getQueryTimerExtensionWebGL1();return t.getQueryObjectEXT(e,t.QUERY_RESULT_EXT)/1e6}}isQueryAvailable(e,t){if(0===t)return!0;if(2===t){const t=this.gl,n=this.getQueryTimerExtensionWebGL2(),a=t.getQueryParameter(e,t.QUERY_RESULT_AVAILABLE);return null==this.disjoint&&(this.disjoint=this.gl.getParameter(n.GPU_DISJOINT_EXT)),a&&!this.disjoint}{const t=this.getQueryTimerExtensionWebGL1(),n=t.getQueryObjectEXT(e,t.QUERY_RESULT_AVAILABLE_EXT);return null==this.disjoint&&(this.disjoint=this.gl.getParameter(t.GPU_DISJOINT_EXT)),n&&!this.disjoint}}pollFence(e){return new Promise((t=>{this.addItemToPoll((()=>e.isFencePassed()),(()=>t()))}))}pollItems(){const e=function(e){let t=0;for(;t<e.length;++t){if(!e[t]())break}return t-1}(this.itemsToPoll.map((e=>e.isDoneFn)));for(let t=0;t<=e;++t){const{resolveFn:e}=this.itemsToPoll[t];e()}this.itemsToPoll=this.itemsToPoll.slice(e+1)}addItemToPoll(e,a){if(this.itemsToPoll.push({isDoneFn:e,resolveFn:a}),this.itemsToPoll.length>1)return;let r;"setTimeoutCustom"in t().platform&&(r=t().platform.setTimeoutCustom.bind(t().platform)),n.repeatedTry((()=>(this.pollItems(),0===this.itemsToPoll.length)),(()=>0),null,r)}bindTextureToFrameBuffer(e){this.throwIfDisposed(),ua(this.gl,e,this.framebuffer),this.debug&&da(this.gl)}unbindTextureToFrameBuffer(){null!=this.outputTexture?(ua(this.gl,this.outputTexture,this.framebuffer),this.debug&&da(this.gl)):ca(this.gl,this.framebuffer)}downloadMatrixDriver(e,t){this.bindTextureToFrameBuffer(e);const n=t();return this.unbindTextureToFrameBuffer(),n}setOutputMatrixTextureDriver(e,t,n){this.throwIfDisposed();const a=this.gl;ua(a,e,this.framebuffer),this.debug&&da(a),this.outputTexture=e,Mn(a,(()=>a.viewport(0,0,t,n))),Mn(a,(()=>a.scissor(0,0,t,n)))}setOutputMatrixWriteRegionDriver(e,t,n,a){this.throwIfDisposed(),Mn(this.gl,(()=>this.gl.scissor(e,t,n,a)))}throwIfDisposed(){if(this.disposed)throw new Error("Attempted to use disposed GPGPUContext.")}throwIfNoProgram(){if(null==this.program)throw new Error("No GPU program is currently set.")}}function Pr(e){return(t,a,o,s,i)=>{const l=r.assertAndGetBroadcastShape(t,a),u=l.length,c=n.computeStrides(l),d=n.sizeFromShape(l),p=n.getTypedArrayFromDType(i,d),h=t.length,f=a.length,x=n.computeStrides(t),m=n.computeStrides(a),g=r.getBroadcastDims(t,l),b=r.getBroadcastDims(a,l);if(g.length+b.length===0)for(let t=0;t<p.length;++t)p[t]=e(o[t%o.length],s[t%s.length]);else for(let t=0;t<p.length;++t){const a=n.indexToLoc(t,u,c),r=a.slice(-h);g.forEach((e=>r[e]=0));const i=n.locToIndex(r,h,x),l=a.slice(-f);b.forEach((e=>l[e]=0));const d=n.locToIndex(l,f,m);p[t]=e(o[i],s[d])}return[p,l]}}const Lr=Pr(((e,t)=>e+t));const Br=Pr(((e,t)=>e&t));function Vr(e){return(t,a,r)=>{const o=n.getArrayFromDType(a,t.length);for(let n=0;n<t.length;++n)o[n]=e(t[n],r);return o}}const Wr=Vr((e=>Math.ceil(e)));const Ur=Pr(((e,t)=>e===t?1:0)),Mr=Vr((e=>Math.exp(e))),Gr=Vr((e=>Math.expm1(e))),zr=Vr((e=>Math.floor(e))),Xr=Pr(((e,t)=>Math.floor(e/t)));const Hr=Pr(((e,t)=>e>t?1:0)),jr=Pr(((e,t)=>e>=t?1:0)),Kr=Pr(((e,t)=>e<t?1:0)),qr=Pr(((e,t)=>e<=t?1:0));const Yr=Vr((e=>Math.log(e)));const Qr=Pr(((e,t)=>Math.max(e,t))),Zr=Pr(((e,t)=>Math.min(e,t))),Jr=Pr(((e,t)=>e*t));const eo=Pr(((e,t)=>e!==t?1:0));function to(e,t,n,a){const r=[];let o=0;const s=t.length-1+n.length,i=new Array(s).fill(null).map((()=>[0]));!function(e,t){for(let n=0;n<e.length;++n){const a=e[n],r=n===e.length-1?t:e[n+1].length;if(0===a.length)throw new Error("Ragged splits may not be empty");if(a[0]<0)throw new Error("Ragged splits must be non-negative");if(a[a.length-1]>r)throw new Error("Ragged splits must not point past values");for(let e=1;e<a.length;++e)if(a[e-1]>a[e])throw new Error("Ragged splits must be sorted in ascending order")}}(n,a);let l=1;for(let e=0;e<t.length-1;++e){l*=t[e];const n=t[e+1];for(let t=1;t<l+1;++t)i[e].push(t*n)}for(let a=0;a<e.length;++a){let s=e[a],l=e[a]+1;for(let e=0;e<n.length;++e){const a=n[e],r=e+t.length-1;if(r>=0){const e=i[r],t=e[e.length-1]-a[s];for(let e=s;e<l;++e)i[r].push(a[e+1]+t)}s=a[s],l=a[l]}l!==s&&(r.push([s,l]),o+=l-s)}return{outSplits:i,valueSlices:r,numValues:o}}function no(e,t){const n=e.slice(0,t);for(;n.length<t;)n.push(1);for(let a=t;a<e.length;a++)n[t-1]*=e[a];return n}function ao(e,t,a,r,o){const s=t.slice();s[0]=o;const i=n.getArrayFromDType(a,n.sizeFromShape(s)),l=e.length;return function(e,t,n,a,r,o){const s=no(t,2)[1],i=no(o,2)[1];let l=0;for(const t of n)for(let n=t[0];n<t[1];++n){for(let t=0;t<a;++t)r[l*i+t]=e[n*s+t];++l}}(e,t,r,0===l?0:l/t[0],i,s),[i,s]}var ro=r.RowPartitionType;class oo{constructor(e,t,n,a,o,s,i,l,u,c){this.shape=e,this.shapeShape=t,this.values=n,this.valuesShape=a,this.valuesDType=o,this.defaultValue=s,this.defaultValueShape=i,this.rowPartitionValues=l,this.rowPartitionValuesShapes=u,this.rowPartitionTypes=r.getRowPartitionTypesHelper(c),this.raggedRank=r.getRaggedRank(this.rowPartitionTypes)}getRowPartitionTypeByDimension(e){return this.rowPartitionTypes[0]===ro.FIRST_DIM_SIZE?this.rowPartitionTypes[e+1]:this.rowPartitionTypes[e]}getRowPartitionTensor(e){return this.rowPartitionTypes[0]===ro.FIRST_DIM_SIZE?this.rowPartitionValues[e+1]:this.rowPartitionValues[e]}getMaxWidth(e){const t=this.getRowPartitionTensor(e-1);switch(this.getRowPartitionTypeByDimension(e-1)){case ro.VALUE_ROWIDS:return oo.getMaxWidthValueRowID(t);case ro.ROW_SPLITS:return oo.getMaxWidthRowSplit(t);default:throw new Error(`Cannot handle partition type ${ro[this.getRowPartitionTypeByDimension(e-1)]}`)}}static getMaxWidthRowSplit(e){const t=e.length;if(0===t||1===t)return 0;let n=0;for(let a=0;a<t-1;++a){const t=e[a+1]-e[a];t>n&&(n=t)}return n}static getMaxWidthValueRowID(e){const t=e.length;if(0===t)return 0;let n=0,a=e[0],r=0;for(let o=1;o<t;++o){const t=e[o];t!==a&&(a=t,r=Math.max(o-n,r),n=o)}return Math.max(t-n,r)}tensorShapeFromTensor(e,t,n=!0){if(0===t.length){if(-1===e[0])return[];throw new Error("The only valid scalar shape tensor is the fully unknown shape specified as -1.")}return io(e,n)}calculateOutputSize(e){const t=this.valuesShape,n=this.defaultValueShape;r.validateDefaultValueShape(n,t);const a=this.tensorShapeFromTensor(this.shape,this.shapeShape),o=r.combineRaggedTensorToTensorShapes(this.raggedRank,a,t);o[0]<0&&(o[0]=e);for(let e=1;e<=this.raggedRank;++e)o[e]<0&&(o[e]=this.getMaxWidth(e));return o}calculateFirstParentOutputIndex(e,t,a){const r=Math.min(e,a),o=[];let s=0;for(let e=0;e<r;++e,s+=t)o.push(s);for(let t=r;t<e;++t)o.push(-1);return n.assert(o.length===e,(()=>"Final length of result must be equal to firstDimension.")),o}calculateOutputIndexRowSplit(e,t,n,a){const r=e.length,o=[];for(let s=0;s<r-1;++s){const r=e[s+1]-e[s];let i=Math.min(a,r),l=t[s];-1===l&&(i=0);for(let e=0;e<i;++e)o.push(l),l+=n;for(let e=0;e<r-i;++e)o.push(-1)}if(r>0&&o.length!==e[r-1])throw new Error("Invalid row split size.");return o}calculateOutputIndexValueRowID(e,t,n,a){const r=e.length,o=[];if(0===r)return[];let s=0,i=e[0];if(i>=t.length)throw new Error(`Got currentValueRowId=${i}, which is not less than ${t.length}`);let l=t[i];o.push(l);for(let u=1;u<r;++u){const r=e[u];if(r===i)l>=0&&(++s,s<a?l+=n:l=-1);else{if(s=0,i=r,r>=t.length)throw new Error(`Got nextValueRowId=${r} which is not less than ${t.length}`);l=t[r]}o.push(l)}if(o.length!==e.length)throw new Error("Invalid row ids.");return o}calculateOutputIndex(e,t,n,a){const r=this.getRowPartitionTensor(e),o=this.getRowPartitionTypeByDimension(e);switch(o){case ro.VALUE_ROWIDS:return this.calculateOutputIndexValueRowID(r,t,n,a);case ro.ROW_SPLITS:if(r.length-1>t.length)throw new Error(`Row partition size is greater than output size: ${r.length-1} > ${t.length}`);return this.calculateOutputIndexRowSplit(r,t,n,a);default:throw new Error(`Unsupported partition type: ${ro[o]}`)}}getFirstDimensionSize(){const e=this.rowPartitionValues[0];if(0===this.rowPartitionTypes.length)throw new Error("No row_partition_types given.");const t=this.rowPartitionTypes[0];switch(t){case ro.FIRST_DIM_SIZE:return e[0];case ro.VALUE_ROWIDS:throw new Error("Cannot handle VALUE_ROWIDS in first dimension.");case ro.ROW_SPLITS:return this.rowPartitionValuesShapes[0][0]-1;default:throw new Error(`Cannot handle type ${ro[t]}`)}}compute(){if(this.rowPartitionValues[0].length<=0)throw new Error("Invalid first partition input. Tensor requires at least one element.");const e=this.getFirstDimensionSize(),t=this.calculateOutputSize(e),a=new Array(this.raggedRank+1);a[a.length-1]=1;for(let e=a.length-2;e>=0;--e)a[e]=a[e+1]*t[e+1];const r=io(t,!1),o=n.getArrayFromDType(this.valuesDType,n.sizeFromShape(r));if(a[0]*t[0]>0){let n=this.calculateFirstParentOutputIndex(e,a[0],t[0]);for(let e=1;e<=this.raggedRank;++e){n=this.calculateOutputIndex(e-1,n,a[e],t[e])}this.setOutput(this.raggedRank,n,o,r)}return[r,o]}setOutput(e,t,a,r){if(0===a.length)return;const o=this.values,s=a;let c=r.slice();c=c.slice(e+1);const d=n.sizeFromShape(c),p=t.length;let h=this.defaultValue;if(h.length!==d&&1!==h.length){const e=this.defaultValueShape;i((()=>{const t=l(h,e),n=u(t,c);h=n.dataSync()}))}let f=0,x=0,m=0;for(let e=0;e<=p;++e){let n=e<p?t[e]:-1;if(n!==m){if(x<m){const e=o.subarray(f*d);so(s.subarray(x*d),e,(m-x)*d)}if(e>=p){const e=a.length;n=Math.floor(e/d)}if(n>m)if(1===this.defaultValue.length)s.subarray(m*d,n*d).fill(this.defaultValue[0]),m=n;else for(;n>m;){so(s.slice(m*d),h,d),++m}n<0?(f=e+1,x=m):(f=e,x=m,m=x+1)}else++m}}}function so(e,t,n){for(let a=0;a<n;a++)e[a]=t[a]}function io(e,t){const n=[];for(let a of e){if(a<0){if(!t)throw new Error(`Dimension ${a} must be >= 0`);if(a<-1)throw new Error(`Dimension ${a} must be >= -1`);a=-1}n.push(a)}return n}const lo=Vr((e=>1/Math.sqrt(e)));const uo=Vr((e=>1/(1+Math.exp(-e))));const co=Vr((e=>Math.sqrt(e))),po=Pr(((e,t)=>{const n=e-t;return n*n})),ho=Vr(((e,t)=>{const{pattern:n,replaceGlobal:a,rewrite:r}=t;return e.replace(new RegExp(n,a?"g":""),r)}));class fo{constructor(e,t,a,r,o,s){this.separator=n.encodeString(e),this.nGramWidths=t,this.leftPad=n.encodeString(a),this.rightPad=n.encodeString(r),this.padWidth=o,this.preserveShort=s}getPadWidth(e){return Math.min(this.padWidth<0?e-1:this.padWidth,e-1)}getNumNGrams(e,t){const n=this.getPadWidth(t);return Math.max(0,e+2*n-t+1)}createNGrams(e,t,n,a,r,o){for(let s=0;s<r;++s){const i=this.getPadWidth(o),l=Math.max(0,i-s),u=Math.max(0,i-(r-(s+1))),c=o-(l+u),d=t+(l>0?0:s-i);let p=0;p+=l*this.leftPad.length;for(let t=0;t<c;++t)p+=e[d+t].length;p+=u*this.rightPad.length;p+=(l+u+c-1)*this.separator.length,n[a+s]=new Uint8Array(p);const h=n[a+s];let f=0;const x=e=>e.forEach((e=>h[f++]=e));for(let e=0;e<l;++e)x(this.leftPad),x(this.separator);for(let t=0;t<c-1;++t)x(e[d+t]),x(this.separator);if(c>0){x(e[d+c-1]);for(let e=0;e<u;++e)x(this.separator),x(this.rightPad)}else{for(let e=0;e<u-1;++e)x(this.rightPad),x(this.separator);x(this.rightPad)}}}compute(e,t){const a=e.length,r=t.length;if(r>0){let e=t[0];if(0!==e)throw new Error(`First split value must be 0, got ${e}`);for(let n=1;n<r;++n){let r=t[n]>=e;if(r=r&&t[n]<=a,!r)throw new Error(`Invalid split value ${t[n]}, must be in [${e}, ${a}]`);e=t[n]}if(e!==a)throw new Error(`Last split value must be data size. Expected ${a}, got ${e}`)}const o=r-1,s=n.getArrayFromDType("int32",r);if(0===a||0===r){const e=new Array(a);for(let e=0;e<=o;++e)s[e]=0;return[e,s]}s[0]=0;for(let e=1;e<=o;++e){const n=t[e]-t[e-1];let a=0;this.nGramWidths.forEach((e=>{a+=this.getNumNGrams(n,e)})),this.preserveShort&&n>0&&0===a&&(a=1),s[e]=s[e-1]+a}const i=new Array(s[o]);for(let n=0;n<o;++n){const a=t[n];let r=s[n];if(this.nGramWidths.forEach((o=>{const s=t[n+1]-t[n],l=this.getNumNGrams(s,o);this.createNGrams(e,a,i,r,l,o),r+=l})),this.preserveShort&&r===s[n]){const o=t[n+1]-t[n];if(0===o)continue;const s=o+2*this.padWidth,l=1;this.createNGrams(e,a,i,r,l,s)}}return[i,s]}}function xo(e,t,n,a){if(!e.length)return;if(0===t.length){for(let t=0;t<e.length;++t)a.push(e.subarray(t,t+1));return}if(1===t.length){const r=t[0];let o=e.indexOf(r);for(;-1!==o;){const t=e.subarray(0,o);n&&0===t.length||a.push(t),o=(e=e.subarray(o+1)).indexOf(r)}return void(n&&0===e.length||a.push(e))}let r=0;for(let o=0;o<e.length+1;o++)if(o===e.length||-1!==t.indexOf(e[o])){const t=e.subarray(r,o);n&&0===t.length||a.push(t),r=o+1}}const mo=Pr(((e,t)=>e-t));const go=(e,t)=>{const n=t.value-e.value;return 0===n?e.index-t.index:n};function bo(e,t,a=0,r=e.length-1){for(;r>a;){if(r-a>600){const n=r-a+1,o=t-a+1,s=Math.log(n),i=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*i*(n-i)/n)*Math.sign(o-n/2);bo(e,t,Math.max(a,Math.floor(t-o*i/n+l)),Math.min(r,Math.floor(t+(n-o)*i/n+l)))}const o=e[t];let s=a,i=r;for(n.swap(e,a,t),go(e[r],o)>0&&n.swap(e,a,r);s<i;){for(n.swap(e,s,i),s++,i--;go(e[s],o)<0;)s+=1;for(;go(e[i],o)>0;)i-=1}0===go(e[a],o)?n.swap(e,a,i):(i+=1,n.swap(e,i,r)),i<=t&&(a=i+1),t<=i&&(r=i-1)}}var vo={__proto__:null,addImpl:Lr,bincountImpl:function(e,t,a,r,o){const s=n.sizeFromShape(r),i=n.makeZerosTypedArray(o,a);for(let n=0;n<e.length;n++){const a=e[n];if(a<0)throw new Error("Input x must be non-negative!");a>=o||(i[a]+=s>0?t[n]:1)}return i},bincountReduceImpl:function(e,t,n,a=!1){const r=e.shape[0],s=e.shape[1],i=o([r,n],t.dtype);for(let o=0;o<r;o++)for(let r=0;r<s;r++){const s=e.get(o,r);if(s<0)throw new Error("Input x must be non-negative!");s>=n||(a?i.set(1,o,s):t.size>0?i.set(i.get(o,s)+t.get(o,r),o,s):i.set(i.get(o,s)+1,o,s))}return i},bitwiseAndImpl:Br,castImpl:function(e,t,a,r){if("int32"===r){return[t,"int32",Int32Array.from(e)]}if("bool"===r){const r=n.toTypedArray([0],a),[o,s]=Pr(((e,t)=>e!==t?1:0))(t,[],e,r,"bool");return[s,"bool",o]}throw new Error(`Error in Cast: failed to cast ${a} to ${r}`)},ceilImpl:Wr,concatImpl:function(e,t,a,o){const s=n.getArrayFromDType(a,n.sizeFromShape(t));if(o&&"string"!==a){let t=0;e.forEach((e=>{const a=n.sizeFromShape(e.shape);s.set(e.vals,t),t+=a}))}else{let n=0;e.forEach((e=>{const o="string"===a?r.fromUint8ToStringArray(e.vals):e.vals;let i=0;for(let a=0;a<e.shape[0];++a){const r=a*t[1]+n;for(let t=0;t<e.shape[1];++t)s[r+t]=o[i++]}n+=e.shape[1]}))}return s},equalImpl:Ur,expImpl:Mr,expm1Impl:Gr,floorDivImpl:Xr,floorImpl:zr,gatherNdImpl:function(e,t,n,a,r,s,i,l,u){const c=o([a,s],n);for(let n=0;n<a;n++){const a=[];let o=0;for(let t=0;t<r;t++){const s=e[n*r+t];o+=s*i[t],a.push(s)}if(o<0||o>=u/s)throw new Error(`Invalid indices: ${a} does not index into ${l}`);for(let e=0;e<s;e++)c.values[n*s+e]=t.get(...t.indexToLoc(o*s+e))}return c},gatherV2Impl:function(e,t,n){const a=o(n,e.dtype);for(let n=0;n<a.size;++n){const r=a.indexToLoc(n).slice(),o=r[0],s=r[2],i=t.locToIndex([o,s]);r[2]=t.values[i];const l=e.locToIndex(r);0<=l&&l<e.values.length&&(a.values[n]=e.values[l])}return a},greaterEqualImpl:jr,greaterImpl:Hr,lessEqualImpl:qr,lessImpl:Kr,linSpaceImpl:function(e,t,a){const r=(t-e)/(a-1),o=n.makeZerosTypedArray(a,"float32");o[0]=e;for(let e=1;e<o.length;e++)o[e]=o[e-1]+r;return o},logImpl:Yr,maxImpl:function(e,t,a,r){const o=n.getTypedArrayFromDType(r,n.sizeFromShape(a));for(let n=0;n<o.length;++n){const a=n*t;let r=e[a];for(let n=0;n<t;++n){const t=e[a+n];(Number.isNaN(t)||t>r)&&(r=t)}o[n]=r}return o},maximumImpl:Qr,minimumImpl:Zr,multiplyImpl:Jr,negImpl:function(e,t,a){const r=n.createScalarValue(-1,a);return Jr([],t,r,e,a)},notEqualImpl:eo,prodImpl:function(e,t,a,o){const[i,l]=r.computeOutAndReduceShapes(e,o),u=s(t,"int32"),c=n.makeZerosTypedArray(n.sizeFromShape(i),u),d=n.sizeFromShape(l);for(let e=0;e<c.length;++e){const t=e*d;let n=1;for(let e=0;e<d;++e)n*=a[t+e];c[e]=n}return{outVals:c,outShape:i,outDtype:u}},raggedGatherImpl:function(e,t,a,r,o,s,i,l){if(0===e.length)throw new Error("paramsNestedSplits must be non empty");if(0===t[0].length)throw new Error("Split tensors must not be scalars");if(function(e,t,a){e.forEach(((e,r)=>{if(e<0||e>=a){const o=n.indexToLoc(r,t.length,n.computeStrides(t)).join(",");throw new Error(`indices[${o}] = ${e} is not in [0, ${a})`)}}))}(s,i,t[0][0]-1),0===r.length)throw new Error("params.rank must be nonzero");const u=r[0],{outSplits:c,valueSlices:d,numValues:p}=to(s,i,e,u),h=function(e){const t=[];for(let a=0;a<e.length;++a){const r=e[a].length,o=n.getArrayFromDType("int32",r);t.push(o),e[a].forEach(((e,t)=>o[t]=e))}return t}(c),f=ao(a,r,o,d,p);return[h,f[0],f[1]]},raggedRangeImpl:function(e,t,a,r,o,s,i){if(t.length>1)throw new Error("starts must be a scalar or vector");if(o.length>1)throw new Error("limits must be a scalar or vector");if(i.length>1)throw new Error("deltas must be a scalar or vector");const l=0===t.length,u=0===o.length,c=0===i.length,d=[];l||d.push(t[0]),u||d.push(o[0]),c||d.push(i[0]);for(let e=1;e<d.length;++e)if(d[e]!==d[e-1])throw new Error("starts, limits, and deltas must have the same shape");const p=0===d.length?1:d[0],h=n.getArrayFromDType("int32",p+1);h[0]=0;for(let t=0;t<p;++t){const n=l?e[0]:e[t],a=u?r[0]:r[t],o=c?s[0]:s[t];if(0===o)throw new Error("Requires delta != 0");let i;if(o>0&&a<n||o<0&&a>n)i=0;else if(i=Math.ceil(Math.abs((a-n)/o)),i>2147483647)throw new Error("Requires ((limit - start) / delta) <= 2147483647");h[t+1]=h[t]+i}const f=h[p],x=n.getArrayFromDType(a,f);let m=0;for(let t=0;t<p;++t){const n=h[t+1]-h[t];let a=l?e[0]:e[t];const r=c?s[0]:s[t];for(let e=0;e<n;++e)x[m++]=a,a+=r}return[h,x]},raggedTensorToTensorImpl:function(e,t,n,a,r,o,s,i,l,u){return new oo(e,t,n,a,r,o,s,i,l,u).compute()},rangeImpl:function(e,t,a,r){if(e===t||e<t&&a<0||t<e&&a>1)return n.makeZerosTypedArray(0,r);const o=Math.abs(Math.ceil((t-e)/a)),s=n.makeZerosTypedArray(o,r);t<e&&1===a&&(a=-1),s[0]=e;for(let e=1;e<s.length;e++)s[e]=s[e-1]+a;return s},rsqrtImpl:lo,scatterImpl:function(e,t,n,a,r,s,i,l,u,d){const p=[a/r,r],h=e.values,f=t.values;if(0===a)return o(n,t.dtype);const x=u instanceof c?u:o(p,t.dtype);"string"==typeof u||"number"==typeof u?x.values.fill(u):"boolean"==typeof u&&x.values.fill(+u);for(let e=0;e<s;e++){const o=[];let s=0;for(let t=0;t<i;t++){const n=h[e*i+t];o.push(n),s+=n*l[t]}if(s<0||s>=a/r)throw new Error(`Invalid indices: ${o} does not index into ${n}`);for(let n=0;n<r;n++)d?x.values[s*r+n]+=f[e*r+n]:x.values[s*r+n]=0===t.rank?f[0]:f[e*r+n]}return x},sigmoidImpl:uo,simpleAbsImpl:function(e){const t=new Float32Array(e.length);for(let n=0;n<e.length;++n)t[n]=Math.abs(e[n]);return t},sliceImpl:function(e,t,a,s,i){const l=d.isSliceContinous(s,t,a),u=n.sizeFromShape(a),c=n.computeStrides(s);if(l){const n=d.computeFlatOffset(t,c);return"string"===i?e.slice(n,n+u):e.subarray(n,n+u)}const p="string"===i?r.fromUint8ToStringArray(e):e,h=o(s,i,p),f=o(a,i);for(let e=0;e<f.size;++e){const n=f.indexToLoc(e),a=n.map(((e,n)=>e+t[n]));f.set(h.get(...a),...n)}return"string"===i?r.fromStringArrayToUint8(f.values):f.values},sparseFillEmptyRowsImpl:function(e,t,a,o,s,i,l){const u=t[0],c=i[0],d=new Array(c),p=new Array(u),h=t[1];if(0===c){if(0!==u)throw new Error(r.getSparseFillEmptyRowsIndicesDenseShapeMismatch(u));return[n.getArrayFromDType(a,0),[0,h],n.getArrayFromDType(s,0),d,p]}let f=!0,x=0;const m=new Array(c).fill(0);for(let t=0;t<u;++t){const n=e[t*h];if(n<0)throw new Error(r.getSparseFillEmptyRowsNegativeIndexErrorMessage(t,n));if(n>=c)throw new Error(r.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(t,n,c));++m[n],f=f&&n>=x,x=n}let g=!0;for(let e=0;e<c;++e){const t=0===m[e];d[e]=t,g=g&&!t,m[e]=Math.max(m[e],1),e>0&&(m[e]+=m[e-1])}if(g&&f){const t=e,n=o;for(let e=0;e<u;++e)p[e]=e;return[t,[u,h],n,d,p]}{const t=m[c-1],r=n.getArrayFromDType(a,t*h),i=n.getArrayFromDType(s,t),f=new Array(c).fill(0);for(let t=0;t<u;++t){const n=e[t*h],a=f[n],s=(0===n?0:m[n-1])+a;f[n]++;for(let n=0;n<h;++n)r[s*h+n]=e[t*h+n];i[s]=o[t],p[t]=s}for(let e=0;e<c;++e){if(0===f[e]){const t=0===e?0:m[e-1];r[t*h+0]=e;for(let e=1;e<h;++e)r[t*h+e]=0;i[t]=l}}return[r,[t,h],i,d,p]}},sparseReshapeImpl:function(e,t,a,o,s){const i=n.sizeFromShape(o),l=t[0],u=s.length,c=[];let d=1,p=-1;for(let e=0;e<u;++e){const t=s[e];if(-1===t){if(-1!==p)throw new Error(r.getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(p,e));p=e,c.push(1)}else{if(t<0)throw new Error(r.getSparseReshapeNegativeOutputDimErrorMessage(e,t));d*=t,c.push(t)}}if(-1!==p){if(d<=0)throw new Error(r.getSparseReshapeEmptyTensorZeroOutputDimErrorMessage());const e=Math.trunc(i/d);if(d*e!==i)throw new Error(r.getSparseReshapeInputOutputMultipleErrorMessage(o,c));c[p]=e}if(n.sizeFromShape(c)!==i)throw new Error(r.getSparseReshapeInputOutputMismatchErrorMessage(o,c));const h=o.length,f=[];if(h>0){f[h-1]=1;for(let e=h-2;e>=0;--e)f[e]=f[e+1]*o[e+1]}const x=[];if(u>0){x[u-1]=1;for(let e=u-2;e>=0;--e)x[e]=x[e+1]*c[e+1]}const m=n.getArrayFromDType(a,l*u);for(let t=0;t<l;++t){let n=0;for(let a=0;a<h;++a)n+=e[t*h+a]*f[a];for(let e=0;e<u;++e)m[t*u+e]=Math.trunc(n/x[e]),n%=x[e]}return[m,[l,u],c]},sparseSegmentReductionImpl:function(e,t,a,o,s,i=!1,l=0){const u=o.length,c=[t[0],e.length/t[0]],d=c[1],p=u>0?s[u-1]+1:0;if(p<0)throw new Error(r.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());const h=t.slice();h[0]=p;const f=h.reduce(((e,t)=>e*t),1),x=n.getArrayFromDType(a,f);if(0===u)return p>0&&x.fill(l),[x,h];if(p<=0)throw new Error(r.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());let m=0,g=1,b=0,v=s[m];for(;;){let t=0;if(g<u){if(t=s[g],v===t){++g;continue}if(v>=t)throw new Error(r.getSparseSegmentReductionNonIncreasingSegmentIdsErrorMessage())}if(v<0||v>=p)throw new Error(r.getSparseSegmentReductionSegmentIdOutOfRangeErrorMessage(v,p));v>b&&x.fill(l,b*d,v*d);for(let t=m;t<g;++t){const n=o[t];if(n<0||n>=c[0])throw new Error(r.getSparseSegmentReductionIndicesOutOfRangeErrorMessage(t,o[t],c[0]));for(let t=0;t<d;t++)x[v*d+t]+=e[n*d+t]}if(i)for(let e=0;e<d;e++)x[v*d+e]/=g-m;if(m=g,++g,b=v+1,v=t,g>u)break}return b<p&&x.fill(l,b*d,p*d),[x,h]},sqrtImpl:co,squaredDifferenceImpl:po,staticRegexReplaceImpl:ho,stridedSliceImpl:function(e,t,n,a){const r=o(e,t.dtype);for(let e=0;e<r.size;e++){const o=r.indexToLoc(e),s=new Array(o.length);for(let e=0;e<s.length;e++)s[e]=o[e]*n[e]+a[e];r.set(t.get(...s),...o)}return r},stringNGramsImpl:function(e,t,n,a,r,o,s,i){return new fo(n,a,r,o,s,i).compute(e,t)},stringSplitImpl:function(e,t,a){const r=e.length,o=[];let s=0,i=0;const l=new Array(r);for(let n=0;n<r;++n){const r=o.length;xo(e[n],t,a,o);const u=o.length-r;l[n]=u,s+=u,i=Math.max(i,u)}const u=n.getArrayFromDType("int32",2*s),c=new Array(s),d=[r,i];let p=0;for(let e=0;e<r;++e)for(let t=0;t<l[e];++t)u[2*p]=e,u[2*p+1]=t,c[p]=o[p],++p;return[u,c,d]},stringToHashBucketFastImpl:function(e,t){const a=n.getArrayFromDType("int32",e.length);for(let r=0;r<e.length;++r)a[r]=n.fingerPrint64(e[r]).modulo(t).getLowBitsUnsigned();return a},subImpl:mo,tileImpl:function(e,t){const n=new Array(e.rank);for(let a=0;a<n.length;a++)n[a]=e.shape[a]*t[a];const a=o(n,e.dtype);for(let t=0;t<a.values.length;++t){const n=a.indexToLoc(t),r=new Array(e.rank);for(let t=0;t<r.length;t++)r[t]=n[t]%e.shape[t];const o=e.locToIndex(r);a.values[t]=e.values[o]}return a},topKImpl:function(e,t,a,r,s){const i=t[t.length-1],[l,u]=[e.length/i,i],c=n.getTypedArrayFromDType(a,l*r),d=n.getTypedArrayFromDType("int32",l*r);for(let t=0;t<l;t++){const n=t*u,a=e.subarray(n,n+u);let o=new Array(a.length);a.forEach(((e,t)=>o[t]={value:e,index:t})),r<o.length&&(bo(o,r),o=o.slice(0,r)),s&&o.sort(go);const i=t*r,l=c.subarray(i,i+r),p=d.subarray(i,i+r);for(let e=0;e<r;e++)l[e]=o[e].value,p[e]=o[e].index}const p=t.slice();return p[p.length-1]=r,[o(p,a,c),o(p,"int32",d)]},transposeImpl:function(e,t,a,r,o){const s=t.length,i=n.sizeFromShape(t),l=n.computeStrides(t),u=n.computeStrides(o),c=n.getTypedArrayFromDType(a,n.sizeFromShape(o));for(let t=0;t<i;++t){const a=n.indexToLoc(t,s,l),o=new Array(a.length);for(let e=0;e<o.length;e++)o[e]=a[r[e]];c[n.locToIndex(o,s,u)]=e[t]}return c},uniqueImpl:function(e,t,a,r){const o=n.parseAxisParam(t,a)[0],s=[1,a[0],1];for(let e=0;e<o;e++)s[0]*=a[e];s[1]=a[o];for(let e=o+1;e<a.length;e++)s[2]*=a[e];const i=new Map,l=new Int32Array(a[o]),u=new c(s,r,e),d=[],p=1===s[0]&&1===s[2];for(let t=0;t<a[o];t++){let n;if(p)n=e[t].toString();else{const e=[];for(let n=0;n<s[0];n++)for(let a=0;a<s[2];a++)e.push(u.get(n,t,a));n=e.join(",")}const a=i.get(n);if(null!=a)l[t]=a;else{const e=i.size;i.set(n,e),l[t]=e,d.push(t)}}const h=s.slice();h[1]=i.size;const f=new c(h,r);d.forEach(((e,t)=>{for(let n=0;n<s[0];n++)for(let a=0;a<s[2];a++)f.set(u.get(n,e,a),n,t,a)}));const x=a.slice();return x[o]=h[1],{outputValues:f.values,outputShape:x,indices:l}}};const{addImpl:Co,bincountImpl:$o,bincountReduceImpl:yo,bitwiseAndImpl:Io,castImpl:wo,ceilImpl:So,concatImpl:Ro,equalImpl:To,expImpl:ko,expm1Impl:No,floorImpl:Eo,gatherNdImpl:Ao,gatherV2Impl:Oo,greaterImpl:Fo,greaterEqualImpl:_o,lessImpl:Do,lessEqualImpl:Po,linSpaceImpl:Lo,logImpl:Bo,maxImpl:Vo,maximumImpl:Wo,minimumImpl:Uo,multiplyImpl:Mo,negImpl:Go,notEqualImpl:zo,prodImpl:Xo,raggedGatherImpl:Ho,raggedRangeImpl:jo,raggedTensorToTensorImpl:Ko,rangeImpl:qo,rsqrtImpl:Yo,scatterImpl:Qo,sigmoidImpl:Zo,simpleAbsImpl:Jo,sliceImpl:es,sparseFillEmptyRowsImpl:ts,sparseReshapeImpl:ns,sparseSegmentReductionImpl:as,sqrtImpl:rs,staticRegexReplaceImpl:os,stridedSliceImpl:ss,stringNGramsImpl:is,stringSplitImpl:ls,stringToHashBucketFastImpl:us,subImpl:cs,tileImpl:ds,topKImpl:ps,transposeImpl:hs,uniqueImpl:fs}=vo;function xs(e,t){return["x","y","z","w","u","v"].slice(0,t).map((t=>`${e}.${t}`))}function ms(e,t){return 1===t?[e]:xs(e,t)}class gs{constructor(e){if(this.variableNames=["A"],this.packedInputs=!1,this.packedOutput=!0,this.outputShape=e,this.rank=e.length,this.enableShapeUniforms=rr(this.outputShape.length),0===this.rank)this.userCode="\n        void main() {\n          setOutput(vec4(getA(), 0., 0., 0.));\n        }\n      ";else{const e=ms("rc",this.rank),t=Za(this.rank),n=this.getOutOfBoundsCondition(e),a=this.getSetup(e),r=this.getOutput(e);this.userCode=`\n        void main() {\n          ${t} rc = getOutputCoords();\n\n          if(${n}) {\n            setOutput(vec4(0));\n          } else {\n            ${a}\n\n            setOutput(vec4(${r}));\n          }\n        }\n      `}}getSourceCoordsArr(e){const t=[];for(let n=0;n<=1;n++)for(let a=0;a<=1;a++){let r=`${0===n?"r":"rp1"}, ${0===a?"c":"cp1"}`;for(let t=2;t<this.rank;t++)r=`${e[e.length-1-t]},`+r;t.push(r)}return t}getOutOfBoundsCondition(e){if(1===this.rank)return`rc > ${this.enableShapeUniforms?"outShape":this.outputShape[0]}`;let t="";for(let n=this.rank-2;n<this.rank;n++)t+=`${e[n]} >= ${this.enableShapeUniforms?`outShape[${n}]`:this.outputShape[n]}`,n<this.rank-1&&(t+="||");return t}getSetup(e){if(1===this.rank)return"";const t=e.slice(-2),n=this.enableShapeUniforms?`outShape[${this.rank} - 1]`:this.outputShape[this.rank-1],a=this.enableShapeUniforms?`outShape[${this.rank} - 2]`:this.outputShape[this.rank-2];return`\n      int r = ${t[0]};\n      int c = ${t[1]};\n      int rp1 = r + 1;\n      int cp1 = c + 1;\n\n      bool cEdge = cp1 >= ${n};\n      bool rEdge = rp1 >= ${a};\n    `}getOutput(e){const t=this.getSourceCoordsArr(e);if(1===this.rank){return`getA(rc), (rc + 1 >= ${this.enableShapeUniforms?"outShape":this.outputShape[0]} ? 0. : getA(rc + 1)), 0, 0`}return`getA(${t[0]}),\n            cEdge ? 0. : getA(${t[1]}),\n            rEdge ? 0. : getA(${t[2]}),\n            rEdge || cEdge ? 0. : getA(${t[3]})`}}class bs{constructor(e,t){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"inputShape",type:"ivec3"}],this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length);let n="";for(let e=0;e<4;e++){let t="thisRC = rc;";e%2==1&&(t+="thisRC.z += 1;"),e>1&&(t+="thisRC.y += 1;"),n+=`\n        ${t}\n        ${e>0?"if(thisRC.y < rows && thisRC.z < cols){":""}\n          int flatIndex = getFlatIndex(thisRC);\n\n          ivec3 inputRC = inputCoordsFromReshapedOutCoords(flatIndex);\n          vec2 inputRCInnerDims = vec2(float(inputRC.y),float(inputRC.z));\n\n          result[${e}] =\n            getChannel(getA(inputRC.x, inputRC.y, inputRC.z), inputRCInnerDims);\n        ${e>0?"}":""}\n      `}var a,r;this.userCode=`\n      ${a=t,r=this.enableShapeUniforms,`\n    ivec3 inputCoordsFromReshapedOutCoords(int index) {\n      ${r?Ba(["r","c","d"],"inputShape"):Pa(["r","c","d"],a)}\n      return ivec3(r, c, d);\n    }\n  `}\n      ${this.enableShapeUniforms?"\n  int getFlatIndex(ivec3 coords) {\n    return coords.x * outShapeStrides[0] + coords.y * outShapeStrides[1] + coords.z;\n  }\n":Va(e)}\n\n      void main() {\n        ivec3 rc = getOutputCoords();\n\n        vec4 result = vec4(0.);\n\n        ivec3 thisRC;\n        int rows = ${this.enableShapeUniforms?"outShape[1]":e[1]};\n        int cols = ${this.enableShapeUniforms?"outShape[2]":e[2]};\n\n        ${n}\n\n        setOutput(result);\n      }\n    `}}class vs{constructor(e){this.gpgpu=e,this.numUsedTextures=0,this.numFreeTextures=0,this._numBytesAllocated=0,this._numBytesFree=0,this.freeTextures={},this.usedTextures={},this.logEnabled=!1}acquireTexture(e,t,n){const a=$s(t,n),r=ys(e,a,n);r in this.freeTextures||(this.freeTextures[r]=[]),r in this.usedTextures||(this.usedTextures[r]=[]);const o=Cs(e,a,this.gpgpu.gl,this.gpgpu.textureConfig,n);if(this.freeTextures[r].length>0){this.numFreeTextures--,this.numUsedTextures++,this._numBytesFree-=o,this.log();const e=this.freeTextures[r].pop();return this.usedTextures[r].push(e),e}let s;return a===Ln.PACKED_2X2_FLOAT32?s=this.gpgpu.createPackedMatrixTexture(e[0],e[1]):a===Ln.PACKED_2X2_FLOAT16?s=this.gpgpu.createFloat16PackedMatrixTexture(e[0],e[1]):a===Ln.UNPACKED_FLOAT32?s=this.gpgpu.createFloat32MatrixTexture(e[0],e[1]):a===Ln.UNPACKED_FLOAT16?s=this.gpgpu.createFloat16MatrixTexture(e[0],e[1]):a===Ln.PACKED_4X1_UNSIGNED_BYTE&&(s=this.gpgpu.createUnsignedBytesMatrixTexture(e[0],e[1])),this.usedTextures[r].push(s),this.numUsedTextures++,this._numBytesAllocated+=o,this.log(),s}releaseTexture(e,n,a,r){if(null==this.freeTextures)return;const o=$s(a,r),s=ys(n,o,r);s in this.freeTextures||(this.freeTextures[s]=[]);const i=Cs(n,o,this.gpgpu.gl,this.gpgpu.textureConfig,r),l=t().getNumber("WEBGL_DELETE_TEXTURE_THRESHOLD");-1!==l&&this._numBytesAllocated>l?(this.gpgpu.deleteMatrixTexture(e.texture),this._numBytesAllocated-=i):(this.freeTextures[s].push(e),this.numFreeTextures++,this._numBytesFree+=i),this.numUsedTextures--;const u=this.usedTextures[s],c=u&&u.indexOf(e);if(null==c||c<0)throw new Error("Cannot release a texture that was never provided by this texture manager");u[c]=u[u.length-1],u.pop(),this.log()}log(){if(!this.logEnabled)return;const e=this.numFreeTextures+this.numUsedTextures;console.log("Free/Used",`${this.numFreeTextures} / ${this.numUsedTextures}`,`(${e})`);const t=this._numBytesFree/this._numBytesAllocated;console.log(`Bytes allocated: ${this._numBytesAllocated}`),console.log(`Bytes unused: ${this._numBytesFree} (${Math.round(100*t)}%)`)}get numBytesAllocated(){return this._numBytesAllocated}get numBytesFree(){return this._numBytesFree}getNumUsedTextures(){return this.numUsedTextures}getNumFreeTextures(){return this.numFreeTextures}dispose(){if(null!=this.freeTextures){for(const e in this.freeTextures)this.freeTextures[e].forEach((e=>{this.gpgpu.deleteMatrixTexture(e.texture)}));for(const e in this.usedTextures)this.usedTextures[e].forEach((e=>{this.gpgpu.deleteMatrixTexture(e.texture)}));this.freeTextures=null,this.usedTextures=null,this.numUsedTextures=0,this.numFreeTextures=0,this._numBytesAllocated=0,this._numBytesFree=0}}}function Cs(e,t,n,a,r){const o=function(e,t){switch(e){case Ln.PACKED_2X2_FLOAT32:return yr(t);case Ln.PACKED_2X2_FLOAT16:return wr(t);case Ln.UNPACKED_FLOAT32:return mr(t);case Ln.UNPACKED_FLOAT16:return br(t);case Ln.PACKED_4X1_UNSIGNED_BYTE:return Cr(t);default:throw new Error(`Unknown physical texture type ${e}`)}}(t,a);let s;if(r){const[t,n]=Wn(e[0],e[1]);s=t*n}else{const[t,n]=Bn(e[0],e[1]);s=t*n}const i=function(e,t){const n=e;if(t===n.R32F)return 4;if(t===n.R16F)return 2;if(t===n.RGBA32F)return 16;if(t===e.RGBA)return 16;if(t===n.RGBA16F)return 8;if(t===n.RGBA8)return 4;throw new Error(`Unknown internal format ${t}`)}(n,o);return s*i}function $s(e,n){if(e===Pn.UPLOAD)return Ln.PACKED_2X2_FLOAT32;if(e===Pn.RENDER||null==e)return function(e){return t().getBool("WEBGL_RENDER_FLOAT32_ENABLED")?e?Ln.PACKED_2X2_FLOAT32:Ln.UNPACKED_FLOAT32:e?Ln.PACKED_2X2_FLOAT16:Ln.UNPACKED_FLOAT16}(n);if(e===Pn.DOWNLOAD||e===Pn.PIXELS)return Ln.PACKED_4X1_UNSIGNED_BYTE;throw new Error(`Unknown logical texture type ${e}`)}function ys(e,t,n){return`${e[0]}_${e[1]}_${t}_${n}`}class Is{constructor(e,t){this.variableNames=["A"],this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length),this.userCode=`\n      float unaryOperation(float x) {\n        ${t}\n      }\n\n      void main() {\n        float x = getAAtOutCoords();\n        float y = unaryOperation(x);\n\n        setOutput(y);\n      }\n    `}}const ws="return x;";class Ss{constructor(e,t){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length),this.userCode=`\n      vec4 unaryOperation(vec4 x) {\n        ${t}\n      }\n\n      void main() {\n        vec4 x = getAAtOutCoords();\n        vec4 y = unaryOperation(x);\n\n        setOutput(y);\n      }\n    `}}class Rs{constructor(e){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!1,this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length);const t=e.length,n=ms("rc",t),a=Za(t),r=function(e,t){if(1===e)return"rc";let n="";for(let a=0;a<e;a++)n+=t[a],a<e-1&&(n+=",");return n}(t,n),o=n.slice(-2),s=t<=1?"rc":`vec2(${o.join(",")})`;this.userCode=`\n      void main() {\n        ${a} rc = getOutputCoords();\n        vec4 packedInput = getA(${r});\n\n        setOutput(getChannel(packedInput, ${s}));\n      }\n    `}}const Ts=p.whereImpl,ks={};const Ns=t().getNumber("CPU_HANDOFF_SIZE_THRESHOLD");class Es extends h{nextDataId(){return Es.nextDataId++}constructor(e){if(super(),this.pendingRead=new WeakMap,this.pendingDisposal=new WeakSet,this.dataRefCount=new WeakMap,this.numBytesInGPU=0,this.uploadWaitMs=0,this.downloadWaitMs=0,this.lastGlFlushTime=0,this.warnedAboutMemory=!1,this.pendingDeletes=0,this.disposed=!1,!t().getBool("HAS_WEBGL"))throw new Error("WebGL is not supported on this device");let n;if(null!=e){if(e instanceof Dr)n=e;else{const a=_n(t().getNumber("WEBGL_VERSION"),e);n=new Dr(a)}this.binaryCache={},this.gpgpuCreatedLocally=!1}else{const e=_n(t().getNumber("WEBGL_VERSION"));n=new Dr(e),this.binaryCache=((a=t().getNumber("WEBGL_VERSION"))in ks||(ks[a]={}),ks[a]),this.gpgpuCreatedLocally=!0}var a;this.gpgpu=n,this.canvas=this.gpgpu.gl.canvas,this.textureManager=new vs(this.gpgpu),this.numMBBeforeWarning=null==t().global.screen?1024:t().global.screen.height*t().global.screen.width*window.devicePixelRatio*600/1024/1024,this.texData=new f(this,x())}numDataIds(){return this.texData.numDataIds()-this.pendingDeletes}writeTexture(e,t,n,a,r,o){const s=this.makeTensorInfo(t,n),i=this.texData.get(s.dataId);i.isPacked=!1,i.texture={texture:e,texShape:[a,r]},i.texShape=[a,r];const l=ga(t),u=new cr(l,!1,o),c=this.runWebGLProgram(u,[s],n,[[a,r]]);return c.shape=t,i.texture=null,this.disposeIntermediateTensorInfo(s),c.dataId}write(e,n,a){if((t().getBool("WEBGL_CHECK_NUMERICAL_PROBLEMS")||t().getBool("DEBUG"))&&this.checkNumericalProblems(e),"complex64"===a&&null!=e)throw new Error("Cannot write to a complex64 dtype. Please use tf.complex(real, imag).");const r={id:this.nextDataId()};return this.texData.set(r,{shape:n,dtype:a,values:e,usage:Pn.UPLOAD,refCount:1}),r}refCount(e){if(this.texData.has(e)){return this.texData.get(e).refCount}return 0}incRef(e){this.texData.get(e).refCount++}decRef(e){if(this.texData.has(e)){this.texData.get(e).refCount--}}move(e,n,a,r,o){if(t().getBool("DEBUG")&&this.checkNumericalProblems(n),"complex64"===r)throw new Error("Cannot write to a complex64 dtype. Please use tf.complex(real, imag).");this.texData.set(e,{shape:a,dtype:r,values:n,usage:Pn.UPLOAD,refCount:o})}disposeIntermediateTensorInfo(e){this.disposeData(e.dataId)}readSync(e){const t=this.texData.get(e),{values:a,dtype:o,complexTensorInfos:s,slice:i,shape:l,isPacked:u}=t;if(null!=i){let t;t=u?new Ss(l,ws):new Is(l,ws);const n=this.runWebGLProgram(t,[{dataId:e,shape:l,dtype:o}],o),a=this.readSync(n.dataId);return this.disposeIntermediateTensorInfo(n),a}if(null!=a)return this.convertAndCacheOnCPU(e);if("string"===o)return a;const c=null!=this.activeTimers;let d,p;if(c&&(d=n.now()),"complex64"===o){const e=this.readSync(s.real.dataId),t=this.readSync(s.imag.dataId);p=r.mergeRealAndImagArrays(e,t)}else p=this.getValuesFromTexture(e);return c&&(this.downloadWaitMs+=n.now()-d),this.convertAndCacheOnCPU(e,p)}async read(e){if(this.pendingRead.has(e)){const t=this.pendingRead.get(e);return new Promise((e=>t.push(e)))}const a=this.texData.get(e),{values:o,shape:s,slice:i,dtype:l,complexTensorInfos:u,isPacked:c}=a;if(null!=i){let t;t=c?new Ss(s,ws):new Is(s,ws);const n=this.runWebGLProgram(t,[{dataId:e,shape:s,dtype:l}],l),a=this.read(n.dataId);return this.disposeIntermediateTensorInfo(n),a}if(null!=o)return this.convertAndCacheOnCPU(e);if(t().getBool("DEBUG")&&!t().getBool("WEBGL_DOWNLOAD_FLOAT_ENABLED")&&2===t().getNumber("WEBGL_VERSION"))throw new Error("tensor.data() with WEBGL_DOWNLOAD_FLOAT_ENABLED=false and WEBGL_VERSION=2 not yet supported.");let d,p,h=null;if("complex64"!==l&&t().get("WEBGL_BUFFER_SUPPORTED")){d=this.decode(e);const t=this.texData.get(d.dataId);h=this.gpgpu.createBufferFromTexture(t.texture.texture,...Vn(s))}if(this.pendingRead.set(e,[]),"complex64"!==l&&await this.gpgpu.createAndWaitForFence(),"complex64"===l){const e=await Promise.all([this.read(u.real.dataId),this.read(u.imag.dataId)]),t=e[0],n=e[1];p=r.mergeRealAndImagArrays(t,n)}else if(null==h)p=this.getValuesFromTexture(e);else{const e=n.sizeFromShape(s);p=this.gpgpu.downloadFloat32MatrixFromBuffer(h,e)}if(null!=d&&this.disposeIntermediateTensorInfo(d),null!=h){const e=this.gpgpu.gl;Mn(e,(()=>e.deleteBuffer(h)))}const f=this.convertAndCacheOnCPU(e,p),m=this.pendingRead.get(e);return this.pendingRead.delete(e),m.forEach((e=>e(f))),this.pendingDisposal.has(e)&&(this.pendingDisposal.delete(e),this.disposeData(e)&&x().removeDataId(e,this),this.pendingDeletes--),f}readToGPU(e,t={}){const n=this.texData.get(e),{values:a,shape:r,slice:o,dtype:s,isPacked:i,texture:l}=n;if("complex64"===s)throw new Error("Does not support reading texture for complex64 dtype.");if(null!=o){let n;n=i?new Ss(r,ws):new Is(r,ws);const a=this.runWebGLProgram(n,[{dataId:e,shape:r,dtype:s}],s),o=this.readToGPU(a,t);return this.disposeIntermediateTensorInfo(a),o}if(null==l)throw null!=a?new Error("Data is not on GPU but on CPU."):new Error("There is no data on GPU or CPU.");const u=this.decode(e,t.customTexShape),c=x().makeTensorFromTensorInfo(u),d=this.texData.get(u.dataId);return Object.assign({tensorRef:c},d.texture)}bufferSync(e){const t=this.readSync(e.dataId);if("string"===e.dtype)try{const a=t.map((e=>n.decodeString(e)));return o(e.shape,e.dtype,a)}catch(e){throw new Error("Failed to decode encoded string bytes into utf-8")}return o(e.shape,e.dtype,t)}checkNumericalProblems(e){if(null!=e)for(let n=0;n<e.length;n++){const a=e[n];if(!Gn(a)){if(t().getBool("WEBGL_RENDER_FLOAT32_CAPABLE"))throw Error(`The value ${a} cannot be represented with your current settings. Consider enabling float32 rendering: 'tf.env().set('WEBGL_RENDER_FLOAT32_ENABLED', true);'`);throw Error(`The value ${a} cannot be represented on this device.`)}}}getValuesFromTexture(e){const{shape:a,dtype:r,isPacked:o}=this.texData.get(e),s=n.sizeFromShape(a);if(t().getBool("WEBGL_DOWNLOAD_FLOAT_ENABLED")){const t=this.decode(e),n=this.texData.get(t.dataId),r=this.gpgpu.downloadMatrixFromPackedTexture(n.texture.texture,...Vn(a)).subarray(0,s);return this.disposeIntermediateTensorInfo(t),r}const i=t().getBool("WEBGL_PACK")&&!0===o,l=i?ga(a):a,u=i?new lr(l):new ir(l),c=this.runWebGLProgram(u,[{shape:l,dtype:r,dataId:e}],"float32"),d=this.texData.get(c.dataId),p=this.gpgpu.downloadByteEncodedFloatMatrixFromOutputTexture(d.texture.texture,d.texShape[0],d.texShape[1]).subarray(0,s);return this.disposeIntermediateTensorInfo(c),p}timerAvailable(){return t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0}time(e){const a=this.activeTimers,r=[];let o=!1;null==this.programTimersStack?(this.programTimersStack=r,o=!0):this.activeTimers.push(r),this.activeTimers=r,e();const s=n.flatten(this.activeTimers.map((e=>e.query))).filter((e=>null!=e)),i=n.flatten(this.activeTimers.map((e=>e.name))).filter((e=>null!=e));this.activeTimers=a,o&&(this.programTimersStack=null);const l={uploadWaitMs:this.uploadWaitMs,downloadWaitMs:this.downloadWaitMs,kernelMs:null,wallMs:null};return(async()=>{if(t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0){const e=await Promise.all(s);l.kernelMs=n.sum(e),l.getExtraProfileInfo=()=>e.map(((e,t)=>({name:i[t],ms:e}))).map((e=>`${e.name}: ${e.ms}`)).join(", ")}else l.kernelMs={error:"WebGL query timers are not supported in this environment."};return this.uploadWaitMs=0,this.downloadWaitMs=0,l})()}memory(){return{unreliable:!1,numBytesInGPU:this.numBytesInGPU,numBytesInGPUAllocated:this.textureManager.numBytesAllocated,numBytesInGPUFree:this.textureManager.numBytesFree}}startTimer(){return t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0?this.gpgpu.beginQuery():{startMs:n.now(),endMs:null}}endTimer(e){return t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0?(this.gpgpu.endQuery(),e):(e.endMs=n.now(),e)}async getQueryTime(e){if(t().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0)return this.gpgpu.waitForQueryAndGetTime(e);const n=e;return n.endMs-n.startMs}disposeData(e,t=!1){if(this.pendingDisposal.has(e))return!1;if(!this.texData.has(e))return!0;if(t?this.texData.get(e).refCount=0:this.texData.get(e).refCount--,!t&&this.texData.get(e).refCount>0)return!1;if(this.pendingRead.has(e))return this.pendingDisposal.add(e),this.pendingDeletes++,!1;this.releaseGPUData(e);const{complexTensorInfos:n}=this.texData.get(e);return null!=n&&(this.disposeData(n.real.dataId,t),this.disposeData(n.imag.dataId,t)),this.texData.delete(e),!0}releaseGPUData(e){const{texture:t,dtype:n,texShape:a,usage:r,isPacked:o,slice:s}=this.texData.get(e),i=s&&s.origDataId||e,l=this.dataRefCount.get(i);l>1?this.dataRefCount.set(i,l-1):(this.dataRefCount.delete(i),null!=t&&(this.numBytesInGPU-=this.computeBytes(a,n),this.textureManager.releaseTexture(t,a,r,o)));const u=this.texData.get(e);u.texture=null,u.texShape=null,u.isPacked=!1,u.slice=null}getTexture(e){return this.uploadToGPU(e),this.texData.get(e).texture.texture}getDataInfo(e){return this.texData.get(e)}shouldExecuteOnCPU(e,a=Ns){return t().getBool("WEBGL_CPU_FORWARD")&&e.every((e=>null==this.texData.get(e.dataId).texture&&n.sizeFromShape(e.shape)<a))}getGPGPUContext(){return this.gpgpu}where(e){r.warn("tf.where() in webgl locks the UI thread. Call tf.whereAsync() instead");const t=e.dataSync();return Ts(e.shape,t)}packedUnaryOp(e,t,n){const a=new Ss(e.shape,t),r=this.compileAndRun(a,[e],n);return x().makeTensorFromTensorInfo(r)}abs(e){if(this.shouldExecuteOnCPU([e])&&"complex64"!==e.dtype){const t=Jo(this.texData.get(e.dataId).values);return this.makeOutput(e.shape,e.dtype,t)}if(t().getBool("WEBGL_PACK_UNARY_OPERATIONS"))return this.packedUnaryOp(e,"return abs(x);",e.dtype);const n=new Is(e.shape,"return abs(x);"),a=this.compileAndRun(n,[e]);return x().makeTensorFromTensorInfo(a)}makeTensorInfo(e,t,a){let r;if("string"===t&&null!=a&&a.length>0&&n.isString(a[0])){const o=a.map((e=>n.encodeString(e)));r=this.write(o,e,t)}else r=this.write(a,e,t);return this.texData.get(r).usage=null,{dataId:r,shape:e,dtype:t}}makeOutput(e,t,n){return x().makeTensorFromTensorInfo(this.makeTensorInfo(e,t,n),this)}unpackTensor(e){const t=new Rs(e.shape);return this.runWebGLProgram(t,[e],e.dtype)}packTensor(e){const t=new gs(e.shape);return this.runWebGLProgram(t,[e],e.dtype,null,!0)}packedReshape(e,t){const n=[xa(e.shape),...ma(e.shape)],a={dtype:e.dtype,shape:n,dataId:e.dataId},r=[xa(t),...ma(t)],o=new bs(r,n),s=[n],i=this.runWebGLProgram(o,[a],e.dtype,s,!0);return{dataId:i.dataId,shape:t,dtype:i.dtype}}decode(e,t){const a=this.texData.get(e),{isPacked:r,shape:o,dtype:s}=a;if(null!=t){const e=n.sizeFromShape(o),a=t[0]*t[1]*4;n.assert(e<=a,(()=>"customTexShape is too small. Row * Column * 4 should be equal or larger than the size of the tensor data."))}const i=ga(o);let l;l=r?new sr(i):new or(i);const u=[null!=t?t:Vn(i)];return{dtype:s,shape:o,dataId:this.runWebGLProgram(l,[{shape:i,dtype:s,dataId:e}],s,u,!0,t).dataId}}runWebGLProgram(e,a,o,s,i=!1,l){const u=this.makeTensorInfo(e.outputShape,o),c=this.texData.get(u.dataId);if(e.packedOutput&&(c.isPacked=!0),e.outPackingScheme===Dn.DENSE){const t=null!=l?l:Vn(e.outputShape);c.texShape=t.map((e=>2*e))}if(null!=e.outTexUsage&&(c.usage=e.outTexUsage),0===n.sizeFromShape(u.shape))return c.values=n.getTypedArrayFromDType(u.dtype,0),u;const d=[],p=a.map((a=>{if("complex64"===a.dtype)throw new Error("GPGPUProgram does not support complex64 input. For complex64 dtypes, please separate the program into real and imaginary parts.");let r=this.texData.get(a.dataId);if(null==r.texture){if(!e.packedInputs&&n.sizeFromShape(a.shape)<=t().getNumber("WEBGL_SIZE_UPLOAD_UNIFORM"))return{shape:a.shape,texData:null,isUniform:!0,uniformValues:r.values};e.packedInputs&&(r.isPacked=!0,r.shape=a.shape)}if(this.uploadToGPU(a.dataId),!!r.isPacked!=!!e.packedInputs)a=r.isPacked?this.unpackTensor(a):this.packTensor(a),d.push(a),r=this.texData.get(a.dataId);else if(r.isPacked&&!Ca(r.shape,a.shape)){const e=a,t=a.shape;a.shape=r.shape,a=this.packedReshape(a,t),d.push(a),r=this.texData.get(a.dataId),e.shape=t}return{shape:a.shape,texData:r,isUniform:!1}}));this.uploadToGPU(u.dataId);const h={shape:u.shape,texData:c,isUniform:!1},f=function(e,a,o){let s="";a.concat(o).forEach((t=>{const a=null!=t.texData&&null!=t.texData.slice&&t.texData.slice.flatOffset>0;if(e.enableShapeUniforms&&!t.isUniform){const i=t.texData.texShape,{useSqueezeShape:l,uniformShape:u,keptDims:c}=Ja(e.packedInputs,t.shape,i);let d="",p="",h="";if(1===u.length&&e.packedInputs){const e=[Math.ceil(i[0]/2),Math.ceil(i[1]/2)];d=`${e[0]>1}_${e[1]>1}`}else if(2!==u.length||e.packedInputs){if(u.length>2&&!e.packedInputs){const e=n.computeStrides(u);h=`${e[0]===i[1]}_${e[e.length-1]===i[1]}`}}else p=`${u[0]>1}_${u[1]>1}`;const f=t.shape.length,x=2===u.length&&n.arraysEqual(t.shape,i),m=1===n.sizeFromShape(t.shape),g=r.getBroadcastDims(t.shape,o.shape),b=!e.packedInputs&&f===o.shape.length&&n.arraysEqual(i,o.texData.texShape),v=e.packedInputs||u.length>2?"":`${i[0]>1}_${i[1]>1}`;s+=`${f}_${b}_${l?c:""}_${u.length}_${m}_${g}_${x}_${d}_${p}_${h}_${v}_${a}`}else{const e=t.isUniform?"uniform":t.texData.texShape;s+=`${t.shape}_${e}_${a}`}}));const i=e.userCode;let l=e.constructor.name;return l+="_"+s+"_"+i+`${t().getNumber("WEBGL_VERSION")}`,l}(e,p,h),x=this.getAndSaveBinary(f,(()=>function(e,n,a,r){const o=a.map(((e,t)=>{const a={logicalShape:e.shape,texShape:e.isUniform?null:e.texData.texShape,isUniform:e.isUniform,isPacked:!e.isUniform&&e.texData.isPacked,flatOffset:null};return null!=e.texData&&null!=e.texData.slice&&e.texData.slice.flatOffset>0&&(a.flatOffset=e.texData.slice.flatOffset),{name:n.variableNames[t],shapeInfo:a}})),s=o.map((e=>e.shapeInfo)),i={logicalShape:r.shape,texShape:r.texData.texShape,isUniform:!1,isPacked:r.texData.isPacked,flatOffset:null},l=Ma(o,i,n),u=jn(e.gl,l),c=e.createProgram(u);return t().get("ENGINE_COMPILE_ONLY")?{program:n,fragmentShader:u,source:l,webGLProgram:c,inShapeInfos:s,outShapeInfo:i,variablesLocations:null,customUniformLocations:null,infLoc:null,nanLoc:null,outShapeLocation:null,outShapeStridesLocation:null,outTexShapeLocation:null}:(e.buildVao(c),Object.assign({program:n,fragmentShader:u,source:l,webGLProgram:c,inShapeInfos:s,outShapeInfo:i},nr(e,n,c)))}(this.gpgpu,e,p,h))),m=null!=this.activeTimers;let g;m&&(g=this.startTimer()),t().get("ENGINE_COMPILE_ONLY")||function(e,a,r,o,s){a.program.enableShapeUniforms||(ar(a.inShapeInfos,r),ar([a.outShapeInfo],[o]));const i=o.texData.texture,l=o.texData.texShape;o.texData.isPacked?e.setOutputPackedMatrixTexture(i.texture,l[0],l[1]):e.setOutputMatrixTexture(i.texture,l[0],l[1]),e.setProgram(a.webGLProgram),e.bindVertexArray(a.webGLProgram.vao),1===t().getNumber("WEBGL_VERSION")&&null!==a.infLoc&&e.gl.uniform1f(a.infLoc,1/0),null!==a.nanLoc&&e.gl.uniform1f(a.nanLoc,NaN);for(let t=0;t<r.length;++t){const o=r[t],{uniform:s,offset:i,shape:l,texShape:u}=a.variablesLocations[t];if(l){const{uniformShape:t}=Ja(a.program.packedInputs,o.shape,o.texData.texShape);switch(t.length){case 1:e.gl.uniform1iv(l,new Int32Array(t));break;case 2:e.gl.uniform2iv(l,new Int32Array(t));break;case 3:e.gl.uniform3iv(l,new Int32Array(t));break;case 4:e.gl.uniform4iv(l,new Int32Array(t))}}if(u&&e.gl.uniform2i(u,o.texData.texShape[0],o.texData.texShape[1]),null!=s)if(o.isUniform)if(n.sizeFromShape(o.shape)<2)e.gl.uniform1f(s,o.uniformValues[0]);else{let t=o.uniformValues;t instanceof Float32Array||(t=new Float32Array(t)),e.gl.uniform1fv(s,t)}else null!=o.texData.slice&&null!=i&&e.gl.uniform1i(i,o.texData.slice.flatOffset),e.setInputMatrixTexture(o.texData.texture.texture,s,t)}const u=a.outShapeLocation;if(u)switch(o.shape.length){case 1:e.gl.uniform1iv(u,new Int32Array(o.shape));break;case 2:e.gl.uniform2iv(u,new Int32Array(o.shape));break;case 3:e.gl.uniform3iv(u,new Int32Array(o.shape));break;case 4:e.gl.uniform4iv(u,new Int32Array(o.shape))}if(a.outShapeStridesLocation){const t=n.computeStrides(o.shape);switch(o.shape.length){case 2:e.gl.uniform1iv(a.outShapeStridesLocation,new Int32Array(t));break;case 3:e.gl.uniform2iv(a.outShapeStridesLocation,new Int32Array(t));break;case 4:e.gl.uniform3iv(a.outShapeStridesLocation,new Int32Array(t))}}if(a.outTexShapeLocation&&e.gl.uniform2i(a.outTexShapeLocation,o.texData.texShape[0],o.texData.texShape[1]),a.program.customUniforms&&s)for(let t=0;t<a.program.customUniforms.length;++t){const n=a.program.customUniforms[t],r=a.customUniformLocations[t],o=s[t];if("float"===n.type)e.gl.uniform1fv(r,o);else if("vec2"===n.type)e.gl.uniform2fv(r,o);else if("vec3"===n.type)e.gl.uniform3fv(r,o);else if("vec4"===n.type)e.gl.uniform4fv(r,o);else if("int"===n.type)e.gl.uniform1iv(r,o);else if("ivec2"===n.type)e.gl.uniform2iv(r,o);else if("ivec3"===n.type)e.gl.uniform3iv(r,o);else{if("ivec4"!==n.type)throw Error(`uniform type ${n.type} is not supported yet.`);e.gl.uniform4iv(r,o)}}e.executeProgram()}(this.gpgpu,x,p,h,s),d.forEach((e=>this.disposeIntermediateTensorInfo(e))),m&&(g=this.endTimer(g),this.activeTimers.push({name:e.constructor.name,query:this.getQueryTime(g)}));const b=t().getNumber("WEBGL_FLUSH_THRESHOLD");if(b>0){const e=n.now();e-this.lastGlFlushTime>b&&(this.gpgpu.gl.flush(),this.lastGlFlushTime=e)}if(!t().getBool("WEBGL_LAZILY_UNPACK")&&c.isPacked&&!1===i){const e=this.unpackTensor(u);return this.disposeIntermediateTensorInfo(u),e}return u}compileAndRun(e,t,n,a,r=!1){n=n||t[0].dtype;return this.runWebGLProgram(e,t,n,a,r)}getAndSaveBinary(e,t){return e in this.binaryCache||(this.binaryCache[e]=t()),this.binaryCache[e]}getTextureManager(){return this.textureManager}dispose(){if(!this.disposed){if(!t().getBool("IS_TEST")){Object.keys(this.binaryCache).forEach((e=>{this.gpgpu.deleteProgram(this.binaryCache[e].webGLProgram),delete this.binaryCache[e]}))}this.textureManager.dispose(),null!=this.canvas&&"undefined"!=typeof HTMLCanvasElement&&this.canvas instanceof HTMLCanvasElement?this.canvas.remove():this.canvas=null,this.gpgpuCreatedLocally&&(this.gpgpu.program=null,this.gpgpu.dispose()),this.disposed=!0}}floatPrecision(){return null==this.floatPrecisionValue&&(this.floatPrecisionValue=i((()=>{if(!t().get("WEBGL_RENDER_FLOAT32_ENABLED")){const e=t().getBool("DEBUG");t().set("DEBUG",!1);const n=this.abs(m(1e-8)).dataSync()[0];if(t().set("DEBUG",e),n>0)return 32}return 16}))),this.floatPrecisionValue}epsilon(){return 32===this.floatPrecision()?1e-7:1e-4}uploadToGPU(e){const a=this.texData.get(e),{shape:r,dtype:o,values:s,texture:i,usage:l,isPacked:u}=a;if(null!=i)return;const c=null!=this.activeTimers;let d;c&&(d=n.now());let p=a.texShape;if(null==p&&(p=ba(r,u),a.texShape=p),null!=s){const e=ga(r);let i,l=p[1],h=p[0];const f=s instanceof Uint8Array||s instanceof Uint8ClampedArray;!u&&f||([l,h]=Wn(p[0],p[1])),i=u?new dr(e,f):new cr(e,f);const x=f?[h,l]:p,m=this.makeTensorInfo(x,o),g=this.texData.get(m.dataId);g.usage=f?Pn.PIXELS:Pn.UPLOAD,g.texShape=x,this.gpgpu.uploadDenseMatrixToTexture(this.getTexture(m.dataId),l,h,s);const b=[[h,l]],v=!0,C=this.runWebGLProgram(i,[m],o,b,v),$=this.texData.get(C.dataId);a.texShape=$.texShape,a.isPacked=$.isPacked,a.usage=$.usage,t().get("ENGINE_COMPILE_ONLY")?this.disposeData(C.dataId):(a.texture=$.texture,a.values=null,this.texData.delete(C.dataId)),this.disposeIntermediateTensorInfo(m),c&&(this.uploadWaitMs+=n.now()-d)}else{const e=this.acquireTexture(p,l,o,u);a.texture=e}}convertAndCacheOnCPU(e,t){const n=this.texData.get(e),{dtype:a}=n;return null!=t&&(n.values=function(e,t){if("float32"===t||"complex64"===t)return e;if("int32"===t||"bool"===t){const n="int32"===t?new Int32Array(e.length):new Uint8Array(e.length);for(let t=0;t<n.length;++t)n[t]=Math.round(e[t]);return n}throw new Error(`Unknown dtype ${t}`)}(t,a)),n.values}acquireTexture(e,t,n,a){if(this.numBytesInGPU+=this.computeBytes(e,n),!this.warnedAboutMemory&&this.numBytesInGPU>1024*this.numMBBeforeWarning*1024){const e=(this.numBytesInGPU/1024/1024).toFixed(2);this.warnedAboutMemory=!0,console.warn(`High memory usage in GPU: ${e} MB, most likely due to a memory leak`)}return this.textureManager.acquireTexture(e,t,a)}computeBytes(e,t){return e[0]*e[1]*n.bytesPerElement(t)}checkCompileCompletion(){for(const[,e]of Object.entries(this.binaryCache))this.checkCompletion_(e)}async checkCompileCompletionAsync(){const e=[];if(this.gpgpu.parallelCompilationExtension){for(const[,t]of Object.entries(this.binaryCache))e.push(this.checkCompletionAsync_(t));return Promise.all(e)}for(const[,t]of Object.entries(this.binaryCache)){const n=new Promise((e=>{try{this.checkCompletion_(t),e(!0)}catch(e){throw e}}));e.push(n)}return Promise.all(e)}async checkCompletionAsync_(e){return this.gpgpu.gl.getProgramParameter(e.webGLProgram,this.gpgpu.parallelCompilationExtension.COMPLETION_STATUS_KHR)?this.checkCompletion_(e):(await g(),this.checkCompletionAsync_(e))}checkCompletion_(e){if(!1===this.gpgpu.gl.getProgramParameter(e.webGLProgram,this.gpgpu.gl.LINK_STATUS)){if(console.log(this.gpgpu.gl.getProgramInfoLog(e.webGLProgram)),!1===this.gpgpu.gl.getShaderParameter(e.fragmentShader,this.gpgpu.gl.COMPILE_STATUS))throw qn(e.source,this.gpgpu.gl.getShaderInfoLog(e.fragmentShader)),new Error("Failed to compile fragment shader.");throw new Error("Failed to link vertex and fragment shaders.")}return!0}getUniformLocations(){for(const e of Object.values(this.binaryCache)){this.gpgpu.buildVao(e.webGLProgram);const{variablesLocations:t,customUniformLocations:n,infLoc:a,nanLoc:r,outShapeLocation:o,outShapeStridesLocation:s,outTexShapeLocation:i}=nr(this.gpgpu,e.program,e.webGLProgram);e.variablesLocations=t,e.customUniformLocations=n,e.infLoc=a,e.nanLoc=r,e.outShapeLocation=o,e.outShapeStridesLocation=s,e.outTexShapeLocation=i}}createTensorFromGPUData(e,t,n){e.channels=e.channels||"RGBA";const{texture:a,height:r,width:o,channels:s}=e,i=x().backend;if(!i.gpgpu.gl.isTexture(a))throw new Error("The texture is invalid. Also, please make sure the texture and the TFJS WebGL backend are using the same canvas. If you want to use your own custom canvas, you have to create and use the custom TFJS WebGL backend created from the canvas through 'new tf.MathBackendWebGL(customCanvas)'.");const l=i.writeTexture(a,t,n,r,o,s);return x().makeTensorFromDataId(l,t,n,i)}}Es.nextDataId=0;const As="4.22.0";function Os(){t().set("WEBGL_FORCE_F16_TEXTURES",!0)}a.isBrowser()&&b("webgl",(()=>new Es),2);const Fs={forceHalfFloat:Os};class _s{constructor(e,t,n){this.variableNames=["A","B"],this.outputShape=r.assertAndGetBroadcastShape(t,n),this.enableShapeUniforms=rr(this.outputShape.length),this.userCode=`\n      float binaryOperation(float a, float b) {\n        ${e}\n      }\n\n      void main() {\n        float a = getAAtOutCoords();\n        float b = getBAtOutCoords();\n        setOutput(binaryOperation(a, b));\n      }\n    `}}const Ds="\n  result.r = isNaN.r ? NAN : result.r;\n  result.g = isNaN.g ? NAN : result.g;\n  result.b = isNaN.b ? NAN : result.b;\n  result.a = isNaN.a ? NAN : result.a;\n";class Ps{constructor(e,t,a,o=!1){this.variableNames=["A","B"],this.supportsBroadcasting=!0,this.packedInputs=!0,this.packedOutput=!0,this.outputShape=r.assertAndGetBroadcastShape(t,a);const s=this.outputShape.length;this.enableShapeUniforms=rr(s);let i="";if(o)if(0===s||1===n.sizeFromShape(this.outputShape))i="\n          result.y = 0.;\n          result.z = 0.;\n          result.w = 0.;\n        ";else{if(i=`\n          ${Za(s)} coords = getOutputCoords();\n        `,1===s)this.enableShapeUniforms?i+="\n            result.y = (coords + 1) >= outShape ? 0. : result.y;\n            result.z = 0.;\n            result.w = 0.;\n          ":i+=`\n            result.y = (coords + 1) >= ${this.outputShape[0]} ? 0. : result.y;\n            result.z = 0.;\n            result.w = 0.;\n          `;else{const e=ms("coords",s);this.enableShapeUniforms?i+=`\n            bool nextRowOutOfBounds =\n              (${e[s-2]} + 1) >= outShape[${s} - 2];\n            bool nextColOutOfBounds =\n              (${e[s-1]} + 1) >= outShape[${s} - 1];\n            result.y = nextColOutOfBounds ? 0. : result.y;\n            result.z = nextRowOutOfBounds ? 0. : result.z;\n            result.w = nextColOutOfBounds || nextRowOutOfBounds ? 0. : result.w;\n          `:i+=`\n            bool nextRowOutOfBounds =\n              (${e[s-2]} + 1) >= ${this.outputShape[s-2]};\n            bool nextColOutOfBounds =\n              (${e[s-1]} + 1) >= ${this.outputShape[s-1]};\n            result.y = nextColOutOfBounds ? 0. : result.y;\n            result.z = nextRowOutOfBounds ? 0. : result.z;\n            result.w = nextColOutOfBounds || nextRowOutOfBounds ? 0. : result.w;\n          `}}this.userCode=`\n      vec4 binaryOperation(vec4 a, vec4 b) {\n        ${e}\n      }\n\n      void main() {\n        vec4 a = getAAtOutCoords();\n        vec4 b = getBAtOutCoords();\n\n        vec4 result = binaryOperation(a, b);\n        ${i}\n\n        setOutput(result);\n      }\n    `}}function Ls(e){const{inputs:t,backend:n}=e,{x:a}=t;return n.incRef(a.dataId),{dataId:a.dataId,shape:a.shape,dtype:a.dtype}}const Bs={kernelName:v,backendName:"webgl",kernelFunc:Ls};function Vs(e){const{inputs:t,backend:n}=e,{real:a,imag:r}=t,o=n.makeTensorInfo(a.shape,"complex64"),s=n.texData.get(o.dataId),i=Ls({inputs:{x:a},backend:n}),l=Ls({inputs:{x:r},backend:n});return s.complexTensorInfos={real:i,imag:l},o}const Ws={kernelName:C,backendName:"webgl",kernelFunc:Vs},Us="return (a < 0.) ? b * a : a;",Ms="\n  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));\n  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);\n";const Gs={kernelName:$,backendName:"webgl",kernelFunc:function(e){const{inputs:a,backend:r,attrs:o}=e,{x:s}=a,{alpha:i}=o,l=r.makeTensorInfo([],"float32",n.createScalarValue(i,"float32")),u=t().getBool("WEBGL_PACK_BINARY_OPERATIONS")?new Ps(Ms,s.shape,l.shape):new _s(Us,s.shape,l.shape),c=r.runWebGLProgram(u,[s,l],"float32");return r.disposeIntermediateTensorInfo(l),c}},zs="return (a < 0.) ? b * a : a;",Xs="\n  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));\n  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);\n";const Hs={kernelName:y,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a}=e,{x:r,alpha:o}=n,s=t().getBool("WEBGL_PACK_BINARY_OPERATIONS")?new Ps(Xs,r.shape,o.shape):new _s(zs,r.shape,o.shape);return a.runWebGLProgram(s,[r,o],"float32")}};function js({opSnippet:e,packedOpSnippet:n,cpuKernelImpl:a,dtype:r}){return({inputs:o,backend:s})=>{const{x:i}=o,l=s,u=r||i.dtype;if(l.shouldExecuteOnCPU([i])&&null!=a){const e=l.texData.get(i.dataId),t=a(e.values,u);return l.makeTensorInfo(i.shape,u,t)}let c;return c=t().getBool("WEBGL_PACK_UNARY_OPERATIONS")&&null!=n?new Ss(i.shape,n):new Is(i.shape,e),l.runWebGLProgram(c,[i],u)}}function Ks({opSnippet:e,packedOpSnippet:n,checkOutOfBounds:a=!1,supportsComplex:o=!1,cpuKernelImpl:i,dtype:l}){return({inputs:u,backend:c})=>{const{a:d,b:p}=u,h=c;if(o&&"complex64"===d.dtype){const t=h.texData.get(d.dataId),n=h.texData.get(p.dataId),[a,r]=[[t.complexTensorInfos.real,n.complexTensorInfos.real],[t.complexTensorInfos.imag,n.complexTensorInfos.imag]].map((t=>{const[n,a]=t,r={dataId:n.dataId,dtype:n.dtype,shape:d.shape},o={dataId:a.dataId,dtype:a.dtype,shape:p.shape},i=new _s(e,d.shape,p.shape);return h.runWebGLProgram(i,[r,o],s(n.dtype,a.dtype))})),o=Vs({inputs:{real:a,imag:r},backend:h});return h.disposeIntermediateTensorInfo(a),h.disposeIntermediateTensorInfo(r),o}const f=l||s(d.dtype,p.dtype);if(("string"===d.dtype||"string"===p.dtype||h.shouldExecuteOnCPU([d,p]))&&null!=i){const e=h.texData.get(d.dataId).values,t=h.texData.get(p.dataId).values,n="string"===d.dtype?r.fromUint8ToStringArray(e):e,a="string"===d.dtype?r.fromUint8ToStringArray(t):t,[o,s]=i(d.shape,p.shape,n,a,f),l=h.makeTensorInfo(s,f);return h.texData.get(l.dataId).values=o,l}let x;return x=t().getBool("WEBGL_PACK_BINARY_OPERATIONS")&&null!=n?new Ps(n,d.shape,p.shape,a):new _s(e,d.shape,p.shape),h.runWebGLProgram(x,[d,p],f)}}function qs(e,t=!1){if("linear"===e)return"return x;";if("relu"===e)return t?"\n  vec4 result = x * vec4(greaterThanEqual(x, vec4(0.0)));\n  bvec4 isNaN = isnan(x);\n\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n":"if (isnan(x)) return x;\n  return (x < 0.0) ? 0.0 : x;\n";if("elu"===e)return t?"\n  vec4 result;\n\n  result.r = (x.r >= 0.0) ? x.r : (exp(x.r) - 1.0);\n  result.g = (x.g >= 0.0) ? x.g : (exp(x.g) - 1.0);\n  result.b = (x.b >= 0.0) ? x.b : (exp(x.b) - 1.0);\n  result.a = (x.a >= 0.0) ? x.a : (exp(x.a) - 1.0);\n\n  return result;\n":"return (x >= 0.0) ? x : (exp(x) - 1.0);";if("relu6"===e)return t?"\n  vec4 result = min(x, vec4(6.)) * vec4(greaterThanEqual(x, vec4(0.0)));\n  bvec4 isNaN = isnan(x);\n\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n":"if (isnan(x)) return x;\n  return (x < 0.0) ? 0.0 : min(6.0, x);\n";if("prelu"===e)return t?Xs:zs;if("leakyrelu"===e)return t?Ms:Us;if("sigmoid"===e)return"return 1.0 / (1.0 + exp(-1.0 * x));";throw new Error(`Activation ${e} has not been implemented for the WebGL backend.`)}class Ys{constructor(e,t,n,a=!1,r=!1,o=!1,s=null,i=!1,l=!1){this.variableNames=["matrixA","matrixB"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=n,this.enableShapeUniforms=rr(this.outputShape.length);const u=a?e[1]:e[2],c=Math.ceil(u/2),d=a?"i * 2, rc.y":"rc.y, i * 2",p=r?"rc.z, i * 2":"i * 2, rc.z",h=a?["a.xxyy","a.zzww"]:["a.xxzz","a.yyww"],f=r?["b.xzxz","b.ywyw"]:["b.xyxy","b.zwzw"];let x="",m="";s&&(x=i?`vec4 activation(vec4 a) {\n          vec4 b = getPreluActivationWeightsAtOutCoords();\n          ${s}\n        }`:l?`vec4 activation(vec4 a) {\n          vec4 b = getLeakyreluAlphaAtOutCoords();\n          ${s}\n        }`:`vec4 activation(vec4 x) {\n          ${s}\n        }`,m="result = activation(result);");const g=o?"result += getBiasAtOutCoords();":"";o&&this.variableNames.push("bias"),i&&this.variableNames.push("preluActivationWeights"),l&&this.variableNames.push("leakyreluAlpha");let b="rc.x",v="rc.x";e[0]<t[0]?b=`imod(rc.x, ${e[0]})`:t[0]<e[0]&&(v=`imod(rc.x, ${t[0]})`),this.userCode=`\n      ${x}\n      // Don't use uniform for sharedDimensionPacked for performance.\n      const float sharedDimension = ${c}.0;\n\n      vec4 dot2x2ARowBCol(ivec3 rc) {\n        vec4 result = vec4(0);\n        int batchA = ${b};\n        int batchB = ${v};\n        for (int i = 0; i < ${c}; i++) {\n          vec4 a = getMatrixA(batchA, ${d});\n          vec4 b = getMatrixB(batchB, ${p});\n\n          // These swizzled products need to be separately added.\n          // See: https://github.com/tensorflow/tfjs/issues/1735\n          result += (${h[0]} * ${f[0]});\n          result += (${h[1]} * ${f[1]});\n        }\n        return result;\n      }\n\n      void main() {\n        ivec3 rc = getOutputCoords();\n        vec4 result = dot2x2ARowBCol(rc);\n\n        ${g}\n\n        ${m}\n\n        setOutput(result);\n      }\n    `}}const Qs="return areal * breal - aimag * bimag;",Zs="return areal * bimag + aimag * breal;";class Js{constructor(e,t,n){this.variableNames=["AReal","AImag","BReal","BImag"],this.outputShape=r.assertAndGetBroadcastShape(t,n),this.userCode=`\n      float binaryOpComplex(\n          float areal, float aimag, float breal, float bimag) {\n        ${e}\n      }\n\n      void main() {\n        float areal = getARealAtOutCoords();\n        float aimag = getAImagAtOutCoords();\n        float breal = getBRealAtOutCoords();\n        float bimag = getBImagAtOutCoords();\n        setOutput(binaryOpComplex(areal, aimag, breal, bimag));\n      }\n    `}}const ei="return a * b;";function ti(e){const{inputs:n,backend:a}=e,{a:o,b:s}=n,i=r.upcastType(o.dtype,s.dtype);if("complex64"===o.dtype){const e=a.texData.get(o.dataId),t=a.texData.get(s.dataId),n=new Js(Qs,o.shape,s.shape),r=new Js(Zs,o.shape,s.shape),i=[{dataId:e.complexTensorInfos.real.dataId,dtype:e.complexTensorInfos.real.dtype,shape:o.shape},{dataId:e.complexTensorInfos.imag.dataId,dtype:e.complexTensorInfos.imag.dtype,shape:o.shape},{dataId:t.complexTensorInfos.real.dataId,dtype:t.complexTensorInfos.real.dtype,shape:s.shape},{dataId:t.complexTensorInfos.imag.dataId,dtype:t.complexTensorInfos.imag.dtype,shape:s.shape}],l=a.runWebGLProgram(n,i,"float32"),u=a.runWebGLProgram(r,i,"float32"),c=Vs({inputs:{real:l,imag:u},backend:a});return a.disposeIntermediateTensorInfo(l),a.disposeIntermediateTensorInfo(u),c}if(a.shouldExecuteOnCPU([o,s])){const e=a.texData.get(o.dataId),t=a.texData.get(s.dataId),[n,r]=Mo(o.shape,s.shape,e.values,t.values,i),l=a.makeTensorInfo(r,i);return a.texData.get(l.dataId).values=n,l}let l;return l=t().getBool("WEBGL_PACK_BINARY_OPERATIONS")?new Ps(ei,o.shape,s.shape):new _s(ei,o.shape,s.shape),a.runWebGLProgram(l,[o,s],i)}const ni={kernelName:I,backendName:"webgl",kernelFunc:ti};function ai(e){const{inputs:t,backend:a,attrs:r}=e,{x:o}=t,{shape:s}=r,i=a,l=n.sizeFromShape(o.shape),u=n.inferFromImplicitShape(s,l),c=n.sizeFromShape(u);n.assert(l===c,(()=>`The new shape (${u}) has ${c} elements and the old shape (${o.shape}) has ${l} elements. The new shape and old shape must have the same number of elements.`));const d=i.texData.get(o.dataId);return!d.isPacked||Ca(o.shape,u)||null!==d.texture&&Ca(d.shape,u)?(i.incRef(o.dataId),{dataId:o.dataId,shape:u,dtype:o.dtype}):function(e,t,n){const a=[xa(e.shape),...ma(e.shape)],r={dtype:e.dtype,shape:a,dataId:e.dataId},o=[xa(t),...ma(t)],s=new bs(o,a),i=[a],l=n.runWebGLProgram(s,[r],e.dtype,i,!0);return{dataId:l.dataId,shape:t,dtype:l.dtype}}(o,u,i)}const ri={kernelName:w,backendName:"webgl",kernelFunc:ai};class oi{constructor(e,t){this.variableNames=["x"];const{windowSize:a,batchSize:r,inSize:o,outSize:s}=e;this.outputShape=[r,s];const i=4*Math.floor(a/4),l=a%4;let u="sumValue += dot(values, ones);";if(null!=t){const e=1/t;u=`sumValue += dot(values * ${n.isInt(e)?e.toPrecision(2):e}, ones);`}let c="";o%a>0&&(c=`\n        if (inIdx < 0 || inIdx >= ${o}) {\n          return 0.0;\n        }\n      `),this.userCode=`\n      const vec4 ones = vec4(1.0, 1.0, 1.0, 1.0);\n\n      float getValue(int batch, int inIdx) {\n        ${c}\n        return getX(batch, inIdx);\n      }\n\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int batch = coords[0];\n        int outIdx = coords[1];\n        int inOffset = outIdx * ${a};\n\n        float sumValue = 0.0;\n\n        for (int i = 0; i < ${i}; i += 4) {\n          int inIdx = inOffset + i;\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            getValue(batch, inIdx + 2),\n            getValue(batch, inIdx + 3)\n          );\n\n          ${u}\n        }\n\n        int inIdx = inOffset + ${i};\n        if (${1===l}) {\n          vec4 values = vec4(getValue(batch, inIdx), 0.0, 0.0, 0.0);\n\n          ${u}\n        } else if (${2===l}) {\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1), 0.0, 0.0);\n\n          ${u}\n        } else if (${3===l}) {\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            getValue(batch, inIdx + 2), 0.0);\n\n          ${u}\n        }\n        setOutput(sumValue);\n      }\n    `}}class si{constructor(e,t){this.variableNames=["x"];const{windowSize:n,batchSize:a,inSize:r,outSize:o}=e;this.outputShape=[a,o];let s="0.0",i="";"prod"===t?s="1.0":"min"===t?(s="1.0 / 1e-20",i="min"):"max"===t&&(s="-1.0 / 1e-20",i="max");let l=`${t}(${t}(${t}(minMaxValue[0], minMaxValue[1]), minMaxValue[2]), minMaxValue[3])`;"sum"===t?l="sumValue":"prod"===t?l="prodValue":"all"===t?l="allValue":"any"===t&&(l="anyValue");const u=4*Math.floor(n/4),c=n%4;let d=`\n      if (${"sum"===t}) {\n        sumValue += dot(values, ones);\n      } else if (${"prod"===t}) {\n        vec2 tmp = vec2(values[0], values[1]) * vec2(values[2], values[3]);\n        prodValue *= tmp[0] * tmp[1];\n      } else {\n        minMaxValue = ${i}(values, minMaxValue);\n        if (${"min"===t} || ${"max"===t}) {\n          minMaxValue = ${i}(values, minMaxValue);\n          bvec4 isNaN = isnan(values);\n          if (isNaN.r || isNaN.g || isNaN.b || isNaN.a) {\n            minMaxValue = vec4(NAN);\n          }\n        }\n      }\n    `,p="vec4";"all"===t?(s="1.0",d="\n        bool reducedAllValue = all(values);\n        float floatedReducedAllValue = float(reducedAllValue);\n        allValue = float(allValue >= 1.0 && floatedReducedAllValue >= 1.0);\n      ",p="bvec4"):"any"===t&&(s="0.0",d="\n        bool reducedAnyValue = any(values);\n        float floatedReducedAnyValue = float(reducedAnyValue);\n        anyValue = float(anyValue >= 1.0 || floatedReducedAnyValue >= 1.0);\n      ",p="bvec4");let h="";r%n>0&&(h=`\n        if (inIdx < 0 || inIdx >= ${r}) {\n          return initializationValue;\n        }\n      `),this.userCode=`\n      const float initializationValue = ${s};\n      const vec4 ones = vec4(1.0, 1.0, 1.0, 1.0);\n\n      float getValue(int batch, int inIdx) {\n        ${h}\n        return getX(batch, inIdx);\n      }\n\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int batch = coords[0];\n        int outIdx = coords[1];\n        int inOffset = outIdx * ${n};\n\n        vec4 minMaxValue = vec4(${s});\n        float prodValue = 1.0;\n        float sumValue = 0.0;\n        float allValue = 1.0;\n        float anyValue = 0.0;\n\n        for (int i = 0; i < ${u}; i += 4) {\n          int inIdx = inOffset + i;\n          ${p} values = ${p}(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            getValue(batch, inIdx + 2),\n            getValue(batch, inIdx + 3)\n          );\n\n          ${d}\n        }\n\n        int inIdx = inOffset + ${u};\n        if (${1===c}) {\n          ${p} values = ${p}(\n            getValue(batch, inIdx),\n            initializationValue,\n            initializationValue,\n            initializationValue\n          );\n\n          ${d}\n        } else if (${2===c}) {\n          ${p} values = ${p}(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            initializationValue,\n            initializationValue\n          );\n\n          ${d}\n        } else if (${3===c}) {\n          ${p} values = ${p}(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            getValue(batch, inIdx + 2),\n            initializationValue\n          );\n\n          ${d}\n        }\n        setOutput(${l});\n      }\n    `}}function ii(e,t,n,a){const o=function(e){const t=[];for(;0===t.length||1!==t[t.length-1].outSize;){const n=t.length?t[t.length-1].outSize:e[1],a=r.computeOptimalWindowSize(n);t.push({inSize:n,windowSize:a,outSize:Math.ceil(n/a)})}return t}(e.shape);let s=e;for(let r=0;r<o.length;r++){const{inSize:i,windowSize:l,outSize:u}=o[r];let c,d;c="mean"===n?0===r?new oi({windowSize:l,inSize:i,batchSize:e.shape[0],outSize:u},i):new oi({windowSize:l,inSize:i,batchSize:e.shape[0],outSize:u}):new si({windowSize:l,inSize:i,batchSize:e.shape[0],outSize:u},n),d=s,s=a.runWebGLProgram(c,[s],t),d.dataId!==e.dataId&&a.disposeIntermediateTensorInfo(d)}return s}class li{constructor(e,t){this.variableNames=["A"];const n=new Array(e.length);for(let a=0;a<n.length;a++)n[a]=e[t[a]];this.outputShape=n,this.rank=n.length;const a=Za(this.rank),r=function(e){const t=e.length;if(t>6)throw Error(`Transpose for rank ${t} is not yet supported`);const n=["resRC.x","resRC.y","resRC.z","resRC.w","resRC.u","resRC.v"],a=new Array(t);for(let t=0;t<e.length;t++)a[e[t]]=n[t];return a.join()}(t);this.userCode=`\n    void main() {\n      ${a} resRC = getOutputCoords();\n      setOutput(getA(${r}));\n    }\n    `}}class ui{constructor(e,t){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0;const n=new Array(e.length);for(let a=0;a<n.length;a++)n[a]=e[t[a]];if(this.outputShape=n,this.rank=n.length,this.rank>6)throw Error(`Packed transpose for rank ${this.rank} is not yet supported.`);const a=Za(this.rank),r=xs("rc",this.rank),o=new Array(this.rank);for(let e=0;e<t.length;e++)o[t[e]]=r[e];const s=`vec2(${o.slice(-2).join()})`,i=`++${r[this.rank-1]} < ${n[this.rank-1]}`,l=`getChannel(getA(${o.join()}), ${s})`;this.userCode=`\n    void main() {\n      ${a} rc = getOutputCoords();\n      vec4 result = vec4(0.);\n      result[0] = ${l};\n      if(${i}) {\n        result[1] = ${l};\n      }\n      --${r[this.rank-1]};\n      if(++${r[this.rank-2]} < ${n[this.rank-2]}) {\n        result[2] = ${l};\n        if(${i}) {\n          result[3] = ${l};\n        }\n      }\n      setOutput(result);\n    }\n    `}}function ci(e,n,a){const r=t().getBool("WEBGL_PACK_ARRAY_OPERATIONS")?new ui(e.shape,n):new li(e.shape,n);return a.runWebGLProgram(r,[e],e.dtype)}function di(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i,keepDims:l}=o;return function(e,t,a,o){const s=t,i=e.shape.length,l=n.parseAxisParam(s,e.shape);let u=l;const c=r.getAxesPermutation(u,i),d=null!=c;let p=e;d&&(p=ci(e,c,o),u=r.getInnerMostAxes(u.length,i)),r.assertAxesAreInnerMostDims("sum",u,i);const[h,f]=r.computeOutAndReduceShapes(p.shape,u);let x=h;a&&(x=r.expandShapeToKeepDim(h,l));const m=n.sizeFromShape(f),g=ai({inputs:{x:p},attrs:{shape:[n.sizeFromShape(e.shape)/m,m]},backend:o}),b=ii(g,S(e.dtype),"sum",o),v=ai({inputs:{x:b},attrs:{shape:x},backend:o});return o.disposeIntermediateTensorInfo(g),o.disposeIntermediateTensorInfo(b),d&&o.disposeIntermediateTensorInfo(p),v}(s,i,l,a)}const pi={kernelName:R,backendName:"webgl",kernelFunc:di};function hi(e){const{inputs:t,backend:n,attrs:a}=e,{x:r}=t,{perm:o}=a,s=n,i=r.shape.length,l=new Array(i);for(let e=0;e<l.length;e++)l[e]=r.shape[o[e]];let u;if(s.shouldExecuteOnCPU([r])){const e=s.texData.get(r.dataId).values,t=hs(e,r.shape,r.dtype,o,l);u=s.makeTensorInfo(l,r.dtype);s.texData.get(u.dataId).values=t}else u=ci(r,o,s);return u}const fi={kernelName:T,backendName:"webgl",kernelFunc:hi};function xi({a:e,b:t,transposeA:a,transposeB:r,backend:o,bias:i=null,preluActivationWeights:l=null,leakyreluAlpha:u=0,activation:c=null}){const d=e.shape.length,p=t.shape.length,h=a?e.shape[d-2]:e.shape[d-1],f=r?t.shape[p-1]:t.shape[p-2],x=a?e.shape[d-1]:e.shape[d-2],m=r?t.shape[p-2]:t.shape[p-1],g=e.shape.slice(0,-2),b=t.shape.slice(0,-2),v=n.sizeFromShape(g),C=n.sizeFromShape(b),$=k.assertAndGetBroadcastShape(e.shape.slice(0,-2),t.shape.slice(0,-2)).concat([x,m]);n.assert(h===f,(()=>`Error in matMul: inner shapes (${h}) and (${f}) of Tensors with shapes ${e.shape} and ${t.shape} and transposeA=${a} and transposeB=${r} must match.`));const y=a?[v,h,x]:[v,x,h],I=r?[C,m,f]:[C,f,m],w=ai({inputs:{x:e},backend:o,attrs:{shape:y}}),S=ai({inputs:{x:t},backend:o,attrs:{shape:I}}),R=[w,S],T=Math.max(v,C),N=a?w.shape[1]:w.shape[2],E=null!=i,A=null!=l,O="leakyrelu"===c,F=null!=c?qs(c,!0):null;let _;if((1===x||1===m)&&N>1e3&&!1===(E||A||O||null!=F)){let e=w,t=S;a&&(e=hi({inputs:{x:w},backend:o,attrs:{perm:[0,2,1]}}),R.push(e)),r&&(t=hi({inputs:{x:S},backend:o,attrs:{perm:[0,2,1]}}),R.push(t));const n=1===m;let s=e;1!==m&&(s=ai({inputs:{x:e},backend:o,attrs:{shape:[T,N,1]}}),R.push(s));const i=1===m?2:1;let l=t;n&&(l=ai({inputs:{x:t},backend:o,attrs:{shape:[T,1,N]}}),R.push(l));const u=ti({inputs:{a:s,b:l},backend:o});_=di({inputs:{x:u},backend:o,attrs:{axis:i,keepDims:!0}}),R.push(u)}else{const c=s(e.dtype,t.dtype),d=new Ys(y,I,[T,x,m],a,r,E,F,A,O),p=[w,S];if(null!=i&&p.push(i),A&&p.push(l),O){const e=o.makeTensorInfo([],"float32",n.createScalarValue(u,"float32"));p.push(e),R.push(e)}_=o.runWebGLProgram(d,p,c)}const D=ai({inputs:{x:_},backend:o,attrs:{shape:$}});R.push(_);for(const e of R)o.disposeIntermediateTensorInfo(e);return D}const mi={kernelName:N,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{a:r,b:o,bias:s,preluActivationWeights:i}=t,{transposeA:l,transposeB:u,activation:c,leakyreluAlpha:d}=a;return xi({a:r,b:o,transposeA:l,transposeB:u,backend:n,bias:s,preluActivationWeights:i,leakyreluAlpha:d,activation:c})}},gi="return abs(x);";const bi={kernelName:E,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a}=e,{x:r}=n;if(a.shouldExecuteOnCPU([r])&&"complex64"!==r.dtype){const e=a.texData.get(r.dataId),t=Jo(e.values);return a.makeTensorInfo(r.shape,r.dtype,t)}let o;return o=t().getBool("WEBGL_PACK_UNARY_OPERATIONS")?new Ss(r.shape,gi):new Is(r.shape,gi),a.runWebGLProgram(o,[r],r.dtype)}},vi={kernelName:A,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  if (abs(x) > 1.) {\n    return NAN;\n  }\n  return acos(x);\n"})},Ci={kernelName:O,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  if (x < 1.0) return NAN;\nreturn log(x + sqrt(x * x - 1.0));"})},$i="return a + b;",yi={kernelName:F,backendName:"webgl",kernelFunc:Ks({opSnippet:$i,packedOpSnippet:$i,supportsComplex:!0,cpuKernelImpl:Co})};class Ii{constructor(e,t){this.outputShape=[],this.outputShape=e,this.variableNames=t.map(((e,t)=>`T${t}`));const n=[];this.variableNames.forEach((e=>{n.push(`float v${e} = get${e}AtOutCoords();`)}));const a=this.variableNames.map((e=>`v${e}`)).join(" + ");this.userCode=`\n      void main() {\n        ${n.join("\n        ")}\n\n        float result = ${a};\n        setOutput(result);\n      }\n    `}}class wi{constructor(e,t){this.outputShape=[],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=e,this.variableNames=t.map(((e,t)=>`T${t}`));const n=[];this.variableNames.forEach((e=>{n.push(`vec4 v${e} = get${e}AtOutCoords();`)}));const a=this.variableNames.map((e=>`v${e}`)).join(" + ");this.userCode=`\n      void main() {\n        ${n.join("\n        ")}\n\n        vec4 result = ${a};\n        setOutput(result);\n      }\n    `}}const Si={kernelName:_,backendName:"webgl",kernelFunc:function e(n){const{inputs:a,backend:r}=n,o=a;if(1===o.length)return Ls({inputs:{x:o[0]},backend:r});if(o.length>t().getNumber("WEBGL_MAX_TEXTURES_IN_SHADER")){const t=Math.floor(o.length/2),n=e({inputs:o.slice(0,t),backend:r}),a=e({inputs:o.slice(t),backend:r});return e({inputs:[n,a],backend:r})}const i=o.map((e=>e.dtype)).reduce(((e,t)=>s(e,t))),l=o.map((e=>e.shape)),u=t().getBool("WEBGL_PACK")?new wi(o[0].shape,l):new Ii(o[0].shape,l);return r.runWebGLProgram(u,o,i)}};const Ri={kernelName:D,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i,keepDims:l}=o,u=s.shape.length,c=n.parseAxisParam(i,s.shape);let d=c;const p=r.getAxesPermutation(d,u);let h=s;null!=p&&(h=hi({inputs:{x:s},backend:a,attrs:{perm:p}}),d=r.getInnerMostAxes(d.length,u)),r.assertAxesAreInnerMostDims("all",d,u);const[f,x]=r.computeOutAndReduceShapes(h.shape,d),m=ai({inputs:{x:h},backend:a,attrs:{shape:[-1,n.sizeFromShape(x)]}}),g=ii(m,m.dtype,"all",a);let b;if(l){b=ai({inputs:{x:g},backend:a,attrs:{shape:r.expandShapeToKeepDim(f,c)}})}else b=ai({inputs:{x:g},backend:a,attrs:{shape:f}});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(g),null!=p&&a.disposeIntermediateTensorInfo(h),b}};const Ti={kernelName:P,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i,keepDims:l}=o,u=s.shape.length,c=n.parseAxisParam(i,s.shape);let d=c;const p=r.getAxesPermutation(d,u);let h=s;null!=p&&(h=hi({inputs:{x:s},backend:a,attrs:{perm:p}}),d=r.getInnerMostAxes(d.length,u)),r.assertAxesAreInnerMostDims("any",d,u);const[f,x]=r.computeOutAndReduceShapes(h.shape,d),m=ai({inputs:{x:h},backend:a,attrs:{shape:[-1,n.sizeFromShape(x)]}}),g=ii(m,m.dtype,"any",a);let b;if(l){b=ai({inputs:{x:g},backend:a,attrs:{shape:r.expandShapeToKeepDim(f,c)}})}else b=ai({inputs:{x:g},backend:a,attrs:{shape:f}});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(g),null!=p&&a.disposeIntermediateTensorInfo(h),b}};class ki{constructor(e,t,n){this.variableNames=["A"];const{windowSize:a,batchSize:r,outSize:o}=e;n||this.variableNames.push("bestIndicesA"),this.outputShape=[r,o];const s="max"===t?">":"<",i=n?"inOffset + i;":"round(getBestIndicesA(batch, inOffset + i));";this.userCode=`\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int batch = coords[0];\n        int outIdx = coords[1];\n        int inOffset = outIdx * ${a};\n\n        int bestIndex = inOffset;\n        float bestValue = getA(batch, bestIndex);\n\n        for (int i = 0; i < ${a}; i++) {\n          int inIdx = ${i};\n          float candidate = getA(batch, inIdx);\n          if (candidate ${s} bestValue) {\n            bestValue = candidate;\n            bestIndex = inIdx;\n          }\n        }\n        setOutput(float(bestIndex));\n      }\n    `}}class Ni{constructor(e,t,a,r){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,n.assert(e.length>2,(()=>`Packed arg${a.charAt(0).toUpperCase()+a.slice(1)} supports only inputs with rank above 2.`));const o=e[e.length-1],s=Math.ceil(o/t);this.outputShape=e.slice(0,-1),s>1&&this.outputShape.push(s),r||this.variableNames.push("bestIndicesA");const i=this.outputShape,l=i.length,u=Za(l),c=ms("coords",l);let d,p;if(1===s){p=l+1;const e=Za(p);d=`\n        ${e} sourceLocR = ${e}(${c.join()}, 0);\n        ++${c[l-1]};\n        ${e} sourceLocG = ${e}(${c.join()}, 0);\n        ++${c[l-2]};\n        ${e} sourceLocA = ${e}(${c.join()}, 0);\n        --${c[l-1]};\n        ${e} sourceLocB = ${e}(${c.join()}, 0);\n        --${c[l-2]};`}else p=l,d=`\n        ${u} sourceLocR = coords;\n        ++${c[l-1]};\n        ${u} sourceLocG = coords;\n        ++${c[l-2]};\n        ${u} sourceLocA = coords;\n        --${c[l-1]};\n        ${u} sourceLocB = coords;\n        --${c[l-2]};`;const h=["x","y","z","w","u","v"].slice(0,p),f="."+h[p-1],x=h.map((e=>"int "+e)),m=ms("sourceLocR",p-1).concat("inIdx.r"),g=ms("sourceLocG",p-1).concat("inIdx.g"),b=ms("sourceLocB",p-1).concat("inIdx.b"),v=ms("sourceLocA",p-1).concat("inIdx.a"),C="max"===a?"greaterThan":"lessThan",$=r?"":`\n          inIdx = round(vec4(getBestIndicesAChannel(${m.join()}),\n                             getBestIndicesAChannel(${g.join()}),\n                             getBestIndicesAChannel(${b.join()}),\n                             getBestIndicesAChannel(${v.join()})));`,y=`vec4(\n            getAChannel(${m.join()}),\n            hasNextCol ? getAChannel(${g.join()}) : 0.,\n            hasNextRow ? getAChannel(${b.join()}) : 0.,\n            hasNextRow && hasNextCol ? getAChannel(${v.join()}) : 0.)`,I=r?"":`\n      float getBestIndicesAChannel(${x.join()}) {\n        return getChannel(getBestIndicesA(${h.join()}),\n                                          vec2(${h.slice(-2).join()}));\n      }`;this.userCode=`\n      float getAChannel(${x.join()}) {\n        return getChannel(getA(${h.join()}),\n                               vec2(${h.slice(-2).join()}));\n      }\n      ${I}\n      void main() {\n        ${u} coords = getOutputCoords();\n        bool hasNextCol = ${c[l-1]} < ${i[l-1]-1};\n        bool hasNextRow = ${c[l-2]} < ${i[l-2]-1};\n        ${d}\n        ivec4 srcIdx = ivec4(sourceLocR${f}, sourceLocG${f},\n          sourceLocB${f}, sourceLocA${f}) * ${t};\n        ivec4 inIdx = srcIdx;\n        vec4 bestIndex = vec4(inIdx);\n        vec4 bestValue = ${y};\n\n        for (int i = 0; i < ${t}; i++) {\n          inIdx = srcIdx;\n          ${$}\n          vec4 candidate = ${y};\n          bvec4 nan = isnan(candidate);\n          bvec4 replace = bvec4(\n            vec4(${C}(candidate, bestValue)) * (vec4(1.0) - vec4(nan)));\n\n          bestValue = vec4(replace.x  ? candidate.x : bestValue.x,\n                           replace.y  ? candidate.y : bestValue.y,\n                           replace.z  ? candidate.z : bestValue.z,\n                           replace.w  ? candidate.w : bestValue.w);\n          bestIndex = mix(bestIndex, vec4(inIdx), vec4(replace));\n          srcIdx++;\n        }\n        setOutput(bestIndex);\n      }\n    `}}function Ei(e,t,n,a=null){let o=t.shape[0],s=t.shape[1];null!=a&&(o=a.shape[0],s=a.shape[1]);const i=r.computeOptimalWindowSize(s),l={windowSize:i,inSize:s,batchSize:o,outSize:Math.ceil(s/i)},u=new ki(l,n,null==a),c=[t];null!=a&&c.push(a);const d=e.runWebGLProgram(u,c,"int32");if(1===d.shape[1])return d;const p=Ei(e,t,n,d);return e.disposeIntermediateTensorInfo(d),p}function Ai(e,t,n,a=null){const o=null!=a?a.shape:t.shape,s=o[o.length-1],i=r.computeOptimalWindowSize(s),l=new Ni(o,i,n,null==a),u=null==a?[t]:[t,a],c=e.runWebGLProgram(l,u,"int32");if(c.shape.length===t.shape.length){const a=Ai(e,t,n,c);return e.disposeIntermediateTensorInfo(c),a}return c}function Oi(e,a,o,s){const i=[o];if(r.assertAxesAreInnerMostDims("arg"+s.charAt(0).toUpperCase()+s.slice(1),i,a.shape.length),!t().getBool("WEBGL_PACK_REDUCE")||a.shape.length<=2){const t=[],o=e.texData.get(a.dataId);let l=a;null!==o&&o.isPacked&&(l=e.unpackTensor(a),t.push(l));const[u,c]=r.computeOutAndReduceShapes(l.shape,i),d=n.sizeFromShape(c),p=ai({inputs:{x:l},backend:e,attrs:{shape:[-1,d]}});t.push(p);const h=Ei(e,p,s);t.push(h);const f=ai({inputs:{x:h},backend:e,attrs:{shape:u}});return t.forEach((t=>e.disposeIntermediateTensorInfo(t))),f}return Ai(e,a,s)}const Fi={kernelName:L,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i}=o;let l=n.parseAxisParam(i,s.shape);const u=r.getAxesPermutation(l,s.shape.length);let c=s;const d=[];null!=u&&(c=hi({inputs:{x:s},backend:a,attrs:{perm:u}}),d.push(c),l=r.getInnerMostAxes(l.length,c.shape.length)),r.assertAxesAreInnerMostDims("argMax",[l[0]],c.shape.length);const p=Oi(a,c,l[0],"max");return d.forEach((e=>a.disposeIntermediateTensorInfo(e))),p}};const _i={kernelName:B,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i}=o;let l=n.parseAxisParam(i,s.shape);const u=r.getAxesPermutation(l,s.shape.length);let c=s;const d=[];null!=u&&(c=hi({inputs:{x:s},backend:a,attrs:{perm:u}}),d.push(c),l=r.getInnerMostAxes(l.length,c.shape.length)),r.assertAxesAreInnerMostDims("argMin",[l[0]],c.shape.length);const p=Oi(a,c,l[0],"min");return d.forEach((e=>a.disposeIntermediateTensorInfo(e))),p}},Di={kernelName:V,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  if (abs(x) > 1.) {\n    return NAN;\n  }\n  return asin(x);\n"})},Pi={kernelName:W,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;return log(x + sqrt(x * x + 1.0));"})},Li={kernelName:U,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return atan(x);\n"})},Bi={kernelName:M,backendName:"webgl",kernelFunc:Ks({opSnippet:"\n  if (isnan(a)) return a;\n  if (isnan(b)) return b;\n\n  return atan(a, b);\n",packedOpSnippet:"\n  vec4 result = atan(a, b);\n  bvec4 isNaNA = isnan(a);\n  bvec4 isNaNB = isnan(b);\n  bvec4 isNaN = bvec4(isNaNA.x || isNaNB.x, isNaNA.y || isNaNB.y, isNaNA.z || isNaNB.z, isNaNA.w || isNaNB.w);\n  \n  result.r = isNaN.r ? NAN : result.r;\n  result.g = isNaN.g ? NAN : result.g;\n  result.b = isNaN.b ? NAN : result.b;\n  result.a = isNaN.a ? NAN : result.a;\n\n  return result;\n"})},Vi={kernelName:G,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  if ((x < -1.0) || (x > 1.0)) return NAN;\nreturn (log(1.0 + x) - log(1.0 - x)) / 2.0;"})};class Wi{constructor(e,t,n,a=!1,r=!1){if(this.variableNames=["x"],"avg"===t&&n)throw new Error("Cannot compute positions for average pool.");const o=e.filterWidth,s=e.strideHeight,i=e.strideWidth,l=e.dilationHeight,u=e.dilationWidth,c=e.effectiveFilterHeight,d=e.effectiveFilterWidth,p=e.padInfo.top,h=e.padInfo.left;this.outputShape=e.outShape;const f="avg"===t,x=`((batch  * ${e.inHeight} + xR) * ${e.inWidth} + xC) * ${e.inChannels} + d`,m=`(xR * ${e.inWidth} + xC) * ${e.inChannels} + d`;let g="0.0";if(f||(g="-1.0 / 1e-20"),n){const t=">=";return void(this.userCode=`\n        const ivec2 strides = ivec2(${s}, ${i});\n        const ivec2 pads = ivec2(${p}, ${h});\n\n        void main() {\n          ivec4 coords = getOutputCoords();\n          int batch = coords[0];\n          int d = coords[3];\n\n          ivec2 xRCCorner = coords.yz * strides - pads;\n          int xRCorner = xRCCorner.x;\n          int xCCorner = xRCCorner.y;\n\n          // max/min x(?, ?, d) to get y(yR, yC, d).\n          // ? = to be determined\n          float minMaxValue = 0.0;\n          float minMaxValueFound = 0.0;\n          int minMaxPosition = 0;\n          float avgValue = 0.0;\n\n          for (int wR = 0; wR < ${c};\n              wR += ${l}) {\n            int xR = xRCorner + wR;\n\n            if (xR < 0 || xR >= ${e.inHeight}) {\n              continue;\n            }\n\n            for (int wC = 0; wC < ${d};\n                wC += ${u}) {\n              int xC = xCCorner + wC;\n\n              if (xC < 0 || xC >= ${e.inWidth}) {\n                continue;\n              }\n\n              float value = getX(batch, xR, xC, d);\n\n              // If a min / max value has already been found, use it. If not,\n              // use the current value.\n              float currMinMaxValue = mix(\n                  value, minMaxValue, minMaxValueFound);\n              if (value ${t} currMinMaxValue) {\n                minMaxValue = value;\n                minMaxValueFound = 1.0;\n                minMaxPosition = ${a?r?x:m:`wR * ${d} + wC`};\n              }\n            }\n          }\n          setOutput(float(minMaxPosition));\n        }\n      `)}let b=`${t}(${t}(${t}(minMaxValue[0], minMaxValue[1]), minMaxValue[2]), minMaxValue[3])`;"avg"===t&&(b="avgValue / max(count, 1.0)");const v=4*Math.floor(o/4),C=o%4,$=`\n      if (${f}) {\n        avgValue += dot(values, ones);\n      } else {\n        minMaxValue = max(values, minMaxValue);\n      }\n    `;this.userCode=`\n      const ivec2 strides = ivec2(${s}, ${i});\n      const ivec2 pads = ivec2(${p}, ${h});\n      const float initializationValue = ${g};\n      const vec4 ones = vec4(1.0, 1.0, 1.0, 1.0);\n\n      float count = 0.0;\n\n      float getValue(int batch, int xR, int xC, int d) {\n        if (xC < 0 || xC >= ${e.inWidth}) {\n          return initializationValue;\n        }\n        count += 1.0;\n        return getX(batch, xR, xC, d);\n      }\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d = coords[3];\n\n        ivec2 xRCCorner = coords.yz * strides - pads;\n        int xRCorner = xRCCorner.x;\n        int xCCorner = xRCCorner.y;\n\n        // max/min x(?, ?, d) to get y(yR, yC, d).\n        // ? = to be determined\n        vec4 minMaxValue = vec4(${g});\n        float avgValue = 0.0;\n        count = 0.0;\n\n        for (int wR = 0; wR < ${c};\n            wR += ${l}) {\n          int xR = xRCorner + wR;\n\n          if (xR < 0 || xR >= ${e.inHeight}) {\n            continue;\n          }\n\n          for (int wC = 0; wC < ${v}; wC += 4) {\n            int xC = xCCorner + wC * ${u};\n\n            vec4 values = vec4(\n              getValue(batch, xR, xC, d),\n              getValue(batch, xR, xC + ${u}, d),\n              getValue(batch, xR, xC + 2 * ${u}, d),\n              getValue(batch, xR, xC + 3 * ${u}, d)\n            );\n\n            ${$}\n          }\n\n          int xC = xCCorner + ${v};\n          if (${1===C}) {\n            vec4 values = vec4(\n              getValue(batch, xR, xC, d),\n              initializationValue,\n              initializationValue,\n              initializationValue\n            );\n\n            ${$}\n          } else if (${2===C}) {\n            vec4 values = vec4(\n              getValue(batch, xR, xC, d),\n              getValue(batch, xR, xC + ${u}, d),\n              initializationValue,\n              initializationValue\n            );\n\n            ${$}\n          } else if (${3===C}) {\n            vec4 values = vec4(\n              getValue(batch, xR, xC, d),\n              getValue(batch, xR, xC + ${u}, d),\n              getValue(batch, xR, xC + 2 * ${u}, d),\n              initializationValue\n            );\n\n            ${$}\n          }\n        }\n        setOutput(${b});\n      }\n    `}}class Ui{constructor(e,t,n,a=!1,r=!1){if(this.variableNames=["x"],"avg"===t&&n)throw new Error("Cannot compute positions for average pool.");const o=e.filterWidth,s=e.strideDepth,i=e.strideHeight,l=e.strideWidth,u=e.dilationDepth,c=e.dilationHeight,d=e.dilationWidth,p=e.effectiveFilterDepth,h=e.effectiveFilterHeight,f=e.effectiveFilterWidth,x=e.padInfo.front,m=e.padInfo.top,g=e.padInfo.left;this.outputShape=e.outShape;const b="avg"===t;let v="0.0";if(b||(v="-1.0 / 1e-20"),n){const t=">=";return void(this.userCode=`\n        const ivec3 strides =\n            ivec3(${s}, ${i}, ${l});\n        const ivec3 pads = ivec3(${x}, ${m}, ${g});\n\n        void main() {\n          ivec5 coords = getOutputCoords();\n          int batch = coords.x;\n          int ch = coords.u;\n\n          ivec3 xCorner = ivec3(coords.y, coords.z, coords.w) * strides - pads;\n          int xDCorner = xCorner.x;\n          int xRCorner = xCorner.y;\n          int xCCorner = xCorner.z;\n\n          // max/min x(?, ?, ?, ch) to get y(yD, yR, yC, ch).\n          // ? = to be determined\n          float minMaxValue = 0.0;\n          float minMaxValueFound = 0.0;\n          int minMaxPosition = 0;\n\n          for (int wD = 0; wD < ${p};\n              wD += ${u}) {\n            int xD = xDCorner + wD;\n\n            if (xD < 0 || xD >= ${e.inDepth}) {\n              continue;\n            }\n\n            for (int wR = 0; wR < ${h};\n                wR += ${c}) {\n              int xR = xRCorner + wR;\n\n              if (xR < 0 || xR >= ${e.inHeight}) {\n                continue;\n              }\n\n              for (int wC = 0; wC < ${f};\n                  wC += ${d}) {\n                int xC = xCCorner + wC;\n\n                if (xC < 0 || xC >= ${e.inWidth}) {\n                  continue;\n                }\n\n                float value = getX(batch, xD, xR, xC, ch);\n\n                // If a min / max value has already been found, use it. If not,\n                // use the current value.\n                float currMinMaxValue = mix(\n                    value, minMaxValue, minMaxValueFound);\n                if (value ${t} currMinMaxValue) {\n                  minMaxValue = value;\n                  minMaxValueFound = 1.0;\n                  minMaxPosition = ${a?r?`(((batch * ${e.inDepth} + xD) * ${e.inHeight} + xR) * ${e.inWidth} + xC) * ${e.inChannels} + ch`:`((xD * ${e.inHeight} + xR) * ${e.inWidth} + xC) * ${e.inChannels} + ch`:`wD * ${h} * ${f} +\n                      wR * ${f} + wC`};\n                }\n              }\n            }\n          }\n          setOutput(float(minMaxPosition));\n        }\n      `)}let C=`${t}(${t}(${t}(minMaxValue[0], minMaxValue[1]), minMaxValue[2]), minMaxValue[3])`;"avg"===t&&(C="avgValue / max(count, 1.0)");const $=4*Math.floor(o/4),y=o%4,I=`\n      if (${b}) {\n        avgValue += dot(values, ones);\n      } else {\n        minMaxValue = max(values, minMaxValue);\n      }\n    `;this.userCode=`\n      const ivec3 strides =\n        ivec3(${s}, ${i}, ${l});\n      const ivec3 pads = ivec3(${x}, ${m}, ${g});\n      const float initializationValue = ${v};\n      const vec4 ones = vec4(1.0, 1.0, 1.0, 1.0);\n\n      float count = 0.0;\n\n      float getValue(int batch, int xD, int xR, int xC, int ch) {\n        if (xC < 0 || xC >= ${e.inWidth}) {\n          return initializationValue;\n        }\n        count += 1.0;\n        return getX(batch, xD, xR, xC, ch);\n      }\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int ch = coords.u;\n\n        ivec3 xCorner = ivec3(coords.y, coords.z, coords.w) * strides - pads;\n        int xDCorner = xCorner.x;\n        int xRCorner = xCorner.y;\n        int xCCorner = xCorner.z;\n\n        // max/min x(?, ?, ?, d) to get y(yD, yR, yC, ch).\n        // ? = to be determined\n        vec4 minMaxValue = vec4(${v});\n        float avgValue = 0.0;\n        count = 0.0;\n\n        for (int wD = 0; wD < ${p};\n            wD += ${u}) {\n          int xD = xDCorner + wD;\n\n          if (xD < 0 || xD >= ${e.inDepth}) {\n            continue;\n          }\n\n          for (int wR = 0; wR < ${h};\n            wR += ${c}) {\n            int xR = xRCorner + wR;\n\n            if (xR < 0 || xR >= ${e.inHeight}) {\n              continue;\n            }\n\n            for (int wC = 0; wC < ${$}; wC += 4) {\n              int xC = xCCorner + wC * ${d};\n\n              vec4 values = vec4(\n                getValue(batch, xD, xR, xC, ch),\n                getValue(batch, xD, xR, xC + ${d}, ch),\n                getValue(batch, xD, xR, xC + 2 * ${d}, ch),\n                getValue(batch, xD, xR, xC + 3 * ${d}, ch)\n              );\n\n              ${I}\n            }\n\n            int xC = xCCorner + ${$};\n            if (${1===y}) {\n              vec4 values = vec4(\n                getValue(batch, xD, xR, xC, ch),\n                initializationValue,\n                initializationValue,\n                initializationValue\n              );\n\n              ${I}\n            } else if (${2===y}) {\n              vec4 values = vec4(\n                getValue(batch, xD, xR, xC, ch),\n                getValue(batch, xD, xR, xC + ${d}, ch),\n                initializationValue,\n                initializationValue\n              );\n\n              ${I}\n            } else if (${3===y}) {\n              vec4 values = vec4(\n                getValue(batch, xD, xR, xC, ch),\n                getValue(batch, xD, xR, xC + ${d}, ch),\n                getValue(batch, xD, xR, xC + 2 * ${d}, ch),\n                initializationValue\n              );\n\n              ${I}\n            }\n          }\n        }\n        setOutput(${C});\n      }\n    `}}const Mi={kernelName:z,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t;Oa(s,"avgPool");const{filterSize:i,strides:l,pad:u,dimRoundingMode:c}=o;n.assert(r.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in avgPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const d=r.computePool2DInfo(s.shape,i,l,1,u,c);if(1===d.filterWidth&&1===d.filterHeight&&n.arraysEqual(d.inShape,d.outShape))return Ls({inputs:{x:s},backend:a});const p=new Wi(d,"avg",!1);return a.runWebGLProgram(p,[s],"float32")}};const Gi={kernelName:X,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o}=t,{filterSize:s,strides:i,pad:l,dimRoundingMode:u,dataFormat:c}=a,d=r.computePool3DInfo(o.shape,s,i,[1,1,1],l,u,c),p=new Ui(d,"avg",!1);return n.runWebGLProgram(p,[o],"float32")}};class zi{constructor(e){this.variableNames=["dy"],this.outputShape=e.inShape;const t=e.filterHeight,n=e.filterWidth,a=e.strideHeight,r=e.strideWidth,o=e.dilationHeight,s=e.dilationWidth,i=e.effectiveFilterHeight,l=e.effectiveFilterWidth,u=i-1-e.padInfo.top,c=l-1-e.padInfo.left,d=1/(t*n);this.userCode=`\n      const ivec2 pads = ivec2(${u}, ${c});\n      const float avgMultiplier = float(${d});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n\n        ivec2 dyRCCorner = coords.yz - pads;\n        int dyRCorner = dyRCCorner.x;\n        int dyCCorner = dyRCCorner.y;\n\n        // Convolve dy(?, ?, d) with pos mask(:, :, d) to get dx(xR, xC, d).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${i};\n            wR += ${o}) {\n          float dyR = float(dyRCorner + wR) / ${a}.0;\n\n          if (dyR < 0.0 || dyR >= ${e.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n\n          for (int wC = 0; wC < ${l};\n            wC+= ${s}) {\n            float dyC = float(dyCCorner + wC) / ${r}.0;\n\n            if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                fract(dyC) > 0.0) {\n              continue;\n            }\n            int idyC = int(dyC);\n\n            float dyValue = getDy(b, idyR, idyC, d);\n\n            dotProd += dyValue * avgMultiplier;\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class Xi{constructor(e){this.variableNames=["dy"],this.outputShape=e.inShape;const t=e.filterDepth,n=e.filterHeight,a=e.filterWidth,r=e.strideDepth,o=e.strideHeight,s=e.strideWidth,i=e.dilationDepth,l=e.dilationHeight,u=e.dilationWidth,c=e.effectiveFilterDepth,d=e.effectiveFilterHeight,p=e.effectiveFilterWidth,h=c-1-e.padInfo.front,f=d-1-e.padInfo.top,x=p-1-e.padInfo.left,m=1/(t*n*a);this.userCode=`\n      const ivec3 pads = ivec3(${h}, ${f}, ${x});\n      const float avgMultiplier = float(${m});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int ch = coords.u;\n\n        ivec3 dyCorner = ivec3(coords.y, coords.z, coords.w) - pads;\n        int dyDCorner = dyCorner.x;\n        int dyRCorner = dyCorner.y;\n        int dyCCorner = dyCorner.z;\n\n        // Convolve dy(?, ?, ?, d) with pos mask(:, :, :, ch) to get\n        // dx(xD, xR, xC, ch).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n\n        for (int wD = 0; wD < ${c};\n            wD += ${i}) {\n          float dyD = float(dyDCorner + wD) / ${r}.0;\n\n          if (dyD < 0.0 || dyD >= ${e.outDepth}.0 || fract(dyD) > 0.0) {\n            continue;\n          }\n          int idyD = int(dyD);\n\n          for (int wR = 0; wR < ${d};\n              wR += ${l}) {\n            float dyR = float(dyRCorner + wR) / ${o}.0;\n\n            if (dyR < 0.0 || dyR >= ${e.outHeight}.0 ||\n                fract(dyR) > 0.0) {\n              continue;\n            }\n            int idyR = int(dyR);\n\n            for (int wC = 0; wC < ${p};\n                wC += ${u}) {\n              float dyC = float(dyCCorner + wC) / ${s}.0;\n\n              if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                  fract(dyC) > 0.0) {\n                continue;\n              }\n              int idyC = int(dyC);\n\n              float dyValue = getDy(batch, idyD, idyR, idyC, ch);\n\n              dotProd += dyValue * avgMultiplier;\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}const Hi={kernelName:H,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:o,input:s}=t,i=s,{filterSize:l,strides:u,pad:c,dimRoundingMode:d}=a,p=r.computePool3DInfo(i.shape,l,u,[1,1,1],c,d),h=new Xi(p);return n.runWebGLProgram(h,[o],i.dtype)}};const ji={kernelName:j,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:o,input:s}=t,i=s;Oa([o,s],"avgPoolGrad");const{filterSize:l,strides:u,pad:c}=a,d=r.computePool2DInfo(i.shape,l,u,1,c),p=new zi(d);return n.runWebGLProgram(p,[o],i.dtype)}};const Ki={kernelName:K,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{a:r,b:o}=t,{transposeA:s,transposeB:i}=a;return xi({a:r,b:o,transposeA:s,transposeB:i,backend:n})}};class qi{constructor(e,t,n,a,o,s){this.outputShape=[],this.variableNames=["x","mean","variance"],r.assertAndGetBroadcastShape(e,t),r.assertAndGetBroadcastShape(e,n);let i="0.0";null!=a&&(r.assertAndGetBroadcastShape(e,a),this.variableNames.push("offset"),i="getOffsetAtOutCoords()");let l="1.0";null!=o&&(r.assertAndGetBroadcastShape(e,o),this.variableNames.push("scale"),l="getScaleAtOutCoords()"),this.outputShape=e,this.userCode=`\n      void main() {\n        float x = getXAtOutCoords();\n        float mean = getMeanAtOutCoords();\n        float variance = getVarianceAtOutCoords();\n        float offset = ${i};\n        float scale = ${l};\n        float inv = scale * inversesqrt(variance + float(${s}));\n        setOutput(dot(vec3(x, -mean, offset), vec3(inv, inv, 1)));\n      }\n    `}}class Yi{constructor(e,t,n,a,o,s){this.packedInputs=!0,this.packedOutput=!0,this.variableNames=["x","mean","variance"],r.assertAndGetBroadcastShape(e,t),r.assertAndGetBroadcastShape(e,n);let i="vec4(0.0)";null!=a&&(r.assertAndGetBroadcastShape(e,a),this.variableNames.push("offset"),i="getOffsetAtOutCoords()");let l="vec4(1.0)";null!=o&&(r.assertAndGetBroadcastShape(e,o),this.variableNames.push("scale"),l="getScaleAtOutCoords()"),this.outputShape=e,this.userCode=`\n      void main() {\n        vec4 offset = ${i};\n        vec4 scale = ${l};\n\n        vec4 x = getXAtOutCoords();\n        vec4 mean = getMeanAtOutCoords();\n        vec4 variance = getVarianceAtOutCoords();\n\n        vec4 inv = scale * inversesqrt(variance + vec4(${s}));\n\n        setOutput((x - mean) * inv + offset);\n      }\n    `}}const Qi={kernelName:q,backendName:"webgl",kernelFunc:({inputs:e,backend:a,attrs:r})=>{const{x:o,mean:s,variance:i,offset:l,scale:u}=e;n.assert(s.shape.length===i.shape.length,(()=>"Batch normalization gradient requires mean and variance to have equal ranks.")),n.assert(null==l||s.shape.length===l.shape.length,(()=>"Batch normalization gradient requires mean and offset to have equal ranks.")),n.assert(null==u||s.shape.length===u.shape.length,(()=>"Batch normalization gradient requires mean and scale to have equal ranks."));let{varianceEpsilon:c}=r;null==c&&(c=.001);const d=[o,s,i];let p=null;null!=l&&(p=l.shape,d.push(l));let h=null;null!=u&&(h=u.shape,d.push(u));const f=t().getBool("WEBGL_PACK_NORMALIZATION")?new Yi(o.shape,s.shape,i.shape,p,h,c):new qi(o.shape,s.shape,i.shape,p,h,c);return a.runWebGLProgram(f,d,d[0].dtype)}};class Zi{constructor(e){this.variableNames=["source"],this.outputShape=e,this.rank=e.length;const t=Za(this.rank);this.customUniforms=[{name:"start",arrayIndex:this.rank,type:"int"}];const n=function(e){if(1===e)return"sourceLoc";if(e<=6)return Ji.slice(0,e).map((e=>"sourceLoc."+e)).join(",");throw Error(`Slicing for rank ${e} is not yet supported`)}(this.rank);let a;a=`\n        ${t} sourceLoc;\n        ${t} coords = getOutputCoords();\n        ${e.map(((e,t)=>`sourceLoc.${Ji[t]} = start[${t}] + coords.${Ji[t]};`)).join("\n")}\n      `,this.userCode=`\n      void main() {\n        ${a}\n        setOutput(getSource(${n}));\n      }\n    `}}const Ji=["x","y","z","w","u","v"];class el{constructor(e){this.variableNames=["source"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=e,this.rank=e.length,this.customUniforms=[{name:"start",arrayIndex:this.rank,type:"int"}];const t=Za(this.rank),n=ms("coords",this.rank),a=ms("sourceLoc",this.rank),r=1===this.rank?"sourceLoc":`vec2(${a.slice(-2).join()})`,o=`getChannel(getSource(${a.join()}), ${r})`,s=`\n      result.x = ${o};\n      if (++${n[this.rank-1]} < ${e[this.rank-1]}) {\n        ++${a[this.rank-1]};\n        result.y = ${o};\n        --${a[this.rank-1]};\n      }\n    `,i=1===this.rank?"":`\n      --${n[this.rank-1]};\n      if (++${n[this.rank-2]} < ${e[this.rank-2]}) {\n        ++${a[this.rank-2]};\n        result.z = ${o};\n        if (++${n[this.rank-1]} < ${e[this.rank-1]}) {\n          ++${a[this.rank-1]};\n          result.w = ${o};\n        }\n      }\n    `,l=this.rank<=4?`sourceLoc = coords +\n            ${t}(${e.map(((e,t)=>`start[${t}]`)).join()});`:e.map(((e,t)=>`${a[t]} = ${n[t]} + start[${t}];`)).join("\n");this.userCode=`\n      void main() {\n        ${t} coords = getOutputCoords();\n        ${t} sourceLoc;\n        ${l}\n        vec4 result = vec4(0.);\n        ${s}\n        ${i}\n        setOutput(result);\n      }\n    `}}function tl(e){const{inputs:a,backend:r,attrs:o}=e,{x:s}=a,{begin:i,size:l}=o,[u,c]=d.parseSliceParams(s,i,l);if(d.assertParamsValid(s,u,c),0===n.sizeFromShape(c))return r.makeTensorInfo(c,s.dtype,[]);if(r.shouldExecuteOnCPU([s])||"string"===s.dtype){const e=r.texData.get(s.dataId),t=es(e.values,u,c,s.shape,s.dtype);return r.makeTensorInfo(c,s.dtype,t)}const{isPacked:p}=r.texData.get(s.dataId),h=d.isSliceContinous(s.shape,u,c);if(p||!h){const e=t().getBool("WEBGL_PACK_ARRAY_OPERATIONS")?new el(c):new Zi(c),n=[u];return r.runWebGLProgram(e,[s],s.dtype,n)}return r.uploadToGPU(s.dataId),function(e,t,a,r){const o=r.texData.get(e.dataId),s=r.makeTensorInfo(a,e.dtype),i=r.texData.get(s.dataId);Object.assign(i,o),i.refCount=1,i.shape=a,i.dtype=e.dtype;let l=d.computeFlatOffset(t,n.computeStrides(e.shape));o.slice&&(l+=o.slice.flatOffset),i.slice={flatOffset:l,origDataId:o.slice&&o.slice.origDataId||e.dataId};const u=r.dataRefCount.get(i.slice.origDataId)||1;return r.dataRefCount.set(i.slice.origDataId,u+1),s}(s,u,c,r)}const nl={kernelName:Y,backendName:"webgl",kernelFunc:tl},al={kernelName:Q,backendName:"webgl",kernelFunc:e=>{const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{blockShape:i,crops:l}=o;n.assert(s.shape.length<=4,(()=>"batchToSpaceND for rank > 4 with a WebGL backend not implemented yet"));const u=i.reduce(((e,t)=>e*t)),c=r.getReshaped(s.shape,i,u),d=r.getPermuted(c.length,i.length),p=r.getReshapedPermuted(s.shape,i,u),h=r.getSliceBeginCoords(l,i.length),f=r.getSliceSize(p,l,i.length),x=[],m=ai({inputs:{x:s},backend:a,attrs:{shape:c}}),g=hi({inputs:{x:m},backend:a,attrs:{perm:d}}),b=ai({inputs:{x:g},backend:a,attrs:{shape:p}}),v=tl({inputs:{x:b},backend:a,attrs:{begin:h,size:f}});return x.push(m),x.push(g),x.push(b),x.forEach((e=>a.disposeIntermediateTensorInfo(e))),v}};const rl={kernelName:Z,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:r,weights:o}=t,{size:s}=a,i=n.readSync(r.dataId),l=n.readSync(o.dataId),u=$o(i,l,o.dtype,o.shape,s);return n.makeTensorInfo([s],o.dtype,u)}};const ol={kernelName:J,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a}=e,{a:r,b:o}=n,s=t().getBool("WEBGL_PACK_BINARY_OPERATIONS"),i=t().getNumber("WEBGL_VERSION");if(a.shouldExecuteOnCPU([r,o])||1===i){const e=a.texData.get(r.dataId).values,t=a.texData.get(o.dataId).values,[n,s]=Io(r.shape,o.shape,e,t,r.dtype),i=a.makeTensorInfo(s,r.dtype);return a.texData.get(i.dataId).values=n,i}let l;return l=s?new Ps("\n  int r = int(a.r) & int(b.r);\n  int g = int(a.g) & int(b.g);\n  int rb = int(a.b) & int(b.b);\n  int ra = int(a.a) & int(b.a);\n  return vec4(r, g, rb, ra);\n",r.shape,o.shape,!1):new _s("\n  return float(int(a.r) & int(b.r));\n",r.shape,o.shape),a.runWebGLProgram(l,[r,o],r.dtype)}};const sl={kernelName:ee,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{s0:a,s1:o}=t,s=n.readSync(a.dataId),i=n.readSync(o.dataId),l=r.assertAndGetBroadcastShape(Array.from(s),Array.from(i));return n.makeTensorInfo([l.length],"int32",Int32Array.from(l))}},il=Ks({opSnippet:"return float(a != b);",cpuKernelImpl:zo,dtype:"bool"}),ll={kernelName:te,backendName:"webgl",kernelFunc:il};function ul(e){const{inputs:t,backend:n}=e,{input:a}=t;return Ls({inputs:{x:n.texData.get(a.dataId).complexTensorInfos.real},backend:n})}const cl={kernelName:ne,backendName:"webgl",kernelFunc:ul};const dl={kernelName:ae,backendName:"webgl",kernelFunc:function t(a){const{inputs:r,backend:o,attrs:s}=a,{x:i}=r,{dtype:l}=s;if("complex64"===l){if("complex64"===i.dtype)return Ls({inputs:{x:i},backend:o});const n=e.zeros(i.shape),a=t({inputs:{x:i},backend:o,attrs:{dtype:"float32"}}),r=Vs({inputs:{real:a,imag:n},backend:o});return n.dispose(),o.disposeIntermediateTensorInfo(a),r}if("complex64"===i.dtype){const e=ul({inputs:{input:i},backend:o}),n=t({inputs:{x:e},backend:o,attrs:{dtype:l}});return o.disposeIntermediateTensorInfo(e),n}if(!n.hasEncodingLoss(i.dtype,l)){const e=Ls({inputs:{x:i},backend:o});return{dataId:e.dataId,shape:e.shape,dtype:l}}if(o.shouldExecuteOnCPU([i])){const e=o.texData.get(i.dataId).values,[t,n,a]=wo(e,i.shape,i.dtype,l);return o.makeTensorInfo(t,n,a)}if("int32"===l)return function(e,t){const n=new Is(e.shape,"return float(int(x));"),a=t.runWebGLProgram(n,[e],"int32");return{dataId:a.dataId,shape:a.shape,dtype:a.dtype}}(i,o);if("bool"===l){const e=o.makeTensorInfo([],"bool",n.getTypedArrayFromDType("bool",1)),t=il({inputs:{a:i,b:e},backend:o});return o.disposeIntermediateTensorInfo(e),t}throw new Error(`Error in Cast: failed to cast ${i.dtype} to ${l}`)}},pl="return ceil(x);",hl={kernelName:re,backendName:"webgl",kernelFunc:js({opSnippet:pl,packedOpSnippet:pl,cpuKernelImpl:So})};class fl{constructor(e){this.variableNames=["A"],this.customUniforms=[{name:"minVal",type:"float"},{name:"maxVal",type:"float"}],this.outputShape=e,this.userCode="\n\n      void main() {\n        float value = getAAtOutCoords();\n        if (isnan(value)) {\n          setOutput(value);\n          return;\n        }\n\n        setOutput(clamp(value, minVal, maxVal));\n      }\n    "}}class xl{constructor(e){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"minVal",type:"float"},{name:"maxVal",type:"float"}],this.outputShape=e,this.userCode="\n      void main() {\n        vec4 value = getAAtOutCoords();\n\n        if (any(isnan(value))) {\n          setOutput(value);\n          return;\n        }\n\n        setOutput(clamp(value, vec4(minVal), vec4(maxVal)));\n      }\n    "}}const ml={kernelName:oe,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:r}=e,{x:o}=n,{clipValueMin:s,clipValueMax:i}=r;let l;l=t().getBool("WEBGL_PACK_CLIP")?new xl(o.shape):new fl(o.shape);const u=[[s],[i]];return a.runWebGLProgram(l,[o],o.dtype,u)}};class gl{constructor(e){this.variableNames=["real","imag"],this.outputShape=e,this.userCode="\n      void main() {\n        float re = abs(getRealAtOutCoords());\n        float im = abs(getImagAtOutCoords());\n        float mx = max(re, im);\n\n        // sadly the length function in glsl is not underflow-safe\n        // (at least not on Intel GPUs). So the safe solution is\n        // to ensure underflow-safety in all cases.\n        setOutput(\n          mx == 0.0 ? 0.0 : mx * length(vec2(1, min(re, im)/mx))\n        );\n      }\n    "}}function bl(e,t){return{dataId:t.dataId,dtype:t.dtype,shape:e.shape}}const vl={kernelName:se,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{x:a}=t,r=n.texData.get(a.dataId),o=new gl(a.shape),s=[bl(a,r.complexTensorInfos.real),bl(a,r.complexTensorInfos.imag)];return n.runWebGLProgram(o,s,s[0].dtype)}};class Cl{constructor(e){this.outputShape=[],this.outputShape=r.computeOutShape(e,1),this.variableNames=e.map(((e,t)=>`T${t}`));const t=new Array(e.length-1);t[0]=e[0][1];for(let n=1;n<t.length;n++)t[n]=t[n-1]+e[n][1];const n=[`if (yC < ${t[0]}) setOutput(getT0(yR, yC));`];for(let e=1;e<t.length;e++){const a=t[e-1];n.push(`else if (yC < ${t[e]}) setOutput(getT${e}(yR, yC-${a}));`)}const a=t.length,o=t[t.length-1];n.push(`else setOutput(getT${a}(yR, yC-${o}));`),this.userCode=`\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int yR = coords.x;\n        int yC = coords.y;\n\n        ${n.join("\n        ")}\n      }\n    `}}class $l{constructor(e,t){this.packedInputs=!0,this.packedOutput=!0,this.outputShape=[],this.outputShape=r.computeOutShape(e,t);const n=this.outputShape,a=n.length,o=Za(a),s=ms("coords",a),i=["x","y","z","w","u","v"].slice(0,a);this.variableNames=e.map(((e,t)=>`T${t}`));const l=new Array(e.length-1);l[0]=e[0][t];for(let n=1;n<l.length;n++)l[n]=l[n-1]+e[n][t];const u=i[t],c=i.slice(-2),d=i.join();let p=`if (${u} < ${l[0]}) {\n        return getChannel(\n            getT0(${d}), vec2(${c.join()}));\n        }`;for(let e=1;e<l.length;e++){const t=l[e-1];p+=`\n        if (${u} < ${l[e]}  && ${u} >= ${l[e-1]}) {\n          return getChannel(\n            getT${e}(${yl(i,u,t)}),\n            vec2(${yl(c,u,t)}));\n        }`}const h=l.length,f=l[l.length-1];p+=`\n        return getChannel(\n          getT${h}(${yl(i,u,f)}),\n          vec2(${yl(c,u,f)}));`,this.userCode=`\n      float getValue(${i.map((e=>"int "+e))}) {\n        ${p}\n      }\n\n      void main() {\n        ${o} coords = getOutputCoords();\n        vec4 result = vec4(getValue(${s}), 0., 0., 0.);\n\n        ${s[a-1]} = ${s[a-1]} + 1;\n        if (${s[a-1]} < ${n[a-1]}) {\n          result.g = getValue(${s});\n        }\n\n        ${s[a-2]} = ${s[a-2]} + 1;\n        if (${s[a-2]} < ${n[a-2]}) {\n          result.a = getValue(${s});\n        }\n\n        ${s[a-1]} = ${s[a-1]} - 1;\n        if (${s[a-2]} < ${n[a-2]} &&\n            ${s[a-1]} < ${n[a-1]}) {\n          result.b = getValue(${s});\n        }\n        setOutput(result);\n      }\n    `}}function yl(e,t,n){const a=e.indexOf(t);return e.map(((e,t)=>t===a?`${e} - ${n}`:e)).join()}function Il(e){const{inputs:t,backend:n}=e,{input:a}=t;return Ls({inputs:{x:n.texData.get(a.dataId).complexTensorInfos.imag},backend:n})}const wl={kernelName:ie,backendName:"webgl",kernelFunc:Il};function Sl(e,a,o){const s=e[0].dtype;if("complex64"===s){const t=e.map((e=>ul({inputs:{input:e},backend:o}))),n=e.map((e=>Il({inputs:{input:e},backend:o}))),r=Sl(t,a,o),s=Sl(n,a,o),i=Vs({inputs:{real:r,imag:s},backend:o});return t.forEach((e=>o.disposeIntermediateTensorInfo(e))),n.forEach((e=>o.disposeIntermediateTensorInfo(e))),o.disposeIntermediateTensorInfo(r),o.disposeIntermediateTensorInfo(s),i}let i=o.shouldExecuteOnCPU(e);if("string"===s&&(i=!0),i){const t=e.map((e=>{const t=n.sizeFromShape(e.shape.slice(a));return ai({inputs:{x:e},backend:o,attrs:{shape:[-1,t]}})})),i=t.map((e=>({vals:o.readSync(e.dataId),shape:e.shape}))),l=r.computeOutShape(t.map((e=>e.shape)),1),u=1===t[0].shape[0],c=Ro(i,l,s,u),d=r.computeOutShape(e.map((e=>e.shape)),a),p=o.makeTensorInfo(d,s,c);return t.forEach((e=>o.disposeIntermediateTensorInfo(e))),p}const l=e.filter((e=>n.sizeFromShape(e.shape)>0)),u=t().getBool("WEBGL_PACK_ARRAY_OPERATIONS")&&l[0].shape.length>1;if(1===l.length){const t=u?new Is(e[0].shape,ws):new Ss(e[0].shape,ws);return o.runWebGLProgram(t,e,s)}const c=t().getNumber("WEBGL_MAX_TEXTURES_IN_SHADER");if(l.length>c){const e=[];for(let t=0;t<l.length;t+=c){const n=l.slice(t,t+c);e.push(Sl(n,a,o))}const t=Sl(e,a,o);for(const t of e)o.disposeIntermediateTensorInfo(t);return t}if(u){const e=new $l(l.map((e=>e.shape)),a);return o.runWebGLProgram(e,l,s)}const{tensors2D:d,outShape:p}=function(e,t,a){const o=r.computeOutShape(e.map((e=>e.shape)),t);return{tensors2D:e.map((e=>ai({inputs:{x:e},attrs:{shape:[-1,n.sizeFromShape(e.shape.slice(t))]},backend:a}))),outShape:o}}(l,a,o),h=new Cl(d.map((e=>e.shape))),f=o.runWebGLProgram(h,d,s);d.forEach((e=>o.disposeIntermediateTensorInfo(e)));const x=ai({inputs:{x:f},attrs:{shape:p},backend:o});return o.disposeIntermediateTensorInfo(f),x}function Rl(e){const{inputs:t,backend:a,attrs:o}=e,{axis:s}=o,i=n.parseAxisParam(s,t[0].shape)[0],l=t.map((e=>e.shape));r.assertParamsConsistent(l,i);const u=r.computeOutShape(t.map((e=>e.shape)),i);if(0===n.sizeFromShape(u))return a.makeTensorInfo(u,t[0].dtype,[]);const c=t.filter((e=>n.sizeFromShape(e.shape)>0));return 1===c.length?Ls({inputs:{x:c[0]},backend:a}):Sl(c,i,a)}const Tl={kernelName:le,backendName:"webgl",kernelFunc:Rl};class kl{constructor(e,t=!1,n=null,a=!1,r=!1){this.variableNames=["x","W"],this.outputShape=e.outShape;const o=e.padInfo.top,s=e.padInfo.left,i=e.strideHeight,l=e.strideWidth,u=e.dilationHeight,c=e.dilationWidth,d=e.filterHeight,p=e.filterWidth,h=4*Math.floor(e.inChannels/4),f=e.inChannels%4,x="channelsLast"===e.dataFormat,m=x?1:2,g=x?2:3,b=x?3:1;let v="",C="";n&&(v=a?`float activation(float a) {\n          float b = getPreluActivationWeightsAtOutCoords();\n          ${n}\n        }`:r?`float activation(float a) {\n          float b = getLeakyreluAlphaAtOutCoords();\n          ${n}\n        }`:`\n          float activation(float x) {\n            ${n}\n          }\n        `,C="result = activation(result);");const $=t?"result += getBiasAtOutCoords();":"";t&&this.variableNames.push("bias"),a&&this.variableNames.push("preluActivationWeights"),r&&this.variableNames.push("leakyreluAlpha"),this.userCode=`\n      ${v}\n\n      const ivec2 strides = ivec2(${i}, ${l});\n      const ivec2 pads = ivec2(${o}, ${s});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d2 = coords[${b}];\n\n        ivec2 xRCCorner =\n            ivec2(coords[${m}], coords[${g}]) * strides - pads;\n        int xRCorner = xRCCorner.x;\n        int xCCorner = xRCCorner.y;\n\n        // Convolve x(?, ?, d1) with w(:, :, d1, d2) to get y(yR, yC, d2).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${d}; wR++) {\n          int xR = xRCorner + wR * ${u};\n\n          if (xR < 0 || xR >= ${e.inHeight}) {\n            continue;\n          }\n\n          for (int wC = 0; wC < ${p}; wC++) {\n            int xC = xCCorner + wC * ${c};\n\n            if (xC < 0 || xC >= ${e.inWidth}) {\n              continue;\n            }\n\n            for (int d1 = 0; d1 < ${h}; d1 += 4) {\n              vec4 wValues = vec4(\n                getW(wR, wC, d1, d2),\n                getW(wR, wC, d1 + 1, d2),\n                getW(wR, wC, d1 + 2, d2),\n                getW(wR, wC, d1 + 3, d2)\n              );\n\n              if (${x}) {\n                vec4 xValues = vec4(\n                  getX(batch, xR, xC, d1),\n                  getX(batch, xR, xC, d1 + 1),\n                  getX(batch, xR, xC, d1 + 2),\n                  getX(batch, xR, xC, d1 + 3)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec4 xValues = vec4(\n                  getX(batch, d1, xR, xC),\n                  getX(batch, d1 + 1, xR, xC),\n                  getX(batch, d1 + 2, xR, xC),\n                  getX(batch, d1 + 3, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n            }\n\n            if (${1===f}) {\n\n              if (${x}) {\n                dotProd +=\n                    getX(batch, xR, xC, ${h}) *\n                    getW(wR, wC, ${h}, d2);\n              } else {\n                dotProd +=\n                    getX(batch, ${h}, xR, xC) *\n                    getW(wR, wC, ${h}, d2);\n              }\n\n            } else if (${2===f}) {\n              vec2 wValues = vec2(\n                getW(wR, wC, ${h}, d2),\n                getW(wR, wC, ${h} + 1, d2)\n              );\n\n              if (${x}) {\n                vec2 xValues = vec2(\n                  getX(batch, xR, xC, ${h}),\n                  getX(batch, xR, xC, ${h} + 1)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec2 xValues = vec2(\n                  getX(batch, ${h}, xR, xC),\n                  getX(batch, ${h} + 1, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n\n            } else if (${3===f}) {\n              vec3 wValues = vec3(\n                getW(wR, wC, ${h}, d2),\n                getW(wR, wC, ${h} + 1, d2),\n                getW(wR, wC, ${h} + 2, d2)\n              );\n\n              if (${x}) {\n                vec3 xValues = vec3(\n                  getX(batch, xR, xC, ${h}),\n                  getX(batch, xR, xC, ${h} + 1),\n                  getX(batch, xR, xC, ${h} + 2)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec3 xValues = vec3(\n                  getX(batch, ${h}, xR, xC),\n                  getX(batch, ${h} + 1, xR, xC),\n                  getX(batch, ${h} + 2, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n\n            }\n          }\n        }\n\n        float result = dotProd;\n        ${$}\n        ${C}\n        setOutput(result);\n      }\n    `}}class Nl{constructor(e){this.variableNames=["x","W"],this.outputShape=e.outShape;const t=e.padInfo.front,n=e.padInfo.top,a=e.padInfo.left,r=e.strideDepth,o=e.strideHeight,s=e.strideWidth,i=e.dilationDepth,l=e.dilationHeight,u=e.dilationWidth,c=e.filterDepth,d=e.filterHeight,p=e.filterWidth,h=4*Math.floor(e.inChannels/4),f=e.inChannels%4;this.userCode=`\n      const ivec3 strides = ivec3(${r}, ${o}, ${s});\n      const ivec3 pads = ivec3(${t}, ${n}, ${a});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int d2 = coords.u;\n\n        ivec3 xFRCCorner = ivec3(coords.y, coords.z, coords.w) * strides - pads;\n        int xFCorner = xFRCCorner.x;\n        int xRCorner = xFRCCorner.y;\n        int xCCorner = xFRCCorner.z;\n\n        // Convolve x(?, ?, ?, d1) with w(:, :, :, d1, d2) to get\n        // y(yF, yR, yC, d2). ? = to be determined. : = across all\n        // values in that axis.\n        float dotProd = 0.0;\n        for (int wF = 0; wF < ${c}; wF++) {\n          int xF = xFCorner + wF * ${i};\n\n          if (xF < 0 || xF >= ${e.inDepth}) {\n            continue;\n          }\n\n          for (int wR = 0; wR < ${d}; wR++) {\n            int xR = xRCorner + wR * ${l};\n\n            if (xR < 0 || xR >= ${e.inHeight}) {\n              continue;\n            }\n\n            for (int wC = 0; wC < ${p}; wC++) {\n              int xC = xCCorner + wC * ${u};\n\n              if (xC < 0 || xC >= ${e.inWidth}) {\n                continue;\n              }\n\n              for (int d1 = 0; d1 < ${h}; d1 += 4) {\n                vec4 xValues = vec4(\n                  getX(batch, xF, xR, xC, d1),\n                  getX(batch, xF, xR, xC, d1 + 1),\n                  getX(batch, xF, xR, xC, d1 + 2),\n                  getX(batch, xF, xR, xC, d1 + 3)\n                );\n                vec4 wValues = vec4(\n                  getW(wF, wR, wC, d1, d2),\n                  getW(wF, wR, wC, d1 + 1, d2),\n                  getW(wF, wR, wC, d1 + 2, d2),\n                  getW(wF, wR, wC, d1 + 3, d2)\n                );\n\n                dotProd += dot(xValues, wValues);\n              }\n\n              if (${1===f}) {\n                dotProd +=\n                  getX(batch, xF, xR, xC, ${h}) *\n                  getW(wF, wR, wC, ${h}, d2);\n              } else if (${2===f}) {\n                vec2 xValues = vec2(\n                  getX(batch, xF, xR, xC, ${h}),\n                  getX(batch, xF, xR, xC, ${h} + 1)\n                );\n                vec2 wValues = vec2(\n                  getW(wF, wR, wC, ${h}, d2),\n                  getW(wF, wR, wC, ${h} + 1, d2)\n                );\n                dotProd += dot(xValues, wValues);\n              } else if (${3===f}) {\n                vec3 xValues = vec3(\n                  getX(batch, xF, xR, xC, ${h}),\n                  getX(batch, xF, xR, xC, ${h} + 1),\n                  getX(batch, xF, xR, xC, ${h} + 2)\n                );\n                vec3 wValues = vec3(\n                  getW(wF, wR, wC, ${h}, d2),\n                  getW(wF, wR, wC, ${h} + 1, d2),\n                  getW(wF, wR, wC, ${h} + 2, d2)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class El{constructor(e,t=!1,a=null,r=!1,o=!1){this.variableNames=["x","W"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"pads",type:"ivec2"},{name:"strides",type:"ivec2"},{name:"dilations",type:"ivec2"},{name:"inDims",type:"ivec2"}],this.outputShape=e.outShape,this.enableShapeUniforms=rr(this.outputShape.length);const s=e.padInfo.left,i=e.strideWidth,l=e.dilationWidth,u=e.filterHeight,c=e.filterWidth,d=c;let p="\n       int xR; int xC; int xCOffset;\n       vec4 wTexel; vec4 previous; vec4 final;";for(let e=0;e<c;e++)p+=`\n           vec4 xTexelC${2*e};\n           int xTexelC${2*e}Ready;\n           vec4 xTexelC${2*e+1};\n           int xTexelC${2*e+1}Ready;\n           vec4 xC${e};`;p+=`\n     for (int r = 0; r < ${u}; r++) {\n      for (int d1 = 0; d1 < ${e.inChannels}; d1 += 2) {\n       `;for(let e=0;e<c;e++)p+=`\n           xTexelC${2*e} = vec4(0.0);\n           xTexelC${2*e}Ready = 0;\n           xTexelC${2*e+1} = vec4(0.0);\n           xTexelC${2*e+1}Ready = 0;\n           xC${e} = vec4(0.0);`;p+="\n         xR = xRCorner + r * dilations[0];\n         if (xR >=0 && xR < inDims[0]) {\n       ";for(let t=0;t<(d+1)/2;t++){const a=2*t;if(p+=`\n           xC = xCCorner + ${a*l};\n           `,1===i){if(a<c&&(s%2==1?(p+=`\n                 xCOffset = xC + 1;\n                 if (xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${a}Ready == 0) {\n                   xTexelC${a} = getX(batch, xR, xCOffset, d1);\n\n                   // Need to manually clear unused channels in case\n                   // we're reading from recycled texture.\n                   if (xCOffset + 1 >= inDims[1]) {\n                     xTexelC${a}.zw = vec2(0.0);\n                   }\n                   xTexelC${a}Ready = 1;\n                 }\n               `,p+=1===l&&a>0?`\n                 xC${a} = vec4(xTexelC${a-2}.zw, xTexelC${a}.xy);\n                 `:`\n                   xCOffset = xC + 1 - 2;\n\n                   if (xCOffset >= 0 && xCOffset < inDims[1]) {\n                     previous = getX(batch, xR, xCOffset, d1);\n\n                     // Need to manually clear unused channels in case\n                     // we're reading from recycled texture.\n                     if (xCOffset + 1 >= inDims[1]) {\n                       previous.zw = vec2(0.0);\n                     }\n\n                     xC${a} = vec4(previous.zw, xTexelC${a}.xy);\n                   } else {\n                     xC${a} = vec4(0.0, 0.0, xTexelC${a}.xy);\n                   }\n                   `):p+=`\n                 if (xC >= 0 && xC < inDims[1] && xTexelC${a}Ready == 0) {\n                   xTexelC${a} = getX(batch, xR, xC, d1);\n                   if (xC + 1 >= inDims[1]) {\n                     xTexelC${a}.zw = vec2(0.0);\n                   }\n                   xTexelC${a}Ready = 1;\n                 }\n\n                 xC${a} = xTexelC${a};\n                 `,a+1<c)){const e=s%2==0?n.nearestLargerEven(l):l;l%2==0&&s%2==1||l%2!=0&&s%2!=1?(p+=`\n                   xCOffset = xC + imod(pads[1], 2) + ${e};\n\n                   if (xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${a+1}Ready == 0) {\n                     xTexelC${a+1} = getX(batch, xR, xCOffset, d1);\n\n                     // Need to manually clear unused channels in case\n                     // we're reading from recycled texture.\n                     if (xCOffset + 1 >= inDims[1]) {\n                       xTexelC${a+1}.zw = vec2(0.0);\n                     }\n                     xTexelC${a+1}Ready = 1;\n                   }\n                   `,p+=l>1?`\n                     xCOffset -= 2;\n                     if (xCOffset >= 0 && xCOffset < inDims[1]) {\n                      previous = getX(batch, xR, xCOffset, d1);\n                      xC${a+1} = vec4(previous.zw, xTexelC${a+1}.xy);\n                     } else {\n                      xC${a+1} = vec4(0.0, 0.0, xTexelC${a+1}.xy);\n                     }\n                     `:`\n                     xC${a+1} = vec4(xTexelC${a}.zw, xTexelC${a+1}.xy);\n                     `):p+=1===e?`\n                     xC${a+1} = xTexelC${a};\n                     `:`\n                     xCOffset = xC + ${e};\n\n                     if (xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${a+1}Ready == 0) {\n                       xTexelC${a+1} = getX(batch, xR, xCOffset, d1);\n                       if (xCOffset + 1 >= inDims[1]) {\n                         xTexelC${a+1}.zw = vec2(0.0);\n                       }\n                       xTexelC${a+1}Ready = 1;\n                     }\n\n                     xC${a+1} = xTexelC${a+1};\n                     `}}else a<c&&(s%2==1?(p+=`\n                 xCOffset = xC + 1 - strides[1];\n                 if(xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${a}Ready == 0) {\n                   xTexelC${a} = getX(batch, xR, xCOffset, d1);\n                   // Need to manually clear unused channels in case\n                   // we're reading from recycled texture.\n                   if (xCOffset + 1 >= inDims[1]) {\n                     xTexelC${a}.zw = vec2(0.0);\n                   }\n                   xTexelC${a}Ready = 1;\n                 }\n\n                 if(xC + 1 >= 0 && xC + 1 < inDims[1] && xTexelC${a+1}Ready == 0) {\n                   xTexelC${a+1} = getX(batch, xR, xC + 1, d1);\n                   // Need to manually clear unused channels in case\n                   // we're reading from recycled texture.\n                   if (xC + 2 >= inDims[1]) {\n                     xTexelC${a+1}.zw = vec2(0.0);\n                   }\n                   xTexelC${a+1}Ready = 1;\n                 }\n\n                 xC${a} = vec4(xTexelC${a}.zw, xTexelC${a+1}.zw);\n               `,a+1<c&&(p+=`\n                   final = vec4(0.0);\n                   xCOffset = xC + 1 + strides[1];\n                   if(xCOffset >= 0 && xCOffset < inDims[1]) {\n                     final = getX(batch, xR, xCOffset, d1);\n                   }\n                   xC${a+1} = vec4(xTexelC${a+1}.xy, final.xy);\n                 `)):(p+=`\n                 if(xC >= 0 && xC < inDims[1] && xTexelC${a}Ready == 0) {\n                   xTexelC${a} = getX(batch, xR, xC, d1);\n                   if (xC + 1 >= inDims[1]) {\n                     xTexelC${a}.zw = vec2(0.0);\n                   }\n                   xTexelC${a}Ready = 1;\n                 }\n\n                 xCOffset = xC + strides[1];\n                 if(xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${a+1}Ready == 0) {\n                   xTexelC${a+1} = getX(batch, xR, xCOffset, d1);\n                   if (xCOffset + 1 >= inDims[1]) {\n                     xTexelC${a+1}.zw = vec2(0.);\n                   }\n                   xTexelC${a+1}Ready = 1;\n                 }\n\n                 xC${a} = vec4(\n                   xTexelC${a}.xy, xTexelC${a+1}.xy);\n               `,a+1<c&&(p+=`\n                   xC${a+1} = vec4(xTexelC${a}.zw, xTexelC${a+1}.zw);\n                 `)));a<c&&(p+=`\n             wTexel = getW(r, ${a}, d1, d2);\n             dotProd += xC${a}.xxzz * vec4(wTexel.xy, wTexel.xy);\n             if(d1 + 1 < ${e.inChannels}) {\n               dotProd += xC${a}.yyww * vec4(wTexel.zw, wTexel.zw);\n             }\n           `,a+1<c&&(p+=`\n               wTexel = getW(r, ${a+1}, d1, d2);\n               dotProd += xC${a+1}.xxzz * vec4(wTexel.xy, wTexel.xy);\n               if(d1 + 1 < ${e.inChannels}) {\n                 dotProd += xC${a+1}.yyww * vec4(wTexel.zw, wTexel.zw);\n               }\n             `))}p+="\n     }\n   ",p+="\n     }\n   ",p+="\n     }\n   ";let h="",f="";a&&(h=r?`vec4 activation(vec4 a) {\n           vec4 b = getPreluActivationWeightsAtOutCoords();\n           ${a}\n         }`:o?`vec4 activation(vec4 a) {\n           vec4 b = getLeakyreluAlphaAtOutCoords();\n           ${a}\n         }`:`vec4 activation(vec4 x) {\n           ${a}\n         }`,f="result = activation(result);");const x=t?"result += getBiasAtOutCoords();":"";t&&this.variableNames.push("bias"),r&&this.variableNames.push("preluActivationWeights"),o&&this.variableNames.push("leakyreluAlpha"),this.userCode=`\n       ${h}\n\n       void main() {\n         ivec4 coords = getOutputCoords();\n         int batch = coords.x;\n         ivec2 xRCCorner = coords.yz * strides - pads;\n         int d2 = coords.w;\n         int xRCorner = xRCCorner.x;\n         int xCCorner = xRCCorner.y;\n\n         //intialize dotProd with a small epsilon seems to reduce GPU accuracy loss.\n         vec4 dotProd = vec4(0.000000000000001);\n\n         ${p}\n\n         vec4 result = dotProd - vec4(0.000000000000001);\n         ${x}\n         ${f}\n         setOutput(result);\n       }\n     `}}class Al{constructor(e,t){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"inputShape",type:"ivec4"},{name:"pad",type:"ivec2"},{name:"stride",type:"ivec2"},{name:"dilation",type:"ivec2"},{name:"inChannels",type:"int"},{name:"itemsPerBlockRow",type:"int"},{name:"outWidth",type:"int"}],this.outputShape=e,this.enableShapeUniforms=rr(this.outputShape.length);const{dataFormat:n}=t,a=Da(),r="channelsLast"===n,o=r?1:2,s=r?2:3,i=this.enableShapeUniforms?"if(blockIndex < outShape[2] && pos < outShape[1]) {":`if(blockIndex < ${e[2]} && pos < ${e[1]}) {`;let l="";for(let e=0;e<=1;e++)for(let t=0;t<=1;t++)l+=`\n          blockIndex = rc.z + ${t};\n          pos = rc.y + ${e};\n\n          ${i}\n            offsetY = int(blockIndex / outWidth) * stride[0] - pad[0];\n            d0 = offsetY + dilation[0] * (pos / itemsPerBlockRow);\n\n            if(d0 < inputShape[${o}] && d0 >= 0) {\n              // Use custom imod instead mod. On Intel GPU, mod may generate\n              // unexpected value.\n              // https://github.com/tensorflow/tfjs/issues/5447\n              offsetX = imod(blockIndex, outWidth) * stride[1] - pad[1];\n              d1 = offsetX + dilation[1] * (imod(pos, itemsPerBlockRow) /\n                  inChannels);\n\n              if(d1 < inputShape[${s}] && d1 >= 0) {\n\n                ch = imod(pos, inChannels);\n\n                if (${r}) {\n                  innerDims = vec2(d1, ch);\n                  result[${2*e+t}] = getChannel(\n                    getA(rc.x, d0, int(innerDims.x),\n                    int(innerDims.y)), innerDims);\n                } else {\n                  innerDims = vec2(d0, d1);\n                  result[${2*e+t}] = getChannel(\n                    getA(rc.x, ch, int(innerDims.x),\n                    int(innerDims.y)), innerDims);\n                }\n              }\n            }\n          }\n        `;this.userCode=`\n      void main() {\n        ivec3 rc = getOutputCoords();\n\n        vec4 result = vec4(0);\n\n        int blockIndex, pos, offsetY, d0, offsetX, d1, ch;\n        vec2 innerDims;\n\n        ${l}\n\n        ${a.output} = result;\n      }\n    `}}function Ol(e,t){const n=e.length;return n>=3?t?[...e.slice(0,-3),e[n-3]*e[n-2],e[n-1]]:[...e.slice(0,-3),e[n-3],e[n-2]*e[n-1]]:!t&&1===n&&e[0]>1?[e[0],1]:null}function Fl({x:e,filter:t,convInfo:a,backend:r,bias:o=null,preluActivationWeights:s=null,leakyreluAlpha:i=0,activation:l=null}){const u=e.shape,c=r.texData.get(e.dataId),d=a.inChannels,p=u[0]*u[1]*u[2],h=a.outChannels,f="channelsLast"===a.dataFormat;let x;const m=[];if(null!=s){const e=Ol(s.shape,f);null!=e&&(s=ai({inputs:{x:s},backend:r,attrs:{shape:e}}),m.push(s))}if(null!=o){const e=Ol(o.shape,f);null!=e&&(o=ai({inputs:{x:o},backend:r,attrs:{shape:e}}),m.push(o))}if(!((1===p||1===h)&&d>1e3)&&c.isPacked&&f&&null!=c.texture&&u[2]%2!=0&&n.arraysEqual(c.shape.slice(-3),u.slice(-3))){const d=u[0]*u[1]*(u[2]+1),p={dataId:e.dataId,shape:[1,d,a.inChannels],dtype:e.dtype},h=c.shape;c.shape=c.shape.slice(),c.shape[c.shape.length-2]++,n.assert(Ca(c.shape,p.shape),(()=>`packed reshape ${c.shape} to ${p.shape} isn't free`));const f=ai({inputs:{x:t},backend:r,attrs:{shape:[1,a.inChannels,a.outChannels]}});m.push(f);const g=xi({a:p,b:f,backend:r,transposeA:false,transposeB:false,bias:o,activation:l,preluActivationWeights:s,leakyreluAlpha:i}),b=r.texData.get(g.dataId);n.assert(b.isPacked,(()=>"batchMatMul result is expected to be packed")),c.shape=h,b.shape=a.outShape,x=Ls({inputs:{x:g},backend:r}),x.shape=a.outShape,m.push(g)}else{const n=a.outHeight*a.outWidth,u=ai({inputs:{x:e},backend:r,attrs:{shape:f?[a.batchSize,n,a.inChannels]:[a.batchSize,a.inChannels,n]}}),c=ai({inputs:{x:t},backend:r,attrs:{shape:[1,a.inChannels,a.outChannels]}}),d=xi({a:f?u:c,b:f?c:u,transposeA:!f,transposeB:false,backend:r,bias:o,activation:l,preluActivationWeights:s,leakyreluAlpha:i});x=ai({inputs:{x:d},backend:r,attrs:{shape:a.outShape}}),m.push(u),m.push(c),m.push(d)}for(const e of m)r.disposeIntermediateTensorInfo(e);return x}function _l({x:e,filter:t,convInfo:a,backend:r,bias:o=null,preluActivationWeights:s=null,leakyreluAlpha:i=0,activation:l=null}){const{filterWidth:u,filterHeight:c,inChannels:d,outWidth:p,outHeight:h,dataFormat:f}=a,x="channelsLast"===f,m=u*c*d,g=h*p,b=[a.batchSize,m,g],v=[];if(null!=s){const e=Ol(s.shape,x);null!=e&&(s=ai({inputs:{x:s},backend:r,attrs:{shape:e}}),v.push(s))}if(null!=o){const e=Ol(o.shape,x);null!=e&&(o=ai({inputs:{x:o},backend:r,attrs:{shape:e}}),v.push(o))}const C=ai({inputs:{x:t},backend:r,attrs:{shape:[1,m,n.sizeFromShape(t.shape)/m]}});v.push(C);const $=new Al(b,a),y=[e.shape,[a.padInfo.top,a.padInfo.left],[a.strideHeight,a.strideWidth],[a.dilationHeight,a.dilationWidth],[a.inChannels],[a.filterWidth*a.inChannels],[a.outWidth]],I=r.runWebGLProgram($,[e],"float32",y),w=ai({inputs:{x:I},backend:r,attrs:{shape:b}});v.push(I),v.push(w);const S=null!=o,R=null!=s,T="leakyrelu"===l,k=l?qs(l,!0):null,N=new Ys(x?w.shape:C.shape,x?C.shape:w.shape,x?[a.batchSize,g,a.outChannels]:[a.batchSize,a.outChannels,g],!0,!1,S,k,R,T),E=x?[w,C]:[C,w];if(o&&E.push(o),R&&E.push(s),T){const e=r.makeTensorInfo([],"float32",n.createScalarValue(i,"float32"));E.push(e),v.push(e)}const A=r.runWebGLProgram(N,E,"float32"),O=ai({inputs:{x:A},backend:r,attrs:{shape:a.outShape}});v.push(A);for(const e of v)r.disposeIntermediateTensorInfo(e);return O}const Dl={kernelName:ue,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:o}=e,{x:s,filter:i}=n,{strides:l,pad:u,dataFormat:c,dilations:d,dimRoundingMode:p}=o,h=r.convertConv2DDataFormat(c),f=r.computeConv2DInfo(s.shape,i.shape,l,d,u,p,!1,h);let x;if(1!==f.filterHeight||1!==f.filterWidth||1!==f.dilationHeight||1!==f.dilationWidth||1!==f.strideHeight||1!==f.strideWidth||"SAME"!==f.padInfo.type&&"VALID"!==f.padInfo.type)if(f.strideWidth<=2&&"channelsLast"===h&&t().getBool("WEBGL_EXP_CONV")){const e=new El(f),t=[[f.padInfo.top,f.padInfo.left],[f.strideHeight,f.strideWidth],[f.dilationHeight,f.dilationWidth],[f.inHeight,f.inWidth]];x=a.runWebGLProgram(e,[s,i],"float32",t)}else if(t().getBool("WEBGL_CONV_IM2COL"))x=_l({x:s,filter:i,convInfo:f,backend:a});else{const e=new kl(f);x=a.runWebGLProgram(e,[s,i],"float32")}else x=Fl({x:s,filter:i,convInfo:f,backend:a});const m=ai({inputs:{x:x},backend:a,attrs:{shape:f.outShape}});return a.disposeIntermediateTensorInfo(x),m}};class Pl{constructor(e){this.variableNames=["x","dy"],this.outputShape=e.filterShape;const t=e.strideHeight,n=e.strideWidth,a=e.padInfo.top,r=e.padInfo.left,o="channelsLast"===e.dataFormat;this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int wR = coords.x;\n        int wC = coords.y;\n        int d1 = coords.z;\n        int d2 = coords.w;\n\n        // Convolve x(?, ?, d1) with dy(:, :, d2) to get dw(wR, wC, d1, d2).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n\n        for (int b = 0; b < ${e.batchSize}; b++) {\n          for (int yR = 0; yR < ${e.outHeight}; yR++) {\n            int xR = wR + yR * ${t} - ${a};\n\n            if (xR < 0 || xR >= ${e.inHeight}) {\n              continue;\n            }\n\n            for (int yC = 0; yC < ${e.outWidth}; yC++) {\n              int xC = wC + yC * ${n} - ${r};\n\n              if (xC < 0 || xC >= ${e.inWidth}) {\n                continue;\n              }\n\n              ${o?"float dyValue = getDy(b, yR, yC, d2);\n              float xValue = getX(b, xR, xC, d1);\n              dotProd += (xValue * dyValue);":"float dyValue = getDy(b, d2, yR, yC);\n              float xValue = getX(b, d1, xR, xC);\n              dotProd += (xValue * dyValue);"}\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class Ll{constructor(e){this.variableNames=["dy","W"],this.outputShape=e.inShape;const t=e.filterHeight,n=e.filterWidth,a=e.strideHeight,r=e.strideWidth,o="channelsLast"===e.dataFormat,s=t-1-e.padInfo.top,i=n-1-e.padInfo.left,l=o?1:2,u=o?2:3,c=o?3:1;this.userCode=`\n      const ivec2 pads = ivec2(${s}, ${i});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d1 = coords[${c}];\n\n        ivec2 dyCorner = ivec2(coords[${l}], coords[${u}]) - pads;\n        int dyRCorner = dyCorner.x;\n        int dyCCorner = dyCorner.y;\n\n        // Convolve dy(?, ?, d2) with w(:, :, d1, d2) to compute dx(xR, xC, d1).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${t}; wR++) {\n          float dyR = float(dyRCorner + wR) / ${a}.0;\n\n          if (dyR < 0.0 || dyR >= ${e.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n\n          int wRPerm = ${t} - 1 - wR;\n\n          for (int wC = 0; wC < ${n}; wC++) {\n            float dyC = float(dyCCorner + wC) / ${r}.0;\n\n            if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                fract(dyC) > 0.0) {\n              continue;\n            }\n            int idyC = int(dyC);\n\n            int wCPerm = ${n} - 1 - wC;\n\n            for (int d2 = 0; d2 < ${e.outChannels}; d2++) {\n\n              if (${o}) {\n                float xValue = getDy(batch, idyR, idyC, d2);\n                float wValue = getW(wRPerm, wCPerm, d1, d2);\n                dotProd += xValue * wValue;\n              } else {\n                float xValue = getDy(batch, d2, idyR, idyC);\n                float wValue = getW(wRPerm, wCPerm, d1, d2);\n                dotProd += xValue * wValue;\n              }\n\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class Bl{constructor(e){this.variableNames=["x","dy"],this.outputShape=e.filterShape;const t=e.strideDepth,n=e.strideHeight,a=e.strideWidth,r=e.padInfo.front,o=e.padInfo.top,s=e.padInfo.left;this.userCode=`\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int wF = coords.x;\n        int wR = coords.y;\n        int wC = coords.z;\n        int d1 = coords.w;\n        int d2 = coords.u;\n\n        float dotProd = 0.0;\n\n        for (int b = 0; b < ${e.batchSize}; b++) {\n          for (int yF = 0; yF < ${e.outDepth}; yF++) {\n            int xF = wF + yF * ${t} - ${r};\n\n            if (xF < 0 || xF >= ${e.inDepth}) {\n              continue;\n            }\n\n            for (int yR = 0; yR < ${e.outHeight}; yR++) {\n              int xR = wR + yR * ${n} - ${o};\n\n              if (xR < 0 || xR >= ${e.inHeight}) {\n                continue;\n              }\n\n              for (int yC = 0; yC < ${e.outWidth}; yC++) {\n                int xC = wC + yC * ${a} - ${s};\n\n                if (xC < 0 || xC >= ${e.inWidth}) {\n                  continue;\n                }\n\n                float dyValue = getDy(b, yF, yR, yC, d2);\n                float xValue = getX(b, xF, xR, xC, d1);\n                dotProd += (xValue * dyValue);\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class Vl{constructor(e){this.variableNames=["dy","W"],this.outputShape=e.inShape;const t=e.filterDepth,n=e.filterHeight,a=e.filterWidth,r=e.strideDepth,o=e.strideHeight,s=e.strideWidth,i=t-1-e.padInfo.front,l=n-1-e.padInfo.top,u=a-1-e.padInfo.left;this.userCode=`\n      const ivec3 pads = ivec3(${i}, ${l}, ${u});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int d1 = coords.u;\n\n\n        ivec3 dyCorner = ivec3(coords.y, coords.z, coords.w) - pads;\n        int dyFCorner = dyCorner.x;\n        int dyRCorner = dyCorner.y;\n        int dyCCorner = dyCorner.z;\n\n        float dotProd = 0.0;\n        for (int wF = 0; wF < ${t}; wF++) {\n          float dyF = float(dyFCorner + wF) / ${r}.0;\n\n          if (dyF < 0.0 || dyF >= ${e.outDepth}.0 || fract(dyF) > 0.0) {\n            continue;\n          }\n          int idyF = int(dyF);\n\n          int wFPerm = ${t} - 1 - wF;\n\n          for (int wR = 0; wR < ${n}; wR++) {\n            float dyR = float(dyRCorner + wR) / ${o}.0;\n\n            if (dyR < 0.0 || dyR >= ${e.outHeight}.0 ||\n              fract(dyR) > 0.0) {\n              continue;\n            }\n            int idyR = int(dyR);\n\n            int wRPerm = ${n} - 1 - wR;\n\n            for (int wC = 0; wC < ${a}; wC++) {\n              float dyC = float(dyCCorner + wC) / ${s}.0;\n\n              if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                  fract(dyC) > 0.0) {\n                continue;\n              }\n              int idyC = int(dyC);\n\n              int wCPerm = ${a} - 1 - wC;\n\n              for (int d2 = 0; d2 < ${e.outChannels}; d2++) {\n                float xValue = getDy(batch, idyF, idyR, idyC, d2);\n                float wValue = getW(wFPerm, wRPerm, wCPerm, d1, d2);\n                dotProd += xValue * wValue;\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}const Wl={kernelName:ce,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o,dy:s}=t,{strides:i,pad:l,dataFormat:u,dimRoundingMode:c,filterShape:d}=a,p=r.convertConv2DDataFormat(u),h=r.computeConv2DInfo(o.shape,d,i,1,l,c,!1,p),f=new Pl(h);return n.runWebGLProgram(f,[o,s],"float32")}};class Ul{constructor(e){this.variableNames=["dy","W"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"strides",type:"vec2"}],this.outputShape=e.inShape,this.enableShapeUniforms=rr(this.outputShape.length);const t=e.filterHeight,n=e.filterWidth,a=t-1-e.padInfo.top,r=n-1-e.padInfo.left;this.userCode=`\n      const ivec2 pads = ivec2(${a}, ${r});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d1 = coords[3];\n\n        ivec2 dyCorner = ivec2(coords[1], coords[2]) - pads;\n        int dyRCorner = dyCorner.x;\n        int dyCCorner = dyCorner.y;\n\n        vec4 result = vec4(0.);\n        for (int wR = 0; wR < ${t}; wR++) {\n          float dyR = float(dyRCorner + wR) / strides[0];\n          if (dyR < 0.0 || dyR >= ${e.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n          int wRPerm = ${t} - 1 - wR;\n\n          for (int wC = 0; wC < ${n}; wC++) {\n            int wCPerm = ${n} - 1 - wC;\n\n            float dyC = float(dyCCorner + wC) / strides[1];\n            bool idyCVal = (dyC >= 0.0) && (dyC < ${e.outWidth}.0)\n              && (fract(dyC) == 0.0);\n            int idyC = int(dyC);\n\n            float dyC2 = float(dyCCorner + wC + 1) / strides[1];\n            bool idyCVal2 = (dyC2 >= 0.0) && (dyC2 < ${e.outWidth}.0)\n              && (fract(dyC2) == 0.0);\n            int idyC2 = int(dyC2);\n\n            if (idyCVal && idyCVal2) {\n              for (int d2 = 0; d2 < ${e.outChannels}; d2 += 2) {\n                vec4 wValue = getW(wRPerm, wCPerm, d1, d2);\n                vec4 dySample = getDy(batch, idyR, idyC, d2);\n                vec4 dySample2 = (idyC / 2 == idyC2 / 2) ?\n                  dySample : getDy(batch, idyR, idyC2, d2);\n\n                vec2 dyValue = mod(float(idyC), 2.) == 0. ?\n                  dySample.xy : dySample.zw;\n                result.xy += vec2(dot(dyValue, wValue.xy),\n                  dot(dyValue, wValue.zw));\n\n                dyValue = mod(float(idyC2), 2.) == 0. ?\n                  dySample2.xy : dySample2.zw;\n                result.zw += vec2(dot(dyValue, wValue.xy),\n                  dot(dyValue, wValue.zw));\n              }\n            } else if (idyCVal) {\n              for (int d2 = 0; d2 < ${e.outChannels}; d2 += 2) {\n                vec4 wValue = getW(wRPerm, wCPerm, d1, d2);\n                vec4 dySample = getDy(batch, idyR, idyC, d2);\n                vec2 dyValue = mod(float(idyC), 2.) == 0. ?\n                  dySample.xy : dySample.zw;\n                result.xy += vec2(dot(dyValue, wValue.xy),\n                  dot(dyValue, wValue.zw));\n              }\n            } else if (idyCVal2) {\n              for (int d2 = 0; d2 < ${e.outChannels}; d2 += 2) {\n                vec4 wValue = getW(wRPerm, wCPerm, d1, d2);\n                vec4 dySample = getDy(batch, idyR, idyC2, d2);\n                vec2 dyValue = mod(float(idyC2), 2.) == 0. ?\n                  dySample.xy : dySample.zw;\n                result.zw += vec2(dot(dyValue, wValue.xy),\n                  dot(dyValue, wValue.zw));\n              }\n            }\n          }\n        }\n        setOutput(result);\n      }\n    `}}const Ml={kernelName:de,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:o}=e,{dy:s,filter:i}=n,{inputShape:l,strides:u,pad:c,dataFormat:d,dimRoundingMode:p}=o,h=r.convertConv2DDataFormat(d),f=r.computeConv2DInfo(l,i.shape,u,1,c,p,!1,h);if(t().getBool("WEBGL_PACK_CONV2DTRANSPOSE")&&"channelsLast"===h){const e=[[f.strideHeight,f.strideWidth]],t=new Ul(f);return a.runWebGLProgram(t,[s,i],"float32",e)}{const e=new Ll(f);return a.runWebGLProgram(e,[s,i],"float32")}}};const Gl={kernelName:pe,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o,filter:s}=t,{strides:i,pad:l,dilations:u}=a,c=r.computeConv3DInfo(o.shape,s.shape,i,u,l),d=new Nl(c);return n.runWebGLProgram(d,[o,s],"float32")}};const zl={kernelName:he,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o,dy:s}=t,{strides:i,pad:l,filterShape:u}=a,c=r.computeConv3DInfo(o.shape,u,i,1,l),d=new Bl(c);return n.runWebGLProgram(d,[o,s],"float32")}};const Xl={kernelName:fe,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:o,filter:s}=t,{pad:i,strides:l,inputShape:u}=a,c=r.computeConv3DInfo(u,s.shape,l,1,i),d=new Vl(c);return n.runWebGLProgram(d,[o,s],"float32")}},Hl={kernelName:xe,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return cos(x);\n",packedOpSnippet:`\n  vec4 result = cos(x);\n  bvec4 isNaN = isnan(x);\n  ${Ds}\n  return result;\n`})},jl={kernelName:me,backendName:"webgl",kernelFunc:js({opSnippet:"\n  float e2x = exp(-x);\n  return (e2x + 1.0 / e2x) / 2.0;\n"})};class Kl{constructor(e,t,n,a,r){this.variableNames=["Image","Boxes","BoxInd"],this.outputShape=[];const[o,s,i,l]=e,[u]=t,[c,d]=n;this.outputShape=[u,c,d,l];const p="bilinear"===a?1:0,[h,f]=[s-1+".0",i-1+".0"],[x,m,g]=c>1?[""+(s-1)/(c-1),"(y2-y1) * height_ratio",`y1*${h} + float(y)*(height_scale)`]:["0.0","0.0",`0.5 * (y1+y2) * ${h}`],[b,v,C]=d>1?[""+(i-1)/(d-1),"(x2-x1) * width_ratio",`x1*${f} + float(x)*(width_scale)`]:["0.0","0.0",`0.5 * (x1+x2) * ${f}`];this.userCode=`\n      const float height_ratio = float(${x});\n      const float width_ratio = float(${b});\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int y = coords[1];\n        int x = coords[2];\n        int d = coords[3];\n\n        // get box vals\n        float y1 = getBoxes(b,0);\n        float x1 = getBoxes(b,1);\n        float y2 = getBoxes(b,2);\n        float x2 = getBoxes(b,3);\n\n        // get image in batch index\n        int bInd = round(getBoxInd(b));\n        if(bInd < 0 || bInd >= ${o}) {\n          return;\n        }\n\n        float height_scale = ${m};\n        float width_scale = ${v};\n\n        float in_y = ${g};\n        if( in_y < 0.0 || in_y > ${h} ) {\n          setOutput(float(${r}));\n          return;\n        }\n        float in_x = ${C};\n        if( in_x < 0.0 || in_x > ${f} ) {\n          setOutput(float(${r}));\n          return;\n        }\n\n        vec2 sourceFracIndexCR = vec2(in_x,in_y);\n        if(${p} == 1) {\n          // Compute the four integer indices.\n          ivec2 sourceFloorCR = ivec2(sourceFracIndexCR);\n          ivec2 sourceCeilCR = ivec2(ceil(sourceFracIndexCR));\n\n          float topLeft = getImage(b, sourceFloorCR.y, sourceFloorCR.x, d);\n          float bottomLeft = getImage(b, sourceCeilCR.y, sourceFloorCR.x, d);\n          float topRight = getImage(b, sourceFloorCR.y, sourceCeilCR.x, d);\n          float bottomRight = getImage(b, sourceCeilCR.y, sourceCeilCR.x, d);\n\n          vec2 fracCR = sourceFracIndexCR - vec2(sourceFloorCR);\n\n          float top = topLeft + (topRight - topLeft) * fracCR.x;\n          float bottom = bottomLeft + (bottomRight - bottomLeft) * fracCR.x;\n          float newValue = top + (bottom - top) * fracCR.y;\n          setOutput(newValue);\n        } else {\n          // Compute the coordinators of nearest neighbor point.\n          ivec2 sourceNearestCR = ivec2(floor(\n            sourceFracIndexCR + vec2(0.5,0.5)));\n          float newValue = getImage(b, sourceNearestCR.y, sourceNearestCR.x, d);\n          setOutput(newValue);\n        }\n      }\n    `}}const ql={kernelName:ge,backendName:"webgl",kernelFunc:e=>{const{inputs:t,backend:n,attrs:a}=e,{image:r,boxes:o,boxInd:s}=t,{cropSize:i,method:l,extrapolationValue:u}=a,c=new Kl(r.shape,o.shape,i,l,u);return n.runWebGLProgram(c,[r,o,s],"float32")}};var Yl;!function(e){e.Prod="*",e.Sum="+"}(Yl||(Yl={}));class Ql{constructor(e,t,n,a){this.op=e,this.outputShape=t,this.variableNames=["x"],this.customUniforms=[{name:"index",type:"float"}];const r=this.outputShape.length,o=this.op===Yl.Prod?"1.0":"0.0",s=n?o:`getX(${Zl(r,"coords",this.op)})`,i=this.outputShape[this.outputShape.length-1];let l="",u="";n?(l=a?"end != "+(i-1):"end != 0",u=a?"end + 1":"end - 1"):(l=a?`end + pow2 < ${i}`:"end >= pow2",u=a?"end + pow2":"end - pow2"),this.userCode=`\n      void main() {\n        ${Za(r)} coords = getOutputCoords();\n        int end = ${Jl(r,"coords",this.op)};\n        float val = ${s};\n        int pow2 = int(pow(2.0, index));\n        if (${l}) {\n          int idx = ${u};\n          ${Jl(r,"coords",this.op)} = idx;\n          val ${this.op}= getX(${Zl(r,"coords",this.op)});\n        }\n        setOutput(val);\n      }\n    `}}function Zl(e,t,n){if(1===e)return`${t}`;if(2===e)return`${t}.x, ${t}.y`;if(3===e)return`${t}.x, ${t}.y, ${t}.z`;if(4===e)return`${t}.x, ${t}.y, ${t}.z, ${t}.w`;throw new Error(`Cumulative ${n} for rank ${e} is not yet supported`)}function Jl(e,t,n){if(1===e)return`${t}`;if(2===e)return`${t}.y`;if(3===e)return`${t}.z`;if(4===e)return`${t}.w`;throw new Error(`Cumulative ${n} for rank ${e} is not yet supported`)}function eu(e,t,n,a,o,s){const i=t.shape.length,l=r.getAxesPermutation([a],i);let u=t;null!=l&&(u=hi({inputs:{x:t},backend:n,attrs:{perm:l}}));const c=r.getInnerMostAxes(1,i)[0];if(c!==i-1)throw new Error(`WebGL cumprod shader expects an inner-most axis=${t.shape.length-1} but got axis=${a}`);const d=u.shape[c];let p=Ls({inputs:{x:u},backend:n});for(let t=0;t<=Math.ceil(Math.log2(d))-1;t++){const a=new Ql(e,u.shape,!1,s),r=[[t]],o=p;p=n.runWebGLProgram(a,[p],p.dtype,r),n.disposeIntermediateTensorInfo(o)}if(o){const t=new Ql(e,u.shape,o,s),a=p;p=n.runWebGLProgram(t,[p],p.dtype),n.disposeIntermediateTensorInfo(a)}if(null!=l){const e=hi({inputs:{x:p},backend:n,attrs:{perm:r.getUndoAxesPermutation(l)}});return n.disposeIntermediateTensorInfo(p),n.disposeIntermediateTensorInfo(u),e}return p}const tu={kernelName:be,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:r}=t,{axis:o,exclusive:s,reverse:i}=a;return eu(Yl.Prod,r,n,o,s,i)}};const nu={kernelName:ve,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:r}=t,{axis:o,exclusive:s,reverse:i}=a;return eu(Yl.Sum,r,n,o,s,i)}};const au={kernelName:Ce,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:r,weights:o}=t,{size:s,binaryOutput:i}=a;if(1===r.shape.length){const e=n.readSync(r.dataId),t=n.readSync(o.dataId),a=$o(e,t,o.dtype,o.shape,s);return n.makeTensorInfo([s],o.dtype,a)}if(2===r.shape.length){const e=n.bufferSync(r),t=n.bufferSync(o),a=yo(e,t,s,i);return n.makeTensorInfo(a.shape,o.dtype,a.values)}throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank${r.shape.length}.`)}};class ru{constructor(e,t,n){this.variableNames=["x"],this.outputShape=[],this.outputShape=e,this.blockSize=t,this.dataFormat=n,this.userCode=`\n    void main() {\n      ivec4 coords = getOutputCoords();\n      int b = coords[0];\n      int h = ${this.getHeightCoordString()};\n      int w = ${this.getWidthCoordString()};\n      int d = ${this.getDepthCoordString()};\n\n      int in_h = h / ${t};\n      int offset_h = imod(h, ${t});\n      int in_w = w / ${t};\n      int offset_w = imod(w, ${t});\n      int offset_d = (offset_h * ${t} + offset_w) *\n        ${this.getOutputDepthSize()};\n      int in_d = d + offset_d;\n\n      float result = ${this.getInputSamplingString()};\n      setOutput(result);\n    }\n  `}getHeightCoordString(){return"NHWC"===this.dataFormat?"coords[1]":"coords[2]"}getWidthCoordString(){return"NHWC"===this.dataFormat?"coords[2]":"coords[3]"}getDepthCoordString(){return"NHWC"===this.dataFormat?"coords[3]":"coords[1]"}getOutputDepthSize(){return"NHWC"===this.dataFormat?this.outputShape[3]:this.outputShape[1]}getInputSamplingString(){return"NHWC"===this.dataFormat?"getX(b, in_h, in_w, in_d)":"getX(b, in_d, in_h, in_w)"}}const ou={kernelName:$e,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:r}=t,{blockSize:o,dataFormat:s}=a,i=r.shape[0],l=("NHWC"===s?r.shape[1]:r.shape[2])*o,u=("NHWC"===s?r.shape[2]:r.shape[3])*o,c=("NHWC"===s?r.shape[3]:r.shape[1])/(o*o),d=new ru("NHWC"===s?[i,l,u,c]:[i,c,l,u],o,s);return n.runWebGLProgram(d,[r],r.dtype)}};class su{constructor(e,t=!1,n=null,a=!1,r=!1){this.variableNames=["x","W"],this.customUniforms=[{name:"pads",type:"ivec2"},{name:"strides",type:"ivec2"},{name:"dilations",type:"ivec2"},{name:"inDims",type:"ivec2"}],this.outputShape=e.outShape,this.enableShapeUniforms=rr(this.outputShape.length);const o=e.filterHeight,s=e.filterWidth,i=e.outChannels/e.inChannels;let l="",u="";n&&(l=a?`float activation(float a) {\n          float b = getPreluActivationWeightsAtOutCoords();\n          ${n}\n        }`:r?`float activation(float a) {\n          float b = getLeakyreluAlphaAtOutCoords();\n          ${n}\n        }`:`\n          float activation(float x) {\n            ${n}\n          }\n        `,u="result = activation(result);");const c=t?"result += getBiasAtOutCoords();":"";t&&this.variableNames.push("bias"),a&&this.variableNames.push("preluActivationWeights"),r&&this.variableNames.push("leakyreluAlpha"),this.userCode=`\n      ${l}\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords.x;\n        ivec2 xRCCorner = coords.yz * strides - pads;\n        int d2 = coords.w;\n        int d1 = d2 / ${i};\n        int q = d2 - d1 * ${i};\n\n        int xRCorner = xRCCorner.x;\n        int xCCorner = xRCCorner.y;\n\n        // Convolve x(?, ?, d1) with w(:, :, d1, q) to get y(yR, yC, d2).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        // TO DO(dsmilkov): Flatten the two for loops and vec4 the operations.\n        for (int wR = 0; wR < ${o}; wR++) {\n          int xR = xRCorner + wR * dilations[0];\n\n          if (xR < 0 || xR >= inDims[0]) {\n            continue;\n          }\n\n          for (int wC = 0; wC < ${s}; wC++) {\n            int xC = xCCorner + wC * dilations[1];\n\n            if (xC < 0 || xC >= inDims[1]) {\n              continue;\n            }\n\n            float xVal = getX(batch, xR, xC, d1);\n            float wVal = getW(wR, wC, d1, q);\n            dotProd += xVal * wVal;\n          }\n        }\n\n        float result = dotProd;\n        ${c}\n        ${u}\n        setOutput(result);\n      }\n    `}}class iu{constructor(e,t=!1,a=null,r=!1,o=!1){this.variableNames=["x","W"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"pads",type:"ivec2"},{name:"strides",type:"ivec2"},{name:"dilations",type:"ivec2"},{name:"inDims",type:"ivec2"}],this.outputShape=e.outShape,this.enableShapeUniforms=rr(this.outputShape.length);const s=e.outChannels/e.inChannels,i=e.padInfo.left,l=e.strideWidth,u=e.dilationWidth,c=e.filterHeight,d=e.filterWidth,p=d;let h="\n      int xR; int xC; int xCOffset;\n      vec4 wTexel; vec4 previous; vec4 final;";for(let e=0;e<d;e++)h+=`\n          vec4 xTexelC${2*e};\n          int xTexelC${2*e}Ready;\n          vec4 xTexelC${2*e+1};\n          int xTexelC${2*e+1}Ready;\n          vec4 xC${e};`;h+=`\n    for (int r = 0; r < ${c}; r++) {\n      `;for(let e=0;e<d;e++)h+=`\n          xTexelC${2*e} = vec4(0.0);\n          xTexelC${2*e}Ready = 0;\n          xTexelC${2*e+1} = vec4(0.0);\n          xTexelC${2*e+1}Ready = 0;\n          xC${e} = vec4(0.0);`;h+="\n        xR = xRCorner + r * dilations[0];\n        if (xR >=0 && xR < inDims[0]) {\n      ";for(let e=0;e<(p+1)/2;e++){const t=2*e;if(h+=`\n          xC = xCCorner + ${t*u};\n          `,1===l){if(t<d&&(i%2==1?(h+=`\n                xCOffset = xC + 1;\n                if (xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${t}Ready == 0) {\n                  xTexelC${t} = getX(batch, xR, xCOffset, d1);\n\n                  // Need to manually clear unused channels in case\n                  // we're reading from recycled texture.\n                  if (xCOffset + 1 >= inDims[1]) {\n                    xTexelC${t}.zw = vec2(0.0);\n                  }\n                  xTexelC${t}Ready = 1;\n                }\n              `,h+=1===u&&t>0?`\n                xC${t} = vec4(xTexelC${t-2}.zw, xTexelC${t}.xy);\n                `:`\n                  xCOffset = xC + 1 - 2;\n\n                  if (xCOffset >= 0 && xCOffset < inDims[1]) {\n                    previous = getX(batch, xR, xCOffset, d1);\n\n                    // Need to manually clear unused channels in case\n                    // we're reading from recycled texture.\n                    if (xCOffset + 1 >= inDims[1]) {\n                      previous.zw = vec2(0.0);\n                    }\n\n                    xC${t} = vec4(previous.zw, xTexelC${t}.xy);\n                  } else {\n                    xC${t} = vec4(0.0, 0.0, xTexelC${t}.xy);\n                  }\n                  `):h+=`\n                if (xC >= 0 && xC < inDims[1] && xTexelC${t}Ready == 0) {\n                  xTexelC${t} = getX(batch, xR, xC, d1);\n                  if (xC + 1 >= inDims[1]) {\n                    xTexelC${t}.zw = vec2(0.0);\n                  }\n                  xTexelC${t}Ready = 1;\n                }\n\n                xC${t} = xTexelC${t};\n                `,t+1<d)){const e=i%2==0?n.nearestLargerEven(u):u;u%2==0&&i%2==1||u%2!=0&&i%2!=1?(h+=`\n                  xCOffset = xC + imod(pads[1], 2) + ${e};\n\n                  if (xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${t+1}Ready == 0) {\n                    xTexelC${t+1} = getX(batch, xR, xCOffset, d1);\n\n                    // Need to manually clear unused channels in case\n                    // we're reading from recycled texture.\n                    if (xCOffset + 1 >= inDims[1]) {\n                      xTexelC${t+1}.zw = vec2(0.0);\n                    }\n                    xTexelC${t+1}Ready = 1;\n                  }\n                  `,h+=u>1?`\n                    xCOffset -= 2;\n                    if (xCOffset >= 0 && xCOffset < inDims[1]) {\n                     previous = getX(batch, xR, xCOffset, d1);\n                     xC${t+1} = vec4(previous.zw, xTexelC${t+1}.xy);\n                    } else {\n                     xC${t+1} = vec4(0.0, 0.0, xTexelC${t+1}.xy);\n                    }\n                    `:`\n                    xC${t+1} = vec4(xTexelC${t}.zw, xTexelC${t+1}.xy);\n                    `):h+=1===e?`\n                    xC${t+1} = xTexelC${t};\n                    `:`\n                    xCOffset = xC + ${e};\n\n                    if (xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${t+1}Ready == 0) {\n                      xTexelC${t+1} = getX(batch, xR, xCOffset, d1);\n                      if (xCOffset + 1 >= inDims[1]) {\n                        xTexelC${t+1}.zw = vec2(0.0);\n                      }\n                      xTexelC${t+1}Ready = 1;\n                    }\n\n                    xC${t+1} = xTexelC${t+1};\n                    `}}else t<d&&(i%2==1?(h+=`\n                xCOffset = xC + 1 - strides[1];\n                if(xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${t}Ready == 0) {\n                  xTexelC${t} = getX(batch, xR, xCOffset, d1);\n                  // Need to manually clear unused channels in case\n                  // we're reading from recycled texture.\n                  if (xCOffset + 1 >= inDims[1]) {\n                    xTexelC${t}.zw = vec2(0.0);\n                  }\n                  xTexelC${t}Ready = 1;\n                }\n\n                if(xC + 1 >= 0 && xC + 1 < inDims[1] && xTexelC${t+1}Ready == 0) {\n                  xTexelC${t+1} = getX(batch, xR, xC + 1, d1);\n                  // Need to manually clear unused channels in case\n                  // we're reading from recycled texture.\n                  if (xC + 2 >= inDims[1]) {\n                    xTexelC${t+1}.zw = vec2(0.0);\n                  }\n                  xTexelC${t+1}Ready = 1;\n                }\n\n                xC${t} = vec4(xTexelC${t}.zw, xTexelC${t+1}.zw);\n              `,t+1<d&&(h+=`\n                  final = vec4(0.0);\n                  xCOffset = xC + 1 + strides[1];\n                  if(xCOffset >= 0 && xCOffset < inDims[1]) {\n                    final = getX(batch, xR, xCOffset, d1);\n                  }\n                  xC${t+1} = vec4(xTexelC${t+1}.xy, final.xy);\n                `)):(h+=`\n                if(xC >= 0 && xC < inDims[1] && xTexelC${t}Ready == 0) {\n                  xTexelC${t} = getX(batch, xR, xC, d1);\n                  if (xC + 1 >= inDims[1]) {\n                    xTexelC${t}.zw = vec2(0.0);\n                  }\n                  xTexelC${t}Ready = 1;\n                }\n\n                xCOffset = xC + strides[1];\n                if(xCOffset >= 0 && xCOffset < inDims[1] && xTexelC${t+1}Ready == 0) {\n                  xTexelC${t+1} = getX(batch, xR, xCOffset, d1);\n                  if (xCOffset + 1 >= inDims[1]) {\n                    xTexelC${t+1}.zw = vec2(0.);\n                  }\n                  xTexelC${t+1}Ready = 1;\n                }\n\n                xC${t} = vec4(\n                  xTexelC${t}.xy, xTexelC${t+1}.xy);\n              `,t+1<d&&(h+=`\n                  xC${t+1} = vec4(xTexelC${t}.zw, xTexelC${t+1}.zw);\n                `)));t<d&&(h+=`\n            wTexel = getW(r, ${t}, d1, q);\n            dotProd += xC${t} * vec4(wTexel.xz, wTexel.xz);\n          `,t+1<d&&(h+=`\n              wTexel = getW(r, ${t+1}, d1, q);\n              dotProd += xC${t+1} * vec4(wTexel.xz, wTexel.xz);\n            `))}h+="\n    }\n  ",h+="\n      }\n    ";let f="",x="";a&&(f=r?`vec4 activation(vec4 a) {\n          vec4 b = getPreluActivationWeightsAtOutCoords();\n          ${a}\n        }`:o?`vec4 activation(vec4 a) {\n          vec4 b = getLeakyreluAlphaAtOutCoords();\n          ${a}\n        }`:`vec4 activation(vec4 x) {\n          ${a}\n        }`,x="result = activation(result);");const m=t?"result += getBiasAtOutCoords();":"";t&&this.variableNames.push("bias"),r&&this.variableNames.push("preluActivationWeights"),o&&this.variableNames.push("leakyreluAlpha"),this.userCode=`\n      ${f}\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords.x;\n        ivec2 xRCCorner = coords.yz * strides - pads;\n        int d2 = coords.w;\n        int d1 = d2 / ${s};\n        int q = d2 - d1 * ${s};\n        int xRCorner = xRCCorner.x;\n        int xCCorner = xRCCorner.y;\n\n        //intialize dotProd with a small epsilon seems to reduce GPU accuracy loss.\n        vec4 dotProd = vec4(0.000000000000001);\n\n        ${h}\n\n        vec4 result = dotProd - vec4(0.000000000000001);\n        ${m}\n        ${x}\n        setOutput(result);\n      }\n    `}}const lu={kernelName:ye,backendName:"webgl",kernelFunc:function(e){const{inputs:a,backend:o,attrs:s}=e,{x:i,filter:l}=a,{strides:u,pad:c,dilations:d,dimRoundingMode:p}=s;let h=d;null==h&&(h=[1,1]),n.assert(r.eitherStridesOrDilationsAreOne(u,h),(()=>`Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides ${u} and dilations '${h}'`));const f=r.computeConv2DInfo(i.shape,l.shape,u,h,c,p,!0);let x;x=t().getBool("WEBGL_PACK_DEPTHWISECONV")&&f.strideWidth<=2&&f.outChannels/f.inChannels==1?new iu(f):new su(f);const m=[[f.padInfo.top,f.padInfo.left],[f.strideHeight,f.strideWidth],[f.dilationHeight,f.dilationWidth],[f.inHeight,f.inWidth]];return o.runWebGLProgram(x,[i,l],"float32",m)}};class uu{constructor(e){this.variableNames=["x","dy"],this.outputShape=e.filterShape;const t=e.strideHeight,n=e.strideWidth,a=e.padInfo.top,r=e.padInfo.left,o=e.outChannels/e.inChannels;this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int wR = coords.x;\n        int wC = coords.y;\n        int d1 = coords.z;\n        int dm = coords.w;\n        int d2 = d1 * ${o} + dm;\n\n        float dotProd = 0.0;\n\n        // TO DO: Vec4 over the batch size\n        for (int b = 0; b < ${e.batchSize}; b++) {\n          for (int yR = 0; yR < ${e.outHeight}; yR++) {\n            int xR = wR + yR * ${t} - ${a};\n\n            if (xR < 0 || xR >= ${e.inHeight}) {\n              continue;\n            }\n\n            for (int yC = 0; yC < ${e.outWidth}; yC++) {\n              int xC = wC + yC * ${n} - ${r};\n\n              if (xC < 0 || xC >= ${e.inWidth}) {\n                continue;\n              }\n\n              float dyValue = getDy(b, yR, yC, d2);\n              float xValue = getX(b, xR, xC, d1);\n              dotProd += (xValue * dyValue);\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class cu{constructor(e){this.variableNames=["dy","W"],this.outputShape=e.inShape;const t=e.filterHeight,n=e.filterWidth,a=e.strideHeight,r=e.strideWidth,o=t-1-e.padInfo.top,s=n-1-e.padInfo.left,i=e.outChannels/e.inChannels;this.userCode=`\n      const ivec2 pads = ivec2(${o}, ${s});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d1 = coords[3];\n        ivec2 dyCorner = coords.yz - pads;\n        int dyRCorner = dyCorner.x;\n        int dyCCorner = dyCorner.y;\n\n        float dotProd = 0.0;\n\n        for (int wR = 0; wR < ${t}; wR++) {\n          float dyR = float(dyRCorner + wR) / ${a}.0;\n\n          if (dyR < 0.0 || dyR >= ${e.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n\n          int wRPerm = ${t} - 1 - wR;\n\n          for (int wC = 0; wC < ${n}; wC++) {\n            float dyC = float(dyCCorner + wC) / ${r}.0;\n\n            if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                fract(dyC) > 0.0) {\n              continue;\n            }\n            int idyC = int(dyC);\n\n            int wCPerm = ${n} - 1 - wC;\n\n            // TO DO: Vec4 over the channelMul\n            for (int dm = 0; dm < ${i}; dm++) {\n              int d2 = d1 * ${i} + dm;\n              float xValue = getDy(batch, idyR, idyC, d2);\n              float wValue = getW(wRPerm, wCPerm, d1, dm);\n              dotProd += xValue * wValue;\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}const du={kernelName:Ie,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o,dy:s}=t,{strides:i,dilations:l,pad:u,dimRoundingMode:c,filterShape:d}=a,p=r.computeConv2DInfo(o.shape,d,i,l,u,c,!0),h=new uu(p);return n.runWebGLProgram(h,[o,s],"float32")}};const pu={kernelName:we,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:o,filter:s}=t,{strides:i,dilations:l,pad:u,dimRoundingMode:c,inputShape:d}=a,p=r.computeConv2DInfo(d,s.shape,i,l,u,c,!0),h=new cu(p);return n.runWebGLProgram(h,[o,s],"float32")}};class hu{constructor(e){this.variableNames=["X"],this.outputShape=[e,e],this.userCode="\n      void main() {\n          ivec2 coords = getOutputCoords();\n          float val = coords[0] == coords[1] ? getX(coords[0]) : 0.0;\n          setOutput(val);\n      }\n    "}}const fu={kernelName:Se,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a}=e,{x:r}=t,o=[...r.shape,...r.shape],s=n.sizeFromShape(r.shape),i=ai({inputs:{x:r},backend:a,attrs:{shape:[s]}}),l=new hu(s),u=a.runWebGLProgram(l,[i],i.dtype),c=ai({inputs:{x:u},backend:a,attrs:{shape:o}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(u),c}};class xu{constructor(e){this.variableNames=["x","W"],this.outputShape=e.outShape;const{inHeight:t,inWidth:n,padInfo:a,strideHeight:r,strideWidth:o,filterHeight:s,filterWidth:i,dilationHeight:l,dilationWidth:u}=e,{top:c,left:d}=a;this.userCode=`\n      const ivec2 strides = ivec2(${r}, ${o});\n      const ivec2 pads = ivec2(${c}, ${d});\n      const float neg_infinity = -3.4e38;\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords.x;\n        int d1 = coords.w;\n        ivec2 outTopLeftCorner =\n            coords.yz * strides - pads;\n        int hBeg = outTopLeftCorner.x;\n        int wBeg = outTopLeftCorner.y;\n\n        float curVal = neg_infinity;\n        for (int h = 0; h < ${s}; h++) {\n          int hIn = hBeg + h * ${l};\n\n          if (hIn >= 0 && hIn < ${t}) {\n            for (int w = 0; w < ${i}; w++) {\n              int wIn = wBeg + w * ${u};\n\n              if (wIn >= 0 && wIn < ${n}) {\n                float xVal = getX(batch, hIn, wIn, d1);\n                float wVal = getW(h, w, d1);\n\n                float val = xVal + wVal;\n                if (val > curVal) {\n                  curVal = val;\n                }\n              }\n            }\n          }\n        }\n\n        float result = curVal;\n        setOutput(result);\n      }\n    `}}const mu={kernelName:Re,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o,filter:s}=t,{strides:i,pad:l,dilations:u}=a,c=r.computeDilation2DInfo(o.shape,s.shape,i,l,"NHWC",u);let d;const p=new xu(c);d=n.runWebGLProgram(p,[o,s],"float32");const h=ai({inputs:{x:d},backend:n,attrs:{shape:c.outShape}});return n.disposeIntermediateTensorInfo(d),h}};const gu={kernelName:Te,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{equation:s}=o,i=t,{allDims:l,summedDims:u,idDims:c}=r.decodeEinsumEquation(s,i.length);r.checkEinsumDimSizes(l.length,c,i);const{path:d,steps:p}=r.getEinsumComputePath(u,c),h=p.length;let f=null,x=l.length;const m=[];for(let e=0;e<h;++e){for(const t of p[e]){const{permutationIndices:e,expandDims:o}=r.getEinsumPermutation(x,c[t]);let s;r.isIdentityPermutation(e)?s=i[t]:(s=hi({inputs:{x:i[t]},backend:a,attrs:{perm:e}}),m.push(s));const l=s.shape.slice();for(let e=0;e<o.length;++e)l.splice(o[e],0,1);n.arraysEqual(s.shape,l)||(s=ai({inputs:{x:s},backend:a,attrs:{shape:l}}),m.push(s)),null===f?f=s:(f=ti({inputs:{a:s,b:f},backend:a}),m.push(f))}e<h-1&&(d[e]>=0&&(f=di({inputs:{x:f},backend:a,attrs:{axis:d[e]-(l.length-x),keepDims:!1}}),m.push(f)),x--)}for(const e of m)e!==f&&a.disposeIntermediateTensorInfo(e);return f}},bu={kernelName:ke,backendName:"webgl",kernelFunc:js({opSnippet:"return (x >= 0.0) ? x : (exp(x) - 1.0);",packedOpSnippet:"\n  vec4 result;\n\n  result.r = (x.r >= 0.0) ? x.r : (exp(x.r) - 1.0);\n  result.g = (x.g >= 0.0) ? x.g : (exp(x.g) - 1.0);\n  result.b = (x.b >= 0.0) ? x.b : (exp(x.b) - 1.0);\n  result.a = (x.a >= 0.0) ? x.a : (exp(x.a) - 1.0);\n\n  return result;\n"})},vu={kernelName:Ne,backendName:"webgl",kernelFunc:e=>{const{inputs:n,backend:a}=e,{dy:r,y:o}=n,s=t().getBool("WEBGL_PACK_BINARY_OPERATIONS")?new Ps("\n  vec4 bGTEZero = vec4(greaterThanEqual(b, vec4(0.)));\n  return (bGTEZero * a) + ((vec4(1.0) - bGTEZero) * (a * (b + vec4(1.0))));\n",r.shape,o.shape):new _s("return (b >= 0.0) ? a : a * (b + 1.0);",r.shape,o.shape);return a.runWebGLProgram(s,[r,o],r.dtype)}},Cu={kernelName:Ee,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a == b);",packedOpSnippet:"\n  return vec4(equal(a, b));\n",dtype:"bool",cpuKernelImpl:To})},$u={kernelName:Ae,backendName:"webgl",kernelFunc:js({opSnippet:`\n  // Error function is calculated approximately with elementary function.\n  // See "Handbook of Mathematical Functions with Formulas,\n  // Graphs, and Mathematical Tables", Abramowitz and Stegun.\n  float p = ${r.ERF_P};\n  float a1 = ${r.ERF_A1};\n  float a2 = ${r.ERF_A2};\n  float a3 = ${r.ERF_A3};\n  float a4 = ${r.ERF_A4};\n  float a5 = ${r.ERF_A5};\n\n  float sign = sign(x);\n  x = abs(x);\n  float t = 1.0 / (1.0 + p * x);\n  return sign * (1.0 - (((((a5*t + a4)*t) + a3)*t + a2)*t + a1)*t*exp(-x*x));\n`})},yu=js({opSnippet:"if (isnan(x)) return x;\n  return exp(x);\n",packedOpSnippet:"\n  vec4 result = exp(x);\n  bvec4 isNaN = isnan(x);\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n",cpuKernelImpl:ko,dtype:"float32"}),Iu={kernelName:Oe,backendName:"webgl",kernelFunc:yu};function wu(e){const{inputs:t,attrs:a,backend:r}=e,{dim:o}=a,{input:s}=t,i=s.shape.length,l=s.shape.slice();let u=o;return o<0&&(n.assert(-(i+1)<=o,(()=>`Axis must be in the interval [${-(i+1)}, ${i}]`)),u=i+o+1),l.splice(u,0,1),ai({inputs:{x:s},backend:r,attrs:{shape:l}})}const Su={kernelName:Fe,backendName:"webgl",kernelFunc:wu},Ru="return exp(x) - 1.0;",Tu={kernelName:_e,backendName:"webgl",kernelFunc:js({opSnippet:Ru,packedOpSnippet:Ru,cpuKernelImpl:No})};class ku{constructor(e,t,n){this.variableNames=["real","imag"];const a=t[1];this.outputShape=t;const r=n?`2.0 * ${Math.PI}`:`-2.0 * ${Math.PI}`,o=n?`${a}.0`:"1.0";let s;if("real"===e)s="return real * expR - imag * expI;";else{if("imag"!==e)throw new Error(`FFT component must be either "real" or "imag", got ${e}.`);s="return real * expI + imag * expR;"}this.userCode=`\n      const float exponentMultiplier = ${r};\n\n      float unaryOpComplex(float real, float expR, float imag, float expI) {\n        ${s}\n      }\n\n      float mulMatDFT(int batch, int index) {\n        float indexRatio = float(index) / float(${a});\n        float exponentMultiplierTimesIndexRatio =\n            exponentMultiplier * indexRatio;\n\n        float result = 0.0;\n\n        for (int i = 0; i < ${a}; i++) {\n          // x = (-2|2 * PI / N) * index * i;\n          float x = exponentMultiplierTimesIndexRatio * float(i);\n          float expR = cos(x);\n          float expI = sin(x);\n          float real = getReal(batch, i);\n          float imag = getImag(batch, i);\n\n          result +=\n              unaryOpComplex(real, expR, imag, expI) / ${o};\n        }\n\n        return result;\n      }\n\n      void main() {\n        ivec2 coords = getOutputCoords();\n        setOutput(mulMatDFT(coords[0], coords[1]));\n      }\n    `}}function Nu(e,t,a){const r=a.texData.get(e.dataId),o=n.sizeFromShape(e.shape),s=e.shape[e.shape.length-1],i=ai({inputs:{x:e},backend:a,attrs:{shape:[o/s,s]}}),l=i.shape,u=new ku("real",l,t),c=new ku("imag",l,t),d=[{dataId:r.complexTensorInfos.real.dataId,dtype:r.complexTensorInfos.real.dtype,shape:l},{dataId:r.complexTensorInfos.imag.dataId,dtype:r.complexTensorInfos.imag.dtype,shape:l}],p=a.runWebGLProgram(u,d,"float32"),h=a.runWebGLProgram(c,d,"float32"),f=Vs({inputs:{real:p,imag:h},backend:a});a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(h);const x=ai({inputs:{x:f},backend:a,attrs:{shape:e.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(f),x}const Eu={kernelName:De,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{input:a}=t;return Nu(a,!1,n)}};class Au{constructor(e,t){this.outputShape=[],this.customUniforms=[{name:"value",type:"float"}],this.variableNames=["x"],this.outputShape=e,this.userCode="\n      void main() {\n        // Input can be obtained from uniform value.\n        setOutput(value);\n      }\n    "}}function Ou(e){const{backend:t,attrs:a}=e,{shape:r,value:o}=a;let{dtype:s}=a;if(s=s||n.inferDtype(o),"string"===s){const e=n.getArrayFromDType(s,n.sizeFromShape(r));return e.fill(o),t.makeTensorInfo(r,s,e)}{const e=new Au(r,o),n=[[o]];return t.runWebGLProgram(e,[],s,n)}}const Fu={kernelName:Pe,backendName:"webgl",kernelFunc:Ou};class _u{constructor(e){this.variableNames=["Image"],this.outputShape=[];const t=e[2];this.outputShape=e,this.userCode=`\n        void main() {\n          ivec4 coords = getOutputCoords();\n          int x = coords[2];\n\n          int coordX = ${t} - x - 1;\n          float outputValue;\n          if(coordX >= 0 && coordX < ${t}) {\n            outputValue = getImage(coords[0], coords[1], coordX, coords[3]);\n          } else {\n            outputValue = getImage(coords[0], coords[1], coords[2], coords[3]);\n          }\n          setOutput(outputValue);\n        }\n    `}}const Du={kernelName:Le,backendName:"webgl",kernelFunc:({inputs:e,backend:t})=>{const{image:n}=e,a=t,r=new _u(n.shape);return a.runWebGLProgram(r,[n],n.dtype)}},Pu="return floor(x);",Lu={kernelName:Be,backendName:"webgl",kernelFunc:js({opSnippet:Pu,packedOpSnippet:Pu,cpuKernelImpl:Eo})},Bu={kernelName:Ve,backendName:"webgl",kernelFunc:Ks({opSnippet:"\n  float s = sign(a) * sign(b);\n  int ia = round(a);\n  int ib = round(b);\n  if (ib != 0) {\n    // Windows (D3D) wants guaranteed non-zero int division at compile-time.\n    return float(idiv(ia, ib, s));\n  } else {\n    return NAN;\n  }\n",packedOpSnippet:"\n  ivec4 ia = round(a);\n  ivec4 ib = round(b);\n  bvec4 cond = notEqual(ib, ivec4(0));\n  ivec4 result = ivec4(0);\n  vec4 s = sign(a) * sign(b);\n\n  // Windows (D3D) wants guaranteed non-zero int division at compile-time.\n  if (cond[0]) {\n    result[0] = idiv(ia[0], ib[0], s[0]);\n  }\n  if (cond[1]) {\n    result[1] = idiv(ia[1], ib[1], s[1]);\n  }\n  if (cond[2]) {\n    result[2] = idiv(ia[2], ib[2], s[2]);\n  }\n  if (cond[3]) {\n    result[3] = idiv(ia[3], ib[3], s[3]);\n  }\n  return vec4(result);\n",dtype:"int32"})};class Vu{constructor(e){this.variableNames=["A"];const t=Da(),[n,a]=e;this.outputShape=e,this.userCode=`\n      void main() {\n        ivec3 coords = getOutputCoords();\n        int texR = coords[0];\n        int texC = coords[1];\n        int depth = coords[2];\n        vec2 uv = (vec2(texC, texR) + halfCR) / vec2(${a}.0, ${n}.0);\n\n        vec4 values = ${t.texture2D}(A, uv);\n        float value;\n        if (depth == 0) {\n          value = values.r;\n        } else if (depth == 1) {\n          value = values.g;\n        } else if (depth == 2) {\n          value = values.b;\n        } else if (depth == 3) {\n          value = values.a;\n        }\n\n        setOutput(floor(value * 255.0 + 0.5));\n      }\n    `}}class Wu{constructor(e){this.variableNames=["A"],this.packedInputs=!1,this.packedOutput=!0;const t=Da(),[n,a]=e;this.outputShape=e,this.userCode=`\n      void main() {\n        ivec3 coords = getOutputCoords();\n        int texR = coords[0];\n        int texC = coords[1];\n        int depth = coords[2];\n\n        vec4 result = vec4(0.);\n\n        for(int row=0; row<=1; row++) {\n          for(int col=0; col<=1; col++) {\n            texC = coords[1] + row;\n            depth = coords[2] + col;\n\n            vec2 uv = (vec2(texC, texR) + halfCR) /\n                       vec2(${a}.0, ${n}.0);\n            vec4 values = ${t.texture2D}(A, uv);\n            float value;\n            if (depth == 0) {\n              value = values.r;\n            } else if (depth == 1) {\n              value = values.g;\n            } else if (depth == 2) {\n              value = values.b;\n            } else if (depth == 3) {\n              value = values.a;\n            }\n\n            result[row * 2 + col] = floor(value * 255.0 + 0.5);\n          }\n        }\n\n        ${t.output} = result;\n      }\n    `}}const Uu={kernelName:We,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:r}=e;let{pixels:o}=n;const{numChannels:s}=r,i="undefined"!=typeof HTMLVideoElement&&o instanceof HTMLVideoElement,l="undefined"!=typeof HTMLImageElement&&o instanceof HTMLImageElement,[u,c]=i?[o.videoWidth,o.videoHeight]:[o.width,o.height],d=[c,u],p=[c,u,s];if(l||i){const e=t().getBool("CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU");null!=Mu&&e===Gu||(Gu=e,Mu=document.createElement("canvas").getContext("2d",{willReadFrequently:Gu})),Mu.canvas.width=u,Mu.canvas.height=c,Mu.drawImage(o,0,0,u,c),o=Mu.canvas}const h=a.makeTensorInfo(d,"int32");a.texData.get(h.dataId).usage=Pn.PIXELS,a.gpgpu.uploadPixelDataToTexture(a.getTexture(h.dataId),o);const f=t().getBool("WEBGL_PACK")?new Wu(p):new Vu(p),x=a.runWebGLProgram(f,[h],"int32");return a.disposeData(h.dataId),x}};let Mu,Gu=t().getBool("CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU");const zu={kernelName:Ue,backendName:"webgl",kernelFunc:function(e){const{inputs:a,backend:o,attrs:s}=e,{x:i,filter:l,bias:u,preluActivationWeights:c}=a,{strides:d,pad:p,dataFormat:h,dilations:f,dimRoundingMode:x,activation:m,leakyreluAlpha:g}=s,b=r.convertConv2DDataFormat(h),v=r.computeConv2DInfo(i.shape,l.shape,d,f,p,x,!1,b);let C;const $=[],y=null!=u,I=null!=c,w="leakyrelu"===m,S=()=>{const e=[i,l],t=(e,t)=>{if("NCHW"===t&&1===e.shape.length&&1!==e.shape[0]){const t=ai({inputs:{x:e},backend:o,attrs:{shape:[e.shape[0],1,1]}});return $.push(t),t}return e};if(y&&e.push(t(u,h)),I&&e.push(t(c,h)),w){const t=o.makeTensorInfo([],"float32",n.createScalarValue(g,"float32"));e.push(t),$.push(t)}return e};if(1!==v.filterHeight||1!==v.filterWidth||1!==v.dilationHeight||1!==v.dilationWidth||1!==v.strideHeight||1!==v.strideWidth||"SAME"!==v.padInfo.type&&"VALID"!==v.padInfo.type)if(v.strideWidth<=2&&"channelsLast"===b&&t().getBool("WEBGL_EXP_CONV")){const e=m?qs(m,!0):null,t=new El(v,y,e,I,w),n=[[v.padInfo.top,v.padInfo.left],[v.strideHeight,v.strideWidth],[v.dilationHeight,v.dilationWidth],[v.inHeight,v.inWidth]],a=S();C=o.runWebGLProgram(t,a,"float32",n)}else if(t().getBool("WEBGL_CONV_IM2COL"))C=_l({x:i,filter:l,convInfo:v,backend:o,bias:u,activation:m,preluActivationWeights:c,leakyreluAlpha:g});else{const e=m?qs(m,!1):null,t=new kl(v,y,e,I,w),n=S();C=o.runWebGLProgram(t,n,"float32")}else C=Fl({x:i,filter:l,convInfo:v,backend:o,bias:u,activation:m,preluActivationWeights:c,leakyreluAlpha:g});const R=ai({inputs:{x:C},backend:o,attrs:{shape:v.outShape}});return $.push(C),$.forEach((e=>o.disposeIntermediateTensorInfo(e))),R}};const Xu={kernelName:Me,backendName:"webgl",kernelFunc:function(e){const{inputs:a,backend:o,attrs:s}=e,{x:i,filter:l,bias:u,preluActivationWeights:c}=a,{strides:d,pad:p,dilations:h,dimRoundingMode:f,activation:x,leakyreluAlpha:m}=s,g=[];let b=h;null==b&&(b=[1,1]),n.assert(r.eitherStridesOrDilationsAreOne(d,b),(()=>`Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides ${d} and dilations '${b}'`));const v=r.computeConv2DInfo(i.shape,l.shape,d,b,p,f,!0),C=t().getBool("WEBGL_PACK_DEPTHWISECONV")&&v.strideWidth<=2&&v.outChannels/v.inChannels==1,$=x?qs(x,C):null,y=[i,l],I=null!=u,w=null!=c,S="leakyrelu"===x;if(I&&y.push(u),w&&y.push(c),S){const e=o.makeTensorInfo([],"float32",n.createScalarValue(m,"float32"));y.push(e),g.push(e)}let R;R=C?new iu(v,I,$,w,S):new su(v,I,$,w,S);const T=[[v.padInfo.top,v.padInfo.left],[v.strideHeight,v.strideWidth],[v.dilationHeight,v.dilationWidth],[v.inHeight,v.inWidth]],k=o.runWebGLProgram(R,y,"float32",T);return g.forEach((e=>o.disposeIntermediateTensorInfo(e))),k}};class Hu{constructor(e,t,n,a){this.sliceDim=e,this.strides=t,this.paramsShape=a,this.variableNames=["x","indices"],this.outputShape=n;const r=Za(n.length);let o="\n    int index;";for(let e=0;e<this.sliceDim;e++)o+=`\n          index = round(getIndices(coords[0], ${e}));\n          out_of_bounds = out_of_bounds || index < 0;\n          out_of_bounds = out_of_bounds || index >= ${this.paramsShape[e]};\n          flattenIndex += index * ${this.strides[e]};`;this.userCode=`\n         void main() {\n          ${r} coords = getOutputCoords();\n          int flattenIndex = 0;\n          bool out_of_bounds = false;\n\n          ${o}\n\n          setOutput(out_of_bounds ? 0.0 : getX(flattenIndex, coords[1]));\n        }\n      `}}const ju={kernelName:Ge,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a}=e,{params:o,indices:s}=t,i=s.shape,l=i[i.length-1],u=n.sizeFromShape(o.shape),[c,d,p,h]=r.prepareAndValidate(o,s),f=ai({inputs:{x:s},backend:a,attrs:{shape:[d,l]}}),x=ai({inputs:{x:o},backend:a,attrs:{shape:[n.sizeFromShape(o.shape)/p,p]}});if(a.shouldExecuteOnCPU([o,s])||"string"===o.dtype){const e=a.readSync(s.dataId),t=a.bufferSync(o),n=Ao(e,t,o.dtype,d,l,p,h,o.shape,u);return a.makeTensorInfo(c,o.dtype,n.values)}const m=new Hu(l,h,[d,p],o.shape),g=a.runWebGLProgram(m,[x,f],x.dtype),b=ai({inputs:{x:g},backend:a,attrs:{shape:c}});return a.disposeIntermediateTensorInfo(f),a.disposeIntermediateTensorInfo(x),a.disposeIntermediateTensorInfo(g),b}};class Ku{constructor(e,t){this.variableNames=["A","indices"],this.outputShape=t,this.rank=t.length;const n=Za(this.rank),a=function(e,t){const n=["resRC.x","resRC.y","resRC.z","resRC.w"],a=[];for(let t=0;t<e.length;t++)2===t?a.push("index"):a.push(`${n[t]}`);return a.join()}(e);this.userCode=`\n      void main() {\n        ${n} resRC = getOutputCoords();\n        int index = int(getIndices(resRC.x, resRC.z));\n        float inBounds = (index >= 0) && (index < ${e[2]}) ? 1.0 : 0.0;\n        setOutput(inBounds * getA(${a}));\n      }\n    `}}function qu(e){const{inputs:a,backend:o,attrs:s}=e,{x:i,indices:l}=a,{axis:u,batchDims:c}=s,d=n.parseAxisParam(u,i.shape)[0];if(t().get("DEBUG")){const e=o.readSync(l.dataId),t=i.shape[d];for(let a=0;a<e.length;++a){const r=e[a];n.assert(r<=t-1&&r>=0,(()=>`GatherV2: the index value ${r} is not in [0, ${t-1}]`))}}const p=r.segment_util.collectGatherOpShapeInfo(i,l,d,c),h=n.sizeFromShape(l.shape),f=[],x=ai({inputs:{x:i},backend:o,attrs:{shape:[p.batchSize,p.outerSize,p.dimSize,p.sliceSize]}}),m=ai({inputs:{x:l},backend:o,attrs:{shape:[p.batchSize,h/p.batchSize]}});f.push(x),f.push(m);const g=[p.batchSize,p.outerSize,h/p.batchSize,p.sliceSize];if(o.shouldExecuteOnCPU([i,l])||"string"===i.dtype){const e=o.bufferSync(m),t=o.bufferSync(x),n=Oo(t,e,g);return f.forEach((e=>o.disposeIntermediateTensorInfo(e))),o.makeTensorInfo(p.outputShape,n.dtype,n.values)}const b=new Ku(x.shape,g),v=o.runWebGLProgram(b,[x,m],x.dtype);f.push(v);const C=ai({inputs:{x:v},backend:o,attrs:{shape:p.outputShape}});return f.forEach((e=>o.disposeIntermediateTensorInfo(e))),C}const Yu={kernelName:ze,backendName:"webgl",kernelFunc:qu},Qu={kernelName:Xe,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a > b);",packedOpSnippet:"\n  return vec4(greaterThan(a, b));\n",cpuKernelImpl:Fo,dtype:"bool"})},Zu={kernelName:He,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a >= b);",packedOpSnippet:"\n  return vec4(greaterThanEqual(a, b));\n",dtype:"bool",cpuKernelImpl:_o})};const Ju={kernelName:je,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{input:a}=t;return Nu(a,!0,n)}},ec={kernelName:Ke,backendName:"webgl",kernelFunc:js({opSnippet:"return float(!isnan(x) && !isinf(x));",dtype:"bool"})},tc={kernelName:qe,backendName:"webgl",kernelFunc:js({opSnippet:"return float(isinf(x));",dtype:"bool"})},nc={kernelName:Ye,backendName:"webgl",kernelFunc:js({opSnippet:"return float(isnan(x));",dtype:"bool"})},ac={kernelName:Qe,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a < b);",packedOpSnippet:"\n  return vec4(lessThan(a, b));\n",cpuKernelImpl:Do,dtype:"bool"})},rc={kernelName:Ze,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a <= b);",packedOpSnippet:"\n  return vec4(lessThanEqual(a, b));\n",cpuKernelImpl:Po,dtype:"bool"})};const oc={kernelName:Je,backendName:"webgl",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:r,num:o}=n,s=Lo(a,r,o);return t.makeTensorInfo([s.length],"float32",s)}},sc={kernelName:et,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return x < 0.0 ? 0./0. : log(x);\n",packedOpSnippet:"\n  vec4 result = log(x);\n  bvec4 isNaN = isnan(x);\n  result.r = isNaN.r ? x.r : (x.r < 0.0 ? 0./0. : result.r);\n  result.g = isNaN.g ? x.g : (x.g < 0.0 ? 0./0. : result.g);\n  result.b = isNaN.b ? x.b : (x.b < 0.0 ? 0./0. : result.b);\n  result.a = isNaN.a ? x.a : (x.a < 0.0 ? 0./0. : result.a);\n  return result;\n",cpuKernelImpl:Bo})},ic={kernelName:tt,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return log(1.0 + x);\n"})},lc={kernelName:nt,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a >= 1.0 && b >= 1.0);",packedOpSnippet:"\n  return vec4(\n    vec4(greaterThanEqual(a, vec4(1.0))) *\n    vec4(greaterThanEqual(b, vec4(1.0))));\n",dtype:"bool"})},uc={kernelName:at,backendName:"webgl",kernelFunc:js({opSnippet:"return float(!(x >= 1.0));"})},cc={kernelName:rt,backendName:"webgl",kernelFunc:Ks({opSnippet:"return float(a >= 1.0 || b >= 1.0);",packedOpSnippet:"\n  return min(\n    vec4(greaterThanEqual(a, vec4(1.0))) +\n    vec4(greaterThanEqual(b, vec4(1.0))),\n    vec4(1.0));\n",dtype:"bool"})};class dc{constructor(e,t,n,a,r){this.variableNames=["x"],this.outputShape=[];const o=t,s=e[3]-1;let i;this.outputShape=e;const l=`float(${n}) + float(${a}) * sum`;i=.5===r?`inversesqrt(${l})`:1===r?`1.0/(${l})`:`exp(log(${l}) * float(-${r}));`,this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int r = coords[1];\n        int c = coords[2];\n        int d = coords[3];\n        float x = getX(b, r, c, d);\n        float sum = 0.0;\n        for (int j = -${o}; j <= ${o}; j++) {\n          int idx = d + j;\n          if (idx >= 0 && idx <=  ${s}) {\n            float z = getX(b, r, c, idx);\n            sum += z * z;\n          }\n        }\n        float val = x * ${i};\n        setOutput(val);\n      }\n    `}}class pc{constructor(e,t,n,a,r){this.variableNames=["x"],this.outputShape=[],this.packedInputs=!0,this.packedOutput=!0;const o=t,s=e[3]-1;let i;this.outputShape=e;const l=`float(${n}) + float(${a}) * sum`;i=.5===r?`inversesqrt(${l})`:1===r?`1.0/(${l})`:`exp(log(${l}) * float(-${r}));`,this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords.x;\n        int r = coords.y;\n        int c = coords.z;\n        int d = coords.w;\n\n        bool hasNextCol = d < ${this.outputShape[3]};\n        bool hasNextRow = c < ${this.outputShape[2]};\n\n        vec4 sum = vec4(0.);\n        vec4 xFragAtOutputCoords = getX(b, r, c, d);\n\n        vec4 xAtOutputCoords = vec4(\n          getChannel(xFragAtOutputCoords, vec2(c, d)),\n          hasNextCol ?\n            getChannel(xFragAtOutputCoords, vec2(c, d + 1)) : 0.0,\n          hasNextRow ?\n            getChannel(xFragAtOutputCoords , vec2(c + 1, d)) : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getChannel(xFragAtOutputCoords, vec2(c + 1, d + 1)) : 0.0\n        );\n\n        int firstChannel = d - ${o};\n        vec2 cache = vec2(0.);\n        if(firstChannel >= 0){\n          vec4 firstChannelFrag = getX(b, r, c, firstChannel);\n          cache.x = getChannel(firstChannelFrag, vec2(c, firstChannel));\n            if(hasNextRow){\n              cache.y = getChannel(firstChannelFrag, vec2(c + 1, firstChannel));\n            }\n        }\n\n        ivec2 depth = ivec2(d, d + 1);\n        for (int j = - ${o}; j <= ${o}; j++) {\n          ivec2 idx = depth + j;\n          bvec2 aboveLowerBound = greaterThanEqual(idx, ivec2(0));\n          bvec2 belowUpperBound = lessThanEqual(idx, ivec2(${s}));\n\n          bool depthInRange = aboveLowerBound.x && belowUpperBound.x;\n          bool depthPlusOneInRange = aboveLowerBound.y && belowUpperBound.y;\n\n          if(depthInRange || depthPlusOneInRange){\n            vec4 z = vec4(0.);\n            vec4 xFragAtCurrentDepth;\n            z.xz = cache.xy;\n            if(depthPlusOneInRange && hasNextCol){\n              xFragAtCurrentDepth = idx.y != d ?\n                getX(b, r, c, idx.y) : xFragAtOutputCoords;\n              z.y = getChannel(xFragAtCurrentDepth, vec2(c, idx.y));\n              if(hasNextRow){\n                z.w = getChannel(xFragAtCurrentDepth, vec2(c + 1, idx.y));\n              }\n            }\n            cache.xy = z.yw;\n            sum += z * z;\n          }\n        }\n        vec4 result = xAtOutputCoords * ${i};\n        setOutput(result);\n      }\n    `}}const hc={kernelName:ot,backendName:"webgl",kernelFunc:e=>{const{inputs:n,backend:a,attrs:r}=e,{x:o}=n,{depthRadius:s,bias:i,alpha:l,beta:u}=r,c=t().getBool("WEBGL_PACK_NORMALIZATION")?new pc(o.shape,s,i,l,u):new dc(o.shape,s,i,l,u);return a.runWebGLProgram(c,[o],o.dtype)}};class fc{constructor(e,t,n,a,r){this.variableNames=["inputImage","outputImage","dy"],this.outputShape=[],this.outputShape=e,this.depth=e[3],this.depthRadius=t,this.bias=n,this.alpha=a,this.beta=r,this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int r = coords[1];\n        int c = coords[2];\n\n        float result = 0.0;\n        for (int d = 0; d < ${this.depth}; ++d) {\n          int depthBegin = int(max(0.0, float(d - ${t})));\n          int depthEnd = int(min(float(${this.depth}),\n              float(d + ${t} + 1)));\n\n          const int MIN_DEPTH_BEGIN = 0;\n          const int MAX_DEPTH_END = ${this.depth};\n\n          float norm = 0.0;\n          for (int k = MIN_DEPTH_BEGIN; k < MAX_DEPTH_END; ++k) {\n            if (k < depthBegin){\n              continue;\n            }\n            else if (k >= depthBegin && k < depthEnd) {\n              norm += getInputImage(b, r, c, k) * getInputImage(b, r, c, k);\n            }\n            else {\n              break;\n            }\n          }\n\n          norm = float(${a}) * norm + float(${n});\n\n          for(int k = MIN_DEPTH_BEGIN; k < MAX_DEPTH_END; ++k){\n            if (k < depthBegin){\n              continue;\n            }\n            else if (k >= depthBegin && k < depthEnd){\n              float dyi = -2.0 * float(${a})\n                * float(${r})\n                * getInputImage(b, r, c, k) * getOutputImage(b, r, c, d)\n                / norm;\n              if (k == d) {\n                dyi += pow(norm, -1.0 * ${r});\n              }\n              if (k == coords[3]) {\n                dyi *= getDy(b, r, c, d);\n                result += dyi;\n              }\n            }\n            else {\n              break;\n            }\n          }\n      }\n      setOutput(result);\n      }\n    `}}const xc={kernelName:st,backendName:"webgl",kernelFunc:e=>{const{inputs:t,backend:n,attrs:a}=e,{x:r,y:o,dy:s}=t,{depthRadius:i,bias:l,alpha:u,beta:c}=a,d=new fc(r.shape,i,l,u,c);return n.runWebGLProgram(d,[r,o,s],r.dtype)}};function mc(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{reductionIndices:i,keepDims:l}=o,u=s.shape.length,c=n.parseAxisParam(i,s.shape);let d=c;const p=r.getAxesPermutation(d,u),h=null!=p,f=a.shouldExecuteOnCPU([s]);let x=s;if(h){if(f){const e=a.texData.get(x.dataId).values,t=new Array(u);for(let e=0;e<t.length;e++)t[e]=s.shape[p[e]];const n=hs(e,s.shape,s.dtype,p,t);x=a.makeTensorInfo(t,s.dtype);a.texData.get(x.dataId).values=n}else x=ci(s,p,a);d=r.getInnerMostAxes(d.length,u)}r.assertAxesAreInnerMostDims("max",d,u);const[m,g]=r.computeOutAndReduceShapes(x.shape,d);let b,v=m;if(l&&(v=r.expandShapeToKeepDim(m,c)),f){const e=a.texData.get(x.dataId).values,t=Vo(e,n.sizeFromShape(g),v,s.dtype);b=a.makeTensorInfo(v,s.dtype);a.texData.get(b.dataId).values=t}else b=function(e,t,a,r){const o=n.sizeFromShape(t),s=ai({inputs:{x:e},attrs:{shape:[n.sizeFromShape(e.shape)/o,o]},backend:r}),i=ii(s,e.dtype,"max",r),l=ai({inputs:{x:i},attrs:{shape:a},backend:r});return r.disposeIntermediateTensorInfo(s),r.disposeIntermediateTensorInfo(i),l}(x,g,v,a);return h&&a.disposeIntermediateTensorInfo(x),b}const gc={kernelName:it,backendName:"webgl",kernelFunc:mc},bc={kernelName:lt,backendName:"webgl",kernelFunc:Ks({opSnippet:"\n  if (isnan(a)) return a;\n  if (isnan(b)) return b;\n\n  return max(a, b);\n",packedOpSnippet:"\n  vec4 result = vec4(max(a, b));\n  bvec4 isNaNA = isnan(a);\n  bvec4 isNaNB = isnan(b);\n  bvec4 isNaN = bvec4(isNaNA.x || isNaNB.x, isNaNA.y || isNaNB.y, isNaNA.z || isNaNB.z, isNaNA.w || isNaNB.w);\n  \n  result.r = isNaN.r ? NAN : result.r;\n  result.g = isNaN.g ? NAN : result.g;\n  result.b = isNaN.b ? NAN : result.b;\n  result.a = isNaN.a ? NAN : result.a;\n\n  return result;\n",cpuKernelImpl:Wo})};const vc={kernelName:ut,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t;Oa(s,"maxPool");const{filterSize:i,strides:l,pad:u,dimRoundingMode:c}=o;n.assert(r.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in maxPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const d=r.computePool2DInfo(s.shape,i,l,1,u,c);if(1===d.filterWidth&&1===d.filterHeight&&n.arraysEqual(d.inShape,d.outShape))return Ls({inputs:{x:s},backend:a});const p=new Wi(d,"max",!1);return a.runWebGLProgram(p,[s],s.dtype)}};const Cc={kernelName:ct,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o}=t,{filterSize:s,strides:i,pad:l,dataFormat:u,dimRoundingMode:c}=a,d=r.computePool3DInfo(o.shape,s,i,[1,1,1],l,c,u),p=new Ui(d,"max",!1);return n.runWebGLProgram(p,[o],o.dtype)}};class $c{constructor(e){this.variableNames=["dy","maxPos"],this.outputShape=e.inShape;const t=e.strideHeight,n=e.strideWidth,a=e.dilationHeight,r=e.effectiveFilterHeight,o=e.effectiveFilterWidth,s=r-1-e.padInfo.top,i=o-1-e.padInfo.left,l=r*o-1;this.userCode=`\n      const ivec2 pads = ivec2(${s}, ${i});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n\n        ivec2 dyRCCorner = coords.yz - pads;\n        int dyRCorner = dyRCCorner.x;\n        int dyCCorner = dyRCCorner.y;\n\n        // Convolve dy(?, ?, d) with pos mask(:, :, d) to get dx(xR, xC, d).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${r};\n          wR += ${a}) {\n          float dyR = float(dyRCorner + wR) / ${t}.0;\n\n          if (dyR < 0.0 || dyR >= ${e.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n\n          for (int wC = 0; wC < ${o}; wC++) {\n            float dyC = float(dyCCorner + wC) / ${n}.0;\n\n            if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                fract(dyC) > 0.0) {\n              continue;\n            }\n            int idyC = int(dyC);\n\n            float dyValue = getDy(b, idyR, idyC, d);\n            int maxPosValue = ${l} - int(getMaxPos(b, idyR, idyC, d));\n\n            // Get the current value, check it against the value from the\n            // position matrix.\n            int curPosValue = wR * ${o} + wC;\n            float mask = float(maxPosValue == curPosValue ? 1.0 : 0.0);\n\n            dotProd += dyValue * mask;\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}class yc{constructor(e){this.variableNames=["dy","maxPos"],this.outputShape=e.inShape;const t=e.strideDepth,n=e.strideHeight,a=e.strideWidth,r=e.dilationDepth,o=e.dilationHeight,s=e.dilationWidth,i=e.effectiveFilterDepth,l=e.effectiveFilterHeight,u=e.effectiveFilterWidth,c=i-1-e.padInfo.front,d=l-1-e.padInfo.top,p=u-1-e.padInfo.left,h=i*l*u-1;this.userCode=`\n      const ivec3 pads = ivec3(${c}, ${d}, ${p});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int ch = coords.u;\n\n        ivec3 dyCorner = ivec3(coords.y, coords.z, coords.w) - pads;\n        int dyDCorner = dyCorner.x;\n        int dyRCorner = dyCorner.y;\n        int dyCCorner = dyCorner.z;\n\n        // Convolve dy(?, ?, ?, ch) with pos mask(:, :, :, d) to get\n        // dx(xD, xR, xC, ch).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n\n        for (int wD = 0; wD < ${i};\n           wD += ${r}) {\n          float dyD = float(dyDCorner + wD) / ${t}.0;\n\n          if (dyD < 0.0 || dyD >= ${e.outDepth}.0 || fract(dyD) > 0.0) {\n            continue;\n          }\n          int idyD = int(dyD);\n\n          for (int wR = 0; wR < ${l};\n              wR += ${o}) {\n            float dyR = float(dyRCorner + wR) / ${n}.0;\n\n            if (dyR < 0.0 || dyR >= ${e.outHeight}.0 ||\n                fract(dyR) > 0.0) {\n              continue;\n            }\n            int idyR = int(dyR);\n\n            for (int wC = 0; wC < ${u};\n                wC += ${s}) {\n              float dyC = float(dyCCorner + wC) / ${a}.0;\n\n              if (dyC < 0.0 || dyC >= ${e.outWidth}.0 ||\n                  fract(dyC) > 0.0) {\n                continue;\n              }\n              int idyC = int(dyC);\n\n              float dyValue = getDy(batch, idyD, idyR, idyC, ch);\n              int maxPosValue = ${h} -\n                  int(getMaxPos(batch, idyD, idyR, idyC, ch));\n\n              // Get the current value, check it against the value from the\n              // position matrix.\n              int curPosValue =\n                  wD * ${l} * ${u} +\n                  wR * ${u} + wC;\n              float mask = float(maxPosValue == curPosValue ? 1.0 : 0.0);\n\n              dotProd += dyValue * mask;\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `}}const Ic={kernelName:dt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:o,input:s}=t,i=s,{filterSize:l,strides:u,pad:c,dimRoundingMode:d}=a,p=r.computePool3DInfo(i.shape,l,u,[1,1,1],c,d),h=new Ui(p,"max",!0),f=n.runWebGLProgram(h,[i],i.dtype),x=new yc(p),m=n.runWebGLProgram(x,[o,f],i.dtype);return n.disposeIntermediateTensorInfo(f),m}};const wc={kernelName:pt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:o,input:s,output:i}=t,l=s;Oa([s,i],"maxPoolGrad");const{filterSize:u,strides:c,pad:d,dimRoundingMode:p}=a,h=r.computePool2DInfo(l.shape,u,c,1,d,p),f=new Wi(h,"max",!0),x=n.runWebGLProgram(f,[l],l.dtype),m=new $c(h),g=n.runWebGLProgram(m,[o,x],l.dtype);return n.disposeIntermediateTensorInfo(x),g}};const Sc={kernelName:ht,backendName:"webgl",kernelFunc:({inputs:e,attrs:t,backend:a})=>{const{x:o}=e,{filterSize:s,strides:i,pad:l,includeBatchInIndex:u}=t,c=a;n.assert(4===o.shape.length,(()=>`Error in maxPool: input must be rank 4 but got rank ${o.shape.length}.`));const d=[1,1];n.assert(r.eitherStridesOrDilationsAreOne(i,d),(()=>`Error in maxPool: Either strides or dilations must be 1. Got strides ${i} and dilations '${d}'`));const p=r.computePool2DInfo(o.shape,s,i,d,l),[h,f]=function(e,t,n,a){let r=new Wi(n,"max",!1);const o=a.runWebGLProgram(r,[e],"float32");return r=new Wi(n,"max",!0,!0,t),[o,a.runWebGLProgram(r,[e],"float32")]}(o,u,p,c);return[h,f]}};const Rc={kernelName:ft,backendName:"webgl",kernelFunc:({inputs:e,attrs:t,backend:a})=>{const{x:o}=e,{keepDims:s,axis:i}=t,l=a,u=o.shape.length,c=n.parseAxisParam(i,o.shape);let d=c;const p=r.getAxesPermutation(d,u),h=null!=p,f=l.shouldExecuteOnCPU([o]),x=[];let m=o;if(h){if(f){const e=l.texData.get(m.dataId).values,t=new Array(u);for(let e=0;e<t.length;e++)t[e]=o.shape[p[e]];const n=hs(e,o.shape,o.dtype,p,t);m=l.makeTensorInfo(t,o.dtype);l.texData.get(m.dataId).values=n}else m=ci(o,p,l);x.push(m),d=r.getInnerMostAxes(d.length,u)}r.assertAxesAreInnerMostDims("sum",d,u);const[g,b]=r.computeOutAndReduceShapes(m.shape,d);let v=g;s&&(v=r.expandShapeToKeepDim(g,c));const C=function(e,t,a,r){const o=n.sizeFromShape(t),s=ai({inputs:{x:e},attrs:{shape:[n.sizeFromShape(e.shape)/o,o]},backend:r}),i=ii(s,"float32","mean",r),l=ai({inputs:{x:i},attrs:{shape:a},backend:r});return r.disposeIntermediateTensorInfo(s),r.disposeIntermediateTensorInfo(i),l}(m,b,v,l);for(const e of x)l.disposeIntermediateTensorInfo(e);return C}};const Tc={kernelName:xt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i,keepDims:l}=o,u=s.shape.length,c=n.parseAxisParam(i,s.shape);let d=c;const p=r.getAxesPermutation(d,u);let h=s;null!=p&&(h=hi({inputs:{x:s},backend:a,attrs:{perm:p}}),d=r.getInnerMostAxes(d.length,s.shape.length)),r.assertAxesAreInnerMostDims("min",d,u);const[f,x]=r.computeOutAndReduceShapes(h.shape,d),m=ai({inputs:{x:h},backend:a,attrs:{shape:[-1,n.sizeFromShape(x)]}}),g=ii(m,m.dtype,"min",a);let b;if(l){b=ai({inputs:{x:g},backend:a,attrs:{shape:r.expandShapeToKeepDim(f,c)}})}else b=ai({inputs:{x:g},backend:a,attrs:{shape:f}});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(g),null!=p&&a.disposeIntermediateTensorInfo(h),b}},kc={kernelName:mt,backendName:"webgl",kernelFunc:Ks({opSnippet:"\n  if (isnan(a)) return a;\n  if (isnan(b)) return b;\n\n  return min(a, b);\n",packedOpSnippet:"\n  vec4 result = vec4(min(a, b));\n  bvec4 isNaNA = isnan(a);\n  bvec4 isNaNB = isnan(b);\n  bvec4 isNaN = bvec4(isNaNA.x || isNaNB.x, isNaNA.y || isNaNB.y, isNaNA.z || isNaNB.z, isNaNA.w || isNaNB.w);\n  \n  result.r = isNaN.r ? NAN : result.r;\n  result.g = isNaN.g ? NAN : result.g;\n  result.b = isNaN.b ? NAN : result.b;\n  result.a = isNaN.a ? NAN : result.a;\n\n  return result;\n",cpuKernelImpl:Uo})};class Nc{constructor(e,t,n){this.variableNames=["x"],this.outputShape=t.map(((t,n)=>t[0]+e[n]+t[1]));const a=e.length,r=Za(a),o=t.map((e=>e[0])).join(","),s=t.map(((t,n)=>t[0]+e[n])).join(","),i=["coords[0]","coords[1]","coords[2]","coords[3]"].slice(0,a),l="reflect"===n?0:1;this.userCode=1!==a?`\n      ${r} start = ${r}(${o});\n      ${r} end = ${r}(${s});\n\n      void main() {\n        ${r} outC = getOutputCoords();\n        for (int i = 0; i < ${a}; i++) {\n          if (outC[i] < start[i]) {\n            outC[i] = start[i] * 2 - outC[i] - ${l};\n          } else if(outC[i] >= end[i]) {\n            outC[i] = (end[i] - 1) * 2 - outC[i] + ${l};\n          }\n        }\n        ${r} coords = outC - start;\n        setOutput(getX(${i}));\n      }\n    `:`\n        int start = ${o};\n        int end = ${s};\n\n        void main() {\n          int outC = getOutputCoords();\n          if (outC < start) {\n            outC = start * 2 - outC - ${l};\n          } else if(outC >= end) {\n            outC = (end - 1) * 2 - outC + ${l};\n          }\n          setOutput(getX(outC - start));\n        }\n      `}}class Ec{constructor(e,t,n){this.variableNames=["x"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=t.map(((t,n)=>t[0]+e[n]+t[1]));const a=e.length,r=Za(a),o=t.map((e=>e[0])).join(","),s=t.map(((t,n)=>t[0]+e[n])).join(","),i=ms("rc",a),l=ms("source",a),u=`${i[a-1]} < ${this.outputShape[a-1]}`,c=1===a?"source":`vec2(${l.slice(-2).join()})`,d="reflect"===n?0:1;let p="";if(1===a){const e=`\n        ${r} source = rc;\n        if (source < start) {\n          source = start * 2 - source - ${d};\n        } else if (source >= end) {\n          source = (end - 1) * 2 - source + ${d};\n        }\n        source -= start;\n      `;p=`\n        ${r} rc = outputLoc;\n        ${e}\n        result[0] = getChannel(getX(${l.join()}), ${c});\n        ${i[a-1]} += 1;\n        if(${u}) {\n          ${e}\n          result[1] = getChannel(getX(${l.join()}), ${c});\n        }\n      `}else{const e=`\n        ${r} source = rc;\n        ${r} lt = ${r}(lessThan(source, start));\n        ${r} gte = ${r}(greaterThanEqual(source, end));\n        ${r} orig = 1 - (lt + gte);\n        source = orig * source +\n                lt * (start * 2 - source - ${d}) +\n                gte * ((end - 1) * 2 - source + ${d});\n        source -= start;\n      `;p=`\n        ${r} rc = outputLoc;\n        ${e}\n        result[0] = getChannel(getX(${l.join()}), ${c});\n        ${i[a-1]} += 1;\n        if(${u}) {\n          ${e}\n          result[1] = getChannel(getX(${l.join()}), ${c});\n        }\n        rc = outputLoc;\n        ${i[a-2]} += 1;\n        if(${i[a-2]} < ${this.outputShape[a-2]}) {\n          ${e}\n          result[2] = getChannel(getX(${l.join()}), ${c});\n          ${i[a-1]} += 1;\n          if(${u}) {\n            ${e}\n            result[3] = getChannel(getX(${l.join()}), ${c});\n          }\n        }\n      `}this.userCode=`\n      const ${r} start = ${r}(${o});\n      const ${r} end = ${r}(${s});\n\n      void main() {\n        ${r} outputLoc = getOutputCoords();\n        vec4 result = vec4(0.);\n        ${p}\n        setOutput(result);\n      }\n    `}}const Ac={kernelName:gt,backendName:"webgl",kernelFunc:({inputs:e,backend:n,attrs:a})=>{const{x:r}=e,{paddings:o,mode:s}=a,i=t().getBool("WEBGL_PACK_ARRAY_OPERATIONS")?new Ec(r.shape,o,s):new Nc(r.shape,o,s);return n.runWebGLProgram(i,[r],r.dtype)}},Oc={kernelName:bt,backendName:"webgl",kernelFunc:Ks({opSnippet:"if (b == 0.0) return NAN;\n  return mod(a, b);",packedOpSnippet:"\n  vec4 result = mod(a, b);\n  bvec4 isNaN = equal(b, vec4(0.0));\n  \n  result.r = isNaN.r ? NAN : result.r;\n  result.g = isNaN.g ? NAN : result.g;\n  result.b = isNaN.b ? NAN : result.b;\n  result.a = isNaN.a ? NAN : result.a;\n\n  return result;\n"})};class Fc{constructor(e,t,n){this.variableNames=["probs"],this.customUniforms=[{name:"seed",type:"float"}],this.outputShape=[e,n],this.userCode=`\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int batch = coords[0];\n\n        float r = random(seed);\n        float cdf = 0.0;\n\n        for (int i = 0; i < ${t-1}; i++) {\n          cdf += getProbs(batch, i);\n\n          if (r < cdf) {\n            setOutput(float(i));\n            return;\n          }\n        }\n\n        // If no other event happened, last event happened.\n        setOutput(float(${t-1}));\n      }\n    `}}const _c=Ks({opSnippet:"\nif (a == b) {\n  return 1.0;\n};\nreturn a / b;",packedOpSnippet:"\n  // vec4 one = vec4(equal(a, b));\n  // return one + (vec4(1.0) - one) * a / b;\n  vec4 result = a / b;\n  if(a.x == b.x) {\n    result.x = 1.;\n  }\n  if(a.y == b.y) {\n    result.y = 1.;\n  }\n  if(a.z == b.z) {\n    result.z = 1.;\n  }\n  if(a.w == b.w) {\n    result.w = 1.;\n  }\n\n  return result;\n",checkOutOfBounds:!0}),Dc={kernelName:vt,backendName:"webgl",kernelFunc:_c},Pc="return a - b;",Lc=Ks({opSnippet:Pc,packedOpSnippet:Pc,supportsComplex:!0,cpuKernelImpl:cs}),Bc={kernelName:Ct,backendName:"webgl",kernelFunc:Lc};function Vc(e){const{inputs:t,backend:a,attrs:o}=e,{logits:s}=t,{dim:i}=o,l=n.parseAxisParam([i],s.shape),u=mc({inputs:{x:s},backend:a,attrs:{reductionIndices:l,keepDims:!1}}),c=r.expandShapeToKeepDim(u.shape,l),d=ai({inputs:{x:u},backend:a,attrs:{shape:c}}),p=Lc({inputs:{a:s,b:d},backend:a}),h=yu({inputs:{x:p},backend:a}),f=di({inputs:{x:h},backend:a,attrs:{axis:l,keepDims:!1}}),x=ai({inputs:{x:f},backend:a,attrs:{shape:c}}),m=_c({inputs:{a:h,b:x},backend:a});return a.disposeIntermediateTensorInfo(u),a.disposeIntermediateTensorInfo(d),a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(h),a.disposeIntermediateTensorInfo(f),a.disposeIntermediateTensorInfo(x),m}const Wc={kernelName:$t,backendName:"webgl",kernelFunc:Vc};const Uc={kernelName:yt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{logits:r}=t,{numSamples:o,seed:s,normalized:i}=a,l=i?r:Vc({inputs:{logits:r},backend:n,attrs:{dim:r.shape.length-1}}),u=l.shape[0],c=l.shape[1],d=new Fc(u,c,o),p=[[s]],h=n.runWebGLProgram(d,[l],"int32",p);return i||n.disposeIntermediateTensorInfo(l),h}};const Mc={kernelName:It,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a}=e,{x:r}=n;if(a.shouldExecuteOnCPU([r])){const e=a.texData.get(r.dataId),[t,n]=Go(e.values,r.shape,r.dtype);return a.makeTensorInfo(n,r.dtype,t)}let o;return o=t().getBool("WEBGL_PACK_UNARY_OPERATIONS")?new Ss(r.shape,"\n  vec4 result = -x;\n  bvec4 isNaN = isnan(x);\n\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n"):new Is(r.shape,"if (isnan(x)) return x;\n  return -x;\n"),a.runWebGLProgram(o,[r],r.dtype)}},Gc=p.nonMaxSuppressionV3Impl;const zc={kernelName:wt,backendName:"webgl",kernelFunc:function(e){r.warn("tf.nonMaxSuppression() in webgl locks the UI thread. Call tf.nonMaxSuppressionAsync() instead");const{inputs:t,backend:n,attrs:a}=e,{boxes:o,scores:s}=t,{maxOutputSize:i,iouThreshold:l,scoreThreshold:u}=a,c=n.readSync(o.dataId),d=n.readSync(s.dataId),{selectedIndices:p}=Gc(c,d,i,l,u);return n.makeTensorInfo([p.length],"int32",new Int32Array(p))}},Xc=p.nonMaxSuppressionV4Impl;const Hc={kernelName:St,backendName:"webgl",kernelFunc:function(e){r.warn("tf.nonMaxSuppression() in webgl locks the UI thread. Call tf.nonMaxSuppressionAsync() instead");const{inputs:t,backend:n,attrs:a}=e,{boxes:o,scores:s}=t,{maxOutputSize:i,iouThreshold:l,scoreThreshold:u,padToMaxOutputSize:c}=a,d=n.readSync(o.dataId),p=n.readSync(s.dataId),{selectedIndices:h,validOutputs:f}=Xc(d,p,i,l,u,c);return[n.makeTensorInfo([h.length],"int32",new Int32Array(h)),n.makeTensorInfo([],"int32",new Int32Array([f]))]}},jc=p.nonMaxSuppressionV5Impl;const Kc={kernelName:Rt,backendName:"webgl",kernelFunc:function(e){r.warn("tf.nonMaxSuppression() in webgl locks the UI thread. Call tf.nonMaxSuppressionAsync() instead");const{inputs:t,backend:n,attrs:a}=e,{boxes:o,scores:s}=t,{maxOutputSize:i,iouThreshold:l,scoreThreshold:u,softNmsSigma:c}=a,d=n.readSync(o.dataId),p=n.readSync(s.dataId),h=i,f=l,x=u,m=c,{selectedIndices:g,selectedScores:b}=jc(d,p,h,f,x,m);return[n.makeTensorInfo([g.length],"int32",new Int32Array(g)),n.makeTensorInfo([b.length],"float32",new Float32Array(b))]}};class qc{constructor(e,t,n,a){this.variableNames=["indices"],this.outputShape=[e,t],this.userCode=`\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int index = round(getIndices(coords.x));\n        setOutput(mix(float(${a}), float(${n}),\n                      float(index == coords.y)));\n      }\n    `}}const Yc={kernelName:Tt,backendName:"webgl",kernelFunc:e=>{const{inputs:t,backend:a,attrs:r}=e,{indices:o}=t,{dtype:s,depth:i,onValue:l,offValue:u}=r,c=n.sizeFromShape(o.shape),d=new qc(c,i,l,u),p=ai({inputs:{x:o},backend:a,attrs:{shape:[c]}}),h=a.runWebGLProgram(d,[p],s);a.disposeIntermediateTensorInfo(p);const f=ai({inputs:{x:h},backend:a,attrs:{shape:[...o.shape,i]}});return a.disposeIntermediateTensorInfo(h),f}};function Qc(e){const{inputs:t,backend:n}=e,{x:a}=t;if("complex64"===a.dtype){const e=ul({inputs:{input:a},backend:n}),t=Qc({inputs:{x:e},backend:n}),r=Il({inputs:{input:a},backend:n}),o=Qc({inputs:{x:r},backend:n}),s=Vs({inputs:{real:t,imag:o},backend:n});return n.disposeIntermediateTensorInfo(e),n.disposeIntermediateTensorInfo(t),n.disposeIntermediateTensorInfo(r),n.disposeIntermediateTensorInfo(o),s}return Ou({attrs:{shape:a.shape,dtype:a.dtype,value:"string"===a.dtype?"":0},backend:n})}const Zc={kernelName:kt,backendName:"webgl",kernelFunc:Qc};const Jc={kernelName:Nt,backendName:"webgl",kernelFunc:function e(t){const{inputs:n,backend:a}=t,{x:r}=n;if("string"===r.dtype)throw new Error("onesLike is not supported under string dtype");if("complex64"===r.dtype){const t=ul({inputs:{input:r},backend:a}),n=e({inputs:{x:t},backend:a}),o=Il({inputs:{input:r},backend:a}),s=Qc({inputs:{x:o},backend:a}),i=Vs({inputs:{real:n,imag:s},backend:a});return a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(o),a.disposeIntermediateTensorInfo(s),i}return Ou({attrs:{shape:r.shape,dtype:r.dtype,value:1},backend:a})}};const ed={kernelName:Et,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:r}=e,{axis:o}=r;if(1===t.length)return wu({inputs:{input:t[0]},backend:a,attrs:{dim:o}});const s=t[0].shape,i=t[0].dtype;t.forEach((e=>{n.assertShapesMatch(s,e.shape,"All tensors passed to stack must have matching shapes"),n.assert(i===e.dtype,(()=>"All tensors passed to stack must have matching dtypes"))}));const l=[],u=Rl({inputs:t.map((e=>{const t=wu({inputs:{input:e},backend:a,attrs:{dim:o}});return l.push(t),t})),backend:a,attrs:{axis:o}});return l.forEach((e=>a.disposeIntermediateTensorInfo(e))),u}};class td{constructor(e,t,n){this.variableNames=["x"],this.customUniforms=[{name:"value",type:"float"}],this.outputShape=t.map(((t,n)=>t[0]+e[n]+t[1]));const a=e.length,r=Za(a),o=t.map((e=>e[0])).join(","),s=t.map(((t,n)=>t[0]+e[n])).join(","),i=["coords[0]","coords[1]","coords[2]","coords[3]"].slice(0,a);this.userCode=1!==a?`\n      ${r} start = ${r}(${o});\n      ${r} end = ${r}(${s});\n\n      void main() {\n        ${r} outC = getOutputCoords();\n        if (any(lessThan(outC, start)) || any(greaterThanEqual(outC, end))) {\n          setOutput(value);\n        } else {\n          ${r} coords = outC - start;\n          setOutput(getX(${i}));\n        }\n      }\n    `:`\n        int start = ${o};\n        int end = ${s};\n\n        void main() {\n          int outC = getOutputCoords();\n          if (outC < start || outC >= end) {\n            setOutput(value);\n          } else {\n            setOutput(getX(outC - start));\n          }\n        }\n      `}}class nd{constructor(e,t,n){this.variableNames=["x"],this.packedInputs=!0,this.packedOutput=!0,this.customUniforms=[{name:"value",type:"float"}],this.outputShape=t.map(((t,n)=>t[0]+e[n]+t[1]));const a=e.length,r=Za(a),o=t.map((e=>e[0])).join(","),s=t.map(((t,n)=>t[0]+e[n])).join(","),i=ms("rc",a),l=ms("source",a),u=`${i[a-1]} < ${this.outputShape[a-1]}`,c=1===a?"source":`vec2(${l.slice(-2).join()})`,d=[`${r} rc = outputLoc;`,`${i[a-1]} += 1;\n       if(${u}) {\n      `,1===a?"":`}\n       rc = outputLoc;\n       ${i[a-2]} += 1;\n       if(${i[a-2]} < ${this.outputShape[a-2]}) {`,1===a?"":`  ${i[a-1]} += 1;\n         if(${u}) {`],p=1===a?"rc < start || rc >= end":"any(lessThan(rc, start)) || any(greaterThanEqual(rc, end))";let h="";for(let e=0,t=1===a?2:4;e<t;e++)h+=`\n        ${d[e]}\n        if (${p}) {\n          result[${e}] = float(value);\n        } else {\n          ${r} source = rc - start;\n          result[${e}] = getChannel(getX(${l.join()}), ${c});\n        }\n      `;h+=1===a?"} ":"}}",this.userCode=`\n      const ${r} start = ${r}(${o});\n      const ${r} end = ${r}(${s});\n\n      void main() {\n        ${r} outputLoc = getOutputCoords();\n        vec4 result = vec4(0.);\n        ${h}\n        setOutput(result);\n      }\n    `}}const ad=e=>{const{inputs:a,backend:r,attrs:o}=e,{x:s}=a,{paddings:i,constantValue:l}=o;if(0===n.sizeFromShape(s.shape)){return Ou({backend:r,attrs:{shape:i.map(((e,t)=>e[0]+s.shape[t]+e[1])),value:l,dtype:s.dtype}})}const u=t().getBool("WEBGL_PACK_ARRAY_OPERATIONS")?new nd(s.shape,i,l):new td(s.shape,i,l),c=[[l]];return r.runWebGLProgram(u,[s],s.dtype,c)},rd={kernelName:At,backendName:"webgl",kernelFunc:ad},od={kernelName:Ot,backendName:"webgl",kernelFunc:Ks({opSnippet:"\n  if(a < 0.0 && floor(b) < b){\n    return NAN;\n  }\n  if (b == 0.0) {\n    return 1.0;\n  }\n  return (round(mod(b, 2.0)) != 1) ?\n      pow(abs(a), b) : sign(a) * pow(abs(a), b);\n",packedOpSnippet:"\n  // isModRound1 has 1 for components with round(mod(b, 2.0)) == 1, 0 otherwise.\n  vec4 isModRound1 = vec4(equal(round(mod(b, 2.0)), ivec4(1)));\n  vec4 multiplier = sign(a) * isModRound1 + (vec4(1.0) - isModRound1);\n  vec4 result = multiplier * pow(abs(a), b);\n\n  // Ensure that a^0 = 1, including 0^0 = 1 as this correspond to TF and JS\n  bvec4 isExpZero = equal(b, vec4(0.0));\n  result.r = isExpZero.r ? 1.0 : result.r;\n  result.g = isExpZero.g ? 1.0 : result.g;\n  result.b = isExpZero.b ? 1.0 : result.b;\n  result.a = isExpZero.a ? 1.0 : result.a;\n\n  bvec4 isNaN1 = lessThan(a, vec4(0.0));\n  bvec4 isNaN2 = lessThan(floor(b), b);\n  bvec4 isNaN = bvec4(isNaN1.x && isNaN2.x, isNaN1.y && isNaN2.y, isNaN1.z && isNaN2.z, isNaN1.w && isNaN2.w);\n  \n  result.r = isNaN.r ? NAN : result.r;\n  result.g = isNaN.g ? NAN : result.g;\n  result.b = isNaN.b ? NAN : result.b;\n  result.a = isNaN.a ? NAN : result.a;\n\n  return result;\n"})};const sd={kernelName:Ft,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{axis:i,keepDims:l}=o,u=s.shape.length,c=[],d=n.parseAxisParam(i,s.shape);let p=d;const h=r.getAxesPermutation(p,u);let f,x=s;if(null!=h&&(x=hi({inputs:{x:s},backend:a,attrs:{perm:h}}),p=r.getInnerMostAxes(p.length,u),c.push(x)),r.assertAxesAreInnerMostDims("prod",p,u),a.shouldExecuteOnCPU([x])){const e=a.texData.get(x.dataId).values,{outVals:t,outShape:n,outDtype:r}=Xo(x.shape,x.dtype,e,p);f=a.makeTensorInfo(n,r,t)}else{const[e,t]=r.computeOutAndReduceShapes(x.shape,p),o=n.sizeFromShape(t),i=ai({inputs:{x:x},backend:a,attrs:{shape:[-1,o]}}),l=ii(i,S(s.dtype),"prod",a);f=ai({inputs:{x:l},backend:a,attrs:{shape:e}}),c.push(i),c.push(l)}if(l){c.push(f);const e=r.expandShapeToKeepDim(f.shape,d);f=ai({inputs:{x:f},backend:a,attrs:{shape:e}})}return c.forEach((e=>a.disposeIntermediateTensorInfo(e))),f}};const id={kernelName:_t,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{paramsNestedSplits:r,paramsDenseValues:o,indices:s}=t,{outputRaggedRank:i}=a,l=r.map((e=>n.readSync(e.dataId))),u=r.map((e=>e.shape)),c=n.readSync(o.dataId),d=n.readSync(s.dataId),[p,h,f]=Ho(l,u,c,o.shape,o.dtype,d,s.shape,i),x=p.map((e=>n.makeTensorInfo([e.length],"int32",e))),m=n.makeTensorInfo(f,o.dtype,h);return x.concat([m])}};const ld={kernelName:Dt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{starts:a,limits:r,deltas:o}=t,s=n.readSync(a.dataId),i=n.readSync(r.dataId),l=n.readSync(o.dataId),[u,c]=jo(s,a.shape,a.dtype,i,r.shape,l,o.shape);return[n.makeTensorInfo([u.length],"int32",u),n.makeTensorInfo([c.length],a.dtype,c)]}};const ud={kernelName:Pt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{shape:r,values:o,defaultValue:s,rowPartitionTensors:i}=t,{rowPartitionTypes:l}=a,u=n.readSync(r.dataId),c=n.readSync(o.dataId),d=n.readSync(s.dataId),p=i.map((e=>n.readSync(e.dataId))),h=i.map((e=>e.shape)),[f,x]=Ko(u,r.shape,c,o.shape,o.dtype,d,s.shape,p,h,l);return n.makeTensorInfo(f,o.dtype,x)}},cd=e=>{const{backend:t,attrs:n}=e,{start:a,stop:r,step:o,dtype:s}=n,i=qo(a,r,o,s);return t.makeTensorInfo([i.length],s,i)},dd={kernelName:Lt,backendName:"webgl",kernelFunc:cd},pd={kernelName:Bt,backendName:"webgl",kernelFunc:js({opSnippet:"return 1.0 / x;"})},hd={kernelName:Vt,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return (x < 0.0) ? 0.0 : x;\n",packedOpSnippet:"\n  vec4 result = x * vec4(greaterThanEqual(x, vec4(0.0)));\n  bvec4 isNaN = isnan(x);\n\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n"})},fd={kernelName:Wt,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return (x < 0.0) ? 0.0 : min(6.0, x);\n",packedOpSnippet:"\n  vec4 result = min(x, vec4(6.)) * vec4(greaterThanEqual(x, vec4(0.0)));\n  bvec4 isNaN = isnan(x);\n\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n"})};class xd{constructor(e,t,n,a,r){this.variableNames=["A"],this.outputShape=[];const[o,s,i,l]=e;this.outputShape=[o,t,n,l];const u=[a&&t>1?s-1:s,a&&n>1?i-1:i],c=[a&&t>1?t-1:t,a&&n>1?n-1:n];let d;d=r?"(vec2(yRC) + vec2(0.5)) * effectiveInputOverOutputRatioRC - vec2(0.5)":"vec2(yRC) * effectiveInputOverOutputRatioRC",this.userCode=`\n      const vec2 effectiveInputOverOutputRatioRC = vec2(\n          ${u[0]/c[0]},\n          ${u[1]/c[1]});\n      const vec2 inputShapeRC = vec2(${s}.0, ${i}.0);\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        ivec2 yRC = coords.yz;\n\n        // Fractional source index.\n        vec2 sourceFracIndexRC = ${d};\n\n        // Compute the four integer indices.\n        ivec2 sourceFloorRC = ivec2(max(sourceFracIndexRC, vec2(0.0)));\n        ivec2 sourceCeilRC = ivec2(\n          min(inputShapeRC - 1.0, ceil(sourceFracIndexRC)));\n\n        float topLeft = getA(b, sourceFloorRC.x, sourceFloorRC.y, d);\n        float bottomLeft = getA(b, sourceCeilRC.x, sourceFloorRC.y, d);\n        float topRight = getA(b, sourceFloorRC.x, sourceCeilRC.y, d);\n        float bottomRight = getA(b, sourceCeilRC.x, sourceCeilRC.y, d);\n\n        vec2 fracRC = sourceFracIndexRC - vec2(sourceFloorRC);\n\n        float top = topLeft + (topRight - topLeft) * fracRC.y;\n        float bottom = bottomLeft + (bottomRight - bottomLeft) * fracRC.y;\n        float newValue = top + (bottom - top) * fracRC.x;\n\n        setOutput(newValue);\n      }\n    `}}class md{constructor(e,t,n,a,r){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=[];const[o,s,i,l]=e;this.outputShape=[o,t,n,l];const u=[a&&t>1?s-1:s,a&&n>1?i-1:i],c=[a&&t>1?t-1:t,a&&n>1?n-1:n];let d;d=r?"(vec3(yRC) + vec3(0.5)) * effectiveInputOverOutputRatioRC - vec3(0.5)":"vec3(yRC) * effectiveInputOverOutputRatioRC",this.userCode=`\n      const vec3 effectiveInputOverOutputRatioRC = vec3(\n          ${u[0]/c[0]},\n          ${u[1]/c[1]},\n          ${u[1]/c[1]});\n      const vec3 inputShapeRC = vec3(${s}.0, ${i}.0,\n                                     ${i}.0);\n\n      float getAValue(int b, int r, int c, int d) {\n        return getChannel(getA(b, r, c, d), vec2(c, d));\n      }\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        // Calculate values for next column in yRC.z.\n        ivec3 yRC = coords.yzz + ivec3(0, 0, 1);\n\n        // Fractional source index.\n        vec3 sourceFracIndexRC = ${d};\n\n        // Compute the four integer indices.\n        ivec3 sourceFloorRC = ivec3(max(sourceFracIndexRC, vec3(0.0)));\n        ivec3 sourceCeilRC = ivec3(\n          min(inputShapeRC - 1.0, ceil(sourceFracIndexRC)));\n\n        // Should we calculate next column and row elements in 2x2 packed cell.\n        bool hasNextCol = d < ${l-1};\n        bool hasNextRow = coords.z < ${n-1};\n\n        // In parallel, construct four corners for all four components in\n        // packed 2x2 cell.\n        vec4 topLeft = vec4(\n          getAValue(b, sourceFloorRC.x, sourceFloorRC.y, d),\n          hasNextCol ? getAValue(b, sourceFloorRC.x, sourceFloorRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceFloorRC.x, sourceFloorRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceFloorRC.x, sourceFloorRC.z, d + 1) : 0.0);\n\n        vec4 bottomLeft = vec4(\n          getAValue(b, sourceCeilRC.x, sourceFloorRC.y, d),\n          hasNextCol ? getAValue(b, sourceCeilRC.x, sourceFloorRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceCeilRC.x, sourceFloorRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceCeilRC.x, sourceFloorRC.z, d + 1) : 0.0);\n\n        vec4 topRight = vec4(\n          getAValue(b, sourceFloorRC.x, sourceCeilRC.y, d),\n          hasNextCol ? getAValue(b, sourceFloorRC.x, sourceCeilRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceFloorRC.x, sourceCeilRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceFloorRC.x, sourceCeilRC.z, d + 1) : 0.0);\n\n        vec4 bottomRight = vec4(\n          getAValue(b, sourceCeilRC.x, sourceCeilRC.y, d),\n          hasNextCol ? getAValue(b, sourceCeilRC.x, sourceCeilRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceCeilRC.x, sourceCeilRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceCeilRC.x, sourceCeilRC.z, d + 1) : 0.0);\n\n        vec3 fracRC = sourceFracIndexRC - vec3(sourceFloorRC);\n\n        vec4 top = mix(topLeft, topRight, fracRC.yyzz);\n        vec4 bottom = mix(bottomLeft, bottomRight, fracRC.yyzz);\n        vec4 newValue = mix(top, bottom, fracRC.x);\n\n        setOutput(newValue);\n      }\n    `}}const gd={kernelName:Ut,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:r}=e,{images:o}=n,{alignCorners:s,halfPixelCenters:i,size:l}=r,[u,c]=l,d=t().getBool("WEBGL_PACK_IMAGE_OPERATIONS")?new md(o.shape,u,c,s,i):new xd(o.shape,u,c,s,i);return a.runWebGLProgram(d,[o],"float32")}};class bd{constructor(e,t,n){this.variableNames=["dy"],this.outputShape=[],this.outputShape=t;const[,a,r]=t,[,o,s]=e,i=[n&&o>1?a-1:a,n&&s>1?r-1:r],l=[n&&o>1?o-1:o,n&&s>1?s-1:s],u=i[0]/l[0],c=i[1]/l[1],d=1/u,p=1/c,h=2*Math.ceil(d)+2,f=2*Math.ceil(p)+2;this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        int r = coords[1];\n        int c = coords[2];\n\n        float accumulator = 0.0;\n\n        const float heightScale = float(${u});\n        const float widthScale = float(${c});\n\n        const float invHeightScale = float(${d});\n        const float invWidthScale = float(${p});\n\n        const int winHeight = int(${h});\n        const int winWidth = int(${f});\n\n        // Compute bounds for where in dy we will look\n        float startRLerp = floor(float(r) * invHeightScale);\n        int startDyR = int(startRLerp - float(winHeight / 2));\n\n        float startCLerp = floor(float(c) * invWidthScale);\n        int startDyC = int(startCLerp - float(winWidth / 2));\n\n        // Loop over dy\n        for (int dyROffset = 0; dyROffset < winHeight; dyROffset++) {\n          int dyR = dyROffset + startDyR;\n\n          // Guard against the window exceeding the bounds of dy\n          if (dyR < 0 || dyR >= ${o}) {\n            continue;\n          }\n\n          for (int dyCOffset = 0; dyCOffset < winWidth; dyCOffset++) {\n            int dyC = dyCOffset + startDyC;\n\n            // Guard against the window exceeding the bounds of dy\n            if (dyC < 0 || dyC >= ${s}) {\n              continue;\n            }\n\n            float dxR = float(dyR) * heightScale;\n            int topDxRIndex = int(floor(dxR));\n            int bottomDxRIndex = int(min(ceil(dxR), ${a-1}.0));\n            float dxRLerp = dxR - float(topDxRIndex);\n            float inverseDxRLerp = 1.0 - dxRLerp;\n\n            float dxC = float(dyC) * widthScale;\n            int leftDxCIndex = int(floor(dxC));\n            int rightDxCIndex = int(min(ceil(dxC), ${r-1}.0));\n            float dxCLerp = dxC - float(leftDxCIndex);\n            float inverseDxCLerp = 1.0 - dxCLerp;\n\n            if (r == topDxRIndex && c == leftDxCIndex) {\n              // topLeft\n              accumulator +=\n                getDy(b, dyR, dyC, d) * inverseDxRLerp * inverseDxCLerp;\n            }\n\n            if (r == topDxRIndex && c == rightDxCIndex) {\n              // topRight\n              accumulator += getDy(b, dyR, dyC, d) * inverseDxRLerp * dxCLerp;\n            }\n\n            if (r == bottomDxRIndex && c == leftDxCIndex) {\n              // bottomLeft\n              accumulator += getDy(b, dyR, dyC, d) * dxRLerp * inverseDxCLerp;\n            }\n\n            if (r == bottomDxRIndex && c == rightDxCIndex) {\n              // bottomRight\n              accumulator += getDy(b, dyR, dyC, d) * dxRLerp * dxCLerp;\n            }\n          }\n        }\n        // End loop over dy\n\n        setOutput(accumulator);\n      }\n    `}}const vd={kernelName:Mt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{images:r,dy:o}=t,{alignCorners:s}=a,i=new bd(o.shape,r.shape,s);return n.runWebGLProgram(i,[o],o.dtype)}};class Cd{constructor(e,t,n,a,r){this.variableNames=["A"],this.outputShape=[];const[o,s,i,l]=e;this.outputShape=[o,t,n,l];const u=[a&&t>1?s-1:s,a&&n>1?i-1:i],c=[a&&t>1?t-1:t,a&&n>1?n-1:n],d=a?"0.5":"0.0";let p;p=r?"max((vec2(yRC) + vec2(0.5)) * effectiveInputOverOutputRatioRC, vec2(0.0))":"vec2(yRC) * effectiveInputOverOutputRatioRC",this.userCode=`\n      const vec2 effectiveInputOverOutputRatioRC = vec2(\n          ${u[0]/c[0]},\n          ${u[1]/c[1]});\n      const vec2 inputShapeRC = vec2(${s}.0, ${i}.0);\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        ivec2 yRC = coords.yz;\n\n        // Fractional source index.\n        vec2 sourceFracIndexRC = ${p};\n\n        // Compute the coordinators of nearest neighbor point.\n        ivec2 sourceNearestRC = ivec2(\n          min(inputShapeRC - 1.0, floor(sourceFracIndexRC + ${d})));\n        float newValue = getA(b, sourceNearestRC.x, sourceNearestRC.y, d);\n\n        setOutput(newValue);\n      }\n    `}}class $d{constructor(e,t,n,a,r){this.variableNames=["A"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=[];const[o,s,i,l]=e;this.outputShape=[o,t,n,l];const u=[a&&t>1?s-1:s,a&&n>1?i-1:i],c=[a&&t>1?t-1:t,a&&n>1?n-1:n],d=a?"0.5":"0.0";let p;p=r?"max((vec3(yRC) + vec3(0.5)) * effectiveInputOverOutputRatioRC, vec3(0.0))":"vec3(yRC) * effectiveInputOverOutputRatioRC",this.userCode=`\n      const vec3 effectiveInputOverOutputRatioRC = vec3(\n          ${u[0]/c[0]},\n          ${u[1]/c[1]},\n          ${u[1]/c[1]});\n      const vec3 inputShapeRC = vec3(${s}.0, ${i}.0,\n                                     ${i}.0);\n\n      float getAValue(int b, int r, int c, int d) {\n        return getChannel(getA(b, r, c, d), vec2(c, d));\n      }\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        // Calculate values for next column in yRC.z.\n        ivec3 yRC = coords.yzz + ivec3(0, 0, 1);\n\n        // Fractional source index.\n        vec3 sourceFracIndexRC = ${p};\n\n        // Compute the coordinators of nearest neighbor point.\n        ivec3 sourceNearestRC = ivec3(\n          min(inputShapeRC - 1.0, floor(sourceFracIndexRC + ${d})));\n\n        // Should we calculate next column and row elements in 2x2 packed cell.\n        bool hasNextCol = d < ${l-1};\n        bool hasNextRow = coords.z < ${n-1};\n\n        vec4 newValue = vec4(\n          getAValue(b, sourceNearestRC.x, sourceNearestRC.y, d),\n          hasNextCol ? getAValue(b, sourceNearestRC.x, sourceNearestRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceNearestRC.x, sourceNearestRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceNearestRC.x, sourceNearestRC.z, d + 1) : 0.0);\n\n        setOutput(newValue);\n      }\n    `}}const yd={kernelName:Gt,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:r}=e,{images:o}=n,{alignCorners:s,halfPixelCenters:i,size:l}=r,[u,c]=l,d=t().getBool("WEBGL_PACK_IMAGE_OPERATIONS")?new $d(o.shape,u,c,s,i):new Cd(o.shape,u,c,s,i);return a.runWebGLProgram(d,[o],o.dtype)}};class Id{constructor(e,t,n){this.variableNames=["dy"],this.outputShape=[],this.outputShape=t;const[,a,r]=t,[,o,s]=e,i=[n&&o>1?a-1:a,n&&s>1?r-1:r],l=[n&&o>1?o-1:o,n&&s>1?s-1:s],u=i[0]/l[0],c=i[1]/l[1],d=1/u,p=1/c,h=2*Math.ceil(d)+2,f=2*Math.ceil(p)+2;this.userCode=`\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        int r = coords[1];\n        int c = coords[2];\n\n        float accumulator = 0.0;\n\n        const float heightScale = float(${u});\n        const float widthScale = float(${c});\n\n        const float invHeightScale = float(${d});\n        const float invWidthScale = float(${p});\n\n        const int winHeight = int(${h});\n        const int winWidth = int(${f});\n\n        // Compute bounds for where in dy we will look\n        float startRLerp = floor(float(r) * invHeightScale);\n        int startDyR = int(floor(startRLerp - float(winHeight / 2)));\n\n        float startCLerp = floor(float(c) * invWidthScale);\n        int startDyC = int(floor(startCLerp - float(winWidth / 2)));\n\n        // Loop over dy\n        for (int dyROffset = 0; dyROffset < winHeight; dyROffset++) {\n          int dyR = dyROffset + startDyR;\n\n          // Guard against the window exceeding the bounds of dy\n          if (dyR < 0 || dyR >= ${o}) {\n            continue;\n          }\n\n          for (int dyCOffset = 0; dyCOffset < winWidth; dyCOffset++) {\n            int dyC = dyCOffset + startDyC;\n\n            // Guard against the window exceeding the bounds of dy\n            if (dyC < 0 || dyC >= ${s}) {\n              continue;\n            }\n\n            float sourceFracRow =\n              float(${i[0]}) *\n                (float(dyR) / float(${l[0]}));\n\n            float sourceFracCol =\n                float(${i[1]}) *\n                  (float(dyC) / float(${l[1]}));\n\n            int sourceNearestRow = int(min(\n                float(int(${a}) - 1),\n                ${n} ? float(round(sourceFracRow)) :\n                                  float(floor(sourceFracRow))));\n\n            int sourceNearestCol = int(min(\n                float(int(${r}) - 1),\n                ${n} ? float(round(sourceFracCol)) :\n                                  float(floor(sourceFracCol))));\n\n            if (r == sourceNearestRow && c == sourceNearestCol) {\n              accumulator += getDy(b, dyR, dyC, d);\n            }\n          }\n        }\n        // End loop over dy\n\n        setOutput(accumulator);\n      }\n    `}}const wd={kernelName:zt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{images:r,dy:o}=t,{alignCorners:s}=a,i=new Id(o.shape,r.shape,s);return n.runWebGLProgram(i,[o],o.dtype)}};class Sd{constructor(e,t){this.variableNames=["x"];const n=e.length;if(n>4)throw new Error(`WebGL backend: Reverse of rank-${n} tensor is not yet supported`);if(this.outputShape=e,1===n)return void(this.userCode=`\n        void main() {\n          int coord = getOutputCoords();\n          setOutput(getX(${e[0]} - coord - 1));\n        }\n      `);const a=e.map(((n,a)=>(n=>-1!==t.indexOf(n)&&1!==e[n]?`${e[n]} - coords[${n}] - 1`:`coords[${n}]`)(a))).join(","),r=Za(n);this.userCode=`\n      void main() {\n        ${r} coords = getOutputCoords();\n        setOutput(getX(${a}));\n      }\n    `}}class Rd{constructor(e,t){this.variableNames=["x"],this.packedInputs=!0,this.packedOutput=!0;const n=e.length;if(n>4)throw new Error(`WebGL backend: Reverse of rank-${n} tensor is not yet supported`);this.outputShape=e;const a=ms("rc",n),r=`${a[n-1]} + 1 < ${this.outputShape[n-1]}`,o=`${a[n-2]} + 1 < ${this.outputShape[n-2]}`,s=Za(n);function i(n){const a=e.map(((a,r)=>function(n,a){return-1!==t.indexOf(n)&&1!==e[n]?`${e[n]} - ${a[n]} - 1`:`${a[n]}`}(r,n)));return`getChannel(getX(${a.join(",")}), vec2(${a.slice(-2).join(",")}))`}this.userCode=1===n?`\n        void main(){\n          int rc = getOutputCoords();\n          vec4 result = vec4(0.);\n          result.r = getChannel(getX(${e[0]} - rc - 1),\n            ${e[0]} - rc - 1);\n          if(${r}){\n              result.g = getChannel(getX(${e[0]} - (rc  + 1) - 1),\n                ${e[0]} - (rc  + 1) - 1);\n          }\n          setOutput(result);\n        }\n      `:`\n        void main() {\n          ${s} rc = getOutputCoords();\n          vec4 result = vec4(0.);\n          result.r = ${function(e){return i(e)}(a.slice())};\n          if(${r}){\n            result.g = ${function(e){return e[n-1]="("+e[n-1]+" + 1)",i(e)}(a.slice())};\n          }\n          if(${o}) {\n            result.b = ${function(e){return e[n-2]="("+e[n-2]+" + 1)",i(e)}(a.slice())};\n            if(${r}) {\n              result.a = ${function(e){return e[n-1]="("+e[n-1]+" + 1)",e[n-2]="("+e[n-2]+" + 1)",i(e)}(a.slice())};\n            }\n          }\n          setOutput(result);\n        }\n    `}}const Td={kernelName:Xt,backendName:"webgl",kernelFunc:function(e){const{inputs:a,backend:r,attrs:o}=e,{x:s}=a,{dims:i}=o,l=s.shape.length,u=n.parseAxisParam(i,s.shape);if(0===l)return Ls({inputs:{x:s},backend:r});const c=t().getBool("WEBGL_PACK_ARRAY_OPERATIONS")?new Rd(s.shape,u):new Sd(s.shape,u);return r.runWebGLProgram(c,[s],s.dtype)}};class kd{constructor(e,t){this.variableNames=["Image"],this.outputShape=[],this.customUniforms=[{name:"params",type:"vec4"}];const n=e[1],a=e[2];this.outputShape=e;let r="";r="number"==typeof t?`float outputValue = ${t.toFixed(2)};`:`\n        vec3 fill = vec3(${t.join(",")});\n        float outputValue = fill[coords[3]];`,this.userCode=`\n        void main() {\n          ivec4 coords = getOutputCoords();\n          int x = coords[2];\n          int y = coords[1];\n          float coordXFloat = (float(x) - params[0]) * params[3] -\n            (float(y) - params[1]) * params[2];\n          float coordYFloat = (float(x) - params[0]) * params[2] +\n            (float(y) - params[1]) * params[3];\n          int coordX = int(round(coordXFloat + params[0]));\n          int coordY = int(round(coordYFloat + params[1]));\n          ${r}\n          if(coordX >= 0 && coordX < ${a} && coordY >= 0 && coordY < ${n}) {\n            outputValue = getImage(coords[0], coordY, coordX, coords[3]);\n          }\n          setOutput(outputValue);\n        }\n    `}}const Nd={kernelName:Ht,backendName:"webgl",kernelFunc:({inputs:e,attrs:t,backend:n})=>{const{image:a}=e,{radians:o,fillValue:s,center:i}=t,l=n,u=new kd(a.shape,s),[c,d]=r.getImageCenter(i,a.shape[1],a.shape[2]),p=[[c,d,Math.sin(o),Math.cos(o)]];return l.runWebGLProgram(u,[a],a.dtype,p)}},Ed={kernelName:jt,backendName:"webgl",kernelFunc:js({opSnippet:"\n  // OpenGL ES does not support round function.\n  // The algorithm is based on banker's rounding.\n  float base = floor(x);\n  if ((x - base) < 0.5) {\n    return floor(x);\n  } else if ((x - base) > 0.5) {\n    return ceil(x);\n  } else {\n    if (mod(base, 2.0) == 0.0) {\n      return base;\n    } else {\n      return base + 1.0;\n    }\n  }\n"})},Ad={kernelName:Kt,backendName:"webgl",kernelFunc:js({opSnippet:"return inversesqrt(x);",cpuKernelImpl:Yo})};class Od{constructor(e,t,n,a,r,o,s=!0,i=!1){this.variableNames=["updates","indices","defaultValue"],this.outputShape=o;const l=Za(r.length),u=Za(o.length);let c="";1===n?c="i":2===n&&(c="i, j");const d=`getIndices(${c})`;let p="";1===a?p="i":2===a&&(p="i, coords[1]");const h=`getUpdates(${p})`;let f="";i&&(f="coords[0], coords[1]");const x=`getDefaultValue(${f})`,m=t>1?"strides[j]":"strides";this.userCode=`\n        ${l} strides = ${l}(${r});\n\n        void main() {\n          ${u} coords = getOutputCoords();\n          float sum = 0.0;\n          bool found = false;\n          for (int i = 0; i < ${e}; i++) {\n            int flattenedIndex = 0;\n            for (int j = 0; j < ${t}; j++) {\n              int index = round(${d});\n              flattenedIndex += index * ${m};\n            }\n            if (flattenedIndex == coords[0]) {\n              sum += ${h};\n              found = true;\n            }\n          }\n          setOutput(mix(${x}, sum, float(found)));\n        }\n      `}}class Fd{constructor(e,t,n,a,r,o,s=!0,i=!1){this.variableNames=["updates","indices","defaultValue"],this.packedInputs=!0,this.packedOutput=!0,this.outputShape=o;const l=Za(r.length),u=Za(o.length);let c="";1===n?c="i":2===n&&(c="i, j");const d=`getIndices(${c})`;let p="";1===a?p="i":2===a&&(p="i, coords[1]");const h=`getUpdates(${p})`;let f="";i&&(f="coords[0], coords[1]");const x=`getDefaultValue(${f})`,m=t>1?"strides[j]":"strides",g=t>1?"strides[j + 1]":"strides";this.userCode=`\n        ${l} strides = ${l}(${r});\n\n        void main() {\n          ${u} coords = getOutputCoords();\n          vec4 sum = vec4(0.);\n          vec4 found = vec4(0.);\n          for (int i = 0; i < ${e}; i+=2) {\n            ivec2 flattenedIndex = ivec2(0);\n            for (int j = 0; j < ${t}; j+=2) {\n              ivec4 index = round(${d});\n              flattenedIndex += index.xz * ${m};\n              if (j + 1 < ${t}) {\n                flattenedIndex += index.yw * ${g};\n              }\n            }\n            if (flattenedIndex[0] == coords[0] || flattenedIndex[1] == coords[0] ||\n                flattenedIndex[0] == coords[0] + 1 || flattenedIndex[1] == coords[0] + 1) {\n              vec4 updVals = ${h};\n              if (flattenedIndex[0] == coords[0]) {\n                sum.xy += updVals.xy;\n                found.xy = vec2(1.);\n              } else if (flattenedIndex[0] == coords[0] + 1) {\n                sum.zw += updVals.xy;\n                found.zw = vec2(1.);\n              }\n              if (flattenedIndex[1] == coords[0]) {\n                sum.xy += updVals.zw;\n                found.xy = vec2(1.);\n              } else if (flattenedIndex[1] == coords[0] + 1) {\n                sum.zw += updVals.zw;\n                found.zw = vec2(1.);\n              }\n            }\n          }\n          setOutput(mix(${x}, sum, found));\n        }\n      `}}const _d={kernelName:qt,backendName:"webgl",kernelFunc:function(e){const{inputs:n,backend:a,attrs:o}=e,{indices:s,updates:i}=n,{shape:l}=o,{sliceRank:u,numUpdates:c,sliceSize:d,strides:p,outputSize:h}=r.calculateShapes(i,s,l),f=[h/d,d];if(0===h)return a.makeTensorInfo(l,s.dtype);const x=ai({inputs:{x:s},backend:a,attrs:{shape:[c,u]}}),m=ai({inputs:{x:i},backend:a,attrs:{shape:[c,d]}}),g=a.makeTensorInfo([],"float32",new Float32Array([0]));let b;b=t().getBool("WEBGL_PACK")?new Fd(c,u,x.shape.length,m.shape.length,p,f):new Od(c,u,x.shape.length,m.shape.length,p,f);const v=a.runWebGLProgram(b,[m,x,g],m.dtype),C=ai({inputs:{x:v},backend:a,attrs:{shape:l}});return a.disposeIntermediateTensorInfo(x),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(v),a.disposeIntermediateTensorInfo(g),C}};class Dd{constructor(e,n,a,r){this.variableNames=["sortedSequence","values"],this.customUniforms=[{name:"numInputs",type:"int"}],this.outputShape=[e,a];const o=`for (int i = 0; i < ${Math.ceil(Math.log2(n+1))}; ++i) { if (left >= right) break;`,s=2===t().getNumber("WEBGL_VERSION")?"while (left < right) {":o,i="left"===r?"<":"<=";this.userCode=`\n       int findBound(int batch, float value) {\n         int left = 0;\n         int right = numInputs;\n         int mid;\n         ${s}\n           mid = (left + right) / 2;\n           if (getSortedSequence(batch, mid) ${i} value) {\n             left = mid + 1;\n           } else {\n             right = mid;\n           }\n         }\n         return right;\n       }\n\n       void main() {\n         ivec2 coords = getOutputCoords();\n         int batch = coords[0];\n         int valueIndex = coords[1];\n\n         float value = getValues(batch, valueIndex);\n\n         setOutput(float(findBound(batch, value)));\n       }\n     `}}const Pd={kernelName:Yt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{sortedSequence:r,values:o}=t,{side:s}=a,i=new Dd(r.shape[0],r.shape[1],o.shape[1],s),l=[[r.shape[1]]];return n.runWebGLProgram(i,[r,o],"int32",l)}};class Ld{constructor(e,t,n){let a,r;if(this.variableNames=["c","a","b"],this.outputShape=t,n>4)throw Error(`Where for rank ${n} is not yet supported`);if(1===n)r="resRC",a="resRC";else{const n=["resRC.x","resRC.y","resRC.z","resRC.w"],o=[],s=[];for(let a=0;a<t.length;a++)s.push(`${n[a]}`),a<e&&o.push(`${n[a]}`);a=o.join(),r=s.join()}const o=Za(n);this.userCode=`\n      void main() {\n        ${o} resRC = getOutputCoords();\n        float cVal = getC(${a});\n        if (cVal >= 1.0) {\n          setOutput(getA(${r}));\n        } else {\n          setOutput(getB(${r}));\n        }\n      }\n    `}}const Bd={kernelName:Qt,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{condition:a,t:r,e:o}=t,i=new Ld(a.shape.length,r.shape,r.shape.length);return n.runWebGLProgram(i,[a,r,o],s(r.dtype,o.dtype))}},Vd={kernelName:Zt,backendName:"webgl",kernelFunc:js({opSnippet:`\n  // Stable and Attracting Fixed Point (0, 1) for Normalized Weights.\n  // see: https://arxiv.org/abs/1706.02515\n  float scaleAlpha = ${r.SELU_SCALEALPHA};\n  float scale = ${r.SELU_SCALE};\n  return (x >= 0.0) ? scale * x : scaleAlpha * (exp(x) - 1.0);\n`})},Wd={kernelName:Jt,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return 1.0 / (1.0 + exp(-1.0 * x));\n",packedOpSnippet:"\n  vec4 result = 1.0 / (1.0 + exp(-1.0 * x));\n  bvec4 isNaN = isnan(x);\n\n  result.r = isNaN.r ? x.r : result.r;\n  result.g = isNaN.g ? x.g : result.g;\n  result.b = isNaN.b ? x.b : result.b;\n  result.a = isNaN.a ? x.a : result.a;\n\n  return result;\n",cpuKernelImpl:Zo})},Ud={kernelName:en,backendName:"webgl",kernelFunc:js({opSnippet:"\n  if (isnan(x)) { return 0.0; }\n  return sign(x);\n"})},Md={kernelName:tn,backendName:"webgl",kernelFunc:js({opSnippet:"if (isnan(x)) return x;\n  return sin(x);\n",packedOpSnippet:`\n  vec4 result = sin(x);\n  bvec4 isNaN = isnan(x);\n  ${Ds}\n  return result;\n`})},Gd={kernelName:nn,backendName:"webgl",kernelFunc:js({opSnippet:"\n  float e2x = exp(x);\n  return (e2x - 1.0 / e2x) / 2.0;\n"})},zd={kernelName:an,backendName:"webgl",kernelFunc:js({opSnippet:"\n  float epsilon = 1.1920928955078125e-7;\n  float threshold = log(epsilon) + 2.0;\n\n  bool too_large = x > -threshold;\n  bool too_small = x < threshold;\n\n  float result;\n  float exp_x = exp(x);\n\n  if (too_large){\n    result = x;\n  }\n  else if (too_small){\n    result = exp_x;\n  }\n  else{\n    result = log(exp_x + 1.0);\n  }\n  return result;\n"})},Xd={kernelName:rn,backendName:"webgl",kernelFunc:e=>{const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{blockShape:i,paddings:l}=o;n.assert(s.shape.length<=4,(()=>"spaceToBatchND for rank > 4 with a WebGL backend not implemented yet"));const u=i.reduce(((e,t)=>e*t)),c=[[0,0]];c.push(...l);for(let e=1+i.length;e<s.shape.length;++e)c.push([0,0]);const d=[],p=ad({inputs:{x:s},backend:a,attrs:{paddings:c,constantValue:0}}),h=r.getReshaped(p.shape,i,u,!1),f=r.getPermuted(h.length,i.length,!1),x=r.getReshapedPermuted(p.shape,i,u,!1),m=ai({inputs:{x:p},backend:a,attrs:{shape:h}}),g=hi({inputs:{x:m},backend:a,attrs:{perm:f}}),b=ai({inputs:{x:g},backend:a,attrs:{shape:x}});return d.push(p),d.push(m),d.push(g),d.forEach((e=>a.disposeIntermediateTensorInfo(e))),b}};const Hd={kernelName:on,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{indices:a,values:r,denseShape:o,defaultValue:s}=t;if(1!==o.shape.length)throw new Error(`Dense shape must be a vector, saw:\n         ${o.shape}`);if(2!==a.shape.length)throw new Error(`Indices must be a matrix, saw:\n         ${a.shape}`);if(1!==r.shape.length)throw new Error(`Values must be a vector, saw:\n         ${r.shape}`);if(0!==s.shape.length)throw new Error(`Default value must be a scalar, saw:\n        ${s.shape}`);const i=n.readSync(a.dataId),l=n.readSync(r.dataId),u=n.readSync(o.dataId),c=n.readSync(s.dataId)[0],[d,p,h,f,x]=ts(i,a.shape,a.dtype,l,r.dtype,u,c);return[n.makeTensorInfo(p,a.dtype,d),n.makeTensorInfo([p[0]],r.dtype,h),n.makeTensorInfo([f.length],"bool",new Uint8Array(f.map((e=>Number(e))))),n.makeTensorInfo([x.length],a.dtype,new Int32Array(x))]}};const jd={kernelName:sn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{inputIndices:a,inputShape:r,newShape:o}=t;if(2!==a.shape.length)throw new Error(`Input indices should be a matrix but received shape ${a.shape}`);if(1!==r.shape.length)throw new Error(`Input shape should be a vector but received shape ${r.shape}`);if(1!==o.shape.length)throw new Error(`Target shape should be a vector but received shape ${o.shape}`);const s=Array.from(n.readSync(r.dataId)),i=n.readSync(a.dataId),l=Array.from(n.readSync(o.dataId)),[u,c,d]=ns(i,a.shape,a.dtype,s,l);return[n.makeTensorInfo(c,a.dtype,u),n.makeTensorInfo([d.length],o.dtype,new Int32Array(d))]}};const Kd={kernelName:ln,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:r,segmentIds:o}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==r.shape.length)throw new Error(`Indices should be a vector but received shape\n              ${r.shape}`);if(1!==o.shape.length)throw new Error(`Segment ids should be a vector but received shape\n              ${o.shape}`);const s=n.readSync(a.dataId),i=n.readSync(r.dataId),l=n.readSync(o.dataId),[u,c]=as(s,a.shape,a.dtype,i,l,!0);return n.makeTensorInfo(c,a.dtype,u)}};const qd={kernelName:un,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:r,segmentIds:o}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==r.shape.length)throw new Error(`Indices should be a vector but received shape\n             ${r.shape}`);if(1!==o.shape.length)throw new Error(`Segment ids should be a vector but received shape\n             ${o.shape}`);const s=n.readSync(a.dataId),i=n.readSync(r.dataId),l=n.readSync(o.dataId),[u,c]=as(s,a.shape,a.dtype,i,l);return n.makeTensorInfo(c,a.dtype,u)}};const Yd={kernelName:cn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{sparseIndices:s,sparseValues:i,defaultValue:l}=t,{outputShape:u}=o,{sliceRank:c,numUpdates:d,sliceSize:p,strides:h,outputSize:f}=r.calculateShapes(i,s,u);if("string"===i.dtype){const e=a.bufferSync(s),t=a.bufferSync(i),r=n.decodeString(a.readSync(l.dataId)[0]),o=Qo(e,t,u,f,p,d,c,h,r,false);return a.makeTensorInfo(u,o.dtype,o.values)}const x=new Od(d,c,s.shape.length,i.shape.length,h,[f,1],false),m=a.runWebGLProgram(x,[i,s,l],i.dtype),g=ai({inputs:{x:m},backend:a,attrs:{shape:u}});return a.disposeIntermediateTensorInfo(m),g}};const Qd={kernelName:dn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s}=t,{numOrSizeSplits:i,axis:l}=o,u=n.parseAxisParam(l,s.shape)[0],c=r.prepareSplitSize(s,i,u),d=s.shape.length,p=new Array(d).fill(0),h=s.shape.slice();return c.map((e=>{const t=[...h];t[u]=e;const n=tl({inputs:{x:s},backend:a,attrs:{begin:p,size:t}});return p[u]+=e,n}))}},Zd="return sqrt(x);",Jd={kernelName:pn,backendName:"webgl",kernelFunc:js({opSnippet:Zd,packedOpSnippet:Zd,cpuKernelImpl:rs})},ep={kernelName:hn,backendName:"webgl",kernelFunc:js({opSnippet:"return x * x;"})},tp={kernelName:fn,backendName:"webgl",kernelFunc:Ks({opSnippet:"return (a - b) * (a - b);",packedOpSnippet:"return (a - b) * (a - b);"})};const np={kernelName:xn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:o}=t;if("string"!==o.dtype)throw new Error("Input must be of datatype string");const s=n.readSync(o.dataId),i=r.fromUint8ToStringArray(s),l=os(i,"string",a);return n.makeTensorInfo(o.shape,"string",l)}};const ap={kernelName:mn,backendName:"webgl",kernelFunc:function({inputs:e,attrs:t,backend:n}){const{x:a}=e,r=`if (isnan(x)) return x;\n    return x > 0.0 ? 1.0 : float(${t.alpha});\n  `,o=new Is(a.shape,r);return n.runWebGLProgram(o,[a],a.dtype)}};class rp{constructor(e,t,n){this.variableNames=["x"],this.outputShape=n;const a=n.length,r=Za(n.length),o=Za(n.length);let s="";if(1===a)s="coords * strides + begin";else{let e=0;s=n.map(((t,a)=>(e++,1===n.length?`coords * strides[${a}] + begin[${a}]`:`coords[${e-1}] * strides[${a}] + begin[${a}]`))).join(",")}this.userCode=`\n      ${r} begin = ${r}(${e});\n      ${r} strides = ${r}(${t});\n\n      void main() {\n        ${o} coords = getOutputCoords();\n        setOutput(getX(${s}));\n      }\n    `}}const op={kernelName:gn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:r}=e,{x:s}=t,{begin:i,end:l,strides:u,beginMask:c,endMask:p,ellipsisMask:h,newAxisMask:f,shrinkAxisMask:x}=r,{finalShapeSparse:m,finalShape:g,isIdentity:b,sliceDim0:v,isSimpleSlice:C,begin:$,end:y,strides:I}=d.sliceInfo(s.shape,i,l,u,c,p,h,f,x);let w;if(b)w=ai({inputs:{x:s},backend:a,attrs:{shape:g}});else if(v||C){n.assert(s.shape.length>=1,(()=>`Input must have rank at least 1, got: ${s.shape.length}`));const e=d.computeOutShape($,y,I),t=tl({inputs:{x:s},backend:a,attrs:{begin:$,size:e}});w=ai({inputs:{x:t},backend:a,attrs:{shape:g}}),a.disposeIntermediateTensorInfo(t)}else{if(a.shouldExecuteOnCPU([s])){const e=a.readSync(s.dataId),t=o(s.shape,s.dtype,e),n=ss(m,t,I,$);w=a.makeTensorInfo(g,s.dtype,n.values)}else{const e=new rp($,I,m);w=a.runWebGLProgram(e,[s],s.dtype)}}const S=ai({inputs:{x:w},backend:a,attrs:{shape:g}});return a.disposeIntermediateTensorInfo(w),S}};const sp={kernelName:bn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{separator:r,nGramWidths:o,leftPad:s,rightPad:i,padWidth:l,preserveShortSequences:u}=a,{data:c,dataSplits:d}=t,p=n.readSync(c.dataId),h=n.readSync(d.dataId),[f,x]=is(p,h,r,o,s,i,l,u);return[n.makeTensorInfo([f.length],"string",f),n.makeTensorInfo(d.shape,"int32",x)]}};const ip={kernelName:vn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{skipEmpty:r}=a,{input:o,delimiter:s}=t;if("string"!==o.dtype)throw new Error("Input must be of datatype string");if(1!==o.shape.length)throw new Error(`Input must be a vector, got shape: ${o.shape}`);if(0!==s.shape.length)throw new Error(`Delimiter must be a scalar, got shape: ${s.shape}`);const i=n.readSync(o.dataId),l=n.readSync(s.dataId)[0],[u,c,d]=ls(i,l,r),p=c.length;return[n.makeTensorInfo([p,2],"int32",u),n.makeTensorInfo([p],"string",c),n.makeTensorInfo([2],"int32",new Int32Array(d))]}};const lp={kernelName:Cn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{numBuckets:r}=a,{input:o}=t;if("string"!==o.dtype)throw new Error("Input must be of datatype string");if(r<=0)throw new Error("Number of buckets must be at least 1");const s=n.readSync(o.dataId),i=us(s,r);return n.makeTensorInfo(o.shape,"int32",i)}},up={kernelName:$n,backendName:"webgl",kernelFunc:js({opSnippet:"return tan(x);"})},cp={kernelName:yn,backendName:"webgl",kernelFunc:js({opSnippet:"\n  float e2x = exp(-2.0 * abs(x));\n  return sign(x) * (1.0 - e2x) / (1.0 + e2x);\n"})};const dp={kernelName:In,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{tensor:o,indices:s,updates:i}=t,{sliceRank:l,numUpdates:u,sliceSize:c,strides:d,outputSize:p}=r.calculateShapes(i,s,o.shape),h=[p/c,c];if(0===p)return n.makeTensorInfo(o.shape,s.dtype);const f=ai({inputs:{x:s},backend:n,attrs:{shape:[u,l]}}),x=ai({inputs:{x:i},backend:n,attrs:{shape:[u,c]}}),m=ai({inputs:{x:o},backend:n,attrs:{shape:h}}),g=new Od(u,l,f.shape.length,x.shape.length,d,h,!1,!0),b=n.runWebGLProgram(g,[x,f,m],m.dtype),v=ai({inputs:{x:b},backend:n,attrs:{shape:o.shape}});return n.disposeIntermediateTensorInfo(f),n.disposeIntermediateTensorInfo(x),n.disposeIntermediateTensorInfo(m),n.disposeIntermediateTensorInfo(b),v}};class pp{constructor(e,t){this.variableNames=["A"];const n=new Array(e.length);for(let a=0;a<n.length;a++)n[a]=e[a]*t[a];this.outputShape=n,this.rank=n.length;const a=Za(this.rank),r=function(e){const t=e.length;if(t>5)throw Error(`Tile for rank ${t} is not yet supported`);if(1===t)return`imod(resRC, ${e[0]})`;const n=["resRC.x","resRC.y","resRC.z","resRC.w","resRC.u"],a=[];for(let t=0;t<e.length;t++)a.push(`imod(${n[t]}, ${e[t]})`);return a.join()}(e);this.userCode=`\n      void main() {\n        ${a} resRC = getOutputCoords();\n        setOutput(getA(${r}));\n      }\n    `}}function hp(e){const{inputs:t,backend:a,attrs:r}=e,{x:s}=t,{reps:i}=r;if("string"===s.dtype||s.shape.length>5){const e=a.readSync(s.dataId),t="string"===s.dtype?e.map((e=>n.decodeString(e))):e,r=o(s.shape,s.dtype,t),l=ds(r,i);return a.makeTensorInfo(l.shape,l.dtype,l.values)}const l=new pp(s.shape,i);return a.runWebGLProgram(l,[s],s.dtype)}const fp={kernelName:wn,backendName:"webgl",kernelFunc:hp};class xp{constructor(e){this.variableNames=["x","indices"],this.customUniforms=[{name:"n",type:"int"},{name:"firstPass",type:"int"},{name:"negativeInf",type:"float"},{name:"dir",type:"int"},{name:"inc",type:"int"}],this.outputShape=e,this.userCode="\n       void main() {\n         ivec2 coords = getOutputCoords();\n         int batch = coords[0];\n         int elemIdx = coords[1];\n\n         // We compare elements pair-wise within a group of size 2 * inc.\n         // The comparing rule for each group alternates between ascending\n         // and descending. Within each group, we compare each pair at\n         // positions i and i+inc. To decide whether an element at position i\n         // is x0 or x1, we mod it by 2 * inc, if the result is smaller than\n         // inc, it is in the first half of the group, we denote it as x0,\n         // otherwise we denote it as x1.\n         // For example, as shown in the Bitonic top K paper referenced above,\n         // Figure5(a) shows that element[1] is in the\n         // second half of the group when group size is 2, but it is in the\n         // first half of the group when group size is 4.\n\n         bool isFirstInPair = imod(elemIdx, 2 * inc) < inc;\n         int i = isFirstInPair ? elemIdx : elemIdx - inc;\n\n         int i0 = firstPass == 1 ? i : int(getIndices(batch, i));\n         int i1 = firstPass == 1 ? i + inc : int(getIndices(batch, i + inc));\n         float x0 = i0 < n ? getX(batch, i0) : negativeInf;\n         float x1 = i1 < n ? getX(batch, i1) : negativeInf;\n\n         // Denotes which direction indices are in (ascending or descending).\n         bool reverse = imod(elemIdx, 2 * dir) >= dir;\n         bool isGreater = x0 > x1 || (x0 == x1 && i1 > i0);\n         if (reverse == isGreater) { // Elements in opposite order of direction\n           int iTemp = i0;\n           i0 = i1;\n           i1 = iTemp;\n         }\n         if (isFirstInPair) {\n            setOutput(float(i0));\n         } else {\n            setOutput(float(i1));\n         }\n       }\n     "}}class mp{constructor(e){this.variableNames=["x","indices"],this.customUniforms=[{name:"n",type:"int"},{name:"firstPass",type:"int"},{name:"k",type:"int"}],this.outputShape=e,this.userCode="\n    void main() {\n         // Takes max of indices (0, k), (1, k + 1), (2, k + 2) ...\n         ivec2 coords = getOutputCoords();\n         int batch = coords[0];\n         int elemIdx = coords[1];\n\n         // The output size is half of the previous size.\n         // If the previous sequence is | | | | _ _ _ _  | | | |  _ _ _ _ (k=4),\n         // we only need to output the indices at positions |, the indices at\n         // positions _ can be thrown away, see Figure5(b) After Phase 2\n         // (Merge phase) in the Bitonic Top K paper referenced above.\n         // For example, the paper shows we only need to output the orange bars.\n         // The output sequence should look like this | | | | | | | |.\n         // Because the sequence is halved, to map the output index back\n         // to the previous sequence to find the corresponding value,\n         // we need to double the index. When we double the index,\n         // we basically interpolate a position, so 2i looks like\n         // | _ | _ | _ | _ | _ | _ | _. We move the | to the first k position\n         // of each 2k positions by - elemIdx % k. E.g. for output at\n         // index 4,5,6,7, we want to get the corresponding element at\n         // original index 8,9,10,11, for output at index 8,9,10,11,\n         // we want to get the corresponding element at original index\n         // 16,17,18,19, so on and so forth.\n\n         int i = elemIdx < k ? elemIdx : (elemIdx * 2 - imod(elemIdx, k));\n         int i0 = firstPass == 1 ? i : int(getIndices(batch, i));\n         int i1 = firstPass == 1 ? i + k : int(getIndices(batch, i + k));\n\n         float x0 = getX(batch, i0);\n         float x1 = i1 < n ? getX(batch, i1) : x0;\n\n         setOutput(x0 >= x1 ? float(i0) : float(i1));\n       }\n     "}}function gp(e,t){null!==t&&e.disposeIntermediateTensorInfo(t)}function bp(e){let t=1;for(;t<e;)t*=2;return t}const vp={kernelName:Sn,backendName:"webgl",kernelFunc:function(e){const{inputs:a,backend:r,attrs:o}=e,{x:s}=a,{k:i,sorted:l}=o,u=t().getNumber("TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD"),c=t().getNumber("TOPK_K_CPU_HANDOFF_THRESHOLD"),d=s.shape,p=d[d.length-1];if(r.shouldExecuteOnCPU([s])||p<u||i>c){const e=r.readSync(s.dataId),[t,n]=ps(e,d,s.dtype,i,l);return[r.makeTensorInfo(t.shape,t.dtype,t.values),r.makeTensorInfo(n.shape,n.dtype,n.values)]}if(0===i)return d[d.length-1]=0,[r.makeTensorInfo(d,s.dtype,[]),r.makeTensorInfo(d,"int32",[])];if(1===p)return[s,Ou({attrs:{shape:d,dtype:"int32",value:0},backend:r})];const h=r.texData.get(s.dataId),f=null!==h&&h.isPacked,x=f?r.unpackTensor(s):s,m=n.sizeFromShape(d)/p,g=ai({inputs:{x:x},attrs:{shape:[m,p]},backend:r});f&&gp(r,x);const b=bp(i),v=bp(p);let C=null;const $=()=>null===C?[g,g]:[g,C],y=(e,t,n)=>{const a=$(),o=new xp(n),s=[[p],[null===C?1:0],[Number.NEGATIVE_INFINITY],[e],[t]],i=C;C=r.runWebGLProgram(o,a,"int32",s),gp(r,i)};for(let e=1;e<b;e*=2){const t=2*e;for(let n=e;n>=1;n/=2)y(t,n,[m,v])}for(let e=v;e>b;e/=2){const t=$(),n=new mp([m,e/2]),a=[[p],[null===C?1:0],[b]],o=C;C=r.runWebGLProgram(n,t,"int32",a),gp(r,o);const s=b/2,i=2*s;for(let e=s;e>=1;e/=2)y(i,e,C.shape)}let I=C;C=tl({inputs:{x:C},backend:r,attrs:{begin:0,size:[m,i]}}),gp(r,I);let w=qu({inputs:{x:g,indices:C},backend:r,attrs:{axis:1,batchDims:1}});gp(r,g);const S=d.slice(0,-1);S.push(i),I=C,C=ai({inputs:{x:C},attrs:{shape:S},backend:r}),gp(r,I);const R=w;return w=ai({inputs:{x:w},attrs:{shape:S},backend:r}),gp(r,R),[w,C]}};class Cp{constructor(e,t,n,a,r,o){this.variableNames=["Image","Transforms"],this.outputShape=o;const s="nearest"===n?1:2;let i;switch(a){case"constant":default:i=1;break;case"reflect":i=2;break;case"wrap":i=3;break;case"nearest":i=4}this.userCode=`\n            float mapCoord(float outCoord, float len) {\n              float inCoord = outCoord;\n              if(${i} == 2) {\n                if (inCoord < 0.0) {\n                  if (len <= 1.0) {\n                    inCoord = 0.0;\n                  } else {\n                    float sz2 = 2.0 * len;\n                    if (inCoord < sz2) {\n                      inCoord = sz2 * float(int(float(-inCoord / sz2))) +\n                      inCoord;\n                    }\n                    inCoord = inCoord < -len ? inCoord + sz2 : -inCoord - 1.0;\n                  }\n                } else if (inCoord > len - 1.0) {\n                  if (len <= 1.0) {\n                    inCoord = 0.0;\n                  } else {\n                    float sz2 = 2.0 * len;\n                    inCoord -= sz2 * float(int(float(inCoord / sz2)));\n                    if (inCoord >= len) {\n                      inCoord = sz2 - inCoord - 1.0;\n                    }\n                  }\n                }\n                return clamp(inCoord, 0.0, len - 1.0);\n              } else if (${i} == 3) {\n                if (inCoord < 0.0) {\n                  if (len <= 1.0) {\n                    inCoord = 0.0;\n                  } else {\n                    float sz = len - 1.0;\n                    inCoord += len * (float(int(float(-inCoord / sz))) + 1.0);\n                  }\n                } else if (inCoord > len - 1.0) {\n                  if (len <= 1.0) {\n                    inCoord = 0.0;\n                  } else {\n                    float sz = len - 1.0;\n                    inCoord -= len * float(int(float(inCoord / sz)));\n                  }\n                }\n                return clamp(inCoord, 0.0, len - 1.0);\n              } else if (${i} == 4) {\n                return clamp(outCoord, 0.0, len - 1.0);\n              } else {\n                return outCoord;\n              }\n            }\n\n            float readWithFillValue(int batch, int coordY, int coordX,\n              int channel) {\n              float outputValue;\n              if (0 <= coordY && coordY < ${e} && 0 <= coordX && coordX < ${t}) {\n                  outputValue = getImage(batch, coordY, coordX, channel);\n              } else {\n                outputValue = float(${r});\n              }\n              return outputValue;\n            }\n\n            void main() {\n              ivec4 coords = getOutputCoords();\n              float outputValue;\n              int batch = coords[0];\n              int x = coords[2];\n              int y = coords[1];\n              int channel = coords[3];\n              float xf = float(x);\n              float yf = float(y);\n              float a1 = getTransforms(batch, 0);\n              float a2 = getTransforms(batch, 1);\n              float a3 = getTransforms(batch, 2);\n              float b1 = getTransforms(batch, 3);\n              float b2 = getTransforms(batch, 4);\n              float b3 = getTransforms(batch, 5);\n              float c1 = getTransforms(batch, 6);\n              float c2 = getTransforms(batch, 7);\n              float projection = c1 * xf + c2 * yf + 1.0;\n              if (projection == 0.0) {\n                outputValue = float(${r});\n              } else {\n                float inX = (a1 * xf + a2 * yf + a3) / projection;\n                float inY = (b1 * xf + b2 * yf + b3) / projection;\n                float mapX = mapCoord(inX, float(${t}));\n                float mapY = mapCoord(inY, float(${e}));\n\n                if (${s} == 1) {\n                  int coordY = int(round(mapY));\n                  int coordX = int(round(mapX));\n                  outputValue = readWithFillValue(batch, coordY, coordX,\n                    channel);\n                } else {\n                  float yFloor = floor(mapY);\n                  float xFloor = floor(mapX);\n                  float yCeil = yFloor + 1.0;\n                  float xCeil = xFloor + 1.0;\n                  float valueYFloor = (xCeil - mapX) *\n                  readWithFillValue(batch, int(yFloor), int(xFloor), channel) +\n                  (mapX - xFloor) *\n                  readWithFillValue(batch, int(yFloor), int(xCeil), channel);\n                  float valueYCeil = (xCeil - mapX) *\n                  readWithFillValue(batch, int(yCeil), int(xFloor), channel) +\n                  (mapX - xFloor) *\n                  readWithFillValue(batch, int(yCeil), int(xCeil), channel);\n                  outputValue = (yCeil - mapY) * valueYFloor +\n                  (mapY - yFloor) * valueYCeil;\n                }\n              }\n              setOutput(outputValue);\n            }\n        `}}const $p={kernelName:Rn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{image:r,transforms:o}=t,{interpolation:s,fillMode:i,fillValue:l,outputShape:u}=a,[c,d,p,h]=r.shape,[f,x]=null!=u?u:[d,p],m=new Cp(d,p,s,i,l,[c,f,x,h]);return n.runWebGLProgram(m,[r,o],"float32")}};const yp={kernelName:Tn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,attrs:n,backend:a}=e,{axis:r}=n,{x:o}=t;Oa(o,"unique"),console.warn("WARNING: ","UI might be locked temporarily as data is being downloaded");const s=a.readSync(o.dataId),{outputValues:i,outputShape:l,indices:u}=fs(s,r,o.shape,o.dtype);return[a.makeTensorInfo(l,o.dtype,i),a.makeTensorInfo([u.length],"int32",u)]}};const Ip={kernelName:kn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{value:r}=t;let{axis:o}=a;o<0&&(o+=r.shape.length);const s=r,i=s.shape.length,l=r.shape[o],u=new Array(i-1);let c=0;for(let e=0;e<i;e++)e!==o&&(u[c++]=s.shape[e]);const d=[],p=new Array(i).fill(0),h=s.shape.slice();h[o]=1;const f=new Array(l);for(let e=0;e<f.length;e++){p[o]=e;const t=tl({inputs:{x:s},backend:n,attrs:{begin:p,size:h}}),a=ai({inputs:{x:t},backend:n,attrs:{shape:u}});f[e]=a,d.push(t)}return d.forEach((e=>n.disposeIntermediateTensorInfo(e))),f}};class wp{constructor(e,t){this.variableNames=["x","segmentIds"];const n=e.windowSize,a=e.batchSize,r=e.inSize,o=e.numSegments,s=o*Math.ceil(r/n);this.outputShape=[a,s];const i=4*Math.floor(n/4),l=n%4,u="\n        sumValue += dot(values, segFilter);\n    ";let c="";r%n>0&&(c=`\n        if (inIdx < 0 || inIdx >= ${r}) {\n          return initializationValue;\n        }\n      `);let d="";r%n>0&&(d=`\n        if (inIdx < 0 || inIdx >= ${r}) {\n          return -1.0;\n        }\n      `),this.userCode=`\n      const float initializationValue = 0.0;\n\n      float getValue(int batch, int inIdx) {\n        ${c}\n        return getX(batch, inIdx);\n      }\n\n      float getSegmentIdAtIndex(int inIdx) {\n        ${d}\n        return getSegmentIds(inIdx);\n      }\n\n      void main() {\n        ivec2 coords = getOutputCoords();\n        int batch = coords[0];\n        int outIdx = coords[1];\n        int inOffset = int(floor(float(outIdx) / float(\n          ${o})) * float(${n}));\n        int currentSeg = int(mod(float(outIdx), float(${o})));\n\n        float sumValue = 0.0;\n\n        for (int i = 0; i < ${i}; i += 4) {\n          int inIdx = inOffset + i;\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            getValue(batch, inIdx + 2),\n            getValue(batch, inIdx + 3)\n          );\n\n          vec4 segFilter = vec4(\n            int(getSegmentIdAtIndex(inIdx)) == currentSeg ? 1 : 0,\n            int(getSegmentIdAtIndex(inIdx + 1)) == currentSeg ? 1 : 0,\n            int(getSegmentIdAtIndex(inIdx + 2)) == currentSeg ? 1 : 0,\n            int(getSegmentIdAtIndex(inIdx + 3)) == currentSeg ? 1 : 0\n          );\n\n          ${u}\n        }\n\n        int inIdx = inOffset + ${i};\n        if (${1===l}) {\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            initializationValue,\n            initializationValue,\n            initializationValue\n          );\n\n          int inIdxSeg = int(getSegmentIdAtIndex(inIdx));\n\n          vec4 segFilter = vec4(\n            int(getSegmentIdAtIndex(inIdx)) == currentSeg ? 1 : 0,\n            0,\n            0,\n            0\n          );\n\n          ${u}\n        } else if (${2===l}) {\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            initializationValue,\n            initializationValue\n          );\n\n          vec4 segFilter = vec4(\n            int(getSegmentIdAtIndex(inIdx)) == currentSeg ? 1 : 0,\n            int(getSegmentIdAtIndex(inIdx + 1)) == currentSeg ? 1 : 0,\n              0,\n              0\n          );\n\n          ${u}\n        } else if (${3===l}) {\n          vec4 values = vec4(\n            getValue(batch, inIdx),\n            getValue(batch, inIdx + 1),\n            getValue(batch, inIdx + 2),\n            initializationValue\n          );\n\n          vec4 segFilter = vec4(\n            int(getSegmentIdAtIndex(inIdx)) == currentSeg ? 1 : 0,\n            int(getSegmentIdAtIndex(inIdx + 1)) == currentSeg ? 1 : 0,\n            int(getSegmentIdAtIndex(inIdx + 2)) == currentSeg ? 1 : 0,\n            0\n          );\n\n          ${u}\n        }\n        setOutput(sumValue);\n      }\n    `}}const Sp=[mi,bi,vi,Ci,yi,Si,Ri,Ti,Fi,_i,Di,Pi,Li,Bi,Vi,Mi,Gi,Hi,ji,Ki,Qi,al,rl,ol,sl,dl,hl,ml,Ws,vl,Tl,Dl,Wl,Ml,Gl,zl,Xl,Hl,jl,ql,tu,nu,au,ou,lu,du,pu,fu,mu,gu,bu,vu,Cu,$u,Iu,Su,Tu,Eu,Fu,Du,Lu,Bu,Uu,zu,Xu,ju,Yu,Qu,Zu,Bs,Ju,wl,ec,tc,nc,Gs,ac,rc,oc,sc,ic,lc,uc,cc,hc,xc,gc,bc,vc,Cc,Ic,wc,Sc,Rc,Tc,kc,Ac,Oc,Uc,ni,Mc,zc,Hc,Kc,ll,Yc,Jc,ed,rd,od,Hs,sd,id,ld,ud,dd,cl,Dc,pd,hd,fd,ri,gd,vd,yd,wd,Td,Nd,Ed,Ad,_d,Pd,Bd,Vd,Wd,Ud,Md,Gd,nl,Wc,zd,Xd,Hd,jd,Kd,qd,Yd,Qd,Jd,ep,tp,np,ap,op,sp,ip,lp,Bc,pi,up,cp,dp,fp,vp,$p,fi,yp,Ip,{kernelName:Nn,backendName:"webgl",kernelFunc:function(e){const{inputs:t,backend:a,attrs:o}=e,{x:s,segmentIds:i}=t,{numSegments:l}=o,u=s.shape.length,c=[];let d=0;const p=r.getAxesPermutation([d],u);let h=s;null!=p&&(h=hi({inputs:{x:s},backend:a,attrs:{perm:p}}),c.push(h),d=r.getInnerMostAxes(1,u)[0]);const f=r.segment_util.computeOutShape(h.shape,d,l),x=n.sizeFromShape([h.shape[d]]),m=ai({inputs:{x:h},backend:a,attrs:{shape:[-1,x]}});c.push(m);const g=S(s.dtype),b=(e,t,n,o,s)=>{const i=e.shape[0],l=e.shape[1],u=r.segment_util.segOpComputeOptimalWindowSize(l,s),d=new wp({windowSize:u,inSize:l,batchSize:i,numSegments:s},t),p=a.compileAndRun(d,[e,n],o);if(c.push(p),p.shape[1]===s)return p;const h=cd({backend:a,attrs:{start:0,stop:s,step:1,dtype:"float32"}}),f=hp({inputs:{x:h},backend:a,attrs:{reps:[l/u]}});c.push(h),c.push(f);return b(p,t,f,o,s)},v=ai({inputs:{x:b(m,"unsortedSegmentSum",i,g,l)},backend:a,attrs:{shape:f}});let C=v;if(null!=p){c.push(v);const e=r.getUndoAxesPermutation(p);C=hi({inputs:{x:C},backend:a,attrs:{perm:e}})}return c.forEach((e=>a.disposeIntermediateTensorInfo(e))),C}},Zc];for(const e of Sp)En(e);export{Dr as GPGPUContext,Es as MathBackendWebGL,Os as forceHalfFloat,_r as gpgpu_util,Fn as setWebGLContext,As as version_webgl,Fs as webgl,Fa as webgl_util};
//# sourceMappingURL=tf-backend-webgl.fesm.min.js.map
