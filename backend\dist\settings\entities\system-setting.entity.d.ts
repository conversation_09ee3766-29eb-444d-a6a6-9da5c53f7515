export declare enum SettingType {
    STRING = "string",
    NUMBER = "number",
    BOOLEAN = "boolean",
    JSON = "json",
    ARRAY = "array",
    DATE = "date",
    EMAIL = "email",
    URL = "url",
    PASSWORD = "password"
}
export declare enum SettingCategory {
    GENERAL = "general",
    SECURITY = "security",
    EMAIL = "email",
    NOTIFICATION = "notification",
    INTEGRATION = "integration",
    APPEARANCE = "appearance",
    LOCALIZATION = "localization",
    BACKUP = "backup",
    PERFORMANCE = "performance",
    CUSTOM = "custom"
}
export declare class SystemSetting {
    id: string;
    key: string;
    value: string;
    defaultValue: string;
    type: SettingType;
    category: SettingCategory;
    name: string;
    description: string;
    isRequired: boolean;
    isEncrypted: boolean;
    isEditable: boolean;
    isSystem: boolean;
    validationRules: any;
    options: any;
    sortOrder: number;
    lastModifiedBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
