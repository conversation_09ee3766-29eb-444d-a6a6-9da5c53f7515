import { Repository } from 'typeorm';
import { Leave } from '../entities/leave.entity';
import { LeaveType } from '../entities/leave-type.entity';
export declare class LeaveService {
    private leaveRepository;
    private leaveTypeRepository;
    constructor(leaveRepository: Repository<Leave>, leaveTypeRepository: Repository<LeaveType>);
    create(createLeaveDto: any): Promise<Leave>;
    findAll(filters?: any): Promise<Leave[]>;
    findOne(id: string): Promise<Leave>;
    update(id: string, updateLeaveDto: any): Promise<Leave>;
    remove(id: string): Promise<void>;
    approveLeave(id: string, approvedBy: string, approvalComments?: string): Promise<Leave>;
    rejectLeave(id: string, rejectedBy: string, rejectionComments: string): Promise<Leave>;
    cancelLeave(id: string, reason?: string): Promise<Leave>;
    getLeaveBalance(employeeId: string, leaveTypeId: string, year?: number): Promise<any>;
    getLeaveReport(employeeId: string, year?: number): Promise<any>;
    createLeaveType(createLeaveTypeDto: any): Promise<LeaveType>;
    findAllLeaveTypes(): Promise<LeaveType[]>;
    private calculateLeaveDays;
}
