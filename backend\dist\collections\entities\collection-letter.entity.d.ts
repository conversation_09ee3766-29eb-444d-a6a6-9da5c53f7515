export declare enum LetterType {
    INITIAL_NOTICE = "initial_notice",
    REMINDER = "reminder",
    FINAL_NOTICE = "final_notice",
    DEMAND_LETTER = "demand_letter",
    SETTLEMENT_OFFER = "settlement_offer",
    PAYMENT_PLAN_OFFER = "payment_plan_offer",
    LEGAL_NOTICE = "legal_notice",
    CEASE_AND_DESIST = "cease_and_desist",
    VALIDATION_NOTICE = "validation_notice",
    CUSTOM = "custom"
}
export declare enum LetterStatus {
    DRAFT = "draft",
    PENDING = "pending",
    SENT = "sent",
    DELIVERED = "delivered",
    RETURNED = "returned",
    FAILED = "failed"
}
export declare class CollectionLetter {
    id: string;
    name: string;
    type: LetterType;
    template: string;
    description: string;
    isActive: boolean;
    requiresApproval: boolean;
    variables: string[];
    conditions: any;
    createdBy: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
