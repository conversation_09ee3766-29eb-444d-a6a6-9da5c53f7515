import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CollectionCase } from './collection-case.entity';

export enum DisputeReason {
  AMOUNT_INCORRECT = 'amount_incorrect',
  ALREADY_PAID = 'already_paid',
  NOT_MY_DEBT = 'not_my_debt',
  IDENTITY_THEFT = 'identity_theft',
  STATUTE_OF_LIMITATIONS = 'statute_of_limitations',
  BANKRUPTCY = 'bankruptcy',
  DECEASED = 'deceased',
  QUALITY_ISSUE = 'quality_issue',
  SERVICE_NOT_RECEIVED = 'service_not_received',
  BILLING_ERROR = 'billing_error',
  OTHER = 'other',
}

export enum DisputeStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  REJECTED = 'rejected',
  ESCALATED = 'escalated',
  CLOSED = 'closed',
}

@Entity('collection_disputes')
export class CollectionDispute {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  caseId: string;

  @ManyToOne(() => CollectionCase, collectionCase => collectionCase.disputes)
  @JoinColumn({ name: 'caseId' })
  case: CollectionCase;

  @Column({ length: 50, unique: true })
  disputeNumber: string;

  @Column({
    type: 'enum',
    enum: DisputeReason,
  })
  reason: DisputeReason;

  @Column({
    type: 'enum',
    enum: DisputeStatus,
    default: DisputeStatus.OPEN,
  })
  status: DisputeStatus;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  disputedAmount: number;

  @Column({ type: 'date' })
  disputeDate: Date;

  @Column({ type: 'date', nullable: true })
  responseDate: Date;

  @Column({ type: 'date', nullable: true })
  resolutionDate: Date;

  @Column({ type: 'text', nullable: true })
  customerEvidence: string;

  @Column({ type: 'text', nullable: true })
  companyResponse: string;

  @Column({ type: 'text', nullable: true })
  resolution: string;

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ type: 'json', nullable: true })
  attachments: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
