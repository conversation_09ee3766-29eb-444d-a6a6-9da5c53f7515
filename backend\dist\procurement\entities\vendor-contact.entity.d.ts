import { Vendor } from './vendor.entity';
export declare enum ContactType {
    PRIMARY = "primary",
    SALES = "sales",
    TECHNICAL = "technical",
    BILLING = "billing",
    SUPPORT = "support",
    EMERGENCY = "emergency"
}
export declare class VendorContact {
    id: string;
    vendorId: string;
    vendor: Vendor;
    type: ContactType;
    firstName: string;
    lastName: string;
    jobTitle: string;
    department: string;
    email: string;
    phone: string;
    mobile: string;
    isPrimary: boolean;
    isActive: boolean;
    notes: string;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
