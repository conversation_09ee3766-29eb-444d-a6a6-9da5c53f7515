import { Customer } from './customer.entity';
export declare enum InteractionType {
    CALL = "call",
    EMAIL = "email",
    MEETING = "meeting",
    CHAT = "chat",
    SMS = "sms",
    SOCIAL_MEDIA = "social_media",
    SUPPORT_TICKET = "support_ticket",
    COMPLAINT = "complaint",
    FEEDBACK = "feedback",
    SURVEY = "survey",
    VISIT = "visit",
    OTHER = "other"
}
export declare enum InteractionDirection {
    INBOUND = "inbound",
    OUTBOUND = "outbound"
}
export declare enum InteractionStatus {
    SCHEDULED = "scheduled",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    NO_SHOW = "no_show",
    FOLLOW_UP_REQUIRED = "follow_up_required"
}
export declare class CustomerInteraction {
    id: string;
    customerId: string;
    customer: Customer;
    type: InteractionType;
    direction: InteractionDirection;
    status: InteractionStatus;
    subject: string;
    description: string;
    interactionDate: Date;
    duration: number;
    performedBy: string;
    channel: string;
    participants: string[];
    tags: string[];
    outcome: string;
    nextSteps: string;
    followUpDate: Date;
    followUpBy: string;
    attachments: string[];
    rating: number;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
