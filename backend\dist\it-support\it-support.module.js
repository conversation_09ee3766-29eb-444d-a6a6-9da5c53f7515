"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItSupportModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const support_ticket_entity_1 = require("./entities/support-ticket.entity");
const ticket_comment_entity_1 = require("./entities/ticket-comment.entity");
const ticket_attachment_entity_1 = require("./entities/ticket-attachment.entity");
const knowledge_base_entity_1 = require("./entities/knowledge-base.entity");
const faq_entity_1 = require("./entities/faq.entity");
const asset_entity_1 = require("./entities/asset.entity");
const asset_maintenance_entity_1 = require("./entities/asset-maintenance.entity");
const software_license_entity_1 = require("./entities/software-license.entity");
const it_request_entity_1 = require("./entities/it-request.entity");
const change_request_entity_1 = require("./entities/change-request.entity");
const incident_entity_1 = require("./entities/incident.entity");
const service_catalog_entity_1 = require("./entities/service-catalog.entity");
const support_ticket_service_1 = require("./services/support-ticket.service");
const knowledge_base_service_1 = require("./services/knowledge-base.service");
const asset_service_1 = require("./services/asset.service");
const software_license_service_1 = require("./services/software-license.service");
const it_request_service_1 = require("./services/it-request.service");
const change_request_service_1 = require("./services/change-request.service");
const incident_service_1 = require("./services/incident.service");
const it_report_service_1 = require("./services/it-report.service");
const support_ticket_controller_1 = require("./controllers/support-ticket.controller");
const knowledge_base_controller_1 = require("./controllers/knowledge-base.controller");
const asset_controller_1 = require("./controllers/asset.controller");
const software_license_controller_1 = require("./controllers/software-license.controller");
const it_request_controller_1 = require("./controllers/it-request.controller");
const change_request_controller_1 = require("./controllers/change-request.controller");
const incident_controller_1 = require("./controllers/incident.controller");
const it_report_controller_1 = require("./controllers/it-report.controller");
let ItSupportModule = class ItSupportModule {
};
exports.ItSupportModule = ItSupportModule;
exports.ItSupportModule = ItSupportModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                support_ticket_entity_1.SupportTicket,
                ticket_comment_entity_1.TicketComment,
                ticket_attachment_entity_1.TicketAttachment,
                knowledge_base_entity_1.KnowledgeBase,
                faq_entity_1.FAQ,
                asset_entity_1.Asset,
                asset_maintenance_entity_1.AssetMaintenance,
                software_license_entity_1.SoftwareLicense,
                it_request_entity_1.ITRequest,
                change_request_entity_1.ChangeRequest,
                incident_entity_1.Incident,
                service_catalog_entity_1.ServiceCatalog,
            ]),
        ],
        controllers: [
            support_ticket_controller_1.SupportTicketController,
            knowledge_base_controller_1.KnowledgeBaseController,
            asset_controller_1.AssetController,
            software_license_controller_1.SoftwareLicenseController,
            it_request_controller_1.ITRequestController,
            change_request_controller_1.ChangeRequestController,
            incident_controller_1.IncidentController,
            it_report_controller_1.ITReportController,
        ],
        providers: [
            support_ticket_service_1.SupportTicketService,
            knowledge_base_service_1.KnowledgeBaseService,
            asset_service_1.AssetService,
            software_license_service_1.SoftwareLicenseService,
            it_request_service_1.ITRequestService,
            change_request_service_1.ChangeRequestService,
            incident_service_1.IncidentService,
            it_report_service_1.ITReportService,
        ],
        exports: [
            support_ticket_service_1.SupportTicketService,
            knowledge_base_service_1.KnowledgeBaseService,
            asset_service_1.AssetService,
            software_license_service_1.SoftwareLicenseService,
            it_request_service_1.ITRequestService,
            change_request_service_1.ChangeRequestService,
            incident_service_1.IncidentService,
            it_report_service_1.ITReportService,
        ],
    })
], ItSupportModule);
//# sourceMappingURL=it-support.module.js.map