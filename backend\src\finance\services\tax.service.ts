import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { TaxRecord, TaxType, TaxStatus } from '../entities/tax-record.entity';

@Injectable()
export class TaxService {
  constructor(
    @InjectRepository(TaxRecord)
    private taxRecordRepository: Repository<TaxRecord>,
  ) {}

  async create(createTaxRecordDto: any): Promise<TaxRecord> {
    const taxRecordNumber = await this.generateTaxRecordNumber(createTaxRecordDto.taxType);
    
    const taxRecord = this.taxRecordRepository.create({
      ...createTaxRecordDto,
      taxRecordNumber,
      taxAmount: createTaxRecordDto.taxableAmount * (createTaxRecordDto.taxRate / 100),
    });

    // Calculate total amount
    taxRecord.totalAmount = taxRecord.taxAmount + (taxRecord.penaltyAmount || 0) + (taxRecord.interestAmount || 0);
    taxRecord.outstandingAmount = taxRecord.totalAmount;

    return this.taxRecordRepository.save(taxRecord);
  }

  async findAll(filters?: any): Promise<TaxRecord[]> {
    const queryBuilder = this.taxRecordRepository.createQueryBuilder('taxRecord');

    if (filters?.taxType) {
      queryBuilder.andWhere('taxRecord.taxType = :taxType', { taxType: filters.taxType });
    }

    if (filters?.status) {
      queryBuilder.andWhere('taxRecord.status = :status', { status: filters.status });
    }

    if (filters?.startDate && filters?.endDate) {
      queryBuilder.andWhere('taxRecord.taxPeriodStart >= :startDate AND taxRecord.taxPeriodEnd <= :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    return queryBuilder
      .orderBy('taxRecord.dueDate', 'ASC')
      .getMany();
  }

  async findOne(id: string): Promise<TaxRecord> {
    const taxRecord = await this.taxRecordRepository.findOne({ where: { id } });

    if (!taxRecord) {
      throw new NotFoundException(`Tax record with ID ${id} not found`);
    }

    return taxRecord;
  }

  async update(id: string, updateTaxRecordDto: any): Promise<TaxRecord> {
    const taxRecord = await this.findOne(id);

    Object.assign(taxRecord, updateTaxRecordDto);

    // Recalculate amounts if necessary
    if (updateTaxRecordDto.taxableAmount || updateTaxRecordDto.taxRate) {
      taxRecord.taxAmount = taxRecord.taxableAmount * (taxRecord.taxRate / 100);
    }

    taxRecord.totalAmount = taxRecord.taxAmount + (taxRecord.penaltyAmount || 0) + (taxRecord.interestAmount || 0);
    taxRecord.outstandingAmount = taxRecord.totalAmount - taxRecord.paidAmount;

    return this.taxRecordRepository.save(taxRecord);
  }

  async remove(id: string): Promise<void> {
    const taxRecord = await this.findOne(id);
    
    if (taxRecord.status === TaxStatus.FILED || taxRecord.status === TaxStatus.PAID) {
      throw new Error('Cannot delete filed or paid tax record');
    }

    await this.taxRecordRepository.remove(taxRecord);
  }

  async fileReturn(id: string, filedBy: string, referenceNumber?: string): Promise<TaxRecord> {
    const taxRecord = await this.findOne(id);

    taxRecord.status = TaxStatus.FILED;
    taxRecord.filedDate = new Date();
    taxRecord.filedBy = filedBy;
    if (referenceNumber) {
      taxRecord.referenceNumber = referenceNumber;
    }

    return this.taxRecordRepository.save(taxRecord);
  }

  async recordPayment(id: string, paymentAmount: number): Promise<TaxRecord> {
    const taxRecord = await this.findOne(id);

    taxRecord.paidAmount += paymentAmount;
    taxRecord.outstandingAmount = taxRecord.totalAmount - taxRecord.paidAmount;

    if (taxRecord.outstandingAmount <= 0) {
      taxRecord.status = TaxStatus.PAID;
      taxRecord.paidDate = new Date();
    }

    return this.taxRecordRepository.save(taxRecord);
  }

  async calculatePenaltiesAndInterest(id: string): Promise<TaxRecord> {
    const taxRecord = await this.findOne(id);
    const today = new Date();

    if (today > taxRecord.dueDate && taxRecord.status !== TaxStatus.PAID) {
      const daysOverdue = Math.floor((today.getTime() - taxRecord.dueDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Simple penalty calculation (1% per month overdue)
      const monthsOverdue = Math.ceil(daysOverdue / 30);
      taxRecord.penaltyAmount = taxRecord.taxAmount * 0.01 * monthsOverdue;
      
      // Simple interest calculation (0.5% per month)
      taxRecord.interestAmount = taxRecord.taxAmount * 0.005 * monthsOverdue;
      
      taxRecord.totalAmount = taxRecord.taxAmount + taxRecord.penaltyAmount + taxRecord.interestAmount;
      taxRecord.outstandingAmount = taxRecord.totalAmount - taxRecord.paidAmount;

      if (taxRecord.status === TaxStatus.FILED) {
        taxRecord.status = TaxStatus.OVERDUE;
      }
    }

    return this.taxRecordRepository.save(taxRecord);
  }

  async getTaxSummary(year: number): Promise<any> {
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year, 11, 31);

    const taxRecords = await this.findAll({
      startDate,
      endDate,
    });

    const summary = {
      year,
      totalTaxLiability: taxRecords.reduce((sum, record) => sum + record.taxAmount, 0),
      totalPaid: taxRecords.reduce((sum, record) => sum + record.paidAmount, 0),
      totalOutstanding: taxRecords.reduce((sum, record) => sum + record.outstandingAmount, 0),
      totalPenalties: taxRecords.reduce((sum, record) => sum + (record.penaltyAmount || 0), 0),
      totalInterest: taxRecords.reduce((sum, record) => sum + (record.interestAmount || 0), 0),
      byTaxType: this.groupByTaxType(taxRecords),
      byStatus: this.groupByStatus(taxRecords),
      overdueRecords: taxRecords.filter(record => record.status === TaxStatus.OVERDUE),
    };

    return summary;
  }

  private async generateTaxRecordNumber(taxType: TaxType): Promise<string> {
    const year = new Date().getFullYear();
    const typePrefix = this.getTaxTypePrefix(taxType);
    const prefix = `${typePrefix}-${year}-`;

    const lastRecord = await this.taxRecordRepository.findOne({
      where: { taxRecordNumber: Like(`${prefix}%`) },
      order: { taxRecordNumber: 'DESC' },
    });

    let nextNumber = 1;
    if (lastRecord) {
      const lastNumber = parseInt(lastRecord.taxRecordNumber.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }

  private getTaxTypePrefix(taxType: TaxType): string {
    switch (taxType) {
      case TaxType.INCOME_TAX: return 'IT';
      case TaxType.SALES_TAX: return 'ST';
      case TaxType.VAT: return 'VAT';
      case TaxType.PAYROLL_TAX: return 'PT';
      case TaxType.PROPERTY_TAX: return 'PROP';
      default: return 'TAX';
    }
  }

  private groupByTaxType(records: TaxRecord[]): any {
    return records.reduce((acc, record) => {
      if (!acc[record.taxType]) {
        acc[record.taxType] = {
          count: 0,
          totalTax: 0,
          totalPaid: 0,
          totalOutstanding: 0,
        };
      }
      acc[record.taxType].count++;
      acc[record.taxType].totalTax += record.taxAmount;
      acc[record.taxType].totalPaid += record.paidAmount;
      acc[record.taxType].totalOutstanding += record.outstandingAmount;
      return acc;
    }, {});
  }

  private groupByStatus(records: TaxRecord[]): any {
    return records.reduce((acc, record) => {
      if (!acc[record.status]) {
        acc[record.status] = {
          count: 0,
          totalAmount: 0,
        };
      }
      acc[record.status].count++;
      acc[record.status].totalAmount += record.totalAmount;
      return acc;
    }, {});
  }
}
