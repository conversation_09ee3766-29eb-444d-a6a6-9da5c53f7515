"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosReceipt = exports.ReceiptFormat = exports.ReceiptType = void 0;
const typeorm_1 = require("typeorm");
const pos_sale_entity_1 = require("./pos-sale.entity");
var ReceiptType;
(function (ReceiptType) {
    ReceiptType["SALE"] = "sale";
    ReceiptType["RETURN"] = "return";
    ReceiptType["EXCHANGE"] = "exchange";
    ReceiptType["VOID"] = "void";
    ReceiptType["REPRINT"] = "reprint";
    ReceiptType["GIFT_RECEIPT"] = "gift_receipt";
})(ReceiptType || (exports.ReceiptType = ReceiptType = {}));
var ReceiptFormat;
(function (ReceiptFormat) {
    ReceiptFormat["THERMAL"] = "thermal";
    ReceiptFormat["LASER"] = "laser";
    ReceiptFormat["EMAIL"] = "email";
    ReceiptFormat["SMS"] = "sms";
    ReceiptFormat["DIGITAL"] = "digital";
})(ReceiptFormat || (exports.ReceiptFormat = ReceiptFormat = {}));
let PosReceipt = class PosReceipt {
    id;
    saleId;
    sale;
    receiptNumber;
    type;
    format;
    content;
    template;
    isPrinted;
    printedAt;
    isEmailed;
    emailedAt;
    emailAddress;
    isSms;
    smsAt;
    phoneNumber;
    customFields;
    metadata;
    createdAt;
    updatedAt;
};
exports.PosReceipt = PosReceipt;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PosReceipt.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PosReceipt.prototype, "saleId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pos_sale_entity_1.PosSale),
    (0, typeorm_1.JoinColumn)({ name: 'saleId' }),
    __metadata("design:type", pos_sale_entity_1.PosSale)
], PosReceipt.prototype, "sale", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PosReceipt.prototype, "receiptNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ReceiptType,
        default: ReceiptType.SALE,
    }),
    __metadata("design:type", String)
], PosReceipt.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ReceiptFormat,
        default: ReceiptFormat.THERMAL,
    }),
    __metadata("design:type", String)
], PosReceipt.prototype, "format", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'longtext' }),
    __metadata("design:type", String)
], PosReceipt.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosReceipt.prototype, "template", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosReceipt.prototype, "isPrinted", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosReceipt.prototype, "printedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosReceipt.prototype, "isEmailed", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosReceipt.prototype, "emailedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true }),
    __metadata("design:type", String)
], PosReceipt.prototype, "emailAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], PosReceipt.prototype, "isSms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], PosReceipt.prototype, "smsAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], PosReceipt.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosReceipt.prototype, "customFields", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PosReceipt.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PosReceipt.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PosReceipt.prototype, "updatedAt", void 0);
exports.PosReceipt = PosReceipt = __decorate([
    (0, typeorm_1.Entity)('pos_receipts')
], PosReceipt);
//# sourceMappingURL=pos-receipt.entity.js.map