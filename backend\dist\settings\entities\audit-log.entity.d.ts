export declare enum AuditAction {
    CREATE = "create",
    READ = "read",
    UPDATE = "update",
    DELETE = "delete",
    LOGIN = "login",
    LOGOUT = "logout",
    EXPORT = "export",
    IMPORT = "import",
    APPROVE = "approve",
    REJECT = "reject",
    SEND = "send",
    RECEIVE = "receive",
    CUSTOM = "custom"
}
export declare enum AuditLevel {
    INFO = "info",
    WARNING = "warning",
    ERROR = "error",
    CRITICAL = "critical"
}
export declare class AuditLog {
    id: string;
    action: AuditAction;
    level: AuditLevel;
    entityType: string;
    entityId: string;
    userId: string;
    userName: string;
    ipAddress: string;
    userAgent: string;
    description: string;
    oldValues: any;
    newValues: any;
    changes: any;
    context: any;
    timestamp: Date;
    metadata: any;
    createdAt: Date;
}
