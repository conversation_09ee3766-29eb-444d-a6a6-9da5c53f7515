import { Repository } from 'typeorm';
import { ReturnedInvoice } from '../entities/returned-invoice.entity';
import { ReturnedInvoiceItem } from '../entities/returned-invoice-item.entity';
import { CreateReturnedInvoiceDto } from '../dto/create-returned-invoice.dto';
export declare class ReturnedInvoiceService {
    private returnedInvoiceRepository;
    private returnedInvoiceItemRepository;
    constructor(returnedInvoiceRepository: Repository<ReturnedInvoice>, returnedInvoiceItemRepository: Repository<ReturnedInvoiceItem>);
    create(createReturnedInvoiceDto: CreateReturnedInvoiceDto, tenantId: string): Promise<ReturnedInvoice>;
    findAll(tenantId: string, filters?: {
        startDate?: string;
        endDate?: string;
        customerId?: string;
        status?: string;
        search?: string;
    }): Promise<ReturnedInvoice[]>;
    findOne(id: string, tenantId: string): Promise<ReturnedInvoice>;
    update(id: string, updateReturnedInvoiceDto: Partial<CreateReturnedInvoiceDto>, tenantId: string): Promise<ReturnedInvoice>;
    remove(id: string, tenantId: string): Promise<void>;
    updateStatus(id: string, status: string, tenantId: string): Promise<ReturnedInvoice>;
    getReturnedInvoiceStats(tenantId: string): Promise<{
        totalReturns: number;
        pendingReturns: number;
        completedReturns: number;
        totalReturnAmount: number;
    }>;
    private generateReturnNumber;
    exportData(tenantId: string, filters?: any): Promise<{
        returnNumber: string;
        customerName: string;
        originalInvoiceNumber: string;
        returnDate: Date;
        originalAmount: number;
        returnAmount: number;
        totalReturnAmount: number;
        status: "pending" | "cancelled" | "completed" | "processing";
        reason: string;
        refundMethod: "refund" | "credit_note" | "exchange";
        processedBy: string;
        processedDate: Date | null;
    }[]>;
}
