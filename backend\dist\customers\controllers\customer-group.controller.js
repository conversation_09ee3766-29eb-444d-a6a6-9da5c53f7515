"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerGroupController = void 0;
const common_1 = require("@nestjs/common");
const customer_group_service_1 = require("../services/customer-group.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CustomerGroupController = class CustomerGroupController {
    customerGroupService;
    constructor(customerGroupService) {
        this.customerGroupService = customerGroupService;
    }
    async create(createGroupDto) {
        return this.customerGroupService.create(createGroupDto);
    }
    async findAll() {
        return this.customerGroupService.findAll();
    }
    async findActive() {
        return this.customerGroupService.getActiveGroups();
    }
    async findAllWithCounts() {
        return this.customerGroupService.getGroupsWithCustomerCounts();
    }
    async findOne(id) {
        return this.customerGroupService.findOne(id);
    }
    async findByCode(code) {
        return this.customerGroupService.findByCode(code);
    }
    async getGroupStatistics(id) {
        return this.customerGroupService.getGroupStatistics(id);
    }
    async update(id, updateGroupDto) {
        return this.customerGroupService.update(id, updateGroupDto);
    }
    async activate(id) {
        return this.customerGroupService.activateGroup(id);
    }
    async deactivate(id) {
        return this.customerGroupService.deactivateGroup(id);
    }
    async addCustomerToGroup(groupId, customerId) {
        return this.customerGroupService.addCustomerToGroup(customerId, groupId);
    }
    async removeCustomerFromGroup(customerId) {
        return this.customerGroupService.removeCustomerFromGroup(customerId);
    }
    async bulkAssignCustomers(groupId, assignmentData) {
        return this.customerGroupService.bulkAssignCustomers(assignmentData.customerIds, groupId);
    }
    async applyGroupDiscount(groupId) {
        await this.customerGroupService.applyGroupDiscountToCustomers(groupId);
        return { message: 'Group discount applied to all customers' };
    }
    async remove(id) {
        return this.customerGroupService.remove(id);
    }
};
exports.CustomerGroupController = CustomerGroupController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('active'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "findActive", null);
__decorate([
    (0, common_1.Get)('with-counts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "findAllWithCounts", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('code/:code'),
    __param(0, (0, common_1.Param)('code')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "findByCode", null);
__decorate([
    (0, common_1.Get)(':id/statistics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "getGroupStatistics", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "activate", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "deactivate", null);
__decorate([
    (0, common_1.Post)(':id/customers/:customerId'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "addCustomerToGroup", null);
__decorate([
    (0, common_1.Delete)(':id/customers/:customerId'),
    __param(0, (0, common_1.Param)('customerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "removeCustomerFromGroup", null);
__decorate([
    (0, common_1.Post)(':id/bulk-assign'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "bulkAssignCustomers", null);
__decorate([
    (0, common_1.Post)(':id/apply-discount'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "applyGroupDiscount", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerGroupController.prototype, "remove", null);
exports.CustomerGroupController = CustomerGroupController = __decorate([
    (0, common_1.Controller)('customer-groups'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [customer_group_service_1.CustomerGroupService])
], CustomerGroupController);
//# sourceMappingURL=customer-group.controller.js.map