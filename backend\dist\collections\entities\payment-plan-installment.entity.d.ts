import { PaymentPlan } from './payment-plan.entity';
export declare enum InstallmentStatus {
    PENDING = "pending",
    PAID = "paid",
    OVERDUE = "overdue",
    PARTIAL = "partial",
    SKIPPED = "skipped",
    WAIVED = "waived"
}
export declare class PaymentPlanInstallment {
    id: string;
    paymentPlanId: string;
    paymentPlan: PaymentPlan;
    installmentNumber: number;
    dueDate: Date;
    amount: number;
    paidAmount: number;
    remainingAmount: number;
    status: InstallmentStatus;
    paidDate: Date;
    daysOverdue: number;
    lateFee: number;
    notes: string;
    paymentDetails: any;
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
