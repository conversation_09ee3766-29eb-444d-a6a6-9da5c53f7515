import { CategoryService } from '../services/category.service';
import { Category } from '../entities/category.entity';
export declare class CategoryController {
    private readonly categoryService;
    constructor(categoryService: CategoryService);
    create(createCategoryDto: Partial<Category>): Promise<Category>;
    findAll(): Promise<Category[]>;
    getCategoryTree(): Promise<Category[]>;
    findRootCategories(): Promise<Category[]>;
    getStatistics(): Promise<any>;
    getProductCountByCategory(): Promise<{
        categoryId: string;
        categoryName: string;
        productCount: number;
    }[]>;
    searchCategories(searchTerm: string): Promise<Category[]>;
    findByParent(parentId: string): Promise<Category[]>;
    findOne(id: string): Promise<Category>;
    getCategoryPath(id: string): Promise<{
        path: string[];
    }>;
    update(id: string, updateCategoryDto: Partial<Category>): Promise<Category>;
    moveCategory(id: string, moveData: {
        newParentId: string | null;
    }): Promise<Category>;
    generateCategoryCode(data: {
        name: string;
    }): Promise<{
        code: string;
    }>;
    remove(id: string): Promise<void>;
}
