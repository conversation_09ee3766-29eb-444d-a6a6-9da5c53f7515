"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TicketService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ticket_entity_1 = require("../entities/ticket.entity");
const ticket_comment_entity_1 = require("../entities/ticket-comment.entity");
let TicketService = class TicketService {
    ticketRepository;
    ticketCommentRepository;
    constructor(ticketRepository, ticketCommentRepository) {
        this.ticketRepository = ticketRepository;
        this.ticketCommentRepository = ticketCommentRepository;
    }
    async create(ticketData) {
        const ticketNumber = await this.generateTicketNumber();
        const ticket = this.ticketRepository.create({
            ...ticketData,
            ticketNumber,
            status: ticket_entity_1.TicketStatus.OPEN,
            createdAt: new Date(),
        });
        return this.ticketRepository.save(ticket);
    }
    async findAll() {
        return this.ticketRepository.find({
            relations: ['assignedTo', 'createdBy', 'comments'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const ticket = await this.ticketRepository.findOne({
            where: { id },
            relations: ['assignedTo', 'createdBy', 'comments', 'comments.author'],
        });
        if (!ticket) {
            throw new common_1.NotFoundException(`Ticket with ID ${id} not found`);
        }
        return ticket;
    }
    async update(id, updateData) {
        await this.ticketRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        const ticket = await this.findOne(id);
        await this.ticketRepository.remove(ticket);
    }
    async findByStatus(status) {
        return this.ticketRepository.find({
            where: { status },
            relations: ['assignedTo', 'createdBy'],
            order: { createdAt: 'DESC' },
        });
    }
    async findByPriority(priority) {
        return this.ticketRepository.find({
            where: { priority },
            relations: ['assignedTo', 'createdBy'],
            order: { createdAt: 'DESC' },
        });
    }
    async findByAssignee(assigneeId) {
        return this.ticketRepository.find({
            where: { assignedToId: assigneeId },
            relations: ['createdBy'],
            order: { createdAt: 'DESC' },
        });
    }
    async findByCreator(creatorId) {
        return this.ticketRepository.find({
            where: { createdById: creatorId },
            relations: ['assignedTo'],
            order: { createdAt: 'DESC' },
        });
    }
    async assignTicket(ticketId, assigneeId) {
        await this.ticketRepository.update(ticketId, {
            assignedToId: assigneeId,
            status: ticket_entity_1.TicketStatus.IN_PROGRESS,
        });
        await this.addComment(ticketId, `Ticket assigned to user ${assigneeId}`, 'system');
        return this.findOne(ticketId);
    }
    async updateStatus(ticketId, status, userId) {
        await this.ticketRepository.update(ticketId, { status });
        await this.addComment(ticketId, `Status changed to ${status}`, userId || 'system');
        return this.findOne(ticketId);
    }
    async updatePriority(ticketId, priority, userId) {
        await this.ticketRepository.update(ticketId, { priority });
        await this.addComment(ticketId, `Priority changed to ${priority}`, userId || 'system');
        return this.findOne(ticketId);
    }
    async addComment(ticketId, content, authorId) {
        const comment = this.ticketCommentRepository.create({
            ticketId,
            content,
            authorId,
            createdAt: new Date(),
        });
        return this.ticketCommentRepository.save(comment);
    }
    async getTicketComments(ticketId) {
        return this.ticketCommentRepository.find({
            where: { ticketId },
            relations: ['author'],
            order: { createdAt: 'ASC' },
        });
    }
    async searchTickets(searchTerm) {
        return this.ticketRepository
            .createQueryBuilder('ticket')
            .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
            .leftJoinAndSelect('ticket.createdBy', 'createdBy')
            .where('ticket.title ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('ticket.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('ticket.ticketNumber ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('ticket.createdAt', 'DESC')
            .getMany();
    }
    async getTicketStatistics() {
        const totalTickets = await this.ticketRepository.count();
        const openTickets = await this.ticketRepository.count({ where: { status: ticket_entity_1.TicketStatus.OPEN } });
        const inProgressTickets = await this.ticketRepository.count({ where: { status: ticket_entity_1.TicketStatus.IN_PROGRESS } });
        const resolvedTickets = await this.ticketRepository.count({ where: { status: ticket_entity_1.TicketStatus.RESOLVED } });
        const closedTickets = await this.ticketRepository.count({ where: { status: ticket_entity_1.TicketStatus.CLOSED } });
        const highPriorityTickets = await this.ticketRepository.count({ where: { priority: ticket_entity_1.TicketPriority.HIGH } });
        const criticalTickets = await this.ticketRepository.count({ where: { priority: ticket_entity_1.TicketPriority.CRITICAL } });
        return {
            totalTickets,
            openTickets,
            inProgressTickets,
            resolvedTickets,
            closedTickets,
            highPriorityTickets,
            criticalTickets,
            resolutionRate: totalTickets > 0 ? ((resolvedTickets + closedTickets) / totalTickets) * 100 : 0,
        };
    }
    async getOverdueTickets() {
        const now = new Date();
        return this.ticketRepository
            .createQueryBuilder('ticket')
            .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
            .leftJoinAndSelect('ticket.createdBy', 'createdBy')
            .where('ticket.dueDate < :now', { now })
            .andWhere('ticket.status NOT IN (:...statuses)', {
            statuses: [ticket_entity_1.TicketStatus.RESOLVED, ticket_entity_1.TicketStatus.CLOSED]
        })
            .orderBy('ticket.dueDate', 'ASC')
            .getMany();
    }
    async getTicketsByCategory(category) {
        return this.ticketRepository.find({
            where: { category },
            relations: ['assignedTo', 'createdBy'],
            order: { createdAt: 'DESC' },
        });
    }
    async escalateTicket(ticketId, reason, userId) {
        const ticket = await this.findOne(ticketId);
        let newPriority = ticket.priority;
        if (ticket.priority === ticket_entity_1.TicketPriority.LOW) {
            newPriority = ticket_entity_1.TicketPriority.MEDIUM;
        }
        else if (ticket.priority === ticket_entity_1.TicketPriority.MEDIUM) {
            newPriority = ticket_entity_1.TicketPriority.HIGH;
        }
        else if (ticket.priority === ticket_entity_1.TicketPriority.HIGH) {
            newPriority = ticket_entity_1.TicketPriority.CRITICAL;
        }
        await this.ticketRepository.update(ticketId, { priority: newPriority });
        await this.addComment(ticketId, `Ticket escalated to ${newPriority}: ${reason}`, userId || 'system');
        return this.findOne(ticketId);
    }
    async closeTicket(ticketId, resolution, userId) {
        await this.ticketRepository.update(ticketId, {
            status: ticket_entity_1.TicketStatus.CLOSED,
            resolution,
            resolvedAt: new Date(),
        });
        await this.addComment(ticketId, `Ticket closed: ${resolution}`, userId || 'system');
        return this.findOne(ticketId);
    }
    async reopenTicket(ticketId, reason, userId) {
        await this.ticketRepository.update(ticketId, {
            status: ticket_entity_1.TicketStatus.OPEN,
            resolvedAt: null,
            resolution: null,
        });
        await this.addComment(ticketId, `Ticket reopened: ${reason}`, userId || 'system');
        return this.findOne(ticketId);
    }
    async generateTicketNumber() {
        const count = await this.ticketRepository.count();
        const sequence = (count + 1).toString().padStart(6, '0');
        const year = new Date().getFullYear();
        return `TKT-${year}-${sequence}`;
    }
    async getDashboardMetrics() {
        const stats = await this.getTicketStatistics();
        const overdueTickets = await this.getOverdueTickets();
        return {
            ...stats,
            overdueCount: overdueTickets.length,
            urgentTickets: stats.highPriorityTickets + stats.criticalTickets,
        };
    }
};
exports.TicketService = TicketService;
exports.TicketService = TicketService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ticket_entity_1.Ticket)),
    __param(1, (0, typeorm_1.InjectRepository)(ticket_comment_entity_1.TicketComment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], TicketService);
//# sourceMappingURL=ticket.service.js.map