"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReturnedInvoiceController = void 0;
const common_1 = require("@nestjs/common");
const returned_invoice_service_1 = require("../services/returned-invoice.service");
const create_returned_invoice_dto_1 = require("../dto/create-returned-invoice.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let ReturnedInvoiceController = class ReturnedInvoiceController {
    returnedInvoiceService;
    constructor(returnedInvoiceService) {
        this.returnedInvoiceService = returnedInvoiceService;
    }
    create(createReturnedInvoiceDto, req) {
        return this.returnedInvoiceService.create(createReturnedInvoiceDto, req.user.tenantId);
    }
    findAll(req, query) {
        const filters = {
            startDate: query.startDate,
            endDate: query.endDate,
            customerId: query.customerId,
            status: query.status,
            search: query.search,
        };
        return this.returnedInvoiceService.findAll(req.user.tenantId, filters);
    }
    getStats(req) {
        return this.returnedInvoiceService.getReturnedInvoiceStats(req.user.tenantId);
    }
    async exportData(req, query) {
        const filters = {
            startDate: query.startDate,
            endDate: query.endDate,
            customerId: query.customerId,
            status: query.status,
            search: query.search,
        };
        const data = await this.returnedInvoiceService.exportData(req.user.tenantId, filters);
        return {
            success: true,
            data,
            message: 'Returned invoices data exported successfully',
        };
    }
    findOne(id, req) {
        return this.returnedInvoiceService.findOne(id, req.user.tenantId);
    }
    update(id, updateReturnedInvoiceDto, req) {
        return this.returnedInvoiceService.update(id, updateReturnedInvoiceDto, req.user.tenantId);
    }
    updateStatus(id, status, req) {
        return this.returnedInvoiceService.updateStatus(id, status, req.user.tenantId);
    }
    remove(id, req) {
        return this.returnedInvoiceService.remove(id, req.user.tenantId);
    }
};
exports.ReturnedInvoiceController = ReturnedInvoiceController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_returned_invoice_dto_1.CreateReturnedInvoiceDto, Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('export'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReturnedInvoiceController.prototype, "exportData", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ReturnedInvoiceController.prototype, "remove", null);
exports.ReturnedInvoiceController = ReturnedInvoiceController = __decorate([
    (0, common_1.Controller)('sales/returned-invoices'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [returned_invoice_service_1.ReturnedInvoiceService])
], ReturnedInvoiceController);
//# sourceMappingURL=returned-invoice.controller.js.map