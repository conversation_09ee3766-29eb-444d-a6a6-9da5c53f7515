import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { CompanyService } from '../company/company.service';
import { RegisterCompanyDto } from './dto/register-company.dto';
import { LoginDto } from './dto/login.dto';
import { User, UserRole } from '../user/entities/user.entity';
export declare class AuthService {
    private userService;
    private companyService;
    private jwtService;
    constructor(userService: UserService, companyService: CompanyService, jwtService: JwtService);
    registerCompany(registerDto: RegisterCompanyDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: UserRole;
            company: {
                id: string;
                name: string;
                slug: string;
                tenantId: string;
            };
        };
        token: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: UserRole;
            company: {
                id: string;
                name: string;
                slug: string;
                tenantId: string;
            };
        };
        token: string;
    }>;
    validateUser(payload: any): Promise<User>;
}
