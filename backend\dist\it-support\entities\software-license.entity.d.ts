export declare enum LicenseType {
    PERPETUAL = "perpetual",
    SUBSCRIPTION = "subscription",
    VOLUME = "volume",
    OEM = "oem",
    TRIAL = "trial",
    FREEWARE = "freeware",
    OPEN_SOURCE = "open_source"
}
export declare enum LicenseStatus {
    ACTIVE = "active",
    EXPIRED = "expired",
    SUSPENDED = "suspended",
    CANCELLED = "cancelled",
    TRIAL = "trial"
}
export declare class SoftwareLicense {
    id: string;
    softwareName: string;
    vendor: string;
    version: string;
    type: LicenseType;
    status: LicenseStatus;
    licenseKey: string;
    totalLicenses: number;
    usedLicenses: number;
    availableLicenses: number;
    purchaseDate: Date;
    expiryDate: Date;
    cost: number;
    annualCost: number;
    currency: string;
    purchaseOrder: string;
    description: string;
    assignedUsers: string[];
    assignedAssets: string[];
    notes: string;
    attachments: string[];
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
